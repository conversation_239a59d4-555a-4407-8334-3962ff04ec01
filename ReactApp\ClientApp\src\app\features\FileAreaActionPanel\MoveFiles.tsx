import React, {  useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { Row, Col, Form, Button, Drawer, Modal as AntModal, Skeleton, Alert, Radio, Space } from 'antd';
import {
    DoubleRightOutlined,ExclamationCircleOutlined
} from "@ant-design/icons/lib/icons";

import styles from "./index.module.less";
import SelectedFilesGrid, {
    ColumnConfig,
} from "@app/features/FileArea/SelectedFilesGrid";
import { IFile } from "@app/types/fileAreaTypes";
import { getFileDetailsByFileId, getBinderFileAreaNodes } from "@app/api/fileAreaService";
import config from "@app/utils/config";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from "@app/utils/logger";
import { FolderTree } from "@app/components/FolderTree";
import { FormInstance } from "antd/lib/form";
import { FileRecord, FileEvents, UrlEvents } from "@app/components/forms/UploaderSubmit/types";
import { Store } from "antd/lib/form/interface";
import { FileDetailsOptions } from "@app/types/FileDetailsOptions";
import { useHistory, useParams } from "react-router-dom";
import {updateContextMenuMoveFilesOption,} from '@app/redux/actions/fileAreaActions';
import { moveFilesService } from '@app/api/fileAreaService';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import { IFolderTreeResponse } from '@app/types/FileAreaFolderTreeTypes';
import { IFolder } from '@app/types/FileAreaFolderTreeTypes';

const { confirm } = AntModal;
const LIMIT = 10;
const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';
const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';


export interface MoveFileProps {
    selectedFiles: IFile[];
    onClosePopup: () => void;
    options: FileDetailsOptions;
    form: FormInstance;
    forManageFiles?: boolean;
    onFormChange?: (event: any) => void;
    onFolderTreeChange: (event: any) => void;
    siteId: string;
    binderId:string;
    onSuccess: () => void;//sync grid
    showMoveFileModal:boolean;
}

export default (props: MoveFileProps) => {
  const formFolder = props.form.getFieldValue('folder')
  const defaultFolder = true ? (formFolder ? formFolder.toString() : '1') : '';
const history = useHistory();

const [selectedClient, setSelectedClient] = useState<any>(null);
const [selectedFileArea, setSelectedFileArea] = useState<any>(null);
const [selectedFolder, setSelectedFolder] = useState(defaultFolder);
const [searchByField, setSearchByField] = useState("clientRef");
const [selectedFileList, setSelectedFileList] = useState<IFile[]>([]);

// Helper functions to get IDs from selected objects
const getSelectedClientId = () => selectedClient?.id || null;
const getSelectedFileAreaId = () => selectedFileArea?.id || null;
const moveColumnConfigs: ColumnConfig[] = [
    { title: "", dataIndex: "remove", key: "remove", width: 40 },
    { title: "Title", dataIndex: "title", key: "title", ellipsis: true },

];
const [binderFileAreaNodes, setBinderFileAreaNodes] = useState<IFolderTreeResponse | null>(null);
const [folderTreeData, setFolderTreeData] = useState(defaultFolder);
const [clientSelectKey, setClientSelectKey] = useState(0);
const [fileAreaKey, setFileAreaKey] = useState(0);
const dispatch = useDispatch();

useEffect(() => {
  setSelectedFileList([]);
  props.form.resetFields();
  setSearchByField("clientRef");
  setClientSelectKey(prevKey => prevKey + 1);
  setFileAreaKey(prevKey => prevKey + 1);
    const mapFolderPath = async () => {
        const fileList: IFile[] = props.selectedFiles;
        setSelectedFileList(fileList);
        props.onFolderTreeChange(-1);
    };
    mapFolderPath();
}, [props.selectedFiles]);

const handleFilesChange = (fileList: any[]) => {
    if (fileList.length > 0) {
        setSelectedFileList(fileList);
        handleMoveFilesUpdateDetails(selectedFolder, fileList, getSelectedClientId(),props.binderId,getSelectedFileAreaId())
    } else {
      handleShowMoveFilesModalCancel(false);
      props.onClosePopup();
    }
};

  const transformFolders = (folders: any[]): IFolder[] => {
    const primaryFolders = folders.filter((folder) => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);

    return primaryFolders.map((folder) => ({
      id: folder.id,
      name: folder.name,
      subFolders: folder.childNodes.map((child: any) => ({
        id: child.id,
        name: child.name,
        subFolders: [],
        retention: child.retention || 0,
      })),
    }));
  };

//client dropdown pagination record
const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {

    const transformFilters: any = {};
    if (searchValue) {
        transformFilters.search = searchValue;
    }

    const getClientIdParameters = {
        limit: LIMIT,
        offset: page - 1,
        field: searchByField,
        ...transformFilters,
    }

    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannelList, getClientIdParameters)
        .then((res: any) => {
            if (res.data) {
                // Auto-select if there's only one client and no selection yet
                if (res.data.records && res.data.records.length === 1 && !selectedClient) {
                    const singleClient = res.data.records[0];
                    setSelectedClient(singleClient);
                    props.form.setFieldsValue({ clientId: singleClient.id });
                    setClientSelectKey(prevKey => prevKey + 1);
                }

                // If we have a selected client but it's not in the current records,
                // merge it to ensure proper display
                if (selectedClient && res.data.records) {
                    const existingIds = res.data.records.map((record: any) => record.id);
                    if (!existingIds.includes(selectedClient.id)) {
                        res.data.records = [selectedClient, ...res.data.records];
                    }
                }

                return res.data;
            } else {
                // logger.error('SideSelection', 'getPaginatedRecords', res.error);
                return []
            }
        })
        .catch((error: any) => {
            // logger.error('SideSelection', 'getPaginatedRecords', error);
            return [];
        });


};

//File area pagination record
const getFileAreaPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<any> => {
  const transformFilters: any = {};
  if (searchValue) {
    transformFilters.search = searchValue;
  }

  const getClientIdParameters = {
    limit: LIMIT,
    offset: page - 1,
    ...transformFilters,
  };

  if (getSelectedClientId()) {
    getClientIdParameters['siteId'] = getSelectedClientId();
  }

  return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList, getClientIdParameters)
    .then((res: any) => {
      if (res.data) {
        let mappedRecords = res.data.records.map((record: any) => ({
          ...record,
          id: record.binderId, // Assign binderId as id
        }));

        // Auto-select if there's only one file area and no selection yet
        if (mappedRecords.length === 1 && !selectedFileArea) {
          const singleFileArea = mappedRecords[0];
          setSelectedFileArea(singleFileArea);
          getFolderTreeByFileArea(singleFileArea.id);
          props.form.setFieldsValue({ binderId: singleFileArea.id });
          setFileAreaKey(prevKey => prevKey + 1);
        }

        // If we have a selected file area but it's not in the current records,
        // merge it to ensure proper display
        if (selectedFileArea && mappedRecords) {
          const existingIds = mappedRecords.map((record: any) => record.id);
          if (!existingIds.includes(selectedFileArea.id)) {
            mappedRecords = [selectedFileArea, ...mappedRecords];
          }
        }

        return {
          records: mappedRecords,
          pageCount: res.data.pageCount,
          pageNumber: res.data.pageNumber,
          pageSize: res.data.pageSize,
          totalRecordCount: res.data.totalRecordCount,
        };
      } else {
        return { records: [], pageCount: 0, pageNumber: 1, pageSize: 0, totalRecordCount: 0 };
      }
    })
    .catch((error: any) => {
      logger.error('Error fetching getFileAreaPaginatedRecords:', 'getFileAreaPaginatedRecords', error);
      return { records: [], pageCount: 0, pageNumber: 1, pageSize: 0, totalRecordCount: 0 };
    });
};



//Getting the folder tree by the selected file Area
const getFolderTreeByFileArea = (fileAreaId: string) => {
   getBinderFileAreaNodes(fileAreaId)
        .then((response: any) => {
            if (response.data) {
                const transformedFolders = transformFolders(response.data.folders);
                setBinderFileAreaNodes({
                  siteId:props.siteId,
                  siteName: response.data.name||"--",
                  siteStatusId: 1,
                  folders: transformedFolders,
                });
            } else {
                return [];
            }
        })
        .catch((error) => {
            logger.error('File Area Module', 'Get Folder Tree By binder id', error);
        });
};

const handleShowMoveFilesModalCancel = (hasSelectedFiles = true) => {
    if (hasSelectedFiles && moveFileDetails?.folderId !== undefined) {
      onCancelMoveFilesModal();
    } else {
      resetMoveFilesModal();
    }
  };

const onCancelMoveFilesModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        resetMoveFilesModal();
      },
    });
  };

    const [moveFileDetails, setMoveFilesDetails] = useState<{
      fileList: IFile[];
      folderId: number;
      sourceClientId: string;
      destinationClientId: number;
      sourcefileAreaId:string;
      destinationfileAreaId:string;
    }>();

   const resetMoveFilesModal = () => {
      props.onSuccess();
      props.form.resetFields();
      setMoveFilesDetails({
        folderId: undefined as any,
        fileList: undefined as any,
        sourceClientId: undefined as any,
        destinationClientId: undefined as any,
        sourcefileAreaId: undefined as any,
        destinationfileAreaId: undefined as any,
      });
      dispatch(updateContextMenuMoveFilesOption(false));
      props.onClosePopup();
    };

     const handleMoveFilesUpdateDetails = (folderId: number, fileList: IFile[], destinationClientId: number,sourcefileAreaId:string,destinationfileAreaId:string) => {
        setMoveFilesDetails({ folderId: folderId, fileList: fileList, sourceClientId: props.siteId, destinationClientId: destinationClientId,sourcefileAreaId:sourcefileAreaId,destinationfileAreaId:destinationfileAreaId });
      };

//Update move files
const handleMoveFileUpdate = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    moveFilesService(
      moveFileDetails?.fileList.map((file) => file.id),
      moveFileDetails?.folderId ? moveFileDetails.folderId : 0,
      moveFileDetails?.sourcefileAreaId ? moveFileDetails.sourcefileAreaId : "",
      moveFileDetails?.destinationfileAreaId ? moveFileDetails.destinationfileAreaId : "",
    )
      .then((response) => {
        successNotification([''], 'File(s) Moved Successfully');
        props.onSuccess();
        resetMoveFilesModal();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], error.message);
        }
        logger.error('File Area Module', 'Move files', error);
      });
  };

return (

  <Drawer
    visible={props.showMoveFileModal}
    title={'Move File(s)'}
    onClose={() => handleShowMoveFilesModalCancel()}
    className={'yjDrawerPanel'}
    width={700}
    placement="right"
    footer={[
      <div className={styles.yjMoveFilesInfoFooter}>
      <span className={styles.yjMoveFilesInfo}>

      </span>

        <div className={styles.yjMoveFilesInfoButtons}>
          <Button key="cancel" type="primary" onClick={() => handleShowMoveFilesModalCancel()}>
            Cancel
          </Button>
          <Button key="Move" type="primary" onClick={handleMoveFileUpdate}
                  disabled={!moveFileDetails?.fileList || !getSelectedClientId() || !getSelectedFileAreaId() || !selectedFolder}>
            Move Files
          </Button>
        </div>
      </div>
    ]}
  >
    <Row gutter={24}>
      <Col span={24}>
      <Form form={props.form} key="moveFilesForm" layout="vertical" >
        <Row gutter={24}>
          <Col span={24}>
              <Form.Item label="" name="fileList">
                {selectedFileList.length > 0 ? (
                  <SelectedFilesGrid
                    onFilesChange={handleFilesChange}
                    columnConfigs={moveColumnConfigs}
                    dataList={selectedFileList}
                  />
                ) : (
                  <Skeleton/>
                )}
              </Form.Item>
          </Col>
          <Col span={24}>
            <Form.Item label="SEARCH BY">
              <Radio.Group
                className={styles.yjSearchByRadioGroup}
                value={searchByField}
                onChange={(e) => {
                  setSearchByField(e.target.value);
                  setClientSelectKey(prevKey => prevKey + 1);
                }}
              >
                <Space>
                  <Radio value="clientRef">Client Ref</Radio>
                  <Radio value="name">Client Name</Radio>
                </Space>
              </Radio.Group>
            </Form.Item>
          </Col>
          <Col span={24}>
                <Form.Item label="MOVE TO CLIENT" name="clientId">
                  <InfinitySelect
                    key={`client-${clientSelectKey}`}
                    returnObject={true}
                    getPaginatedRecords={(page, method, searchValue) => getPaginatedRecords(page, method, searchValue)}
                    formatValue={(value) => {
                      return `${value.displayText}`;
                    }}
                    notFoundContent="No Offices Available"
                    notLoadContent="Failed to load values in office dropdown"
                    onChange={(value, options) => {
                      props.form.resetFields(['binderId']);
                      props.form.resetFields(['folder']);
                      setBinderFileAreaNodes(null);
                      // Store the full client object
                      const clientObject = options && options.length > 0 ? options[0] : null;
                      setSelectedClient(clientObject);
                      // Reset file area selection when client changes
                      setSelectedFileArea(null);
                    }}
                    placeholder="Please Select Client(s)"
                    waitCharCount={3}
                    value={getSelectedClientId()}
                    defaultValues={getSelectedClientId() ? [getSelectedClientId()] : undefined}
                  />
                </Form.Item>
          </Col>

          <Col span={24}>
                <Form.Item label="FILE AREA OF THE CLIENT" name="binderId">
                  <InfinitySelect
                    key={`fileArea-${getSelectedClientId()}-${fileAreaKey}`}
                    returnObject={true}
                    getPaginatedRecords={(page, method, searchValue) => getFileAreaPaginatedRecords(page, method, searchValue)}
                    formatValue={(value) => {
                      return `${value.binderName}`;
                    }}
                    notFoundContent="No file areas Available"
                    notLoadContent="Failed to load values in file areas dropdown"
                    onChange={(value, options) => {
                      // Store the full file area object
                      const fileAreaObject = options && options.length > 0 ? options[0] : null;
                      setSelectedFileArea(fileAreaObject);
                      if (fileAreaObject) {
                        getFolderTreeByFileArea(fileAreaObject.id);
                      }
                    }}
                    placeholder="Please Select File Areas(s)"
                    waitCharCount={3}
                    value={getSelectedFileAreaId()}
                    defaultValues={getSelectedFileAreaId() ? [getSelectedFileAreaId()] : undefined}
                    disabled={!getSelectedClientId()}
                  />
                </Form.Item>
          </Col>

          <Col span={24} className={styles.yjFolderTreeUrlUploader}>
                <Form.Item label="Move" name="folder">
                  <FolderTree
                    showTitle={false}
                    maxHeight="255px"
                    data={binderFileAreaNodes}
                    onSelectFolder={(keys) => {
                      props.onFolderTreeChange(keys);
                      setSelectedFolder(keys);
                      handleMoveFilesUpdateDetails(keys, selectedFileList, getSelectedClientId(), props.binderId, getSelectedFileAreaId())
                    }}
                    disableRoot={true}
                    controlSelection={true}
                    autoExpandParent={true}
                    selectedKeys={[selectedFolder.toString()]}
                    disabled={false}
                    onClick={() => {

                    }}
                  />
                  </Form.Item>

          </Col>
          <Col span={24}>
          <Alert message="File(s) published to portal will be unpublished when moved" type="warning" showIcon />
          </Col>
        </Row>
      </Form>
      </Col>
    </Row>
  </Drawer>
  );
};