@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../../styles/';

.yjAddUserManagementButtonWrapper {
  background: @color-bg-wizard-button-section;
  border-top: 1px solid @border-color-base;
  padding: 1em;

  div {

    button {
      margin: 0 .3em;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .flex-mixin(center, flex, flex-end);
}

.yjUserManagementPageTitle {
  background-color: @color-bg-titlewrapper;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .05);
  padding: 0 2em;

  h2 {
    color: @color-page-title;
    margin: .2em 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 99%;
  }
}
