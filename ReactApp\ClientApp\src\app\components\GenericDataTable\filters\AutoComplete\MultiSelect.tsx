import React, { useContext, useEffect, useState } from "react";
import { Select } from "antd";
import { DataTableContext } from "../../DataTableContext";
import {
  updateFilterValue,
  removeFilterValue,
} from "../../DataTableContext/actions";
import { getOptions } from "../../../../api/genericDataTable";
import { getTableHeaderElement } from "../../util/getTableHeaderElement";

const { Option } = Select;

export default ({ data, endpoint }: any) => {
  const { state, dispatch } = useContext(DataTableContext);
  const [options, setOptions] = useState([]);
  const filter = state.filters[data.key] || { value: [] };

  useEffect(() => {
    if (!data.filter_data) {
      getOptions(endpoint, data.key).then((i) => {
        setOptions(i.data);
      });
    } else {
      const mapFilterData = data.filter_data.map((i: any) => {
        if (typeof i === "object") {
          return {
            value: i.value,
            name: i.name,
          };
        } else {
          return {
            value: i,
            name: i,
          };
        }
      });
      setOptions(mapFilterData);
    }
  }, [data.key, endpoint, data.filter_data]);

  const handleChange = (value: string[]) => {
    if (value.length) {
      const getValueNames = options
        .filter((i: any) => value.includes(i.value))
        .map((i: any) => i.name);
      dispatch(
        updateFilterValue(data.key, {
          value: value,
          displayText: `${data.title}: ${getValueNames.join(", ")}`,
        })
      );
    } else {
      dispatch(removeFilterValue(data.key));
    }
  };

  return (
    <Select
      id={data.key}
      mode="multiple"
      placeholder="Please select"
      onChange={handleChange}
      value={filter.value}
      onClick={(e) => e.stopPropagation()}
      getPopupContainer={() => getTableHeaderElement(data.key)}
    >
      {options.map((option: any, index: number) => (
        <Option
          value={option.value}
          key={`${option.key}-${option.value}-${option.name}`}
        >
          {option.name}
        </Option>
      ))}
    </Select>
  );
};
