import { rootReducer } from "@app/redux/reducers/rootReducer";
import { applyMiddleware, createStore } from "redux";
import ReduxThunk from "redux-thunk";
import { getLocalDateTime } from "@app/utils";
export const middlewares = [ReduxThunk];

export const testStore = (initialState: any) => {
  const createStoreWithMiddleware = applyMiddleware(...middlewares)(
    createStore
  );
  return createStoreWithMiddleware(rootReducer, initialState);
};

it("should contain getLocalDateTime func", () => {
  expect(typeof getLocalDateTime).toBe("function");
});
it("should retrun empty string when no UTC datetime provided", () => {
  expect(getLocalDateTime("")).toBe("");
});
it("should retrun local date time when UTC datetime provided", () => {
  expect(getLocalDateTime("2022-05-30T09:03:41.483Z")).toBe("2022-05-30 14:33");
});
