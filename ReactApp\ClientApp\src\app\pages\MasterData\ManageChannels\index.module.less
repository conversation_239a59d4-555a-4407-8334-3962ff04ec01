@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjManageChannelsInfinityListComponent {
  display: flex;
  width: 100%;

  .yjManageChannelsListItemValue {
    margin-bottom: 0;
    width: 90%;

    p {
      margin-bottom: 0;    
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
    .flex-mixin(center, flex, flex-start);
  }

  .yjManageChannelsListItemAction {

    button {
      border: none;
      box-shadow: none;
      color: @color-primary;
      .flex-mixin(center, flex, flex-end);
    }
  }
}

.yjGridTextCenter {

  span {
    font-size: 13px;
    text-decoration: underline;
  }
}

.yjChannelGridTextWrap {
  margin: 0;
  overflow: hidden;
  padding: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 98%;
}
