import React, { useContext, useEffect, useReducer, useState } from "react";
import { useHistory, useParams } from "react-router-dom";
import { Button, Col, Form, Input, Modal, Row, Skeleton, Tag, Drawer } from "antd";
import {
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  CheckOutlined,
  CloseOutlined,
} from "@ant-design/icons/lib";
import styles from "./index.module.less";
import {
  max,
  required,
  typeWithPattern,
} from "@app/components/forms/validators";
import some from "lodash/some";
import { Store } from "antd/lib/form/interface";

import {
  addNewTag,
  deleteTag,
  getExistingTags,
  updateTag,
} from "@app/api/tagManagementService";
import logger from "../../../utils/logger";
import {
  errorNotification,
  successNotification,
} from "@app/utils/antNotifications";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import { AvoidWhitespace } from "@app/utils/regex";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import { DataTableContext } from "@app/components/GenericDataTable/DataTableContext";
import reducer from "@app/components/GenericDataTable/DataTableContext/reducer";
import { updateFilterDropDownValues } from "@app/components/GenericDataTable/DataTableContext/actions";
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import TagManagement from "./index";

export type Tags = {
  value: number;
  name: string;
};

export interface TagManagementProps {
  binderId: string;
  onSuccess: () => void;
}

export default (props: TagManagementProps) => {
  const MAX_TAG_INPUT_LENGTH = 15;
  const [upFormRef] = Form.useForm();
  const [addFormRef] = Form.useForm();
  const [tagsState, setTagsState] = useState<Tags[]>([]);
  const [currentEditableTagIndexState, setCurrentEditableTagState] = useState(
    -1
  );
  const { binderId } = useParams<any>();
  const [updateSuccessState, setUpdateSuccessState] = useState(false);
  const [changeTagState, setChangeTagState] = useState({});
  const [updatePending, setUpdatePending] = useState(false);
  const history = useHistory();
  const { state } = useContext(DataTableContext);
  const [_, dispatch] = useReducer(reducer, state);

  const { userPermission } = useSelector((state: RootState) => state.userManagement);

  useEffect(() => {
    getExistingTags(props.binderId)
      .then((response) => {
        if (response.data.length > 0) {
          setTagsState(response.data);
        }
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        } else {
          errorNotification([""], "Unable to Retrieve existing tags");
        }

        logger.error("TagManagement", "Retrieve existing Tags", error);
      });
  }, [props.binderId, changeTagState]);

  useEffect(() => {
    dispatch(updateFilterDropDownValues("tags", tagsState));
  }, [tagsState]);

  const { confirm } = Modal;

  const handleTagsUpdate = (response: HTTPResponse<any>, values: Store) => {
    const TAGS_SETTIMEOUT_TIME = 500;
    if (response.data) {
      const tags = [...tagsState];
      setUpdateSuccessState(true);
      successNotification([""], "Tag successfully updated");
      setTimeout(() => {
        setCurrentEditableTagState(-1);
        setUpdatePending(false);
        setChangeTagState({
          value: tags[currentEditableTagIndexState].value,
          name: values.tag,
        });
        props.onSuccess();
      }, TAGS_SETTIMEOUT_TIME);
    } else {
      errorNotification([""], "Tag update failed");
      setUpdatePending(false);
    }
  };

  const handleTagSave = (values: Store) => {
    addNewTag(props.binderId, [values.newTag.trim()])
      .then((response) => {
        setChangeTagState(response.data[0]);
        successNotification([""], "Tag successfully added");
        addFormRef.setFieldsValue({ newTag: "" });
        props.onSuccess();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        } else {
          errorNotification([""], "Tag addition failed");
        }
        logger.error("TagManagement", "Add new Tag", error);
      });
  };

  const handleTagsDelete = (response: HTTPResponse<any>, index: any) => {
    if (response.data) {
      successNotification([""], "Tag successfully deleted");
      const currentTagState = [...tagsState];
      currentTagState.splice(index, 1);
      setTagsState(currentTagState);
      props.onSuccess();
    } else {
      errorNotification([""], "Tag deletion failed");
    }
  };

  const handleUpdate = (currentValue: string) => async (e: any) => {
    var values = await upFormRef.validateFields();

    //if its the same tag value
    if (values.upTag === currentValue) return setCurrentEditableTagState(-1);

    try {
      setUpdatePending(true);

      var response = await updateTag(props.binderId, [
        {
          value: tagsState[currentEditableTagIndexState].value,
          name: values.upTag,
        },
      ]);

      handleTagsUpdate(response, values);
    } catch (error) {
      if (error.statusCode === FORBIDDEN_ERROR_CODE) {
        errorNotification(
          [""],
          "You do not have the permission to perform this action. Please refresh and try again"
        );
      } else {
        errorNotification([""], "Tag update failed");
      }

      logger.error("TagManagement", "Update exiting Tag", error);

      setUpdatePending(false);
    }
  };

  const handleAddNewTag = () => {
    addFormRef.validateFields().then((values) => {
      handleTagSave(values);
    });
  };

  const handleDeleteTag = (index: number) => {
    deleteTag(props.binderId, [tagsState[index].value])
      .then((response) => {
        handleTagsDelete(response, index);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        } else {
          errorNotification([""], "Tag deletion failed");
        }

        logger.error("TagManagement", "Delete existing Tag", error);
      });
  };

  const isTagEditable = (index: number) => {
    return currentEditableTagIndexState === index;
  };

  const checkTagExistence = (requestValue: string, currentValue: string) => {
    //if its the same tag value
    if (requestValue === currentValue) return Promise.resolve();

    return some(
      tagsState,
      (tag) => tag.name.toLowerCase() === requestValue.trim().toLowerCase()
    )
      ? Promise.reject("Name Already Exists")
      : Promise.resolve();
  };

  const handleEditPopup = (index: number) => {
    confirm({
      title:
        "Are you sure? Editing this tag will reflect on all the reference data",
      icon: <ExclamationCircleOutlined />,
      okText: "Yes",
      cancelText: "No",
      onOk() {
        setUpdateSuccessState(false);
        setCurrentEditableTagState(index);
        upFormRef.setFieldsValue({ upTag: tagsState[index].name });
      },
    });
  };

  const handleDeletePopup = (index: number) => {
    confirm({
      title:
        "Are you sure? This tag will be deleted from all the reference data",
      icon: <ExclamationCircleOutlined />,
      okText: "Yes",
      cancelText: "No",
      onOk() {
        handleDeleteTag(index);
      },
    });
  };

  return (
    <>
      <div className={styles.yjModalContentWrapper}>
        <div className={styles.yjManageTagsWrapper}>
          <div className={styles.yjManageTagsHeader}>
            <Row gutter={24}>
              <Form
                layout="inline"
                form={addFormRef}
                onFinish={handleAddNewTag}
                className={styles.yjManageTagsForm}
              >
                <Col span={10}>
                  <Form.Item
                    className={styles.yjManageTagsFormItem}
                    name="newTag"
                    rules={[
                      required,
                      max(MAX_TAG_INPUT_LENGTH),
                      typeWithPattern("string", AvoidWhitespace),
                      {
                        validator(rule, value) {
                          return value
                            ? checkTagExistence(value, "")
                            : Promise.resolve();
                        },
                      },
                    ]}
                  >
                    <Input placeholder={"Enter Tag Name"} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item className={styles.yjManageTagsFormItem}>
                    <Button type="primary" htmlType="submit">
                      Add New Tag
                    </Button>
                  </Form.Item>
                </Col>
              </Form>
            </Row>
          </div>
          <div className={styles.yjManageTagsContent}>
            {tagsState.length > 0 && (
              <h6 className={styles.yjModuleSubHeading}>Existing Tags</h6>
            )}
            {tagsState ? (
              tagsState.map((tag, index) => (
                <div className={styles.yjManageTagsBox}>
                  <Tag className={styles.yjManageTagName}>
                    {isTagEditable(index) ? (
                      <Form form={upFormRef}>
                        <Form.Item
                          className={styles.yjManageTagNameEdit}
                          name="upTag"
                          hasFeedback
                          validateStatus={updateSuccessState ? "success" : ""}
                          rules={[
                            required,
                            max(MAX_TAG_INPUT_LENGTH),
                            typeWithPattern("string", AvoidWhitespace),
                            {
                              validator(rule, value) {
                                return checkTagExistence(value, tag.name);
                              },
                            },
                          ]}
                        >
                          <Input
                            autoFocus={currentEditableTagIndexState !== -1}
                          />
                        </Form.Item>
                      </Form>
                    ) : (
                      <p>{tag.name}</p>
                    )}
                  </Tag>
                  {isTagEditable(index) ? (
                    <>
                      <Button
                        type="primary"
                        className={styles.yjCheckButton}
                        icon={<CheckOutlined />}
                        onClick={handleUpdate(tag.name)}
                        disabled={updatePending}
                      />
                      <Button
                        type="default"
                        className={styles.yjCloseButton}
                        icon={<CloseOutlined />}
                        onClick={() => setCurrentEditableTagState(-1)}
                        disabled={updatePending}
                      />
                    </>
                  ) : (
                    <>
                      <Button
                        type="primary"
                        icon={<EditOutlined />}
                        onClick={() => handleEditPopup(index)}
                      />
                      <Button
                        disabled={!userPermission.privDMSCanDeleteFileTags}
                        type="primary"
                        icon={<DeleteOutlined />}
                        onClick={() => handleDeletePopup(index)}
                      />
                    </>
                  )}
                </div>
              ))
            ) : (
              <Skeleton active />
            )}
          </div>
        </div>
      </div>
    </>
  );
};
