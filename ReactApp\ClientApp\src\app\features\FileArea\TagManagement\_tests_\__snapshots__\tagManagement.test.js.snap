// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Tag Management Test Suite should render and create snapshot properly 1`] = `
<div>
  <div>
    <div>
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -12,
            "marginRight": -12,
          }
        }
      >
        <form
          className="ant-form ant-form-inline"
          onReset={[Function]}
          onSubmit={[Function]}
        >
          <div
            className="ant-col ant-col-10"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      className="ant-input"
                      id="newTag"
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onKeyDown={[Function]}
                      placeholder="Enter Tag Name"
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            className="ant-col ant-col-5"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <button
                      className="ant-btn ant-btn-primary"
                      onClick={[Function]}
                      type="submit"
                    >
                      <span>
                        Add New Tag
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </form>
      </div>
    </div>
    <div />
  </div>
</div>
`;
