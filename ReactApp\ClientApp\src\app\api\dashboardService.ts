import { getParameterizedUrl } from "@app/utils";
import config from "@app/utils/config";
import http, { httpVerbs } from "@app/utils/http";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export const getDashboardPermissonDetails = (
  dashboardId: any,
  channelId: any
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.DashboardService].getWidgetPermissions,
      dashboardId
    ),
    params: { channelId },
  });
};
