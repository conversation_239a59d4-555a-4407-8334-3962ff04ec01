import { Col, Form, Input, Row, Select } from "antd";
import {
  prevenPrecedingSpaces,
  prevenPrecedingSpacesOnPaste,
} from "@app/utils/forms";
import React from "react";
import countries from "world_countries_lists/data/countries/en/countries.json";

export default function AddressDetails() {
  const countryList: any[] = countries.map((c) => {
    return { value: c.alpha2, label: c.name };
  });

  return (
    <Row gutter={24}>
      <Col span={8} key="country">
        <Form.Item label="Country" name="country">
          <Select
            showSearch
            placeholder="Select a Country"
            optionFilterProp="children"
            filterOption={(input, option) =>
              (`${option?.label}` ?? "")
                .toLowerCase()
                .includes(input.toLowerCase())
            }
            options={countryList}
          />
        </Form.Item>
      </Col>
      <Col span={8} key="address">
        <Form.Item label="Address" name="address">
          <Input
            minLength={1}
            maxLength={300}
            onKeyDown={(event) => prevenPrecedingSpaces(event)}
            onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
            autoComplete="off"
          />
        </Form.Item>
      </Col>
      <Col span={8} key="city">
        <Form.Item label="City" name="city">
          <Input
            minLength={1}
            maxLength={100}
            onKeyDown={(event) => prevenPrecedingSpaces(event)}
            onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
            autoComplete="off"
          />
        </Form.Item>
      </Col>
      <Col span={8} key="state">
        <Form.Item label="State/province/region" name="state">
          <Input
            minLength={1}
            maxLength={100}
            onKeyDown={(event) => prevenPrecedingSpaces(event)}
            onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
            autoComplete="off"
          />
        </Form.Item>
      </Col>
      <Col span={8} key="zipCode">
        <Form.Item label="Zip Code" name="zipCode">
          <Input
            minLength={1}
            maxLength={15}
            onKeyDown={(event) => prevenPrecedingSpaces(event)}
            onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
            autoComplete="off"
          />
        </Form.Item>
      </Col>
    </Row>
  );
}
