import formatSize from "../formatSize";

const _1024_BYTES = 1024;

describe("Format Size function test suite", () => {
  it("formatSize should return 0 Bytes after null is passed as a parameter", () => {
    expect(formatSize(null)).toBe("0 Bytes");
  });

  it("formatSize should return 0 Bytes after 0 is passed as a parameter", () => {
    expect(formatSize(null)).toBe("0 Bytes");
  });

  it("formatSize should return 0 Bytes after a negetive value is passed as a parameter", () => {
    expect(formatSize(-100)).toBe("0 Bytes");
  });

  it("formatSize should return 1MB after 1024 is passed as a parameter", () => {
    expect(formatSize(_1024_BYTES)).toBe("1 KB");
  });
  it("formatSize should return 1MB after 1024 * 1024 is passed as a parameter", () => {
    expect(formatSize(_1024_BYTES * _1024_BYTES)).toBe("1 MB");
  });
  it("formatSize should return 1MB after 1024 * 1024 *1024 is passed as a parameter", () => {
    expect(formatSize(_1024_BYTES * _1024_BYTES * _1024_BYTES)).toBe("1 GB");
  });
});
