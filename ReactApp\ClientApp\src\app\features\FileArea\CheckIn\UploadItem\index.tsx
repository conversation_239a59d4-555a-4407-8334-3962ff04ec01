import React, { Fragment } from "react";
import { Progress, Button } from "antd";
import { DeleteOutlined, PaperClipOutlined } from "@ant-design/icons";

import styles from "./index.module.less";

export interface IUploadItem {
  displayProgressBar: boolean;
  displayFile: boolean;
  percent: number;
  title: string;
  onRemove: (index: any) => void;
  index: number;
  reload: boolean;
  selectedIndex: number;
}

export default (props: IUploadItem) => {
  const FULL_PERCENTAGE = 100;
  return (
    <Fragment key={props.index}>
      <div className={styles.yjUploadBlock}>
        <div hidden={!props.displayFile} className={styles.yjCheckInUploadBar}>
          <p>
            <PaperClipOutlined />
            {props.title}
          </p>
          <Button
            disabled={
              props.percent > 0 &&
              props.percent < FULL_PERCENTAGE &&
              props.index !== props.selectedIndex
            }
            onClick={() => props.onRemove(props.index)}
            type="default"
          >
            {" "}
            <DeleteOutlined />
          </Button>
        </div>
        <div
          hidden={!props.displayProgressBar}
          className={styles.yjCheckInProgressBar}
        >
          {props.percent > 0 ? (
            <Progress key={props.index} percent={props.percent} />
          ) : null}
        </div>
      </div>
    </Fragment>
  );
};
