import { <PERSON><PERSON>, <PERSON>lapse, Modal as AntModal, Tag, Modal } from "antd";
import React, { useState } from "react";
import {
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
} from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";

import styles from "./index.module.less";
import { RootState } from "@app/redux/reducers/state";
import {
  CreateFilterTemplateRequest,
  IFilterTemplate,
  SavedFilterTemplate,
} from "@app/types/filterTemplateTypes";
import {
  deleteSavedFilterTemplate,
  editSavedFilterTemplate,
  updateSavedFilterTemplates,
} from "@app/redux/actions/gridsActions";
import FilterTemplateManagementSave from "@app/components/GenericDataTable/FilterTemplateManagement/save";
import { getGridFilterFromIFilterTemplate } from "@app/components/GenericDataTable/FilterTemplateManagement/util";

const { confirm } = AntModal;

const extraHandler = (
  deleteFilterTemplateHandler: any,
  editFilterTemplateNameHandler: any
) => {
  return (
    <>
      <DeleteOutlined
        style={{ paddingRight: 10 }}
        onClick={(event) => {
          // If you don't want click extra trigger collapse, you can prevent this:
          event.stopPropagation();
          deleteFilterTemplateHandler();
        }}
      />
      <EditOutlined
        onClick={(event) => {
          // If you don't want click extra trigger collapse, you can prevent this:
          event.stopPropagation();
          editFilterTemplateNameHandler();
        }}
      />
    </>
  );
};
export default (props: any) => {
  const { tableKey, groupedValue } = props;
  const gridDispatch = useDispatch();
  const {
    savedFilters,
    columns,
  }: { savedFilters: SavedFilterTemplate[]; columns: any } = useSelector(
    (state: RootState) => state.grid
  );

  const { Panel } = Collapse;

  const [showEditFilterNameModal, setShowEditFilterNameModal] = useState(false);
  const [isNameValid, setNameValid] = useState(true);
  const [newFilterName, setNewFilterName] = useState("");
  const [selectedFilterToBeEdited, setSelectedFilterToBeEdited] = useState({});
  const [
    selectedFilterTemplateIdToBeEdited,
    setSelectedFilterTemplateIdToBeEdited,
  ] = useState("");
  const MAX_TAG_LENGTH = 15;

  const deleteFilterTemplate = (filterId: string) => {
    handleOnSavedFilterTemplateDeleteModal(filterId);
  };

  const onDeleteFilters = (filterId: string) => {
    gridDispatch(deleteSavedFilterTemplate(filterId));
    const savedFiltersUpdated = savedFilters.filter(
      (filter) => filter.id !== filterId
    );
    gridDispatch(updateSavedFilterTemplates(savedFiltersUpdated));
  };

  const handleOnSavedFilterTemplateDeleteModal = (filterId: string) => {
    confirm({
      title: "Are you sure you want to delete the saved filter?",
      icon: <ExclamationCircleOutlined />,
      okText: "Yes",
      cancelText: "No",
      onOk() {
        onDeleteFilters(filterId);
      },
    });
  };

  const handleOnSavedFilterTemplateEditModal = (
    templateId: string,
    filter: IFilterTemplate,
    templateName: string
  ) => {
    setShowEditFilterNameModal(true);
    setSelectedFilterToBeEdited(filter);
    setSelectedFilterTemplateIdToBeEdited(templateId);
    setNewFilterName(templateName);
  };

  const onChangeSaveNewFilterName = (name: string) => {
    if (
      savedFilters.findIndex((filter: SavedFilterTemplate) => {
        return filter.name.toLowerCase() === name.toLowerCase().trim();
      }) !== -1
    ) {
      setNameValid(false);
      setNewFilterName(name);
    } else {
      setNameValid(true);
      setNewFilterName(name);
    }
  };

  const onClickSaveFilter = () => {
    const createFilterTemplateRequest: CreateFilterTemplateRequest = {
      name: newFilterName,
      content: selectedFilterToBeEdited,
    };

    gridDispatch(
      editSavedFilterTemplate(
        selectedFilterTemplateIdToBeEdited,
        createFilterTemplateRequest,
        tableKey,
        groupedValue
      )
    );
    setShowEditFilterNameModal(false);
  };

  const triggerExtraHandler = (
    savedFilter: SavedFilterTemplate,
    filter: IFilterTemplate,
    templateName: string
  ) => {
    return extraHandler(
      () => deleteFilterTemplate(savedFilter.id),
      () =>
        handleOnSavedFilterTemplateEditModal(
          savedFilter.id,
          filter,
          templateName
        )
    );
  };

  const onCancelEditFilterNameModal = () => {
    setShowEditFilterNameModal(false);
    setNewFilterName("");
    setNameValid(true);
  };

  return (
    <>
      <Modal
        visible={showEditFilterNameModal}
        title={"Edit Filter Name"}
        className="yjCommonModalSmall"
        maskClosable={false}
        destroyOnClose={true}
        onCancel={onCancelEditFilterNameModal}
        footer={[
          <Button
            type="default"
            key="back"
            onClick={() => onCancelEditFilterNameModal()}
          >
            cancel
          </Button>,
          <Button
            disabled={newFilterName.trim().length < 1 || !isNameValid}
            key="submit"
            type="primary"
            onClick={() => onClickSaveFilter()}
          >
            Save
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <FilterTemplateManagementSave
            isNameValid={isNameValid}
            newFilterName={newFilterName}
            onFilterNameChangeHandler={(name: string) =>
              onChangeSaveNewFilterName(name)
            }
          />
        </div>
      </Modal>
      <div className={styles.yjModalContentWrapper}>
        <div className={"yjSecondaryAccordian"}>
          <Collapse expandIconPosition={"right"} defaultActiveKey={["1"]}>
            {savedFilters.map((savedFilter: SavedFilterTemplate) => {
              const filter: IFilterTemplate = savedFilter.content;
              const templateName = savedFilter.name;
              const gridFilters = getGridFilterFromIFilterTemplate(
                filter,
                columns
              );
              return (
                <Panel
                  header={templateName}
                  key={savedFilter.id}
                  extra={triggerExtraHandler(savedFilter, filter, templateName)}
                >
                  {gridFilters.map((filterTag) => {
                    return (
                      <div key={filterTag.key} className={"yjfilterTag"}>
                        <Tag
                          className={
                            filterTag.displayText.length > MAX_TAG_LENGTH
                              ? styles.yjFilterTagWrap
                              : undefined
                          }
                        >
                          {filterTag && filterTag.displayText.toString()}{" "}
                        </Tag>
                      </div>
                    );
                  })}
                </Panel>
              );
            })}
          </Collapse>
        </div>
      </div>
    </>
  );
};
