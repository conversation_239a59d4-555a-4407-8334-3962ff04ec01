import { getAutocompleteOptions, getColumns, getRecords } from '@app/api/genericDataTable';
import GenericDataTable from '@app/components/GenericDataTable';
import logger from '@app/utils/logger';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import React, { forwardRef, useEffect, useState } from 'react';
import config from '@app/utils/config';
import { Button, Skeleton, Tooltip, Modal } from 'antd';
import { EditOutlined, FolderOpenOutlined } from '@ant-design/icons';
import { FORBIDDEN_ERROR_CODE } from '@app/utils';
import { useHistory, useParams } from 'react-router-dom';
import { Sorter } from '@app/components/GenericDataTable/util';
import debounce from 'lodash/debounce';
import { FormattedDateTime } from '@app/components/FormattedDateTime';
import ShowAllSwitch from '@app/components/Switch/ShowAll';

const SORTER: Sorter = {
  value: 'siteName',
  order: 'ascend',
};

interface BindersGridProps {
  onEdit: (data: any) => void;
  onView: (binderId: string, binderName: string) => void;
  onCountChange?: (count: number) => void;
}

const BindersGrid = forwardRef(({ onEdit, onView,onCountChange }: BindersGridProps) => {
  const { siteName, channelId, siteId } = useParams<any>();
  const history = useHistory();
  const [jobModalVisible, setJobModalVisible] = useState(false);
  const [selectedJob, setSelectedJob] = useState<{ value: number, name: string }[]>([]);
  const [showAllBinders, setShowAllBinders] = useState<boolean>(false);
  const [recordCount, setRecordCount] = useState<number>(0);

  function gTOnErrorLoading(error: any) {
    switch (error.statusCode) {
      case FORBIDDEN_ERROR_CODE:
        history.push('/forbidden');
        break;

      default:
        logger.error('FileAreasList', 'gTOnErrorLoading', error);
        break;
    }
  }

  let fileAreasListData = (state: any, transformFilters: any, queryParams: any) => {
    logger.debug('FileAreas', 'fetchFileAreasData', { state, transformFilters, queryParams });
    return getRecords(
      config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList,
      {
        pagination: {
          current: state.pagination.current,
          pageSize: state.pagination.pageSize,
        },
        sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
        filters: transformFilters,
      },
      queryParams
    );
  };

  const debouncedApiCall = debounce(
    (props: any, value: string, callback: (data: any) => void) => {
      logger.debug('Client File Area', 'debouncedApiCall', { props, value });

      getAutocompleteOptions(
        config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList,
        props.data.key,
        value,
        props.searchFieldParameters
      )
        .then((data: any) => {
          callback(data.data);
        })
        .catch(() => {
          callback([]);
        });
    },
    config.inputDebounceInterval
  );

  const searchPromiseWrapper = (props: any, value: string, callback: any) => {
    debouncedApiCall(props, value, (data: any) => {
      callback(data)
    });
  };

  const handleJobTypeClick = (job: []) => {
    setSelectedJob(job);
    setJobModalVisible(true);
  };

  const closeJobModal = () => {
    setJobModalVisible(false);
    setSelectedJob([]);
  };

  const customRender = {
    action: (text: any, record: any) => (
      <div className={'yjActionIconWrapper'}>
        <Tooltip title="View">
          <Button
            onClick={() => {
              onView(record.binderId, record.binderName)
            }}
            icon={<FolderOpenOutlined />}
          />
        </Tooltip>
        <Tooltip title="Edit">
          <Button
            onClick={() => {
              onEdit(record);
            }}
            icon={<EditOutlined />}
          />
        </Tooltip>
      </div>
    ),
    created: (value: string) => (<FormattedDateTime value={value} />),
    jobs: (jobs: any) =>
      jobs && jobs.length > 0 ? (
        <Button type="primary" onClick={() => handleJobTypeClick(jobs)}>
          View
        </Button>
      ) : (
        "-"
      ),

  };

  const fetchFileAreasData = async (state: any, transformFilters: any, queryParams: any) => {
    
    let result;
    if (showAllBinders) {
      result = await fileAreasListData(state, transformFilters, queryParams);
    } else {
      const activeFilter: Array<any> = state.columns.find((column: any) => column.key === 'status')?.filter_data?.filter((item: any) => item.name !== 'Inactive');
      if (!activeFilter) {
        return fileAreasListData(state, transformFilters, queryParams);
      }

      const filters = {
        ...transformFilters,
        status: transformFilters.status ? transformFilters.status : activeFilter.map(f => f.value),
      }
      return fileAreasListData(state, filters, queryParams);
    };

    if(result.data.totalRecordCount>0){
    setRecordCount(result.data.totalRecordCount);
    }
    
    return result;
  };

  const fetchColumns = async () => {
    const response = await getColumns(config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList);

    return showAllBinders ? response : response.data.map((column: any) => {
      if (column.key === 'status') {
        return {
          ...column,
          filterData: column.filterData.filter((item: any) => item.name !== 'Inactive'),
        };
      }

      return column;
    });
  };

useEffect(() => {
  if (recordCount>0) {  
    if (onCountChange) {
      onCountChange(recordCount);
    }
  }
}
, [recordCount]);

  return (
    <>
      {siteId ? (
        <GenericDataTable
          searchPromise={searchPromiseWrapper}
          dataPromise={fetchFileAreasData}
          columnPromise={fetchColumns()}
          searchFieldParameters={[{ key: 'siteId', value: siteId }]}
          searchQueryParameters={[{ key: 'siteId', value: siteId }]}
          rowKey={'binderId'}
          tableKey={siteId}
          sorted={SORTER}
          key={siteId}
          customRender={customRender}
          onErrorLoading={gTOnErrorLoading}
          hasFilterManagement={true}
          headerActionPanel={<ShowAllSwitch checked={showAllBinders} onChange={setShowAllBinders} />}
        />
      ) : (
        <Skeleton active={true} />
      )}
      <Modal
        title="Job(s)"
        visible={jobModalVisible}
        onCancel={closeJobModal}
        footer={null}
      >
        <ul>
          {selectedJob?.map((job, index) => (
            <li key={index}>{job.name}</li>
          ))}
        </ul>
      </Modal>
    </>
  );
});

export default BindersGrid;
