import {
  requestExpirationStatus,
  requestPasswordProtectionStatus,
  requestStatus,
} from "../portalAreaColorSwitch";

describe("Portalfile Color switch requestStatus function test suite", () => {
  it("should contain requestStatus method", () => {
    expect(typeof requestExpirationStatus).toBe("function");
  });

  it("requestStatus method should be return an empty string when a value is not passed", () => {
    expect(requestStatus()).toBe("");
  });
  it("requestStatus method should be return an empty string when a null value is  passed", () => {
    expect(requestStatus(null)).toBe("");
  });
  it("requestStatus method should be return #73d13d when 0 is passed", () => {
    expect(requestStatus(0)).toBe("#73d13d");
  });
  it("requestStatus method should be return #419cb9 when 0 is passed", () => {
    expect(requestStatus(1)).toBe("#419cb9");
  });
  it("requestStatus method should be return an empty string when a value other than 0 or 1 is passed", () => {
    expect(requestStatus(10)).toBe("");
  });
});

describe("Portalfile Color switch requestPasswordProtectionStatus function test suite", () => {
  it("should contain requestPasswordProtectionStatus method", () => {
    expect(typeof requestPasswordProtectionStatus).toBe("function");
  });

  it("requestPasswordProtectionStatus method should be return an empty string when a value is not passed", () => {
    expect(requestPasswordProtectionStatus()).toBe("");
  });
  it("requestPasswordProtectionStatus method should be return an empty string when a null value is  passed", () => {
    expect(requestPasswordProtectionStatus(null)).toBe("");
  });
  it("requestPasswordProtectionStatus method should be return #73d13d when 0 is passed", () => {
    expect(requestPasswordProtectionStatus("Yes")).toBe("#73d13d");
  });
  it("requestPasswordProtectionStatus method should be return #419cb9 when 0 is passed", () => {
    expect(requestPasswordProtectionStatus("No")).toBe("#ff4d4f");
  });
  it("requestPasswordProtectionStatus method should be return an empty string when a value other than Yes or No is passed", () => {
    expect(requestPasswordProtectionStatus("")).toBe("");
  });
});

describe("Portalfile Color switch requestExpirationStatus function test suite", () => {
  it("should contain requestExpirationStatus method", () => {
    expect(typeof requestExpirationStatus).toBe("function");
  });

  it("requestExpirationStatus method should be return an empty string when a value is not passed", () => {
    expect(requestExpirationStatus()).toBe("");
  });
  it("requestExpirationStatus method should be return an empty string when a null value is  passed", () => {
    expect(requestExpirationStatus(null)).toBe("");
  });
  it("requestExpirationStatus method should be return #ff4d4f when 0 is passed", () => {
    expect(requestExpirationStatus(0)).toBe("#ff4d4f");
  });
  it("requestExpirationStatus method should be return #666666 when 1 is passed", () => {
    expect(requestExpirationStatus(1)).toBe("#666666");
  });
  it("requestExpirationStatus method should be return #ffa940 when 2 is passed", () => {
    expect(requestExpirationStatus(2)).toBe("#ffa940");
  });
  it("requestExpirationStatus method should be return an empty string when a value other than 0,1 or 2 is passed", () => {
    expect(requestExpirationStatus(100)).toBe("");
  });
});
