const MOCK_DATE = "2020-10-19";
export const internalFileSectionHistoryMockData = [
    {
        id: "AA-00024",
        file: "ABCD Tax File.xls",
        userName: "Smith",
        dateTime: MOCK_DATE,
        action: "File Uploaded ",
        changes: {
            previous: {
                id: "-",
                title: "-",
                path: "-",
                level1Folder: "-",
                level2Folder: "-",
                status: "-",
                tags: ["-"],
                year: "-",
                projects: ["-"],
                type: "-",
                expirationStatus: "-",
                expirationDate: "-",
                description: "-",
                size: "-",
                assignee: "-",
                createdBy: "-",
                created: "-",
                fileCondition: "-",
                filePreview: "-",
                optionalEmailContent: "-",
                users: ["-"],
                contacts: ["-"],
                version: "-",
                emailStatus: "-"
            },
            current: {
                id: "AA-00024",
                title: "ABCD Tax File.xls",
                path: "Main Folder > Folder 1 > Folder 3",
                level1Folder: "Folder 1",
                level2Folder: "Folder1 / Folder 2",
                status: "Arrived",
                tags: ["Tax", "Accounting"],
                year: "2020",
                projects: ["-"],
                type: "XLS",
                expirationStatus: "Pending",
                expirationDate: "2021/06/30",
                description: "-",
                size: "5MB",
                assignee: "Smith",
                createdBy: "Smith",
                created: "2020/10/10 02:30",
                fileCondition: "None",
                filePreview: "preview",
                optionalEmailContent: "-",
                users: ["-"],
                contacts: ["-"],
                version: "Version 0",
                emailStatus: "Successful"
            }
        }
    },
    {
        id: "AA-01033",
        file: "New File.pdf",
        userName: "John",
        dateTime: "2020-11-14",
        action: "Assignment Update",
        changes: {
            previous: {
                assignee: "John",
                assignNotes: "-",
                emailStatus: "Successful"
            },
            current: {
                assignee: "Jane",
                assignNotes: "-",
                emailStatus: "Successful"
            }
        }
    },
    {
        id: "None",
        file: "None",
        userName: "Thomas",
        dateTime: MOCK_DATE,
        action: "Saved a New Filter",
        changes: {
            previous: {
                filterName: "-",
                filterTags: ["-"],
            },
            current: {
                filterName: "Filter 1",
                filterTags: ["User Group 1", "2019"],
            }
        }
    },
    {
        id: "None",
        file: "None",
        userName: "Jane",
        dateTime: MOCK_DATE,
        action: "Edited a Tag",
        changes: {
            previous: {
                tags: ["Tax"]
            },
            current: {
                tags: ["Tax Year"]
            }
        }
    },
    {
        id: "AA-01034",
        file: "Tax.2020.pdf",
        userName: "Kate",
        dateTime: MOCK_DATE,
        action: "Status Update",
        changes: {
            previous: {
                status: "Arrived"
            },
            current: {
                status: "Draft"
            }
        }
    },
    {
        id: "AA-00020",
        file: "YJ Client File.pdf",
        userName: "Jack",
        dateTime: MOCK_DATE,
        action: "File Downloaded",
        changes: null
    },
    {
        id: "AA-01032",
        file: "Tax Final.pdf",
        userName: "Zara",
        dateTime: MOCK_DATE,
        action: "Check-Out File",
        changes: {
            previous: {
                downloadedTitle: "-",
                returnDate: "-",
                checkoutNotes: "-",
                status: "Arrived",
                emailStatus: "-"
            },
            current: {
                downloadedTitle: "Tax Final.pdf-AA-01032.pdf",
                returnDate: "-",
                checkoutNotes: "-",
                status: "Arrived",
                emailStatus: "-"
            }
        }
    },
    {
        id: "AA-01031",
        file: "YJ.Doc",
        userName: "Admin Jack",
        dateTime: MOCK_DATE,
        action: "File Deleted",
        changes: {
            previous: {
                id: "AA-01031",
                title: "YJ.doc",
                path: "Main Folder > Folder 2 > Folder 3",
                status: "Arrived",
                tags: ["Tax"],
                year: "2020",
                projects: ["-"],
                type: "DOC",
                expirationStatus: "Pending",
                expirationDate: "2021/12/12",
                description: "-",
                size: "9MB",
                assignee: "Smith",
                createdBy: "Smith",
                created: "2020/10/15 12:30",
                fileCondition: "None",
                filePreview: "preview",
            },
            current: {
                id: "-",
                title: "-",
                path: "-",
                status: "-",
                tags: ["-"],
                year: "-",
                projects: ["-"],
                type: "-",
                expirationStatus: "-",
                expirationDate: "-",
                description: "-",
                size: "-",
                assignee: "-",
                createdBy: "-",
                created: "-",
                fileCondition: "-",
                filePreview: "-",
            }
        }
    },
    {
        id: "AA-01032",
        file: "Tax Final.pdf",
        userName: "Zara",
        dateTime: MOCK_DATE,
        action: "Check-In File",
        changes: {
            previous: {
                status: "Checked-Out",
                assignee: "Zara",
                checkInTimestamp: "-",
                emailStatus: "-",
                version: "Version 1"
            },
            current: {
                status: "Arrived",
                assignee: "Zara",
                checkInTimestamp: "2020/11/18 08:30",
                emailStatus: "Successfull",
                version: "Version 2"
            }
        }
    },
    {
        id: "None",
        file: "None",
        userName: "Crystal",
        dateTime: MOCK_DATE,
        action: "Deleted a Tag",
        changes: {
            previous: {
                status: "Tag Name - Tax Year 2020"
            },
            current: {
                status: "Tag Name - -"
            }
        }
    },

];
