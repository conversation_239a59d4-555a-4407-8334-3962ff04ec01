import React, { useEffect, useState } from "react";
import { Table, Button, Input } from "antd";
import { FileRecord, FileEvents } from "./types";
import { CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import { ColumnsType } from "antd/lib/table";
import {
  DIFFERENT_PROPERTY_UPLOAD,
  SAME_PROPERTY_UPLOAD,
} from "@app/components/Uploader/constants/uploadTypes";

type PropTypes = {
  fileList: FileRecord[];
  fileEvents?: FileEvents;
  uploadType: number;
  forManageFiles?: boolean;
  onDataChange?: (event: any) => void;
  disabledForm?: boolean;
};

const FileList: React.FC<PropTypes> = ({
  fileList,
  fileEvents = {
    onFileRemove: () => {},
    onFileSelect: () => {},
    onFileSelectAll: () => {},
    onSaveSuccess: () => {},
    onTitleUpdate: () => {},
  },
  uploadType,
  forManageFiles = false,
  onDataChange = () => {},
  disabledForm
}) => {
  const {
    onFileSelectAll,
    onFileSelect,
    onTitleUpdate,
    onFileRemove,
  } = fileEvents;
  const checkedLength = fileList.filter((v) => v.checked).length;
  const baseColumns: ColumnsType<FileRecord> = [
    {
      key: "remove",
      render: (record: FileRecord) => (
        <Button
          key={record.referenceNumber}
          icon={<CloseOutlined />}
          className={"yjDeteleFile"}
          onClick={() => onFileRemove(record.referenceNumber)}
        />
      ),
      width: 50,
    },

    {
      key: "title",
      title: "  Title",
      dataIndex: "title",
      render: (text: string, record: FileRecord, index: number) => {
        return (
          <>
            <Input
              disabled={!record.checked}
              key={record.referenceNumber}
              value={text}
              onChange={(e) => onTitleUpdate(e.target.value, index)}
            />
            {record.checked && record.error && (
              <span style={{ color: "red", display: "block" }}>
                {record.error}
              </span>
            )}
          </>
        );
      },
    },
  ];

  const [columns, setColumns] = useState<ColumnsType<FileRecord>>(baseColumns);
  const titleInput = (text: string, record: FileRecord)=> {
    return (
    <>
      <Input
        disabled={disabledForm}
        defaultValue={text}
        onChange={(e) => {
          onDataChange(e.target.value !== "");
          onTitleUpdate(e.target.value,0);
        }}
      />
      {record.checked && record.error && (
        <span style={{ color: "red" }}>{record.error}</span>
      )}
    </>
  );

  }

  useEffect(() => {
    if (forManageFiles) {
      const customColumns: ColumnsType<FileRecord> = [
        {
          key: "title",
          title: "Title",
          dataIndex: "title",
          render: (text: string, record: FileRecord) => titleInput(text,record),
        },
      ];
      setColumns(customColumns);
    }
  }, [forManageFiles]);

  const rowSelection = {
    onSelect: (record: any, selected: any) => {
      const index = fileList.findIndex(
        (fileRecord: FileRecord) =>
          fileRecord.referenceNumber === record.referenceNumber
      );
      onFileSelect(selected, index);
    },
    onSelectAll: (selected: any) => {
      onFileSelectAll(selected);
    },
  };

  return (
    <>
      {!checkedLength && (
        <span style={{ color: "red" }}>Select files to set properties</span>
      )}
      <Table
        rowSelection={
          uploadType === DIFFERENT_PROPERTY_UPLOAD ? rowSelection : undefined
        }
        tableLayout="fixed"
        className={styles.yjTblFileList}
        columns={
          uploadType === SAME_PROPERTY_UPLOAD
            ? columns.filter((col) => col.key !== "checkbox")
            : columns
        }
        dataSource={fileList}
        scroll={{
          y: "31vh",
        }}
        pagination={false}
        rowKey={"referenceNumber"}
      />
    </>
  );
};

export default FileList;
