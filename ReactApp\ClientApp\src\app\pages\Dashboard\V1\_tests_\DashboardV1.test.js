import React from "react";
import renderer from "react-test-renderer";
import {shallow} from "enzyme";
import 'jest-canvas-mock';

import DashboardV1 from "../index";
import initTestSuite from "@app/utils/config/TestSuite";
import {Col, Collapse, Row} from "antd";

const {Panel} = Collapse;
describe("Dashboard V1 Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const dv1Component = shallow(<DashboardV1 />);
        expect(dv1Component.html()).not.toBe(null);
    });

    it("should create and match to snapshot",() => {
        const dv1Component = renderer.create(<DashboardV1 />).toJSON();
        expect(dv1Component).toMatchSnapshot();
    });

    it("should have 5 Row elements",() => {
        const dv1Component = shallow(<DashboardV1 />);
        expect(dv1Component.find(Row)).toHaveLength(5);
    });

    it("should have 14 Col elements",() => {
        const dv1Component = shallow(<DashboardV1 />);
        expect(dv1Component.find(Col)).toHaveLength(14);
    });

    it("should have 11 Collapse elements",() => {
        const dv1Component = shallow(<DashboardV1 />);
        expect(dv1Component.find(Collapse)).toHaveLength(11);
    });

    it("should have 11 Panel elements",() => {
        const dv1Component = shallow(<DashboardV1 />);
        expect(dv1Component.find(Panel)).toHaveLength(11);
    });
});