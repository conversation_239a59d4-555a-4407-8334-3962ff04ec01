import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, Button, Skeleton } from 'antd';
import PageTitle from '@app/components/PageTitle';
import { LockOutlined, PlusOutlined } from '@ant-design/icons/lib';
import PageContent from '@app/components/PageContent';
import TemplateAddEditDrawer from '@app/features/Template/TemplateAddEditDrawer';
import { useForm } from 'antd/lib/form/Form';
import { createTemplate, updateTemplate } from '@app/api/templateService';
import logger from '@app/utils/logger';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import TemplateGrid from "@app/features/Template/TemplateGrid";
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
const Forbidden = React.lazy(() => import("@pages/Common/Forbidden"));

const initialFolderData = [
  {
    id: 1,
    name: 'Primary folder',
    retention: false,
    presist: false,
    subFolders: [
      {
        id: 2,
        name: 'Secondary folder (1)',
        subFolders: [],
        retention: false,
        presist: false,
      },
      {
        id: 3,
        name: 'Secondary folder (2)',
        subFolders: [],
        retention: false,
        presist: false,
      },
    ],
  },
];

const TemplatePage = (props: any) => {
  let [showDrawer, setShowDrawer] = useState(false);
  let [form] = useForm();
  const { userPermission } = useSelector((state: RootState) => state.userManagement);
  const ref = useRef<{ refresh: () => void }>(null);


  const refresh = () => {
    if (ref.current) {
      ref.current.refresh();
    }
  };

  const  convertChildNodesToChildren=(data:any)=> {
    return data.map((node:any) => {
        let newNode = { ...node };
        if (newNode.childNodes) {
            newNode.subFolders = newNode.childNodes.map((child:any) => convertChildNodesToChildren([child])[0]);
            delete newNode.childNodes;
        }
        return newNode;
    });
  }

  const openCreateTemplateDrawer = (data: any) => { 
   const formattedNodes= data.parentNodes? convertChildNodesToChildren(data.parentNodes) :undefined;
    form?.setFieldsValue({
      id: data?.id,
      name: data?.name,
      description: data?.description,
      templateStatus: !data?.id ? true : data?.status?.value === 1 ? true : false,
      hide: data?.hide,
      parentNodes: formattedNodes,
      binderTemplateJobTypeId: data?.binderTemplateJobType?.name ?? null,
    })
    setShowDrawer(true);
  };

  const closeDrawer = () => {
    form.resetFields();
    setShowDrawer(false);
  };

  const formatFolderData = (data: any[]): any[] => {
    return data.map((e) => (e.children ? { id: e.id, name: e.folderName, retention: e.retention, childNodes: formatFolderData(e.children) } : { id: e.id, name: e.folderName, retention: e.retention }));
  };

  const onSuccess = (values: any) => {
    values.parentNodes = formatFolderData(values.parentNodes);
    values.templateStatus = values.templateStatus ? 1 : 2;
    if (values.id) {
      // In update, no need to send binderTemplateJobTypeId as it is disabled in edit,
      delete values.binderTemplateJobTypeId;
      updateTemplate(values)
        .then((res) => {
          successNotification([''], 'Template updated successfully');
          refresh();
          closeDrawer();
        })
        .catch((e) => {
          errorNotification([e?.message || ''],  'Error updating template');
          logger.error('Master Data Module', 'Create Site', e);
        });
    } else {
      createTemplate(values)
        .then(() => {
          successNotification([''], 'Template created successfully');
          refresh();
          closeDrawer();
        })
        .catch((e) => {
          errorNotification([e?.message || ''], 'Error creating template');
          logger.error('Master Data Module', 'Create Site', e);
        });
    }

  };

  return (
    <>
      <PageTitle title={props.title}>
        {userPermission.privDMSCanCreateTemplates &&
          <Button data-testid="create-template-button" onClick={openCreateTemplateDrawer} type="primary" icon={<PlusOutlined />}>
            Create Template
          </Button>}
      </PageTitle>
      <PageContent>
        {userPermission.privDMSCanViewTemplates && <TemplateAddEditDrawer
          data-testid="template-grid"
          initialData={form.getFieldValue('parentNodes') ?? initialFolderData}
          formRef={form}
          onSuccess={onSuccess}
          onDrawerClose={closeDrawer}
          showDrawer={showDrawer}
        />}
        {userPermission.privDMSCanViewTemplates && <TemplateGrid ref={ref} onEdit={(data: any) => openCreateTemplateDrawer(data)} />}
        {userPermission.privDMSCanViewTemplates === false && (
          <Alert
            message="You do not have permission to access any templates. Contact your organization's administrator for assistance"
            icon={<LockOutlined />}
            type="error"
            showIcon
          />
        )}
        {userPermission.privDMSCanViewTemplates === undefined && (
          <Skeleton />
        )}
      </PageContent>
    </>
  );
};

export default TemplatePage;
