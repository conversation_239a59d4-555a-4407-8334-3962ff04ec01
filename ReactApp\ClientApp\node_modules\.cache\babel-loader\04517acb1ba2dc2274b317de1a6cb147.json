{"ast": null, "code": "import \"antd/es/table/style\";\nimport _Table from \"antd/es/table\";\nimport \"antd/es/row/style\";\nimport _Row from \"antd/es/row\";\nimport \"antd/es/tooltip/style\";\nimport _Tooltip from \"antd/es/tooltip\";\nimport \"antd/es/col/style\";\nimport _Col from \"antd/es/col\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\GenericDataTable\\\\AntTableContainer\\\\index.tsx\";\nimport React, { useContext, useEffect, useState } from 'react';\nimport { v4 as uuidv4 } from 'uuid';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { LoadingOutlined, SwapOutlined } from '@ant-design/icons';\nimport { DataTableContext } from '../DataTableContext';\nimport { setSelectedSavedFilter, setTableLoading, updateChange, updateDataRecords } from '../DataTableContext/actions';\nimport logger from '../../../utils/logger';\nimport RecordCount from '../RecordCount';\nimport { prepareFilterObject } from '../util';\nimport { updateGridColumns, updateGridHasUpdates } from '@app/redux/actions/gridsActions';\nimport { MdOpenInNew } from 'react-icons/md';\nimport { VscPinned } from \"react-icons/vsc\";\nimport { getRecords } from \"@app/api/genericDataTable\";\nexport default (props => {\n  var _props$sorted, _props$sorted2, _props$columns2, _props$columns3;\n\n  const ROW_SEPERATER_VALUE = 2;\n  const DEFAULT_COLUMN_COUNTER = 5;\n  const MINIMUM_RESOLUTION_VALUE = 1367;\n  const MAX_SCREEN_WIDTH = 2400;\n  const {\n    state,\n    dispatch\n  } = useContext(DataTableContext);\n  const {\n    searchQueryParameters,\n    gridFilters,\n    tableKey: gridTablekey\n  } = useSelector(store => store.grid);\n  const dispatchGrid = useDispatch();\n  const [tableKey, setTableKey] = useState('');\n  const [tableSorted, setTableSorted] = useState(false);\n  const [sortedInfo, setSortedInfo] = useState({\n    order: (_props$sorted = props.sorted) === null || _props$sorted === void 0 ? void 0 : _props$sorted.order,\n    columnKey: (_props$sorted2 = props.sorted) === null || _props$sorted2 === void 0 ? void 0 : _props$sorted2.value\n  }); //update the table content according to the selection of saved filters by user\n\n  const [generatedColumValues, setGeneratedColumValues] = useState([]);\n  useEffect(() => {\n    if (props.columns) {\n      var _props$columns;\n\n      setGeneratedColumValues((_props$columns = props.columns) === null || _props$columns === void 0 ? void 0 : _props$columns.map(column => {\n        return { ...column,\n          sorter: state.records.length > 0 ? column.sorter : false\n        };\n      }));\n\n      if (!tableSorted) {\n        var _props$sorted3, _props$sorted4;\n\n        setSortedInfo({\n          order: (_props$sorted3 = props.sorted) === null || _props$sorted3 === void 0 ? void 0 : _props$sorted3.order,\n          columnKey: (_props$sorted4 = props.sorted) === null || _props$sorted4 === void 0 ? void 0 : _props$sorted4.value\n        });\n      }\n    }\n  }, [props.columns, state.records, tableSorted]);\n  useEffect(() => {\n    if (props.selectedSavedFilter !== undefined && props.selectedSavedFilter !== null && Object.keys(props.selectedSavedFilter).length !== 0) {\n      const filtersFromSelectedSavedFilter = gridFilters;\n\n      if (!filtersFromSelectedSavedFilter.find(filter => filter === undefined)) {\n        dispatch(setSelectedSavedFilter(filtersFromSelectedSavedFilter));\n      }\n    }\n  }, [gridFilters, props.selectedSavedFilter, dispatch]);\n  useEffect(() => {\n    let canceled = false;\n    let queryParams = searchQueryParameters;\n    if (props.searchQueryParameters) queryParams = [...props.searchQueryParameters, ...queryParams];\n\n    if (state.columns.length) {\n      dispatch(setTableLoading(true));\n      const transformFilters = prepareFilterObject(state.filters);\n\n      if (props.filterByIds) {\n        transformFilters.fileId = props.filterByIds.split(',');\n      }\n\n      logger.debug('Generic DataTable', 'AntTableContainer', {\n        transformFilters\n      });\n      /**\r\n       * TODO - This getRecords with props.endpoint scenario needs to remove after successfully refactor\r\n       */\n\n      if (!props.dataPromise) {\n        logger.error('AntGenericDataTable', 'GenericDataTable', `WARNING! Endpoint property has been deprecated, Please use dataPromise, columnPromise, onGridStateChange etc\n          <GenericDataTable `);\n        logger.warn('AntGenericDataTable', 'GenericDataTable', `WARNING! Endpoint property has been deprecated, Please use dataPromise, columnPromise, onGridStateChange etc\n          <GenericDataTable\n          dataPromise={(state) => fetchData(state)}\n          columnPromise={\n            getColumns(config.api[OperationalServiceTypes.MasterDataService].templates, \"siteManagement\")\n          }\n          rowKey={\"siteId\"}\n          scrollColumnCounter={7}\n          tableKey={\"siteManagement\"}\n          fixedColumns={[\"name\"]}\n          isDraggable={true}\n          noRecordsAvilableMessage={\"No Sites Available\"}\n          hasFilterManagement={true}\n        />\n      `);\n      }\n\n      const dataPromise = !props.dataPromise ? getRecords(props.endpoint, {\n        pagination: {\n          current: state.pagination.current,\n          pageSize: state.pagination.pageSize\n        },\n        sorter: state.sorter ? {\n          key: state.sorter.columnKey,\n          order: state.sorter.order\n        } : {},\n        filters: transformFilters,\n        columns: state.columns.filter(i => i.default === false && i.selected === true).map(i => i.key)\n      }, queryParams) : props.dataPromise(state, transformFilters, queryParams);\n      dataPromise.then(i => {\n        if (!canceled) {\n          dispatchGrid(updateGridHasUpdates(false));\n          dispatch(updateDataRecords(i.data));\n          dispatch(setTableLoading(false));\n          dispatchGrid(updateGridColumns({\n            columns: state.columns,\n            tableKey: gridTablekey\n          }));\n        }\n      }).catch(e => {\n        props.onErrorLoading && props.onErrorLoading(e);\n        logger.error('GenericDataTable', 'GenericDataTableBuilder', e);\n      });\n    }\n\n    props.onGridStateChange && props.onGridStateChange({ ...state,\n      searchQueryParameters\n    });\n    return () => {\n      canceled = true;\n    };\n  }, [state.filters, props.dataPromise, props.endpoint, state.columns, state.sorter, state.pagination.pageSize, dispatch, state.pagination, searchQueryParameters]);\n  useEffect(() => {\n    if (props.onFiltersChange) {\n      props.onFiltersChange(true);\n    }\n  }, [state.filters]);\n  useEffect(() => {\n    if (state.records.length === 0) {\n      //genarate UUID for fir rerender the Table Componanat\n      setTableKey(uuidv4());\n    }\n  }, [props.columns]);\n  useEffect(() => {\n    const tableElement = document.getElementsByClassName('ant-table-body');\n\n    if (tableElement[0]) {\n      tableElement[0].scrollTo({\n        left: 0,\n        behavior: 'smooth'\n      });\n    }\n  }, [state.columns]);\n  useEffect(() => {\n    if (props.updatedGrid) {\n      dispatch(updateChange({\n        current: 1,\n        pageSize: 20,\n        total: state.pagination.total\n      }, {}, {}));\n    }\n  }, [props.updatedGrid, dispatch, state.pagination.total]);\n\n  const onChange = (pagination, filters, sorting) => {\n    var _props$sorted5;\n\n    setTableSorted(true);\n    /**\r\n     * Reset The pagination.current to 1\r\n     * if\r\n     *  Sorter Column Changed or\r\n     *  Sorter Order Changed or\r\n     *  Page Size Change or\r\n     */\n\n    if (sortedInfo.order !== sorting.order || sortedInfo.columnKey !== sorting.field || state.pagination.pageSize !== pagination.pageSize) {\n      pagination.current = 1;\n    }\n\n    if (sorting.field !== ((_props$sorted5 = props.sorted) === null || _props$sorted5 === void 0 ? void 0 : _props$sorted5.value) && !sorting.column) {\n      var _props$sorted6, _props$sorted7;\n\n      setSortedInfo({\n        order: (_props$sorted6 = props.sorted) === null || _props$sorted6 === void 0 ? void 0 : _props$sorted6.order,\n        columnKey: (_props$sorted7 = props.sorted) === null || _props$sorted7 === void 0 ? void 0 : _props$sorted7.value\n      });\n    } else {\n      setSortedInfo({\n        order: sorting.order,\n        columnKey: sorting.field\n      });\n    }\n\n    dispatch(updateChange(pagination, filters, sorting));\n  };\n\n  const sortedColumValues = [...generatedColumValues.map(col => {\n    return { ...col,\n      sortOrder: sortedInfo.columnKey === col.key ? sortedInfo.order : false\n    };\n  })];\n  const paginationConf = {\n    disabled: props.disablePagination,\n    className: 'yjGridPagination',\n    total: state.pagination.total,\n    current: state.pagination.current,\n    pageSize: state.pagination.pageSize,\n    pageSizeOptions: ['10', '20', '25', '50'],\n    style: {\n      display: 'inline-block',\n      position: 'relative',\n      zIndex: 99\n    },\n    showSizeChanger: true,\n    locale: {\n      items_per_page: 'rows'\n    }\n  };\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(_Table, Object.assign({}, props, {\n    rowClassName: (record, index) => {\n      const defaultClassName = index % ROW_SEPERATER_VALUE === 0 ? 'table-row-light' : 'table-row-dark';\n      const additionalClassName = props.rowClassName ? ` ${props.rowClassName(record, index)}` : '';\n      return `${defaultClassName} ${additionalClassName}`;\n    },\n    key: tableKey,\n    className: 'yjTableContainer',\n    columns: sortedColumValues,\n    onRow: props.onRow,\n    rowSelection: props.rowSelection ? {\n      type: 'checkbox',\n      columnWidth: props.showPublishedIcon ? '105px' : '50px',\n      ...props.rowSelection,\n      preserveSelectedRowKeys: true,\n      renderCell: props.showPublishedIcon ? (_value, record, _index, originNode) => {\n        return /*#__PURE__*/React.createElement(_Row, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 255,\n            columnNumber: 21\n          }\n        }, /*#__PURE__*/React.createElement(_Col, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 256,\n            columnNumber: 23\n          }\n        }, originNode), /*#__PURE__*/React.createElement(_Col, {\n          style: {\n            paddingLeft: '10px'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 257,\n            columnNumber: 23\n          }\n        }, record.published && /*#__PURE__*/React.createElement(_Tooltip, {\n          title: 'Published to Portal',\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 259,\n            columnNumber: 27\n          }\n        }, /*#__PURE__*/React.createElement(MdOpenInNew, {\n          color: \"#134A82\",\n          size: 20,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 260,\n            columnNumber: 29\n          }\n        }))), /*#__PURE__*/React.createElement(_Col, {\n          style: {\n            paddingLeft: '5px'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 264,\n            columnNumber: 23\n          }\n        }, record.pinned && /*#__PURE__*/React.createElement(_Tooltip, {\n          title: 'Pinned',\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 266,\n            columnNumber: 27\n          }\n        }, /*#__PURE__*/React.createElement(VscPinned, {\n          color: \"#134A82\",\n          size: 20,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 267,\n            columnNumber: 29\n          }\n        }))), /*#__PURE__*/React.createElement(_Col, {\n          style: {\n            paddingLeft: '5px'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 271,\n            columnNumber: 23\n          }\n        }, record.linked && /*#__PURE__*/React.createElement(_Tooltip, {\n          title: 'Linked to ' + record.linkedSiteCount + ' File Area(s)',\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 274,\n            columnNumber: 29\n          }\n        }, /*#__PURE__*/React.createElement(SwapOutlined, {\n          color: \"#134A82\",\n          size: 20,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 275,\n            columnNumber: 31\n          }\n        }))));\n      } : undefined\n    } : undefined,\n    loading: {\n      spinning: state.loading,\n      indicator: /*#__PURE__*/React.createElement(LoadingOutlined, {\n        style: {\n          fontSize: 36\n        },\n        spin: true,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 288,\n          columnNumber: 22\n        }\n      })\n    },\n    dataSource: state.records,\n    showSorterTooltip: false,\n    onChange: onChange,\n    rowKey: row => {\n      return !!props.rowKey ? row[props.rowKey] : row.id;\n    },\n    locale: {\n      emptyText: Object.keys(state.filters).length > 0 ? 'No Results Found' : props.noRecordsAvilableMessage ? props.noRecordsAvilableMessage : 'No Records Available'\n    },\n    scroll: {\n      x: ((_props$columns2 = props.columns) === null || _props$columns2 === void 0 ? void 0 : _props$columns2.length) && ((_props$columns3 = props.columns) === null || _props$columns3 === void 0 ? void 0 : _props$columns3.length) > (props.scrollColumnCounter !== undefined ? props.scrollColumnCounter : DEFAULT_COLUMN_COUNTER) ? MAX_SCREEN_WIDTH : undefined,\n      y: window.screen.width < MINIMUM_RESOLUTION_VALUE ? props.lowResolutionWidth ? props.lowResolutionWidth : 'auto' : props.highResolutionWidth ? props.highResolutionWidth : 'auto'\n    },\n    pagination: !props.hidePagination && paginationConf,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 234,\n      columnNumber: 7\n    }\n  })), !props.hidePagination && !state.loading && /*#__PURE__*/React.createElement(RecordCount, {\n    total: state.pagination.total,\n    current: state.pagination.current,\n    recordsCount: state.records.length,\n    pageSize: state.pagination.pageSize,\n    selectedRecordCount: props.selectedRecordCount,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 315,\n      columnNumber: 51\n    }\n  }));\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/components/GenericDataTable/AntTableContainer/index.tsx"], "names": ["React", "useContext", "useEffect", "useState", "v4", "uuidv4", "useDispatch", "useSelector", "LoadingOutlined", "SwapOutlined", "DataTableContext", "setSelectedSavedFilter", "setTableLoading", "updateChange", "updateDataRecords", "logger", "RecordCount", "prepareFilterObject", "updateGridColumns", "updateGridHasUpdates", "MdOpenInNew", "VscPinned", "getRecords", "props", "ROW_SEPERATER_VALUE", "DEFAULT_COLUMN_COUNTER", "MINIMUM_RESOLUTION_VALUE", "MAX_SCREEN_WIDTH", "state", "dispatch", "searchQueryParameters", "gridFilters", "table<PERSON><PERSON>", "gridTablekey", "store", "grid", "dispatchGrid", "setTable<PERSON>ey", "tableSorted", "setTableSorted", "sortedInfo", "setSortedInfo", "order", "sorted", "column<PERSON>ey", "value", "generatedColumValues", "setGeneratedColumValues", "columns", "map", "column", "sorter", "records", "length", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "undefined", "Object", "keys", "filtersFromSelectedSavedFilter", "find", "filter", "canceled", "queryParams", "transformFilters", "filters", "filterByIds", "fileId", "split", "debug", "dataPromise", "error", "warn", "endpoint", "pagination", "current", "pageSize", "key", "i", "default", "selected", "then", "data", "catch", "e", "onErrorLoading", "onGridStateChange", "onFiltersChange", "tableElement", "document", "getElementsByClassName", "scrollTo", "left", "behavior", "updatedGrid", "total", "onChange", "sorting", "field", "sortedColumValues", "col", "sortOrder", "paginationConf", "disabled", "disablePagination", "className", "pageSizeOptions", "style", "display", "position", "zIndex", "showSizeChanger", "locale", "items_per_page", "record", "index", "defaultClassName", "additionalClassName", "rowClassName", "onRow", "rowSelection", "type", "columnWidth", "showPublishedIcon", "preserveSelectedRowKeys", "renderCell", "_value", "_index", "originNode", "paddingLeft", "published", "pinned", "linked", "linkedSiteCount", "spinning", "loading", "indicator", "fontSize", "row", "<PERSON><PERSON><PERSON>", "id", "emptyText", "noRecordsAvilableMessage", "x", "scrollColumnCounter", "y", "window", "screen", "width", "lowResolutionWidth", "highResolutionWidth", "hidePagination", "selected<PERSON><PERSON><PERSON><PERSON><PERSON>nt"], "mappings": ";;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,UAAhB,EAA4BC,SAA5B,EAAuCC,QAAvC,QAAuD,OAAvD;AACA,SAASC,EAAE,IAAIC,MAAf,QAA6B,MAA7B;AAEA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,aAAzC;AAEA,SAASC,eAAT,EAA0BC,YAA1B,QAA8C,mBAA9C;AAEA,SAASC,gBAAT,QAAiC,qBAAjC;AACA,SAASC,sBAAT,EAAiCC,eAAjC,EAAkDC,YAAlD,EAAgEC,iBAAhE,QAAyF,6BAAzF;AACA,OAAOC,MAAP,MAAmB,uBAAnB;AACA,OAAOC,WAAP,MAAwB,gBAAxB;AACA,SAASC,mBAAT,QAAoC,SAApC;AAEA,SAASC,iBAAT,EAA4BC,oBAA5B,QAAwD,iCAAxD;AAEA,SAASC,WAAT,QAA4B,gBAA5B;AACA,SAASC,SAAT,QAA0B,iBAA1B;AAGA,SAASC,UAAT,QAA2B,2BAA3B;AAQA,gBAAgBC,KAAD,IAA+C;AAAA;;AAC5D,QAAMC,mBAAmB,GAAG,CAA5B;AACA,QAAMC,sBAAsB,GAAG,CAA/B;AACA,QAAMC,wBAAwB,GAAG,IAAjC;AACA,QAAMC,gBAAgB,GAAG,IAAzB;AACA,QAAM;AAAEC,IAAAA,KAAF;AAASC,IAAAA;AAAT,MAAsB5B,UAAU,CAACS,gBAAD,CAAtC;AACA,QAAM;AAAEoB,IAAAA,qBAAF;AAAyBC,IAAAA,WAAzB;AAAsCC,IAAAA,QAAQ,EAAEC;AAAhD,MAAiE1B,WAAW,CAAE2B,KAAD,IAAsBA,KAAK,CAACC,IAA7B,CAAlF;AACA,QAAMC,YAAY,GAAG9B,WAAW,EAAhC;AACA,QAAM,CAAC0B,QAAD,EAAWK,WAAX,IAA0BlC,QAAQ,CAAS,EAAT,CAAxC;AACA,QAAM,CAACmC,WAAD,EAAcC,cAAd,IAAgCpC,QAAQ,CAAU,KAAV,CAA9C;AACA,QAAM,CAACqC,UAAD,EAAaC,aAAb,IAA8BtC,QAAQ,CAAM;AAChDuC,IAAAA,KAAK,mBAAEnB,KAAK,CAACoB,MAAR,kDAAE,cAAcD,KAD2B;AAEhDE,IAAAA,SAAS,oBAAErB,KAAK,CAACoB,MAAR,mDAAE,eAAcE;AAFuB,GAAN,CAA5C,CAV4D,CAc5D;;AACA,QAAM,CAACC,oBAAD,EAAuBC,uBAAvB,IAAkD5C,QAAQ,CAAM,EAAN,CAAhE;AACAD,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIqB,KAAK,CAACyB,OAAV,EAAmB;AAAA;;AACjBD,MAAAA,uBAAuB,mBACrBxB,KAAK,CAACyB,OADe,mDACrB,eAAeC,GAAf,CAAoBC,MAAD,IAAY;AAC7B,eAAO,EACL,GAAGA,MADE;AAELC,UAAAA,MAAM,EAAEvB,KAAK,CAACwB,OAAN,CAAcC,MAAd,GAAuB,CAAvB,GAA2BH,MAAM,CAACC,MAAlC,GAA2C;AAF9C,SAAP;AAID,OALD,CADqB,CAAvB;;AAQA,UAAI,CAACb,WAAL,EAAkB;AAAA;;AAChBG,QAAAA,aAAa,CAAC;AACZC,UAAAA,KAAK,oBAAEnB,KAAK,CAACoB,MAAR,mDAAE,eAAcD,KADT;AAEZE,UAAAA,SAAS,oBAAErB,KAAK,CAACoB,MAAR,mDAAE,eAAcE;AAFb,SAAD,CAAb;AAID;AACF;AACF,GAjBQ,EAiBN,CAACtB,KAAK,CAACyB,OAAP,EAAgBpB,KAAK,CAACwB,OAAtB,EAA+Bd,WAA/B,CAjBM,CAAT;AAmBApC,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIqB,KAAK,CAAC+B,mBAAN,KAA8BC,SAA9B,IAA2ChC,KAAK,CAAC+B,mBAAN,KAA8B,IAAzE,IAAiFE,MAAM,CAACC,IAAP,CAAYlC,KAAK,CAAC+B,mBAAlB,EAAuCD,MAAvC,KAAkD,CAAvI,EAA0I;AACxI,YAAMK,8BAA8B,GAAG3B,WAAvC;;AACA,UAAI,CAAC2B,8BAA8B,CAACC,IAA/B,CAAqCC,MAAD,IAAiBA,MAAM,KAAKL,SAAhE,CAAL,EAAiF;AAC/E1B,QAAAA,QAAQ,CAAClB,sBAAsB,CAAC+C,8BAAD,CAAvB,CAAR;AACD;AACF;AACF,GAPQ,EAON,CAAC3B,WAAD,EAAcR,KAAK,CAAC+B,mBAApB,EAAyCzB,QAAzC,CAPM,CAAT;AASA3B,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI2D,QAAQ,GAAG,KAAf;AACA,QAAIC,WAAW,GAAGhC,qBAAlB;AAEA,QAAIP,KAAK,CAACO,qBAAV,EAAiCgC,WAAW,GAAG,CAAC,GAAGvC,KAAK,CAACO,qBAAV,EAAiC,GAAGgC,WAApC,CAAd;;AAEjC,QAAIlC,KAAK,CAACoB,OAAN,CAAcK,MAAlB,EAA0B;AACxBxB,MAAAA,QAAQ,CAACjB,eAAe,CAAC,IAAD,CAAhB,CAAR;AAEA,YAAMmD,gBAAgB,GAAG9C,mBAAmB,CAACW,KAAK,CAACoC,OAAP,CAA5C;;AACA,UAAIzC,KAAK,CAAC0C,WAAV,EAAuB;AACrBF,QAAAA,gBAAgB,CAACG,MAAjB,GAA0B3C,KAAK,CAAC0C,WAAN,CAAkBE,KAAlB,CAAwB,GAAxB,CAA1B;AACD;;AACDpD,MAAAA,MAAM,CAACqD,KAAP,CAAa,mBAAb,EAAkC,mBAAlC,EAAuD;AACrDL,QAAAA;AADqD,OAAvD;AAKA;AACN;AACA;;AACM,UAAI,CAACxC,KAAK,CAAC8C,WAAX,EAAwB;AACtBtD,QAAAA,MAAM,CAACuD,KAAP,CAAa,qBAAb,EAAoC,kBAApC,EAAyD;AACjE,6BADQ;AAEAvD,QAAAA,MAAM,CAACwD,IAAP,CAAY,qBAAZ,EAAmC,kBAAnC,EAAwD;AAChE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAdQ;AAeD;;AACD,YAAMF,WAAW,GAAG,CAAC9C,KAAK,CAAC8C,WAAP,GAAqB/C,UAAU,CACjDC,KAAK,CAACiD,QAD2C,EAEjD;AACEC,QAAAA,UAAU,EAAE;AACVC,UAAAA,OAAO,EAAE9C,KAAK,CAAC6C,UAAN,CAAiBC,OADhB;AAEVC,UAAAA,QAAQ,EAAE/C,KAAK,CAAC6C,UAAN,CAAiBE;AAFjB,SADd;AAKExB,QAAAA,MAAM,EAAEvB,KAAK,CAACuB,MAAN,GAAe;AAAEyB,UAAAA,GAAG,EAAEhD,KAAK,CAACuB,MAAN,CAAaP,SAApB;AAA+BF,UAAAA,KAAK,EAAEd,KAAK,CAACuB,MAAN,CAAaT;AAAnD,SAAf,GAA4E,EALtF;AAMEsB,QAAAA,OAAO,EAAED,gBANX;AAOEf,QAAAA,OAAO,EAAEpB,KAAK,CAACoB,OAAN,CAAcY,MAAd,CAAsBiB,CAAD,IAAOA,CAAC,CAACC,OAAF,KAAc,KAAd,IAAuBD,CAAC,CAACE,QAAF,KAAe,IAAlE,EAAwE9B,GAAxE,CAA6E4B,CAAD,IAAOA,CAAC,CAACD,GAArF;AAPX,OAFiD,EAWjDd,WAXiD,CAA/B,GAYhBvC,KAAK,CAAC8C,WAAN,CAAkBzC,KAAlB,EAAyBmC,gBAAzB,EAA2CD,WAA3C,CAZJ;AAeAO,MAAAA,WAAW,CAACW,IAAZ,CAAkBH,CAAD,IAAO;AACtB,YAAI,CAAChB,QAAL,EAAe;AACbzB,UAAAA,YAAY,CAACjB,oBAAoB,CAAC,KAAD,CAArB,CAAZ;AACAU,UAAAA,QAAQ,CAACf,iBAAiB,CAAC+D,CAAC,CAACI,IAAH,CAAlB,CAAR;AACApD,UAAAA,QAAQ,CAACjB,eAAe,CAAC,KAAD,CAAhB,CAAR;AACAwB,UAAAA,YAAY,CACVlB,iBAAiB,CAAC;AAChB8B,YAAAA,OAAO,EAAEpB,KAAK,CAACoB,OADC;AAEhBhB,YAAAA,QAAQ,EAAEC;AAFM,WAAD,CADP,CAAZ;AAMD;AACF,OAZD,EAYGiD,KAZH,CAYUC,CAAD,IAAO;AACd5D,QAAAA,KAAK,CAAC6D,cAAN,IAAwB7D,KAAK,CAAC6D,cAAN,CAAqBD,CAArB,CAAxB;AACApE,QAAAA,MAAM,CAACuD,KAAP,CAAa,kBAAb,EAAiC,yBAAjC,EAA4Da,CAA5D;AACD,OAfD;AAgBD;;AAED5D,IAAAA,KAAK,CAAC8D,iBAAN,IAA2B9D,KAAK,CAAC8D,iBAAN,CAAwB,EAAE,GAAGzD,KAAL;AAAYE,MAAAA;AAAZ,KAAxB,CAA3B;AAEA,WAAO,MAAM;AACX+B,MAAAA,QAAQ,GAAG,IAAX;AACD,KAFD;AAGD,GA9EQ,EA8EN,CAACjC,KAAK,CAACoC,OAAP,EAAgBzC,KAAK,CAAC8C,WAAtB,EAAmC9C,KAAK,CAACiD,QAAzC,EAAmD5C,KAAK,CAACoB,OAAzD,EAAkEpB,KAAK,CAACuB,MAAxE,EAAgFvB,KAAK,CAAC6C,UAAN,CAAiBE,QAAjG,EAA2G9C,QAA3G,EAAqHD,KAAK,CAAC6C,UAA3H,EAAuI3C,qBAAvI,CA9EM,CAAT;AAgFA5B,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIqB,KAAK,CAAC+D,eAAV,EAA2B;AACzB/D,MAAAA,KAAK,CAAC+D,eAAN,CAAsB,IAAtB;AACD;AACF,GAJQ,EAIN,CAAC1D,KAAK,CAACoC,OAAP,CAJM,CAAT;AAMA9D,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI0B,KAAK,CAACwB,OAAN,CAAcC,MAAd,KAAyB,CAA7B,EAAgC;AAC9B;AACAhB,MAAAA,WAAW,CAAChC,MAAM,EAAP,CAAX;AACD;AACF,GALQ,EAKN,CAACkB,KAAK,CAACyB,OAAP,CALM,CAAT;AAOA9C,EAAAA,SAAS,CAAC,MAAM;AACd,UAAMqF,YAAY,GAAGC,QAAQ,CAACC,sBAAT,CAAgC,gBAAhC,CAArB;;AACA,QAAIF,YAAY,CAAC,CAAD,CAAhB,EAAqB;AACnBA,MAAAA,YAAY,CAAC,CAAD,CAAZ,CAAgBG,QAAhB,CAAyB;AACvBC,QAAAA,IAAI,EAAE,CADiB;AAEvBC,QAAAA,QAAQ,EAAE;AAFa,OAAzB;AAID;AACF,GARQ,EAQN,CAAChE,KAAK,CAACoB,OAAP,CARM,CAAT;AAUA9C,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIqB,KAAK,CAACsE,WAAV,EAAuB;AACrBhE,MAAAA,QAAQ,CAAChB,YAAY,CAAC;AAAE6D,QAAAA,OAAO,EAAE,CAAX;AAAcC,QAAAA,QAAQ,EAAE,EAAxB;AAA4BmB,QAAAA,KAAK,EAAElE,KAAK,CAAC6C,UAAN,CAAiBqB;AAApD,OAAD,EAA8D,EAA9D,EAAkE,EAAlE,CAAb,CAAR;AACD;AACF,GAJQ,EAIN,CAACvE,KAAK,CAACsE,WAAP,EAAoBhE,QAApB,EAA8BD,KAAK,CAAC6C,UAAN,CAAiBqB,KAA/C,CAJM,CAAT;;AAMA,QAAMC,QAAQ,GAAG,CAACtB,UAAD,EAAkBT,OAAlB,EAAgCgC,OAAhC,KAAiD;AAAA;;AAChEzD,IAAAA,cAAc,CAAC,IAAD,CAAd;AACA;AACJ;AACA;AACA;AACA;AACA;AACA;;AACI,QAAIC,UAAU,CAACE,KAAX,KAAqBsD,OAAO,CAACtD,KAA7B,IAAsCF,UAAU,CAACI,SAAX,KAAyBoD,OAAO,CAACC,KAAvE,IAAgFrE,KAAK,CAAC6C,UAAN,CAAiBE,QAAjB,KAA8BF,UAAU,CAACE,QAA7H,EAAuI;AACrIF,MAAAA,UAAU,CAACC,OAAX,GAAqB,CAArB;AACD;;AAED,QAAIsB,OAAO,CAACC,KAAR,wBAAkB1E,KAAK,CAACoB,MAAxB,mDAAkB,eAAcE,KAAhC,KAAyC,CAACmD,OAAO,CAAC9C,MAAtD,EAA8D;AAAA;;AAC5DT,MAAAA,aAAa,CAAC;AACZC,QAAAA,KAAK,oBAAEnB,KAAK,CAACoB,MAAR,mDAAE,eAAcD,KADT;AAEZE,QAAAA,SAAS,oBAAErB,KAAK,CAACoB,MAAR,mDAAE,eAAcE;AAFb,OAAD,CAAb;AAID,KALD,MAKO;AACLJ,MAAAA,aAAa,CAAC;AACZC,QAAAA,KAAK,EAAEsD,OAAO,CAACtD,KADH;AAEZE,QAAAA,SAAS,EAAEoD,OAAO,CAACC;AAFP,OAAD,CAAb;AAID;;AAEDpE,IAAAA,QAAQ,CAAChB,YAAY,CAAC4D,UAAD,EAAaT,OAAb,EAAsBgC,OAAtB,CAAb,CAAR;AACD,GA1BD;;AA4BA,QAAME,iBAAiB,GAAG,CACxB,GAAGpD,oBAAoB,CAACG,GAArB,CAA0BkD,GAAD,IAAc;AACxC,WAAO,EACL,GAAGA,GADE;AAELC,MAAAA,SAAS,EAAE5D,UAAU,CAACI,SAAX,KAAyBuD,GAAG,CAACvB,GAA7B,GAAmCpC,UAAU,CAACE,KAA9C,GAAsD;AAF5D,KAAP;AAID,GALE,CADqB,CAA1B;AASA,QAAM2D,cAA+B,GAAG;AACtCC,IAAAA,QAAQ,EAAE/E,KAAK,CAACgF,iBADsB;AAEtCC,IAAAA,SAAS,EAAE,kBAF2B;AAGtCV,IAAAA,KAAK,EAAElE,KAAK,CAAC6C,UAAN,CAAiBqB,KAHc;AAItCpB,IAAAA,OAAO,EAAE9C,KAAK,CAAC6C,UAAN,CAAiBC,OAJY;AAKtCC,IAAAA,QAAQ,EAAE/C,KAAK,CAAC6C,UAAN,CAAiBE,QALW;AAMtC8B,IAAAA,eAAe,EAAE,CAAC,IAAD,EAAO,IAAP,EAAa,IAAb,EAAmB,IAAnB,CANqB;AAOtCC,IAAAA,KAAK,EAAE;AAAEC,MAAAA,OAAO,EAAE,cAAX;AAA2BC,MAAAA,QAAQ,EAAE,UAArC;AAAiDC,MAAAA,MAAM,EAAE;AAAzD,KAP+B;AAQtCC,IAAAA,eAAe,EAAE,IARqB;AAStCC,IAAAA,MAAM,EAAE;AACNC,MAAAA,cAAc,EAAE;AADV;AAT8B,GAAxC;AAcA,sBACE,uDACE,8CACMzF,KADN;AAEE,IAAA,YAAY,EAAE,CAAC0F,MAAD,EAASC,KAAT,KAAmB;AAC/B,YAAMC,gBAAgB,GAAID,KAAK,GAAG1F,mBAAR,KAAgC,CAAhC,GAAoC,iBAApC,GAAwD,gBAAlF;AACA,YAAM4F,mBAAmB,GAAG7F,KAAK,CAAC8F,YAAN,GAAsB,IAAG9F,KAAK,CAAC8F,YAAN,CAAmBJ,MAAnB,EAA2BC,KAA3B,CAAkC,EAA3D,GAA+D,EAA3F;AACA,aAAQ,GAAEC,gBAAiB,IAAGC,mBAAoB,EAAlD;AACD,KANH;AAOE,IAAA,GAAG,EAAEpF,QAPP;AAQE,IAAA,SAAS,EAAE,kBARb;AASE,IAAA,OAAO,EAAEkE,iBATX;AAUE,IAAA,KAAK,EAAE3E,KAAK,CAAC+F,KAVf;AAWE,IAAA,YAAY,EACV/F,KAAK,CAACgG,YAAN,GACI;AACAC,MAAAA,IAAI,EAAE,UADN;AAEAC,MAAAA,WAAW,EAAElG,KAAK,CAACmG,iBAAN,GAA0B,OAA1B,GAAoC,MAFjD;AAGA,SAAGnG,KAAK,CAACgG,YAHT;AAIAI,MAAAA,uBAAuB,EAAE,IAJzB;AAKAC,MAAAA,UAAU,EAAErG,KAAK,CAACmG,iBAAN,GACR,CAACG,MAAD,EAAkBZ,MAAlB,EAA+Ba,MAA/B,EAA+CC,UAA/C,KAA+E;AAC/E,4BACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAAMA,UAAN,CADF,eAEE;AAAK,UAAA,KAAK,EAAE;AAAEC,YAAAA,WAAW,EAAE;AAAf,WAAZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WACGf,MAAM,CAACgB,SAAP,iBACC;AAAS,UAAA,KAAK,EAAE,qBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE,oBAAC,WAAD;AAAa,UAAA,KAAK,EAAC,SAAnB;AAA6B,UAAA,IAAI,EAAE,EAAnC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UADF,CAFJ,CAFF,eASE;AAAK,UAAA,KAAK,EAAE;AAAED,YAAAA,WAAW,EAAE;AAAf,WAAZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WACGf,MAAM,CAACiB,MAAP,iBACC;AAAS,UAAA,KAAK,EAAE,QAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE,oBAAC,SAAD;AAAW,UAAA,KAAK,EAAC,SAAjB;AAA2B,UAAA,IAAI,EAAE,EAAjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UADF,CAFJ,CATF,eAgBE;AAAK,UAAA,KAAK,EAAE;AAAEF,YAAAA,WAAW,EAAE;AAAf,WAAZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAEIf,MAAM,CAACkB,MAAP,iBACE;AAAS,UAAA,KAAK,EAAE,eAAelB,MAAM,CAACmB,eAAtB,GAAwC,eAAxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE,oBAAC,YAAD;AAAc,UAAA,KAAK,EAAC,SAApB;AAA8B,UAAA,IAAI,EAAE,EAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UADF,CAHN,CAhBF,CADF;AA2BD,OA7BS,GA8BR7E;AAnCJ,KADJ,GAsCIA,SAlDR;AAoDE,IAAA,OAAO,EAAE;AACP8E,MAAAA,QAAQ,EAAEzG,KAAK,CAAC0G,OADT;AAEPC,MAAAA,SAAS,eAAE,oBAAC,eAAD;AAAiB,QAAA,KAAK,EAAE;AAAEC,UAAAA,QAAQ,EAAE;AAAZ,SAAxB;AAA0C,QAAA,IAAI,MAA9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAFJ,KApDX;AAwDE,IAAA,UAAU,EAAE5G,KAAK,CAACwB,OAxDpB;AAyDE,IAAA,iBAAiB,EAAE,KAzDrB;AA0DE,IAAA,QAAQ,EAAE2C,QA1DZ;AA2DE,IAAA,MAAM,EAAG0C,GAAD,IAAc;AACpB,aAAO,CAAC,CAAClH,KAAK,CAACmH,MAAR,GAAiBD,GAAG,CAAClH,KAAK,CAACmH,MAAP,CAApB,GAAqCD,GAAG,CAACE,EAAhD;AACD,KA7DH;AA8DE,IAAA,MAAM,EAAE;AACNC,MAAAA,SAAS,EAAEpF,MAAM,CAACC,IAAP,CAAY7B,KAAK,CAACoC,OAAlB,EAA2BX,MAA3B,GAAoC,CAApC,GAAwC,kBAAxC,GAA6D9B,KAAK,CAACsH,wBAAN,GAAiCtH,KAAK,CAACsH,wBAAvC,GAAkE;AADpI,KA9DV;AAiEE,IAAA,MAAM,EAAE;AACNC,MAAAA,CAAC,EACC,oBAAAvH,KAAK,CAACyB,OAAN,oEAAeK,MAAf,KAAyB,oBAAA9B,KAAK,CAACyB,OAAN,oEAAeK,MAAf,KAAyB9B,KAAK,CAACwH,mBAAN,KAA8BxF,SAA9B,GAA0ChC,KAAK,CAACwH,mBAAhD,GAAsEtH,sBAA/F,CAAzB,GACIE,gBADJ,GAEI4B,SAJA;AAKNyF,MAAAA,CAAC,EACCC,MAAM,CAACC,MAAP,CAAcC,KAAd,GAAsBzH,wBAAtB,GACIH,KAAK,CAAC6H,kBAAN,GACE7H,KAAK,CAAC6H,kBADR,GAEE,MAHN,GAII7H,KAAK,CAAC8H,mBAAN,GACE9H,KAAK,CAAC8H,mBADR,GAEE;AAZF,KAjEV;AA+EE,IAAA,UAAU,EAAE,CAAC9H,KAAK,CAAC+H,cAAP,IAAyBjD,cA/EvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KADF,EAkFG,CAAC9E,KAAK,CAAC+H,cAAP,IAAyB,CAAC1H,KAAK,CAAC0G,OAAhC,iBAA2C,oBAAC,WAAD;AAC1C,IAAA,KAAK,EAAE1G,KAAK,CAAC6C,UAAN,CAAiBqB,KADkB;AAE1C,IAAA,OAAO,EAAElE,KAAK,CAAC6C,UAAN,CAAiBC,OAFgB;AAG1C,IAAA,YAAY,EAAE9C,KAAK,CAACwB,OAAN,CAAcC,MAHc;AAI1C,IAAA,QAAQ,EAAEzB,KAAK,CAAC6C,UAAN,CAAiBE,QAJe;AAK1C,IAAA,mBAAmB,EAAEpD,KAAK,CAACgI,mBALe;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAlF9C,CADF;AA4FD,CAxSD", "sourcesContent": ["import React, { useContext, useEffect, useState } from 'react';\r\nimport { v4 as uuidv4 } from 'uuid';\r\nimport { Col, Row, Table, Tooltip } from 'antd';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { TableProps } from 'antd/lib/table';\r\nimport { LoadingOutlined, SwapOutlined } from '@ant-design/icons';\r\n\r\nimport { DataTableContext } from '../DataTableContext';\r\nimport { setSelectedSavedFilter, setTableLoading, updateChange, updateDataRecords } from '../DataTableContext/actions';\r\nimport logger from '../../../utils/logger';\r\nimport RecordCount from '../RecordCount';\r\nimport { prepareFilterObject } from '../util';\r\nimport { RootState } from '@app/redux/reducers/state';\r\nimport { updateGridColumns, updateGridHasUpdates } from '@app/redux/actions/gridsActions';\r\nimport { SavedFilterTemplate } from '@app/types/filterTemplateTypes';\r\nimport { MdOpenInNew } from 'react-icons/md';\r\nimport { VscPinned } from \"react-icons/vsc\";\r\nimport { PaginationProps } from \"antd/lib/pagination\";\r\nimport { DataTablePropType } from \"@app/components/GenericDataTable/GenericDataTableBuilder\";\r\nimport { getRecords } from \"@app/api/genericDataTable\";\r\n\r\ntype CustomTableProps = DataTablePropType & {\r\n  filters: any[];\r\n  selectedSavedFilter?: SavedFilterTemplate;\r\n  disablePagination?: boolean;\r\n}\r\n\r\nexport default (props: TableProps<any> & CustomTableProps) => {\r\n  const ROW_SEPERATER_VALUE = 2;\r\n  const DEFAULT_COLUMN_COUNTER = 5;\r\n  const MINIMUM_RESOLUTION_VALUE = 1367;\r\n  const MAX_SCREEN_WIDTH = 2400;\r\n  const { state, dispatch } = useContext(DataTableContext);\r\n  const { searchQueryParameters, gridFilters, tableKey: gridTablekey } = useSelector((store: RootState) => store.grid);\r\n  const dispatchGrid = useDispatch();\r\n  const [tableKey, setTableKey] = useState<string>('');\r\n  const [tableSorted, setTableSorted] = useState<boolean>(false);\r\n  const [sortedInfo, setSortedInfo] = useState<any>({\r\n    order: props.sorted?.order,\r\n    columnKey: props.sorted?.value,\r\n  });\r\n  //update the table content according to the selection of saved filters by user\r\n  const [generatedColumValues, setGeneratedColumValues] = useState<any>([]);\r\n  useEffect(() => {\r\n    if (props.columns) {\r\n      setGeneratedColumValues(\r\n        props.columns?.map((column) => {\r\n          return {\r\n            ...column,\r\n            sorter: state.records.length > 0 ? column.sorter : false,\r\n          };\r\n        })\r\n      );\r\n      if (!tableSorted) {\r\n        setSortedInfo({\r\n          order: props.sorted?.order,\r\n          columnKey: props.sorted?.value,\r\n        });\r\n      }\r\n    }\r\n  }, [props.columns, state.records, tableSorted]);\r\n\r\n  useEffect(() => {\r\n    if (props.selectedSavedFilter !== undefined && props.selectedSavedFilter !== null && Object.keys(props.selectedSavedFilter).length !== 0) {\r\n      const filtersFromSelectedSavedFilter = gridFilters;\r\n      if (!filtersFromSelectedSavedFilter.find((filter: any) => filter === undefined)) {\r\n        dispatch(setSelectedSavedFilter(filtersFromSelectedSavedFilter));\r\n      }\r\n    }\r\n  }, [gridFilters, props.selectedSavedFilter, dispatch]);\r\n\r\n  useEffect(() => {\r\n    let canceled = false;\r\n    let queryParams = searchQueryParameters;\r\n\r\n    if (props.searchQueryParameters) queryParams = [...props.searchQueryParameters, ...queryParams];\r\n\r\n    if (state.columns.length) {\r\n      dispatch(setTableLoading(true));\r\n\r\n      const transformFilters = prepareFilterObject(state.filters);\r\n      if (props.filterByIds) {\r\n        transformFilters.fileId = props.filterByIds.split(',');\r\n      }\r\n      logger.debug('Generic DataTable', 'AntTableContainer', {\r\n        transformFilters,\r\n      });\r\n\r\n\r\n      /**\r\n       * TODO - This getRecords with props.endpoint scenario needs to remove after successfully refactor\r\n       */\r\n      if (!props.dataPromise) {\r\n        logger.error('AntGenericDataTable', 'GenericDataTable', `WARNING! Endpoint property has been deprecated, Please use dataPromise, columnPromise, onGridStateChange etc\r\n          <GenericDataTable `)\r\n        logger.warn('AntGenericDataTable', 'GenericDataTable', `WARNING! Endpoint property has been deprecated, Please use dataPromise, columnPromise, onGridStateChange etc\r\n          <GenericDataTable\r\n          dataPromise={(state) => fetchData(state)}\r\n          columnPromise={\r\n            getColumns(config.api[OperationalServiceTypes.MasterDataService].templates, \"siteManagement\")\r\n          }\r\n          rowKey={\"siteId\"}\r\n          scrollColumnCounter={7}\r\n          tableKey={\"siteManagement\"}\r\n          fixedColumns={[\"name\"]}\r\n          isDraggable={true}\r\n          noRecordsAvilableMessage={\"No Sites Available\"}\r\n          hasFilterManagement={true}\r\n        />\r\n      `)\r\n      }\r\n      const dataPromise = !props.dataPromise ? getRecords(\r\n        props.endpoint!,\r\n        {\r\n          pagination: {\r\n            current: state.pagination.current,\r\n            pageSize: state.pagination.pageSize,\r\n          },\r\n          sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},\r\n          filters: transformFilters,\r\n          columns: state.columns.filter((i) => i.default === false && i.selected === true).map((i) => i.key),\r\n        },\r\n        queryParams\r\n      ) : props.dataPromise(state, transformFilters, queryParams);\r\n\r\n\r\n      dataPromise.then((i) => {\r\n        if (!canceled) {\r\n          dispatchGrid(updateGridHasUpdates(false));\r\n          dispatch(updateDataRecords(i.data));\r\n          dispatch(setTableLoading(false));\r\n          dispatchGrid(\r\n            updateGridColumns({\r\n              columns: state.columns,\r\n              tableKey: gridTablekey,\r\n            })\r\n          );\r\n        }\r\n      }).catch((e) => {\r\n        props.onErrorLoading && props.onErrorLoading(e);\r\n        logger.error('GenericDataTable', 'GenericDataTableBuilder', e);\r\n      });\r\n    }\r\n\r\n    props.onGridStateChange && props.onGridStateChange({ ...state, searchQueryParameters });\r\n\r\n    return () => {\r\n      canceled = true;\r\n    };\r\n  }, [state.filters, props.dataPromise, props.endpoint, state.columns, state.sorter, state.pagination.pageSize, dispatch, state.pagination, searchQueryParameters]);\r\n\r\n  useEffect(() => {\r\n    if (props.onFiltersChange) {\r\n      props.onFiltersChange(true);\r\n    }\r\n  }, [state.filters]);\r\n\r\n  useEffect(() => {\r\n    if (state.records.length === 0) {\r\n      //genarate UUID for fir rerender the Table Componanat\r\n      setTableKey(uuidv4());\r\n    }\r\n  }, [props.columns]);\r\n\r\n  useEffect(() => {\r\n    const tableElement = document.getElementsByClassName('ant-table-body');\r\n    if (tableElement[0]) {\r\n      tableElement[0].scrollTo({\r\n        left: 0,\r\n        behavior: 'smooth',\r\n      });\r\n    }\r\n  }, [state.columns]);\r\n\r\n  useEffect(() => {\r\n    if (props.updatedGrid) {\r\n      dispatch(updateChange({ current: 1, pageSize: 20, total: state.pagination.total }, {}, {}));\r\n    }\r\n  }, [props.updatedGrid, dispatch, state.pagination.total]);\r\n\r\n  const onChange = (pagination: any, filters: any, sorting: any) => {\r\n    setTableSorted(true);\r\n    /**\r\n     * Reset The pagination.current to 1\r\n     * if\r\n     *  Sorter Column Changed or\r\n     *  Sorter Order Changed or\r\n     *  Page Size Change or\r\n     */\r\n    if (sortedInfo.order !== sorting.order || sortedInfo.columnKey !== sorting.field || state.pagination.pageSize !== pagination.pageSize) {\r\n      pagination.current = 1;\r\n    }\r\n\r\n    if (sorting.field !== props.sorted?.value && !sorting.column) {\r\n      setSortedInfo({\r\n        order: props.sorted?.order,\r\n        columnKey: props.sorted?.value,\r\n      });\r\n    } else {\r\n      setSortedInfo({\r\n        order: sorting.order,\r\n        columnKey: sorting.field,\r\n      });\r\n    }\r\n\r\n    dispatch(updateChange(pagination, filters, sorting));\r\n  };\r\n\r\n  const sortedColumValues = [\r\n    ...generatedColumValues.map((col: any) => {\r\n      return {\r\n        ...col,\r\n        sortOrder: sortedInfo.columnKey === col.key ? sortedInfo.order : false,\r\n      };\r\n    }),\r\n  ];\r\n\r\n  const paginationConf: PaginationProps = {\r\n    disabled: props.disablePagination,\r\n    className: 'yjGridPagination',\r\n    total: state.pagination.total,\r\n    current: state.pagination.current,\r\n    pageSize: state.pagination.pageSize,\r\n    pageSizeOptions: ['10', '20', '25', '50'],\r\n    style: { display: 'inline-block', position: 'relative', zIndex: 99 },\r\n    showSizeChanger: true,\r\n    locale: {\r\n      items_per_page: 'rows',\r\n    },\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Table\r\n        {...props}\r\n        rowClassName={(record, index) => {\r\n          const defaultClassName = (index % ROW_SEPERATER_VALUE === 0 ? 'table-row-light' : 'table-row-dark');\r\n          const additionalClassName = props.rowClassName ? ` ${props.rowClassName(record, index)}` : '';\r\n          return `${defaultClassName} ${additionalClassName}`;\r\n        }}\r\n        key={tableKey}\r\n        className={'yjTableContainer'}\r\n        columns={sortedColumValues}\r\n        onRow={props.onRow}\r\n        rowSelection={\r\n          props.rowSelection\r\n            ? {\r\n              type: 'checkbox',\r\n              columnWidth: props.showPublishedIcon ? '105px' : '50px',\r\n              ...props.rowSelection,\r\n              preserveSelectedRowKeys: true,\r\n              renderCell: props.showPublishedIcon\r\n                ? (_value: boolean, record: any, _index: number, originNode: React.ReactNode) => {\r\n                  return (\r\n                    <Row>\r\n                      <Col>{originNode}</Col>\r\n                      <Col style={{ paddingLeft: '10px' }}>\r\n                        {record.published && (\r\n                          <Tooltip title={'Published to Portal'}>\r\n                            <MdOpenInNew color=\"#134A82\" size={20} />\r\n                          </Tooltip>\r\n                        )}\r\n                      </Col>\r\n                      <Col style={{ paddingLeft: '5px' }}>\r\n                        {record.pinned && (\r\n                          <Tooltip title={'Pinned'}>\r\n                            <VscPinned color=\"#134A82\" size={20} />\r\n                          </Tooltip>\r\n                        )}\r\n                      </Col>\r\n                      <Col style={{ paddingLeft: '5px' }}>\r\n                        {\r\n                          record.linked && (\r\n                            <Tooltip title={'Linked to ' + record.linkedSiteCount + ' File Area(s)'}>\r\n                              <SwapOutlined color=\"#134A82\" size={20} />\r\n                            </Tooltip>\r\n                          )}\r\n                      </Col>\r\n                    </Row>\r\n                  );\r\n                }\r\n                : undefined,\r\n            }\r\n            : undefined\r\n        }\r\n        loading={{\r\n          spinning: state.loading,\r\n          indicator: <LoadingOutlined style={{ fontSize: 36 }} spin />,\r\n        }}\r\n        dataSource={state.records}\r\n        showSorterTooltip={false}\r\n        onChange={onChange}\r\n        rowKey={(row: any) => {\r\n          return !!props.rowKey ? row[props.rowKey] : row.id;\r\n        }}\r\n        locale={{\r\n          emptyText: Object.keys(state.filters).length > 0 ? 'No Results Found' : props.noRecordsAvilableMessage ? props.noRecordsAvilableMessage : 'No Records Available',\r\n        }}\r\n        scroll={{\r\n          x:\r\n            props.columns?.length && props.columns?.length > (props.scrollColumnCounter !== undefined ? props.scrollColumnCounter : DEFAULT_COLUMN_COUNTER)\r\n              ? MAX_SCREEN_WIDTH\r\n              : undefined,\r\n          y:\r\n            window.screen.width < MINIMUM_RESOLUTION_VALUE\r\n              ? props.lowResolutionWidth\r\n                ? props.lowResolutionWidth\r\n                : 'auto'\r\n              : props.highResolutionWidth\r\n                ? props.highResolutionWidth\r\n                : 'auto',\r\n        }}\r\n        pagination={!props.hidePagination && paginationConf}\r\n      />\r\n      {!props.hidePagination && !state.loading && <RecordCount\r\n        total={state.pagination.total}\r\n        current={state.pagination.current}\r\n        recordsCount={state.records.length}\r\n        pageSize={state.pagination.pageSize}\r\n        selectedRecordCount={props.selectedRecordCount}\r\n      />}\r\n    </>\r\n  );\r\n};\r\n"]}, "metadata": {}, "sourceType": "module"}