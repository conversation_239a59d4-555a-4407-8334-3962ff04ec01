import { useMemo } from 'react';
import { defaultDateOptions, defaultDateTimeOptions } from '../utils';

export const useDateTimeFormatter = (
  localizationCultureCode: string = 'en-US',
  showTime: boolean = false
) => {
  const formatter = useMemo(() => {
    if (showTime) {
      return new Intl.DateTimeFormat(localizationCultureCode, defaultDateTimeOptions);
    }
    return new Intl.DateTimeFormat(localizationCultureCode, defaultDateOptions);
  }, [localizationCultureCode, showTime]);

  return formatter.format;
};
