import React from "react";
import renderer from "react-test-renderer";
import { Col, Row, Card } from "antd";
import { shallow } from "enzyme";

import LicenseModulesContent from "../index";
import initTestSuite from "@app/utils/config/TestSuite";

describe("License Modules Content Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const lmcComponent = shallow(<LicenseModulesContent />);
    expect(lmcComponent.html()).not.toBe(null);
  });

  it("should create and match to snapshot", () => {
    const lmcComponent = renderer.create(<LicenseModulesContent />).toJSON();
    expect(lmcComponent).toMatchSnapshot();
  });

  it("should have a Row element", () => {
    const lmcComponent = shallow(<LicenseModulesContent />);
    expect(lmcComponent.find(Row)).toHaveLength(1);
  });

  it("should have 3 Col elements", () => {
    const lmcComponent = shallow(<LicenseModulesContent />);
    expect(lmcComponent.find(Col)).toHaveLength(3);
  });

  it("should have 3 Card elements", () => {
    const lmcComponent = shallow(<LicenseModulesContent />);
    expect(lmcComponent.find(Card)).toHaveLength(3);
  });
});
