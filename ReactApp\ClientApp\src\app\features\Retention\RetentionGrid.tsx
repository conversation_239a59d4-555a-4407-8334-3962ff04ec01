import config from "@app/utils/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import GenericDataTable from "@app/components/GenericDataTable";
import React, { forwardRef, useCallback, useImperativeHandle, useState } from "react";
import { getAutocompleteOptions, getColumns, getRecords } from "@app/api/genericDataTable";
import { Alert, Button, Collapse, Modal as AntModal } from "antd";
import { DeleteOutlined, ExclamationCircleOutlined } from "@ant-design/icons/lib";
import RetentionSearch from "@app/features/Retention/RetentionSearch";
import { deleteRetentions } from "@app/api/retentionService";
import { Sorter } from "@app/components/GenericDataTable/util";
import debounce from "lodash/debounce";
import logger from "@app/utils/logger";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const { Panel } = Collapse;

const { confirm } = AntModal;
const SORTER: Sorter = {
    value: "title",
    order: "ascend",
};
const RetentionGrid = forwardRef(({ onEdit }: any, ref) => {
    const [tableKey, setTableKey] = useState('retentionManagement');
    const [clientID, setClientID] = useState('');
    const [searchQuery, setSearchQuery] = useState<any>(null);
    const [selectedRecords, setSelectedRecords] = useState([]);
    const EXPIRATION_STATUS_PERMANENT = "Permanent";

    useImperativeHandle(ref, () => ({
        refresh: tableKeyUpdate
    }));

    const tableKeyUpdate = () => {
        /**
         * TODO Refresh should address with forwardRef and useImperativeHandle method inside GenericDataTable as well but
         * GenericDataTable need some refactor
         * As soon as got time we need to refactor this component
         */
        logger.info('RetentionGrid', 'tableKeyUpdate', 'Updating tableKey');
        setTableKey(`retentionManagement`);
    }

    let fetchData = (state: any, transformFilters: any, queryParams: any) => {
        logger.debug("RetentionGrid", "fetchData", { state, transformFilters, queryParams });
        return getRecords(
            config.api[OperationalServiceTypes.FileManagementService].retentions,
            {
                pagination: {
                    current: state.pagination.current,
                    pageSize: state.pagination.pageSize,
                },
                sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
                filters: { ...transformFilters, ...searchQuery },
                columns: state.columns.filter((i: any) => i.default === false && i.selected === true).map((i: any) => i.key),
            }, queryParams
        );
    };

    let fetchColumn = (columnQueryParameters: [{ value: any; key: string }]) => {
        return getColumns(config.api[OperationalServiceTypes.FileManagementService].retentions, tableKey, columnQueryParameters);
    };

    const renderSiteGridColumns = () => {
        return {
            hide: (value: string) => {
                return value ? 'Yes' : 'No';
            },
            created: (value: string) => {
                return <FormattedDateTime value={value} />;
            },
            modified: (value: string) => {
                return <FormattedDateTime value={value} />;
            },
            expired: (value: string, record: any) => {
                return record.expirationStatus === EXPIRATION_STATUS_PERMANENT ? '' : <FormattedDateTime value={value} />;
            },
        };
    };

    const onSubmitHandler = (e: any) => {
        setClientID(e.clientID);
        setSearchQuery({ ...e, siteId: e.clientID });
        tableKeyUpdate();
    };


    // Debounced API call
    const debouncedApiCall = debounce(
        (props: any, value: string, callback: (data: any) => void) => {
            logger.debug('RetenstionGrid', 'debouncedApiCall', { props, propsData: props.data, value, searchQuery });
            // Ensure props.searchFieldParameters is an array or default to an empty array
            const searchFieldParameters = Array.isArray(props.searchFieldParameters)
                ? props.searchFieldParameters
                : [];
            const keyValueArray = Object.entries(searchQuery).map(([key, value]) => ({ key, value }));
            getAutocompleteOptions(
                config.api[OperationalServiceTypes.FileManagementService].retentions,
                props.data.key,
                value,
                [searchFieldParameters, ...keyValueArray]
            )
                .then((data: any) => {
                    callback(data.data);
                })
                .catch(() => {
                    callback([]);
                });
        },
        config.inputDebounceInterval
    );

    const searchPromiseWrapper = (props: any, value: string, callback: any) => {
        debouncedApiCall(props, value, (data: any) => {
            callback(data)
        });
    };
    // rowSelection object indicates the need for row selection
    const rowSelection = {
        onChange: (selectedRowKeys: any, selectedRows: []) => {
            setSelectedRecords(selectedRows);
            logger.debug('RetentionGrid', 'Row Selection Changed', { 'selectedRowKeys': selectedRowKeys, 'selectedRows': selectedRows });
        },
    };

    const onRow = (e: any) => { }

    const softDelete = useCallback(() => {
        confirm({
            title: `File(s) will be deleted . Are you sure you want to proceed?`,
            icon: <ExclamationCircleOutlined />,
            okText: 'Yes',
            cancelText: 'No',
            onOk: async () => {
                const fileIds = selectedRecords.map((r: any) => r.fileId);
                await deleteRetentions({ fileIds });
                setSelectedRecords([]);
                setSearchQuery({ ...searchQuery });
            },
            onCancel() {

            },
        });
    }, [selectedRecords, searchQuery]);

    const renderHeaderActionPanel = () => {
        return (
            <Button
                hidden={selectedRecords.length < 0}
                icon={<DeleteOutlined />}
                type="primary"
                onClick={softDelete}
                disabled={selectedRecords.length === 0}
            >
                Delete
            </Button>
        );
    };

    return (
        <Collapse defaultActiveKey={['1', '2']} expandIconPosition="right">
            <Panel header="Search" key="1">
                <RetentionSearch onSubmit={onSubmitHandler} />
            </Panel>
            <Panel header="Files" key="2">
                {searchQuery !== null && (
                    <GenericDataTable
                        headerActionPanel={renderHeaderActionPanel()}
                        rowSelection={rowSelection}
                        onRow={onRow}
                        searchPromise={searchPromiseWrapper}
                        dataPromise={(state, transformFilters, queryParams) => fetchData(state, transformFilters, queryParams)}
                        columnPromise={fetchColumn([{ key: 'siteId', value: clientID }])}
                        rowKey={"fileId"}
                        scrollColumnCounter={7}
                        tableKey={tableKey}
                        sorted={SORTER}
                        isDraggable={true}
                        noRecordsAvilableMessage={"No Retention Available"}
                        hasFilterManagement={true}
                        customRender={renderSiteGridColumns()}
                        onGridStateChange={console.info}
                    />
                )}
                {!(searchQuery) &&
                    <Alert message="The data grid will appear once you search using a Client ID" type="info" showIcon />}
            </Panel>
        </Collapse>
    );
});

export default RetentionGrid;
