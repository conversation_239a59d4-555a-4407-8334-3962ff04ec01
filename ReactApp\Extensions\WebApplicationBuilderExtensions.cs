using App.Metrics.AspNetCore;

namespace Microsoft.AspNetCore.Builder;

internal static class WebApplicationBuilderExtensions
{
    internal static void ConfigureMetrics(this WebApplicationBuilder builder)
    {
        AppMetricsServiceCollectionExtensions.AddMetrics(builder.Services);

        builder.Host.UseMetricsWebTracking(opts =>
        {
            opts.IgnoredRoutesRegexPatterns.Add(@".*\/health?.*");
            opts.OAuth2TrackingEnabled = true;
        });

        builder.Host.UseMetrics(options =>
        {
            options.EndpointOptions = endpointOptions =>
            {
                endpointOptions.MetricsEndpointOutputFormatter = new App.Metrics.Formatters.Prometheus.MetricsPrometheusTextOutputFormatter();
                endpointOptions.MetricsTextEndpointOutputFormatter = new App.Metrics.Formatters.Json.MetricsJsonOutputFormatter();
                endpointOptions.EnvironmentInfoEndpointEnabled = false;
            };
        });
    }
}