import React, { Fragment, useState } from "react";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import Tooltip from "antd/lib/tooltip";
import { withRouter } from "react-router-dom";
import { EyeOutlined, EditOutlined } from "@ant-design/icons";

import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import GenericDataTable from "../../../components/GenericDataTable";
import styles from "./index.module.less";
import Modal from "../../../components/Modal";
import OrganizationManagementContainer from "../../../pages/Onboarding/OrganizationManagement/Container";
import config from "../../../utils/config";
import LicenseDetailsContainer from "../../../components/forms/LicenseManagement/LicenseDetailsContainer";
import statusColorSwitch from "@app/utils/css/statusColorSwitch";
import { TERMINATED } from "@app/constants/licenseStatuses";

import { Sorter } from "../../../components/GenericDataTable/util";

export const formatGridColumnValue = (value: any) => {
  return <p className={styles.yjGridTextFormat}>{value}</p>;
};

const SORTER: Sorter = {
  value: "companyName",
  order: "ascend",
};

const Page = (props: any) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [licenseIdState, setLicenseIdState] = useState();

  const handleCancel = (e: any) => {
    setShowEditModal(false);
  };
  const renderTag = (value: string, id: number) => {
    return (
      <div className={styles.yjGridTextCenter}>
        {" "}
        <Tag className={styles.yjColorTag} color={statusColorSwitch(id)}>
          {value}
        </Tag>
      </div>
    );
  };

  const renderGridColumns = () => {
    return {
      companyName: (value: any) => {
        return (
          <div className={styles.yjCompanyName}>
            <Tooltip placement="leftTop" title={value}>
              {value}
            </Tooltip>
          </div>
        );
      },
      vertical: (value: any) => {
        return formatGridColumnValue(value.name);
      },
      status: (value: any) => {
        return renderTag(value.name, value.value);
      },
      action: (text: any, record: any) => (
        <div className={"yjActionIconWrapper"}>
          <Tooltip title="View">
            <Button
              disabled={false}
              icon={<EyeOutlined />}
              onClick={() => {
                setLicenseIdState(record.licenseId);
                setShowEditModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              disabled={record.status.value === TERMINATED}
              icon={<EditOutlined />}
              onClick={() => {
                props.history.push(
                  `/onboarding/organization-management/edit/${record.licenseId}`
                );
              }}
            />
          </Tooltip>
        </div>
      ),
    };
  };

  return (
    <Fragment>
      <Modal
        visible={showEditModal}
        title={"Organization Management Details"}
        onCancel={handleCancel}
        footer={[
          <Button key="back" type="default" onClick={handleCancel}>
            cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              props.history.push(
                `/onboarding/organization-management/edit/${licenseIdState}`
              );
            }}
          >
            Manage
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <h6 className={styles.yjModuleSubHeading}>Organization Details</h6>

          <OrganizationManagementContainer
            type={"view"}
            licenseId={licenseIdState}
            className={styles.yjModalOrganizationContainer}
          />

          <h6 className={styles.yjModuleSubHeading}>Licence Details</h6>

          <LicenseDetailsContainer licenseId={licenseIdState} disabled />
        </div>
      </Modal>

      <PageTitle title={props.title} />
      <PageContent>
        <GenericDataTable
          endpoint={config.api.organizationAPI.organizations}
          rowKey={"licenseId"}
          tableKey={"organizations"}
          customRender={renderGridColumns()}
          sorted={SORTER}
          hasFilterManagement={false}
          fixedColumns={["licenseId"]}
          isDraggable={true}
        />
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
