import React from "react";
import { <PERSON>er, But<PERSON> } from "antd";
import TagManagementContent from "./index";
import styles from "./index.module.less";

export const TagManagementDrawer = ({
  visible,
  onClose,
  binderId,
  syncGrid,
}: {
  visible: boolean;
  onClose: () => void;
  binderId: string;
  syncGrid: () => void;
}) => (
  <Drawer
    width={700}
    visible={visible}
    title={"Manage Tags"}
    onClose={onClose}
    className={"yjDrawerPanel"}
    footer={[
      <div key={`footer-tag-managment-drawer`} style={{ textAlign: "right" }}>
        <Button
          key="back"
          data-testid="tag-managment-drawer-close"
          type="default"
          onClick={onClose}
        >
          Cancel
        </Button>
      </div>,
    ]}
  >
    <div className={styles.yjModalContentWrapper}>
      <TagManagementContent binderId={binderId} onSuccess={() => syncGrid()} />
    </div>
  </Drawer>
);
