import React, { useState } from "react";
import { But<PERSON>, Modal, Table, Tooltip } from "antd";
import { CloseOutlined, DoubleRightOutlined } from "@ant-design/icons/lib";

import styles from "./index.module.less";
import { IFile } from "@app/types/fileAreaTypes";
export interface ColumnConfig {
  title: string;
  dataIndex: string;
  key: string;
  ellipsis?: boolean;
  width?: number;
  render?: (text: any, record: any, index: number) => React.ReactNode;
}

export interface SelectedFilesGridProps {
  onFilesChange: (event: any) => void;
  columnConfigs: ColumnConfig[];
  dataList: any[];
  hideConfirmation?: boolean;
  noRecordsMessage?: string;
  scroll?: any;
}

const { confirm } = Modal;

export default ({
  columnConfigs,
  dataList,
  onFilesChange,
  hideConfirmation = false,
  noRecordsMessage,
  scroll,
}: SelectedFilesGridProps) => {
  const ROW_SPLITTER_INDEX = 2;
  const [selectedFilesState, setSelectedFilesState] = useState<IFile[]>(
    dataList
  );
  const customRenderForObjects = (key: string, record: any, text: any) => {
    switch (key) {
      case "status":
        return record.status ? (
          <Tooltip placement="leftTop" title={record.status.name}>
            {record.status.name}
          </Tooltip>
        ) : (
          <span>N/A</span>
        );
      case "assignee":
        return record.assignee ? (
          <Tooltip placement="leftTop" title={record.assignee.name}>
            {record.assignee.name}
          </Tooltip>
        ) : (
          <span>N/A</span>
        );
      default:
        return (
          <Tooltip placement="leftTop" title={text}>
            {text}
          </Tooltip>
        );
    }
  };
  const columns = columnConfigs.map((columnConfig) => {
    return {
      title: columnConfig.title,
      dataIndex: columnConfig.dataIndex,
      key: columnConfig.key,
      width: columnConfig.width,
      ellipsis: columnConfig.ellipsis,
      render: (text: any, record: any, index: number) => (
        <>
          {columnConfig.key === "remove" && (
            <Button
              type="primary"
              icon={<CloseOutlined />}
              className={"yjDeteleFile"}
              onClick={() => handleFileRemovePopup(index)}
            />
          )}
          {/* {customRenderForObjects(columnConfig.key, record, text)} */}
          {columnConfig.render ? columnConfig.render(text, record, index)  : customRenderForObjects(columnConfig.key, record, text)}
        </>
      ),
    };
  });

  const handleFilesDelete = (selectedIndex: number) => {
    const filesList = selectedFilesState.filter(
      (state, index) => index !== selectedIndex
    );
    setSelectedFilesState(filesList);
    onFilesChange(filesList);
  };

  const handleFileRemovePopup = (index: number) => {
    if (!hideConfirmation) {
      confirm({
        title: "File(s) will be removed. Do you wish to continue?",
        icon: <DoubleRightOutlined />,
        okText: "Yes",
        cancelText: "No",
        onOk() {
          handleFilesDelete(index);
        },
      });
    } else {
      handleFilesDelete(index);
    }
  };

  return (
    <>
      <Table
        locale={{
          emptyText: noRecordsMessage ? noRecordsMessage : "No files Available",
        }}
        rowKey="id"
        key="yjTableView"
        className={styles.yjSecondaryGridFilearea}
        pagination={false}
        scroll={scroll ? scroll : { y: "31vh" }}
        rowClassName={(record, index) =>
          index % ROW_SPLITTER_INDEX === 0
            ? "table-row-light"
            : "table-row-dark"
        }
        columns={columns}
        dataSource={selectedFilesState}
      />
    </>
  );
};
