import React, { Fragment, useEffect } from 'react';
import { usePara<PERSON>, with<PERSON><PERSON><PERSON> } from 'react-router-dom';
import { useDispatch } from 'react-redux';
import { PageContent } from '@app/layouts/MasterLayout';
import PageTitle from '@app/components/PageTitle';
import { setDynamicBreadcrums } from '@app/redux/actions/configurationActions';
import GenericDataTable from '@app/components/GenericDataTable';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import config from '@app/utils/config';
import styles from './index.module.less';
import { FormattedDateTime } from '@app/components/FormattedDateTime';

const Page = () => {
  const { channelId, siteName , fileId, siteId, binderName, binderId} = useParams<any>();
  const dispatch = useDispatch();

  useEffect(() => {
      const dynamicBreadcrumbs = [
          { title: "Client File Areas", path: "/client-file-area" },
          { title: `${decodeURIComponent(siteName) } - File Areas`, path: `/client-file-area/${siteId}/${siteName}/${channelId}` },
          { title: `${decodeURIComponent(binderName)}`, path: `/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}`},
          { title: `History of ${fileId}`, path: `/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}` }
       ];
        dispatch(setDynamicBreadcrums(dynamicBreadcrumbs));
        return () => {
          dispatch(setDynamicBreadcrums([]));
        };
  }, []);

  const renderGridColumns = () => {
    return {
      fileId: (value: string) => {
        return (
          <div className="yjFileAreaGridTextWrap yJFileAreaRow">
            <span className="yJFileAreaRow">{value}</span>
          </div>
        );
      },
      title: (value: any) => {
        return (
          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>
            <span className="yJFileAreaRow">{value}</span>
          </div>
        );
      },
      dateTime: (value: string) => {
        return <FormattedDateTime value={value} showTime />
      },
      event: (value: any) => {
        return (
          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>
            <span className="yJFileAreaRow">{value}</span>
          </div>
        );
      },
      user: (value: any) => {
        return (
          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>
            <span className="yJFileAreaRow">{value}</span>
          </div>
        );
      }
    };
  };

  return (
    <Fragment>
      <PageTitle title={`${decodeURIComponent(binderName)} - History`} />
      <PageContent>
        <GenericDataTable
          hideHeaderPanel={true}
          lowResolutionWidth="auto"
          highResolutionWidth="auto"
          scrollColumnCounter={6}
          endpoint={config.api[OperationalServiceTypes.FileManagementService].getFileHistory}
          tableKey={'fileHistory'}
          searchQueryParameters={[{ key: 'fileId', value: fileId }]}
          customRender={renderGridColumns()}
        />
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
