import React, { useState, useEffect } from "react";
import { Input } from "antd";
import debounce from "lodash/debounce";

import Dropdown from "./Dropdown";
import { getAutocompleteOptions } from "@app/api/genericDataTable";
import config from "@app/utils/config";
import styles from "./index.module.less";

const { Search } = Input;

export default (props: any) => {
  const [searchData, setSearchData] = useState([]);
  const [loadingData, setLoadingData] = useState(false);
  const [inputText, setInputText] = useState("");
  const [noResults, setNoResults] = useState(false);
  const MINIMUM_NUMBER_OF_CHARCTERS = 2;

  useEffect(() => {
    let mounted = true;
    setNoResults(false);
    const callbackFn = (data: any) => {
      if (mounted) {
        setLoadingData(false);
        setSearchData(data);
        if (!data?.length) {
          setNoResults(true);
        }
      }
    };
    if (inputText?.length > MINIMUM_NUMBER_OF_CHARCTERS) {
      setLoadingData(true);
      props.searchPromise(props, inputText, callbackFn);
    } else {
      setSearchData([]);
      setLoadingData(false);
    }

    return () => {
      mounted = false;
    };
  }, [inputText]);

  const populateData = (value: string) => {
    setInputText(value);
  };

  const onClickItem = (i: string) => {
    setInputText(i);
    props.onClick(i);
  };

  return (
    <div>
      <Search
        autoFocus
        placeholder="Search Text"
        value={inputText}
        onSearch={(value) => {}}
        style={{ width: 200 }}
        onChange={(e) => populateData(e.target.value)}
      />
      <Dropdown data={searchData} loading={loadingData} onClick={onClickItem} />
      {noResults && (
        <div className={styles.noResults}> {"No Results Found"} </div>
      )}
    </div>
  );
};
