import { Switch } from "antd";
import React from "react";
import styles from "./index.module.less";

interface ShowAllProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
}

export default ({
                  checked,
                  onChange,
                }: ShowAllProps) => (
  <div className={styles.switchContainer}>
    <span className={styles.switchLabel}>
      Show All
    </span>
    <Switch
      checked={checked}
      onChange={onChange}
      className={styles.switch} />
  </div>);
