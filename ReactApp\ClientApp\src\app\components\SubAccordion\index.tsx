import React from "react";
import { Collapse } from "antd";

import styles from './index.module.less';

const { Panel } = Collapse;

const text =
  "A sample component implemented by UI team as a reference";

export default () => {
  return (
    <div className={styles.yjSubAccordion}>
      <Collapse>
          <Panel header="This is panel header 1" key="1">
            <Collapse defaultActiveKey="1" className='SubAccordian'>
              <Panel header="This is panel nest panel" key="1">
                <p>{text}</p>
              </Panel>
            <Panel header="This is panel nest panel 2" key="2">
                <p>{text}</p>
              </Panel>
            <Panel header="This is panel nest panel 3" key="3">
                <p>{text}</p>
              </Panel>
            </Collapse>
          </Panel>
          <Panel header="This is panel header 2" key="2">
            <p>{text}</p>
          </Panel>
          <Panel header="This is panel header 3" key="3">
            <p>{text}</p>
          </Panel>
        </Collapse>
    </div>
  );
};

