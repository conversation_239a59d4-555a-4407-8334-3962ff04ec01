import { getParameterizedUrlWith, getParameterizedUrl } from "@app/utils";
import config from "@app/utils/config";
import http, { httpVerbs } from "@app/utils/http";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export const getSiteStatus = (): Promise<HTTPResponse<any>> => {
  return Promise.resolve({
    status: 200,
    data: [
      { value: 1, name: "Active" },
      { value: 2, name: "Disabled" },
    ],
  });
};

export const loadSiteDropdownData = (): Promise<any> => {
  return Promise.all([getSiteStatus()]);
};

export const createSite = (contactData: any) => {
  return http({
    method: httpVerbs.POST,
    url: getParameterizedUrlWith(
      config.api[OperationalServiceTypes.MasterDataService].createSite,
      [{ name: "channelId", value: contactData.channelId }]
    ),
    data: contactData,
  });
};

export const updateSite = (siteData: any) => {
  return http({
    method: httpVerbs.PATCH,
    url: getParameterizedUrlWith(
      config.api[OperationalServiceTypes.MasterDataService].updateSite,
      [
        { name: "channelId", value: siteData.channelId },
        { name: "siteId", value: siteData.siteId },
      ]
    ),
    data: siteData,
  });
};

export const getSite = (siteId: any) => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.MasterDataService].siteById,
      siteId
    ),
  });
};

export const checkCrmSiteIdAvailability = (name: string) => {
  return http({
    method: httpVerbs.HEAD,
    url:
      config.api[OperationalServiceTypes.MasterDataService].crmSiteIdAvailable,
    params: {
      name,
    },
  });
};

export const checkSiteNameAvailability = (channelId: string, name: string) => {
  return http({
    method: httpVerbs.HEAD,
    url:
      config.api[OperationalServiceTypes.MasterDataService].siteNameAvaialble,
    params: {
      channelId,
      name,
    },
  });
};

/**
 * Create a Folder Structure for given site id
 * @param siteId
 */
export const createFolderForGivenSite = (siteId: string, TemplateId: string) => {
  return http({
    method: httpVerbs.POST,
    url: getParameterizedUrlWith(config.api[OperationalServiceTypes.MasterDataService].foldersBySiteId, [{
      name: "siteId",
      value: siteId
    }]),
    data: {TemplateId}
  });
};