import { shallow } from "enzyme";
import React from "react";
import renderer from "react-test-renderer";

import OrganizationManagement from "..";
import initTestSuite from "@app/utils/config/TestSuite";

describe("<OrganizationManagement/>", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should create and match to snapshot", () => {
    const aoComponent = renderer.create(<OrganizationManagement />).toJSON();
    expect(aoComponent).toMatchSnapshot();
  });

  it("should render OrganizationManagementContainer component", () => {
    const component = shallow(
      <OrganizationManagement formRef={null} isViewOnly={false} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should render OrganizationManagementContainer component if prop formRef is null ", () => {
    const component = shallow(<OrganizationManagement formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render OrganizationManagementContainer component if prop  ViewOnly is true ", () => {
    const component = shallow(<OrganizationManagement isViewOnly={true} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render OrganizationManagementContainer component if prop  ViewOnly is false ", () => {
    const component = shallow(<OrganizationManagement isViewOnly={false} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render OrganizationManagementContainer component if props not available", () => {
    const component = shallow(<OrganizationManagement />);
    expect(component.html()).not.toBe(null);
  });
});
