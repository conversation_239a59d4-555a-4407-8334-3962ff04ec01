import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, Button, Skeleton } from 'antd';
import PageTitle from '@app/components/PageTitle';
import { LinkOutlined, LockOutlined, PlusOutlined } from '@ant-design/icons/lib';
import PageContent from '@app/components/PageContent';
import { useForm } from 'antd/lib/form/Form';
import logger from '@app/utils/logger';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import AutoFilingGrid from "@app/features/AutoFiling/AutoFilingGrid";
import AutoFilingAddEditDrawer from "@app/features/AutoFiling/AutoFilingAddEditDrawer";
import { useHistory, withRouter } from "react-router";
import { createAutoFilingRule, updateAutoFilingRule } from "@app/api/autoFilingService";
import { AutoFilingRuleSetupData } from '@app/types/AutoFiling';

const initialFolderData = [
  {
    id: 1,
    name: 'Primary folder',
    retention: false,
    presist: false,
    subFolders: [
      {
        id: 2,
        name: 'Secondary folder (1)',
        subFolders: [],
        retention: false,
        presist: false,
      },
      {
        id: 3,
        name: 'Secondary folder (2)',
        subFolders: [],
        retention: false,
        presist: false,
      },
    ],
  },
];

const AutoFilingPage = (props: any) => {
  let [showDrawer, setShowDrawer] = useState(false);
  let [form] = useForm();
  const { userPermission } = useSelector((state: RootState) => state.userManagement);
  const ref = useRef<{ refresh: () => void }>(null);
  const history = useHistory();


  const refresh = () => {
    if (ref.current) {
      ref.current.refresh();
    }
  };
  const openRuleSetupDrawer = (data?: AutoFilingRuleSetupData) => {
    logger.debug('AutoFilling', 'openRuleSetupDrawer', data)
    form?.setFieldsValue({
      id: data?.id,
      ruleName: data?.ruleName,
      sourceFolderName: data?.sourceFolderName,
      destinationBinderTemplate: data?.binderTemplate?.value || [],
      destinationBinderTemplateNode: data?.binderTemplateNodes,
      year: data?.year,
      sampleFileName: data?.sampleFileName,
      fileNameParserJson: Array.isArray(data?.fileNameParserJson)
        ? data?.fileNameParserJson
        : JSON.parse(data?.fileNameParserJson || "[]"),
      enableNewFileNameGeneration: data?.enableNewFileNameGeneration,
      newFileNameGeneratorJson: data?.newFileNameGeneratorJson ? JSON.parse(data?.newFileNameGeneratorJson) : data?.newFileNameGeneratorJson,
      fileNameGeneratorJson: data?.fileNameGeneratorJson,
    })

    setShowDrawer(true);
  };

  const closeDrawer = () => {
    form.resetFields();
    setShowDrawer(false);
  };

  const formatFolderData = (data: any[]): any[] => {
    return data.map((e) => (e.children ? { id: e.id, name: e.folderName, retention: e.retention, subFolders: formatFolderData(e.children) } : { id: e.id, name: e.folderName, retention: e.retention }));
  };

  const onSuccess = (values: any) => {
    values.destinationBinderTemplateNode = values.destinationBinderTemplateNode[1];
    values.fileNameParserJson = JSON.stringify(values.fileNameParserJson);
    values.newFileNameGeneratorJson = JSON.stringify(values.newFileNameGeneratorJson);

    logger.error('AutoFiling', 'onSuccess: values', values);

    if (values.id) {
      updateAutoFilingRule(values)
        .then(() => {
          successNotification([''], 'AutoFiling Rule updated successfully');
          refresh();
          closeDrawer();
        })
        .catch((e) => {

          errorNotification([e?.message || ''], 'Failed to update auto-filling rules');
          logger.error('Master Data Module', 'Update  AutoFiling Rule ', e);
        });
    } else {
      createAutoFilingRule(values)
        .then(() => {
          successNotification([''], 'AutoFiling Rule created successfully');
          refresh();
          closeDrawer();
        })
        .catch((e) => {

          errorNotification([e?.message || ''], 'Failed to create auto-filling rules');
          logger.error('Master Data Module', 'Create Site', e);
        });
    }

  };

  return (
    <>
      <PageTitle title={props.title}>
        {userPermission.privDMSCanManageAutoFiling &&
          <Button data-testid="create-template-button" onClick={() => history.push("/document-maintenance/auto-filing-setting/logs")} type="primary" icon={<LinkOutlined />}>
            Logs
          </Button>
        }
        {userPermission.privDMSCanManageAutoFiling &&
          <Button data-testid="create-template-button" onClick={() => openRuleSetupDrawer()} type="primary" icon={<PlusOutlined />}>
            Rule Setup
          </Button>
        }
      </PageTitle>
      <PageContent>
        {userPermission.privDMSCanManageAutoFiling && <AutoFilingAddEditDrawer
          data-testid="template-grid"
          initialData={form.getFieldsValue()}
          formRef={form}
          onSuccess={onSuccess}
          onDrawerClose={closeDrawer}
          showDrawer={showDrawer}
        />}
        {userPermission.privDMSCanManageAutoFiling && <AutoFilingGrid ref={ref} onEdit={(data: any) => openRuleSetupDrawer(data)} />}
        {userPermission.privDMSCanManageAutoFiling === false && (
          <Alert
            message="You do not have permission to access autofiling. Contact your organization's administrator for assistance"
            icon={<LockOutlined />}
            type="error"
            showIcon
          />
        )}
        {userPermission.privDMSCanManageAutoFiling === undefined && (
          <Skeleton />
        )}
      </PageContent>
    </>
  );
};

export default withRouter(AutoFilingPage);
;
