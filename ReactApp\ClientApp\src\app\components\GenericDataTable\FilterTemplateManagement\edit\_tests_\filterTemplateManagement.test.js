import React from "react";
import {mount} from "enzyme";
import renderer from "react-test-renderer";
import {Provider} from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import {Collapse} from "antd";

import initTestSuite from "@app/utils/config/TestSuite";
import FilterTemplateManagementEdit from "../";
import ModalCustom from "@app/components/Modal";

const ReduxProvider = ({children, store}) => (
    <Provider store={store}>{children}</Provider>
);
const middleware = [thunk];
const mockStore = configureMockStore(middleware);


const CustomFilterTemplateManagementEdit = (props) => {

    const INITIAL_STATE = {
        fileArea: {
            fileAreaSettings: {
                internalFileStatusUpdate: true
            }
        },
        grid: {
            savedFilters: [],
            columns: []
        }
    }
    const store = mockStore(INITIAL_STATE);

    return (
        <ReduxProvider store={store}>
            <FilterTemplateManagementEdit {...props}/>
        </ReduxProvider>
    );
};

describe("Filter template edit Test Suite", () => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const customFilterEditComponent = mount(<CustomFilterTemplateManagementEdit/>);
        expect(customFilterEditComponent.html()).not.toBe(null);
    });

    it("should create and match to snapshot", () => {
        const customFilterEditComponent = renderer.create(<CustomFilterTemplateManagementEdit/>).toJSON();
        expect(customFilterEditComponent).toMatchSnapshot();
    });

    it("should render with props", () => {
        const customFilterEditComponent = mount(<CustomFilterTemplateManagementEdit siteId={'xxx'}/>);
        expect(customFilterEditComponent.html()).not.toBe(null);
    });

    it("should render with props are null", () => {
        const customFilterEditComponent = mount(<CustomFilterTemplateManagementEdit siteId={null}/>);
        expect(customFilterEditComponent.html()).not.toBe(null);
    });

    it("should render with props are undefined", () => {
        const customFilterEditComponent = mount(<CustomFilterTemplateManagementEdit siteId={undefined}/>);
        expect(customFilterEditComponent.html()).not.toBe(null);
    });

    it("should have a modal custom", () => {
        const customFilterEditComponent = mount(<CustomFilterTemplateManagementEdit siteId={'xxx'}/>);
        expect(customFilterEditComponent.find(ModalCustom)).toHaveLength(1);
    });

    it("should have a collapse", () => {
        const customFilterEditComponent = mount(<CustomFilterTemplateManagementEdit siteId={'xxx'}/>);
        expect(customFilterEditComponent.find(Collapse)).toHaveLength(1);
    });

});
