import React, { useEffect, useState } from "react";
import { ColumnsType } from "antd/lib/table";
import { Button, Skeleton } from "antd";
import { ArrowRightOutlined } from "@ant-design/icons";
import { useDispatch } from "react-redux";

import styles from "./index.module.less";
import DashboardGenericTable from "../DashboardGenericTable";
import config from "@app/utils/config";
import { GenericFilter } from "@app/components/GenericDataTable/types";
import { updatePreAppliedFilters } from "@app/redux/actions/gridsActions";
import PendingRequestsCard from "../PendingRequests/PendingRequestsCard";
import {
  getPendingFilesCardDetails,
  checkSiteAvailable,
} from "@app/api/fileAreaService";
import NonAuthorized from "@app/components/NonAuthorized";
import { encrypt } from "@app/utils/crypto/cryptoText";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export default (props: any) => {
  const dispatch = useDispatch();
  const [loading, setLoading] = useState<boolean>(false);
  const [hasSite, setHasSite] = useState<boolean>(true);
  const [response, setResponse] = useState<any>();

  useEffect(() => {
    setLoading(true);
    checkSiteAvailable(props.channelId)
      .then(() => {
        setHasSite(true);
        getPendingFilesCardDetails(props.channelId)
          .then((responseValue) => {
            setLoading(false);
            setResponse(responseValue.data);
          })
          .catch(() => {
            setLoading(false);
            setResponse(undefined);
          });
      })
      .catch(() => {
        setHasSite(false);
        setLoading(false);
        setResponse(undefined);
      });
  }, [props.channelId]);

  const onClickNavigate = (record: any) => {
    const preAppliedFilters: GenericFilter[] = [
      {
        displayText: record.requestId,
        filterName: "Request ID",
        filterType: "search",
        isArray: false,
        key: "requestId",
        name: record.requestId,
        value: record.requestId,
      },
      {
        displayText: record.uploadedBy,
        filterName: "Uploader",
        filterType: "multiSelect",
        isArray: true,
        key: "uploadedBy",
        name: record.uploadedBy,
        value: record.uploadedBy,
      },
    ];
    dispatch(updatePreAppliedFilters(preAppliedFilters));
    const encryptedName = encrypt(record.siteName);
    props.history.push(
      `/client-file-area/${props.channelId}/${record.siteId}/${encryptedName}/portal/?mode=uploads`
    );
  };

  const columns: ColumnsType = [
    {
      dataIndex: "name",
      className: "yjDashboradTextWrap",
      title: "Request Name",
      width: "30%",
      render: (value: any) => {
        return `${value}`;
      },
    },

    {
      dataIndex: "lastUpdateTimeline",
      title: "Last Uploaded",
      width: "30%",
      render: (value: any) => {
        return `${value}`;
      },
    },
    {
      dataIndex: "uploadedBy",
      className: "yjDashboradTextWrap",
      title: "Uploaded By",
      width: "30%",
      render: (value: any) => {
        return `${value}`;
      },
    },
    {
      dataIndex: "fileCount",
      title: "Files",
      width: "15%",
      render: (value: any) => {
        return `${value} Files`;
      },
    },

    {
      title: "",
      dataIndex: "",
      key: "remove",
      render: (value, record: any) => (
        <Button
          className={"yjNoWrapperButton"}
          onClick={() => onClickNavigate(record)}
          icon={<ArrowRightOutlined />}
        ></Button>
      ),
      width: "20%",
    },
  ];
  return (
    <>
      {loading ? (
        <Skeleton />
      ) : response && response?.filesCount > 0 ? (
        <>
          {" "}
          <PendingRequestsCard response={response} />
          <DashboardGenericTable
            endpoint={
              config.api[OperationalServiceTypes.PortalService].getPendingFiles
            }
            columns={columns}
            {...props}
            options={{ queryParameters: { channelId: props.channelId } }}
            onErrorLoading={(error) => {
              if (error.statusCode === FORBIDDEN_ERROR_CODE) {
                props.history.push("/forbidden");
              }
            }}
          />
        </>
      ) : (
        <NonAuthorized
          styleClassName={styles.yjNonAuthorizedDashboardWrapper}
          title={!hasSite ? "No Sites Available" : "All Good Here!"}
          subTitle={
            !hasSite
              ? "Create a Site or Request your Organisation’s Administrator to Create a Site for this channel"
              : "No files are pending for approval"
          }
        />
      )}
    </>
  );
};
