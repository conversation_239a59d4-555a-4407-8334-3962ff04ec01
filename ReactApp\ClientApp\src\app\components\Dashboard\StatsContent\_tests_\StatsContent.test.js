import React from "react";
import renderer from "react-test-renderer";
import {Col, Row, Card} from "antd";
import {shallow} from "enzyme";

import initTestSuite from "@app/utils/config/TestSuite";
import StatsContent from "../index";

const { Meta } = Card;
describe("Stats Content Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const scComponent = shallow(<StatsContent/>);
        expect(scComponent.html()).not.toBe(null);
    });

    it("should create and match to snapshot", () => {
        const scComponent = renderer.create(<StatsContent/>).toJSON();
        expect(scComponent).toMatchSnapshot();
    });

    it("should have a Row element",() => {
        const scComponent = shallow(<StatsContent />);
        expect(scComponent.find(Row)).toHaveLength(1);
    });

    it("should have 8 Col elements",() => {
        const scComponent = shallow(<StatsContent />);
        expect(scComponent.find(Col)).toHaveLength(8);
    });

    it("should have 8 Card elements",() => {
        const scComponent = shallow(<StatsContent />);
        expect(scComponent.find(Card)).toHaveLength(8);
    });

    it("should have 8 Meta elements",() => {
        const scComponent = shallow(<StatsContent />);
        expect(scComponent.find(Meta)).toHaveLength(8);
    });
});
