import React from "react";
import {
  Form,
  DatePicker,
  Row,
  Col,
  Button,
  Modal as Ant<PERSON>oda<PERSON>,
  Tooltip,
} from "antd";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import Checkbox from "antd/lib/checkbox/Checkbox";
import moment from "moment";

const { confirm } = AntModal;

export interface IPublishFilesOptions {
  fileList: IPublishFiles[];
  onFileListChange: (fileList: IPublishFiles[]) => void;
  closeModal: () => void;
  expirationDate: moment.Moment | undefined;
  setExpirationDate: (date: moment.Moment | undefined) => void;
}

export interface IPublishFiles {
  id: string;
  checked: boolean;
  title: string | null;
}

export default ({
  fileList,
  onFileListChange,
  closeModal,
  expirationDate,
  setExpirationDate,
}: IPublishFilesOptions) => {
  return (
    <>
      <div className={styles.yjModalContentWrapper}>
        <div className={styles.yjPublishFilesWrapper}>
          <div className={styles.yjPublishFilesUpperSection}>
            <Row gutter={24}>
              <Col span={24}>
                <div className={styles.yjPublishFilesFormHeader}>
                  {fileList.length !== 0 && !fileList.some((file: any) => file.checked) && (
                    <div className={styles.yjPublishFilesNotSelectedError}>
                      Select at least one file to set properties.
                    </div>
                  )}
                  <Row gutter={24}>
                    <Col span={1} className={styles.yjPublishFilesColumn}>
                      <Checkbox
                        checked={fileList.every((file: any) => file.checked)}
                        onChange={(e) => {
                          const newFiles = fileList.map((file: any) => {
                            file.checked = e.target.checked;
                            return file;
                          });
                          onFileListChange(newFiles);
                        }}
                        indeterminate={
                          fileList.every((file: any) => file.checked)
                            ? false
                            : fileList.some((file: any) => file.checked)
                        }
                      />
                    </Col>
                    <Col span={1} className={styles.yjPublishFilesColumn}></Col>
                    <Col span={4} className={styles.yjPublishFilesColumn}>
                      <Form.Item
                        children={null}
                        label="ID"
                        colon={false}
                      ></Form.Item>
                    </Col>
                    <Col span={18} className={styles.yjPublishFilesColumn}>
                      <Form.Item
                        children={null}
                        label="File Title"
                        colon={false}
                      ></Form.Item>
                    </Col>
                  </Row>
                </div>
                <div className={styles.yjPublishFilesFormGrid}>
                  {fileList.map((file: any) => (
                    <Row
                      key={file.id}
                      gutter={24}
                      className={styles.yjPublishFilesFormRow}
                    >
                      <Col span={1}>
                        <Checkbox
                          checked={file.checked}
                          onChange={(e) => {
                            file.checked = e.target.checked;
                            onFileListChange([...fileList]);
                          }}
                        />
                      </Col>
                      <Col span={1}>
                        <Tooltip title="Remove File">
                          <Button
                            type="primary"
                            icon={<CloseOutlined />}
                            className={styles.yjDeteleFile}
                            onClick={() => {
                              confirm({
                                title: `File will be removed from publishing. Do you wish to continue?`,
                                icon: <ExclamationCircleOutlined />,
                                okText: "Yes",
                                cancelText: "No",
                                onOk() {
                                  const newFiles = fileList.filter(
                                    (e: any) => e.id !== file.id
                                  );
                                  onFileListChange(newFiles);
                                  if (newFiles.length === 0) closeModal();
                                },
                              });
                            }}
                          />
                        </Tooltip>
                      </Col>
                      <Col span={4}>
                        <div className={styles.yjCheckoutId}>{file.id}</div>
                      </Col>
                      <Col span={18}>
                        <div className={styles.yjCheckoutFileName}>{file.title}</div>
                      </Col>
                    </Row>
                  ))}
                </div>
              </Col>
            </Row>
            <div className={styles.yjPublishFilesExpiration}>
              <h6 className={styles.yjPublishFilesExpirationTitle}>
                Expiration
              </h6>
              <DatePicker
                className={styles.yjPublishFilesExpirationDatePicker}
                use12Hours
                showTime={{ defaultValue: moment("23:59:59", "HH:mm:ss") }}
                disabledTime={(date) => {
                  function range(start: any, end: any) {
                    const result = [];
                    for (let i = start; i < end; i++) {
                      result.push(i);
                    }
                    return result;
                  }
                  if (date?.isSame(moment(), "day"))
                    return {
                      disabledHours: () => range(0, moment().hour() + 1),
                    };
                  return {};
                }}
                format={`${moment.localeData().longDateFormat('L')} h:mm a`}
                disabledDate={(current) =>
                  current && current < moment().startOf("day")
                }
                showNow={false}
                placeholder=""
                value={expirationDate}
                onChange={(date) => {
                  setExpirationDate(date ?? undefined);
                }}
                disabled={!fileList.some((file: any) => file.checked)}
              />
            </div>
          </div>
        </div>
      </div>
    </>
  );
};
