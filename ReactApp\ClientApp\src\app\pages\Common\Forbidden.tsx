import React from "react";

import MasterLayout from "@app/layouts/MasterLayout";
import Routes from "@app/routes";
import menuConfig from "@app/menus/menuConfig";
import AppError from "@app/components/AppError";

const Forbidden = (props: any) => {
  return (
    <MasterLayout {...props} routes={Routes} menus={menuConfig}>
      <AppError
        message={'The page you requested cannot be displayed right now. You may not have the permission to view this page.'}
        showContactAdmin={ true}
        showLogoutButton={false}
      />
    </MasterLayout>
  );
};

export default Forbidden;
