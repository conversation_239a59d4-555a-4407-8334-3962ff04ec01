import React from "react";
import { mount, shallow } from "enzyme";
import Channel from "../index";
import initTestSuite from "@app/utils/config/TestSuite";
import FolderTree from "../../../components/FolderTreeEditor";
import { Form, Input, Select } from "antd";

describe("Channel test suite", () => {
  beforeAll(() => {
    initTestSuite();
  });
  it("should render <Channel />", () => {
    const channelComponent = mount(<Channel />);
    expect(channelComponent.html()).not.toBe(null);
  });

  it("should create <Channel /> and match to snapshot", () => {
    const channelComponent = mount(<Channel />);
    expect(channelComponent).toMatchSnapshot();
  });

  it("should render <Channel /> when form ref is null", () => {
    const channelComponent = mount(<Channel formRef={null} />);
    expect(channelComponent.html()).not.toBe(null);
  });
  it("should render <Channel /> when prop action is edit", () => {
    const channelComponent = mount(<Channel action="edit" />);
    expect(channelComponent.html()).not.toBe(null);
  });
  it("should render <Channel /> when prop action is save", () => {
    const channelComponent = mount(<Channel action="save" />);
    expect(channelComponent.html()).not.toBe(null);
  });
  it("should render <Channel /> when prop action is view", () => {
    const channelComponent = mount(<Channel action="view" />);
    expect(channelComponent.html()).not.toBe(null);
  });
  it("should render <Channel /> when channelDetails is null", () => {
    const channelComponent = mount(<Channel channelDetails={null} />);
    expect(channelComponent.html()).not.toBe(null);
  });
  it("should have a Folder tree Component", () => {
    const component = shallow(<Channel action="save" />);
    expect(component.find(FolderTree)).toHaveLength(1);
  });

  it("should have a form", () => {
    const component = shallow(<Channel />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 3 form input fields", () => {
    const component = shallow(<Channel action="save" />);
    expect(component.find(Form.Item)).toHaveLength(3);
  });

  it('should have label: "Channel name"', () => {
    const component = shallow(<Channel action="save" />);
    expect(component.find(Form.Item).at(0).props().label).toEqual(
      "Channel name"
    );
  });

  it('should have label: "Status', () => {
    const component = shallow(<Channel action="save" />);
    expect(component.find(Form.Item).at(1).props().label).toEqual("Status");
  });

  it('should have label: "description"', () => {
    const component = shallow(<Channel action="save" />);
    expect(component.find(Form.Item).at(2).props().label).toEqual(
      "description"
    );
  });
  it("should have 1 Input Fields", () => {
    const component = mount(<Channel action="save" />);
    expect(component.find(Input).length).toEqual(1);
  });

  it("should have 1 Select", () => {
    const component = mount(<Channel action="save" />);
    expect(component.find(Select).length).toEqual(1);
  });
  it("should have 2 H2 tags", () => {
    const component = mount(<Channel action="save" />);
    expect(component.find("h2").length).toEqual(2);
  });
  it("first H2 tag should be Basic Information", () => {
    const component = mount(<Channel />);
    expect(component.find("h2").at(0).text()).toEqual("Basic Information");
  });
  it("second H2 tag should be Folder Structure", () => {
    const component = mount(<Channel action="save" />);
    expect(component.find("h2").at(1).text()).toEqual("Folder Structure");
  });
});
