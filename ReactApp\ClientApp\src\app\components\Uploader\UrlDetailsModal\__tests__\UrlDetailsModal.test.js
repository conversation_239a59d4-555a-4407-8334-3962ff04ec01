import { shallow } from "enzyme";
import React from "react";
import UrlDetailsModal from "..";
import Modal from "@app/components/Modal";
import UploaderSubmitContainer from "@app/components/forms/UploaderSubmit/UploaderSubmitContainer";

describe("<UrlDetailsModal />", () => {
  it("should render component", () => {
    const component = shallow(<UrlDetailsModal />);
    expect(component.html()).not.toBe(null);
  });

  it("should render with props", () => {
    const component = shallow(
      <UrlDetailsModal
        uploadType={2}
        siteId={"S-001"}
        onClose={() => null}
        onFinish={() => null}
      />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should have title: URL File Uploader", () => {
    const component = shallow(<UrlDetailsModal />);
    expect(component.find(Modal).prop("title")).toEqual("URL File Uploader");
  });

  it("should have OK text: DONE", () => {
    const component = shallow(<UrlDetailsModal />);
    expect(component.find(Modal).prop("okText")).toEqual("DONE");
  });

  it("should render UploaderSubmitContainer", () => {
    const component = shallow(<UrlDetailsModal />);
    expect(component.find(Modal).find(UploaderSubmitContainer)).not.toBe(null);
  });
});
