import { shallow, mount } from "enzyme";
import React from "react";
import renderer from "react-test-renderer";
import { Form, Input } from "antd";
import PhoneInput from "react-phone-input-2";

import Contact from "../index";
import initTestSuite from "@app/utils/config/TestSuite";
import InfinitySelect from "@app/components/InfinitySelect";

describe("<Contact/> test suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("Should render the <Contact/>", () => {
    const component = shallow(<Contact formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <Contact/> and create the snapshot properly", () => {
    const component = renderer.create(<Contact />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("should have a form", () => {
    const component = shallow(<Contact />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 5 form input fields", () => {
    const component = shallow(<Contact />);
    expect(component.find(Form.Item)).toHaveLength(5);
  });
  it('should have label: "First Name"', () => {
    const component = shallow(<Contact />);
    expect(component.find(Form.Item).at(0).props().label).toEqual("First Name");
  });
  it('should have label: "Last Name"', () => {
    const component = shallow(<Contact />);
    expect(component.find(Form.Item).at(1).props().label).toEqual("Last Name");
  });
  it('should have label: "Email Address"', () => {
    const component = shallow(<Contact />);
    expect(component.find(Form.Item).at(2).props().label).toEqual(
      "Email Address"
    );
  });
  it('should have label: "Contact Number"', () => {
    const component = shallow(<Contact />);
    expect(component.find(Form.Item).at(3).props().label).toEqual(
      "Contact Number"
    );
  });
  it("should have 3 Input Fields", () => {
    const component = mount(<Contact />);
    expect(component.find(Input).length).toEqual(3);
  });

  it("should have 1 telephone input Field", () => {
    const component = mount(<Contact />);
    expect(component.find(PhoneInput).length).toEqual(1);
  });
});
