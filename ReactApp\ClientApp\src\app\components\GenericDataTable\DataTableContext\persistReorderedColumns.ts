const LOCAL_KEY = "table_reordered_data";

const getTableData = () => {
  const data = localStorage.getItem(LOCAL_KEY);
  return data ? JSON.parse(data) : null;
};

const getTableColumns = (tableKey?: string): any[] | null => {
  if (!tableKey) {
    return null;
  }
  return getTableData()?.[tableKey];
};

export const setReOrderedColumns = (tableKey: string, columns: any) => {
  if (!tableKey) {
    return;
  }
  let data = getTableData();
  data = data ? data : {};
  data = {
    ...data,
    [tableKey]: columns,
  };
  localStorage.setItem(LOCAL_KEY, JSON.stringify(data));
};

export const findReOrderedColumnByKey = (tableKey: string) => {
  const data = getTableColumns(tableKey);
  if (!data) {
    return [];
  }
  return data;
};
