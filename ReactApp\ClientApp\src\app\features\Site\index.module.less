@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjCreateSiteWrapper {
  background-color: @color-bg-content-wrapper;
  padding: 1.5em;

  button {
    margin-top: 30px;
  }
}

.yjModalContentWrapper {
  margin: -24px;
  max-height: 65vh;
  overflow: hidden auto;
  padding: 1.5em;
}

.yjBadge {
  background: @color-primary;
  border-radius: 10px;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  height: 30px;
  line-height: 20px;
  margin-left: 5px;
  min-width: 30px;
  padding: 6px;
  text-align: center;
  white-space: nowrap;
  z-index: auto;
}

.yjManageSitesInfinityListComponent {
  display: flex;
  width: 100%;

  .yjManageSitesListItemValue {
    width: 92%;

    p {
      margin-bottom: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;

      svg {
        color: @color-secondary;
        margin-right: 5px;
      }
    }
  }

  .yjManageSitesListItemAction {

    button {
      background: @color-danger;
      border: none;
      box-shadow: none;
      color: @color-font-white;
    }

    .flex-mixin(center, flex, flex-end);
  }
}

// ant list styles

.yjManageSitesContactList {
  border-bottom: 1px solid #A3C2D9;
  display: flex;
  padding: 8px 0 8px 0;
  width: 100%;

  .yjManageSitesContactListItems {
    width: 100%;

    p {
      margin-bottom: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;

      svg {
        color: @color-secondary;
        margin-right: 5px;
      }
    }

    .yjManageSitesPreviouslySelectedUsersListName {
      float: left;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: auto;
      margin-right: 10px;
      margin-left: -10px;
      max-width: 80%;
    }
  }

  .yjManageSitesPreviouslySelectedUsersListRight {

    button {
      background: @color-danger;
      border: none;
      box-shadow: none;
      color: @color-font-white;
    }

    .flex-mixin(center, flex, flex-end);
  }
}

.YJPortalUserTag {
  background-color: #24303B;
  border-radius: 5px;
  color: white;
  font-size: small;
  padding: 0;
  text-align: center;

}

.YjManageSitesNewlyCreatedContactsStatus {
  width: 10px;
  height: 10px;
  border-radius: 30px;
  background-color: #78bf59;
  margin-top: 7px;
  margin-right: 10px;
  color: white;
}

.YjManageSitesUpdatedContactsStatus {
  width: 10px;
  height: 10px;
  border-radius: 30px;
  background-color: orange;
  margin-top: 7px;
  margin-right: 10px;
  color: white;
}

.yjManageSitesContactNameStatus {
  float: left;
}

.YJPortalUserTagSpace {
  width: 80px;
  float: left;
}

.yjManageSitesAccordianHeader {
  font-size: 15.2px;
}
