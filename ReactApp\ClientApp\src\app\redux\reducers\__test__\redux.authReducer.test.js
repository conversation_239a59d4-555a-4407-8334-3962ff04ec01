import {authReducer, initialState} from '../authReducer';
import {LOGIN_FAIL, LOGIN_SUCCESS, LOGOUT_SUCCESS,} from "../../actionTypes/authActionTypes";


describe('auth reducer unit tests suite', () => {


    it('should return the initial state', () => {
        expect(authReducer(undefined, {})).toEqual(initialState);
    });

    it('should handle LOGIN_SUCCESS', () => {
        const startAction = {
            type: LOGIN_SUCCESS,
            payload: {
                access_token: 'xxxx',
                menuItems: 'xxxx',
            }
        };
        expect(authReducer({}, startAction)).toEqual({
            access_token: 'xxxx',
            menuItems: 'xxxx', isAuthenticated: true,
            isLoading: false,
            errors: [],
        });
    });

    it('should handle LOGIN_FAIL', () => {
        const startAction = {
            type: LOGIN_FAIL,
        };
        expect(authReducer({}, startAction)).toEqual({
            errors: ["Invalid Credentials"],
            isAuthenticated: false,
            isLoading: false,
        });
    });

    it('should handle LOGOUT_SUCCESS', () => {
        const startAction = {
            type: LOGOUT_SUCCESS,
        };
        expect(authReducer({}, startAction)).toEqual({
            access_token: null,
            user: null,
            isAuthenticated: false,
            isLoading: false,
            errors: [],
        });
    });


});