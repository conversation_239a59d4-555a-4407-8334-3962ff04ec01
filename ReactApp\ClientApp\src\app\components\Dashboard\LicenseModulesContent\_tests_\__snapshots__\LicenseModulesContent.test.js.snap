// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`License Modules Content Test Suite should create and match to snapshot 1`] = `
<div
  className="ant-row"
  style={
    Object {
      "marginBottom": -6,
      "marginLeft": -6,
      "marginRight": -6,
      "marginTop": -6,
    }
  }
>
  <div
    className="ant-col ant-col-8"
    style={
      Object {
        "paddingBottom": 6,
        "paddingLeft": 6,
        "paddingRight": 6,
        "paddingTop": 6,
      }
    }
  >
    <div
      className="ant-card yjLicenedModuleCard"
    >
      <div
        className="ant-card-head"
        style={Object {}}
      >
        <div
          className="ant-card-head-wrapper"
        >
          <div
            className="ant-card-head-title"
          >
            User Management
          </div>
        </div>
      </div>
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <p>
          Users
        </p>
        <p>
          User Groups
        </p>
        <p>
          Permission
        </p>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-8"
    style={
      Object {
        "paddingBottom": 6,
        "paddingLeft": 6,
        "paddingRight": 6,
        "paddingTop": 6,
      }
    }
  >
    <div
      className="ant-card yjLicenedModuleCard"
    >
      <div
        className="ant-card-head"
        style={Object {}}
      >
        <div
          className="ant-card-head-wrapper"
        >
          <div
            className="ant-card-head-title"
          >
            Master Data
          </div>
        </div>
      </div>
      <div
        className="ant-card-body"
        style={Object {}}
      />
    </div>
  </div>
  <div
    className="ant-col ant-col-8"
    style={
      Object {
        "paddingBottom": 6,
        "paddingLeft": 6,
        "paddingRight": 6,
        "paddingTop": 6,
      }
    }
  >
    <div
      className="ant-card yjLicenedModuleCard"
    >
      <div
        className="ant-card-head"
        style={Object {}}
      >
        <div
          className="ant-card-head-wrapper"
        >
          <div
            className="ant-card-head-title"
          >
            File Area
          </div>
        </div>
      </div>
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <p>
          Assign
        </p>
        <p>
          Status
        </p>
        <p>
          Move
        </p>
      </div>
    </div>
  </div>
</div>
`;
