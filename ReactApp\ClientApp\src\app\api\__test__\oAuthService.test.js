import { OAuthService } from "../oAuthService";

describe("OAuth service test suite", () => {
  const authService = new OAuthService();

  it("authservice object should not be null", () => {
    expect(authService).not.toBe(null);
  });
  it("authservice clientId should be YJ_SPA", () => {
    expect(authService.userManager.settings.client_id).toBe("YJ_SPA");
  });
  it("authservice scope should be api_all", () => {
    expect(authService.userManager.settings.scope).toBe("api_all");
  });

  it("authservice login function should exsists", () => {
    expect(typeof authService.login).toBe("function");
  });

  it("authservice logout function should exsists", () => {
    expect(typeof authService.logout).toBe("function");
  });

  it("authservice renewToken function should exsists", () => {
    expect(typeof authService.renewToken).toBe("function");
  });

  it("authservice getUser function should exsists", () => {
    expect(typeof authService.getUser).toBe("function");
  });

  it("authservice login should be defined properly", () => {
    expect(authService.login()).resolves.toBeDefined();
  });
  it("authservice login should be excecuted properly", () => {
    expect(authService.login()).resolves.toBeCalled();
  });
  it("authservice logout should be defined properly", () => {
    expect(authService.logout()).resolves.toBeDefined();
  });
  it("authservice logout should be excecuted properly", () => {
    expect(authService.logout()).resolves.toBeCalled();
  });
  it("authservice renewToken should be defined properly", () => {
    expect(authService.renewToken()).resolves.toBeDefined();
  });
  it("authservice renewToken should be excecuted properly", () => {
    expect(authService.renewToken()).resolves.toBeCalled();
  });
  it("authservice renewToken should be defined properly", () => {
    expect(authService.getUser()).resolves.toBeDefined();
  });
  it("authservice renewToken should be excecuted properly", () => {
    expect(authService.getUser()).resolves.toBeCalled();
  });
});
