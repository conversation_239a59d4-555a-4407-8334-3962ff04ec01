import React from 'react';
import { Drawer, But<PERSON> } from 'antd';
import SubmitButton from '@app/components/SubmitButton';
import UnpublishFilesDrawerContent from './UnpublishFilesDrawerContent';
import styles from './UnpublishFilesDrawer.module.less';

interface UnpublishFilesDrawerProps {
  isLoading: boolean;
  visible: boolean;
  unpublishFileList: any[];
  onClose: () => void;
  onFileListChange: (files: any[]) => void;
  onUnpublish: () => void;
}

const UnpublishFilesDrawer: React.FC<UnpublishFilesDrawerProps> = ({
  isLoading,
  visible,
  unpublishFileList,
  onClose,
  onFileListChange,
  onUnpublish,
}) => {
  return (
    <Drawer
      destroyOnClose={true}
      width={700}
      key={'unpublishfilesmodal'}
      visible={visible}
      title={'Unpublish File(s)'}
      onClose={onClose}
      className={"yjDrawerPanel"}
      footer={[
        <Button key={'cancelPublish'} disabled={isLoading} onClick={onClose} type="default">
          Cancel
        </Button>,
        <SubmitButton
          key={'unPublishFiles'}
          onClick={onUnpublish}
          type="primary"
          disabled={!unpublishFileList.some((file: any) => file.checked)}
        >
          Unpublish
        </SubmitButton>,
      ]}
    >
      <div className={styles.yjModalContentWrapper}>
        <UnpublishFilesDrawerContent fileList={unpublishFileList} onFileListChange={onFileListChange} closeModal={onClose} />
      </div>
    </Drawer>
  );
};

export default UnpublishFilesDrawer;
