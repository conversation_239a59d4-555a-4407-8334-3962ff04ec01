import React, { useEffect, useState } from "react";
import { Row, Col, Skeleton, Form, Tooltip } from "antd";
import { useHistory, useParams } from "react-router-dom";
import {
    DoubleRightOutlined,
} from "@ant-design/icons/lib/icons";

import styles from "./index.module.less";
import SelectedFilesGrid, {
    ColumnConfig,
} from "@app/features/FileArea/SelectedFilesGrid";
import { IFile } from "@app/types/fileAreaTypes";
import { getFileDetailsByFileId, getFolderStructureBySite } from "@app/api/fileAreaService";
import config from "@app/utils/config";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { FORBIDDEN_ERROR_CODE, getParameterizedUrl } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from "@app/utils/logger";
import { FolderTree } from "@app/components/FolderTree";
import { FormInstance } from "antd/lib/form";
import { FileRecord, FileEvents, UrlEvents } from "@app/components/forms/UploaderSubmit/types";
import { Store } from "antd/lib/form/interface";
import { FileDetailsOptions } from "@app/types/FileDetailsOptions";

const LIMIT = 10;

type PropTypes = {
    fileList?: FileRecord[];
    onFinish: (values: Store, fileList: FileRecord[], folderId: number) => void;
    fileEvents?: FileEvents;
};

export interface MoveFileProps {
    selectedFiles: IFile[];
    onNewFolderSelect: (folderId: number, fileList: IFile[], destinationClientId: number) => void;
    onClosePopup: (event: boolean) => void;
    options: FileDetailsOptions;
    form: FormInstance;
    forManageFiles?: boolean;
    onFormChange?: (event: any) => void;
    onFolderTreeChange: (event: any) => void;
}

export default (props: MoveFileProps) => {


    const defaultFolder = props.forManageFiles
        ? props.form.getFieldValue("folder")
            ? props.form.getFieldValue("folder").toString()
            : "1"
        : "";
    const history = useHistory();
    const getFolderPath = async (fileId: string): Promise<string> => {
        let fileDetails = {} as any;
        let returnValue = "";
        await getFileDetailsByFileId(fileId)
            .then(({ data }) => {
                fileDetails = data;
                returnValue = fileDetails.folder.parent
                    ? `${fileDetails.folder.parent.name} > ${fileDetails.folder.name}`
                    : fileDetails.folder.name;
            })
            .catch((error) => {
                if (error.statusCode === FORBIDDEN_ERROR_CODE) {
                    history.push("/forbidden");
                }
            });
        return returnValue;
    };
    const [clientIdSelected, setSelectedClient] = useState(Number);
    const [selectedFolder, setSelectedFolder] = useState(defaultFolder);
    const [selectedFileList, setSelectedFileList] = useState<IFile[]>([]);
    const moveColumnConfigs: ColumnConfig[] = [
        { title: "", dataIndex: "remove", key: "remove", width: 40 },
        { title: "Title", dataIndex: "title", key: "title", ellipsis: true },

    ];

    const [folderTreeData, setFolderTreeData] = useState(defaultFolder);
    useEffect(() => {
        const mapFolderPath = async () => {
            const fileList: IFile[] = await Promise.all(
                props.selectedFiles.map(async (file) => ({
                    ...file,
                    folder: await getFolderPath(file.id),
                }))
            );
            setSelectedFileList(fileList);
            props.onFolderTreeChange(-1);
        };
        mapFolderPath();
    }, [props.selectedFiles]);

    const handleFilesChange = (fileList: any[]) => {

        if (fileList.length > 0) {
            setSelectedFileList(fileList);
            props.onNewFolderSelect(selectedFolder, fileList, clientIdSelected);
        } else {
            props.onClosePopup(false);
        }
    };


    const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {

        const transformFilters: any = {};
        if (searchValue) {
            transformFilters.search = searchValue;
        }

        const getClientIdParameters = {
            limit: LIMIT,
            offset: page - 1,
            ...transformFilters,
        }

        return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannelList, getClientIdParameters)
            .then((res: any) => {

                if (res.data) {
                    
                    return res.data;
                } else {
                    // logger.error('SideSelection', 'getPaginatedRecords', res.error);
                    return []
                }
            })
            .catch((error: any) => {
                // logger.error('SideSelection', 'getPaginatedRecords', error);
                return [];
            });


    };

    //Getting the folder tree by the selected client
    const getFolderTreeByClient = (clientId: string) => {

        getFolderStructureBySite(clientId)
            .then((response: any) => {
                if (response.data) {
                    setFolderTreeData(response.data);
                } else {
                    return [];
                }
            })
            .catch((error) => {

                logger.error('File Area Module', 'Get Folder Tree By Client', error);
            });
    };

    return (
        <>
            {/* {JSON.stringify(props.options)} */}
         <div className={styles.yjModalContentWrapper}>
            <Row gutter={24}>
                <Col span={15}>
                    <Row>
                        <Form.Item label={'Move to Client'} name="clientId" className={styles.moveToClientLabel}>
                        </Form.Item>
                    </Row>
                    <Row>
                        <div className={"yjChannelSelector"}>
                            <InfinitySelect
                                getPaginatedRecords={(page, method, searchValue) => getPaginatedRecords(page, method, searchValue)}
                                formatValue={(value) => {
                                    return `${value.displayText}`;
                                }}
                                notFoundContent="No Offices Available"
                                notLoadContent="Failed to load values in office dropdown"
                                onChange={(e) => {
                                    getFolderTreeByClient(e);
                                    setSelectedClient(e);
                                }}
                                placeholder="Please Select Client(s)"
                                waitCharCount={3}
                            /></div>
                    </Row>
                    <Row gutter={10}>
                        {selectedFileList.length > 0 ? (
                            <SelectedFilesGrid
                                onFilesChange={handleFilesChange}
                                columnConfigs={moveColumnConfigs}
                                dataList={selectedFileList}
                            />
                        ) : (
                            <Skeleton />
                        )}
                    </Row>
                </Col>

                {/* <Col span={1} className={styles.yjMoverArrow}>
                    <DoubleRightOutlined />
                </Col> */}
                <Col span={9} className={styles.yjFolderTreeUrlUploader}>
                    <Form.Item label="Move" name="folder" >
                        <div className={styles.yjFileFinderWrapper}>
                            <div className={styles.yjFileFinderTreeWrapper}>
                                <FolderTree
                                    showTitle={false}
                                    maxHeight="255px"
                                    data={folderTreeData}
                                    onSelectFolder={(keys, info) => {
                                        props.onFolderTreeChange(keys);
                                        setSelectedFolder(keys);
                                        props.onNewFolderSelect(keys, selectedFileList, clientIdSelected)
                                    }}
                                    disableRoot={true}
                                    controlSelection={true}
                                    autoExpandParent={true}
                                    selectedKeys={[selectedFolder.toString()]}
                                    disabled={false}
                                    onClick={() => {

                                    }}
                                />
                            </div>
                        </div>
                    </Form.Item>

                </Col>
            </Row>
            </div>
        </>
    );

};