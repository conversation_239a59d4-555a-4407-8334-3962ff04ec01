import React from "react";
import renderer from "react-test-renderer";
import {shallow} from "enzyme";
import {Col, Collapse, Row} from "antd";

import DashboardV2 from "../index";
import initTestSuite from "@app/utils/config/TestSuite";
import MyRecentDocuments from '@app/components/Dashboard/MyRecentDocuments/';

const {Panel} = Collapse;
describe("Dashboard V2 Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const dv2Component = shallow(<DashboardV2 />);
        expect(dv2Component.html()).not.toBe(null);
    });

    it("should create and match to snapshot",() => {
        const dv2Component = renderer.create(<DashboardV2 />).toJSON();
        expect(dv2Component).toMatchSnapshot();
    });

    it("should have 3 Row elements",() => {
        const dv2Component = shallow(<DashboardV2 />);
        expect(dv2Component.find(Row)).toHaveLength(3);
    });

    it("should have 10 Col elements",() => {
        const dv2Component = shallow(<DashboardV2 />);
        expect(dv2Component.find(Col)).toHaveLength(10);
    });

    it("should have 8 Collapse elements",() => {
        const dv2Component = shallow(<DashboardV2 />);
        expect(dv2Component.find(Collapse)).toHaveLength(8);
    });

    it("should have 8 Panel elements",() => {
        const dv2Component = shallow(<DashboardV2 />);
        expect(dv2Component.find(Panel)).toHaveLength(8);
    });

    it("should have 8 MyRecentDocuments components",() => {
        const dv2Component = shallow(<DashboardV2 />);
        expect(dv2Component.find(MyRecentDocuments)).toHaveLength(8);
    });
});