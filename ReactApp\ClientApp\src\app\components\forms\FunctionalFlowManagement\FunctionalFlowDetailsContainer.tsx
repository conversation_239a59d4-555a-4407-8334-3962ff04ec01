import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "../../../redux/reducers/state";

import {
  EditFunctionalFlow,
  retriveFunctionalFlowData,
} from "../../../redux/actions/functionalFlowActions";
import { Skeleton } from "antd";
import FunctionalFlowManagement from ".";
import { Module } from "../../../redux/types/functionalFlow/functionalFlow";

export interface IFunctionalFlowDetailsContainer {
  licenceId: string;
  actionType: "add" | "edit" | "view";
  onEditFunctionFlow?: () => void | undefined;
}

const FunctionalFlowDetailsContainer: React.FC<IFunctionalFlowDetailsContainer> = ({
  licenceId,
  actionType,
  onEditFunctionFlow,
}) => {
  const dispatch = useDispatch();
  const { isLoading, modules, editModule } = useSelector(
    (state: RootState) => state.functionalFlow
  );

  useEffect(() => {
    dispatch(retriveFunctionalFlowData(licenceId));
  }, [dispatch, licenceId]);

  const onEditFunctionCheckBox = (
    details: Module[] | null | undefined
  ): void => {
    if (onEditFunctionFlow) {
      onEditFunctionFlow();
    }
    dispatch(EditFunctionalFlow(details, editModule));
  };

  return isLoading ? (
    <Skeleton active />
  ) : (
    <FunctionalFlowManagement
      actionType={actionType}
      details={modules}
      onEditFunctionCheckBox={onEditFunctionCheckBox}
    />
  );
};

export default FunctionalFlowDetailsContainer;
