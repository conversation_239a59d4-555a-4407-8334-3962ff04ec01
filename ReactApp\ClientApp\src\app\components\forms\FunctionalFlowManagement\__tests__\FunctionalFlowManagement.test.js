import React from "react";
import { Provider } from "react-redux";
import { mount } from "enzyme";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";

import FunctionalFlowManagement from "../index";
// TODO: required to facilitate missing window events
import "../../../../../unit-test-utils";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const FunctionalFlowManagementWrapper = (props) => {
  const { store, ...compProps } = { ...props };
  return (
    <ReduxProvider store={store}>
      <FunctionalFlowManagement {...compProps} />
    </ReduxProvider>
  );
};

describe("Functional Flow Management Component", () => {
  it("should render Functional details container component", () => {
    const INITIAL_STATE = { grid: { selectedElement: {} } };
    const store = mockStore(INITIAL_STATE);

    const component = mount(
      <FunctionalFlowManagementWrapper
        actionType="add"
        licenceId="LC-00024"
        key="1"
        store={store}
      />
    );
    expect(component.html()).not.toBeNull();
  });

  // TODO: Unit tests for view action to verify the required components are disabled
});
