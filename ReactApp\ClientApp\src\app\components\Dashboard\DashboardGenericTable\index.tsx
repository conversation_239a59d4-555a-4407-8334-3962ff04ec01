import { Skeleton, Table } from "antd";
import { ColumnsType } from "antd/lib/table";
import React, { useEffect, useState } from "react";

import useStateRef from "@app/hooks/useStateRef";
import logger from "@app/utils/logger";
import { getGenericDashboardRecords } from "./utils";

export interface DashBoardGenericGridProps {
  endpoint: string;
  dataKey: string;
  options?: any;
  columns?: ColumnsType<any>;
  onErrorLoading?: (error: any) => void;
}

export default ({
  endpoint,
  options,
  columns,
  dataKey,
  onErrorLoading,
}: DashBoardGenericGridProps) => {
  const [data, setData] = useState<Array<any>>([]);
  const [pagination, setPagination, paginationRef] = useStateRef({
    current: 1,
    pageSize: 10,
    total: 0,
    pageSizeOptions: ["10", "20", "25", "50"],
    size: "small",
    showSizeChanger: true,
  });
  const [loading, setLoading] = useState<boolean>(true);

  const fetchRecords = () => {
    setLoading(true);
    getPaginatedRecords()
      .then((response: any) => {
        setPagination({
          ...pagination,
          current: response.data?.pageNumber,
          pageSize: response.data?.pageSize,
          total: response.data?.totalRecordCount,
        });
        setLoading(false);
        setData(response.data?.records);
      })
      .catch((error) => {
        onErrorLoading && onErrorLoading(error);
      });
  };

  useEffect(() => {
    fetchRecords();
  }, [endpoint, options]);

  const getPaginatedRecords = async (): Promise<Array<any>> => {
    const optionList = {
      pagination: paginationRef.current,
      queryParameters: options.queryParameters,
    };
    return getGenericDashboardRecords(endpoint, optionList)
      .then((response: any) => {
        return response;
      })
      .catch((error: any) => {
        onErrorLoading && onErrorLoading(error);
        logger.error("Genraric Module", "Inifinity Component", error);
        return {};
      });
  };

  const handleTableChange = (paginationInput: any) => {
    setPagination({
      ...pagination,
      current: paginationInput.current,
      pageSize: paginationInput.pageSize,
      total: paginationInput.total,
    });
    fetchRecords();
  };

  return !!data.length ? (
    <Table
      className={"yjDashboardTableWrapper"}
      sticky
      key={dataKey}
      columns={columns}
      rowKey={dataKey}
      dataSource={data}
      pagination={pagination}
      loading={loading}
      onChange={handleTableChange}
    />
  ) : (
    <Skeleton active />
  );
};
