// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Filter Template Save New Filter Test Suite should render and create snapshot properly 1`] = `
<form
  className="ant-form ant-form-horizontal"
  onReset={[Function]}
  onSubmit={[Function]}
>
  <div
    className="ant-row ant-form-item ant-form-item-with-help ant-form-item-has-error"
    style={Object {}}
  >
    <div
      className="ant-col ant-form-item-control"
      style={Object {}}
    >
      <div
        className="ant-form-item-control-input"
      >
        <div
          className="ant-form-item-control-input-content"
        >
          <input
            autoComplete="off"
            className="ant-input"
            maxLength={30}
            onBlur={[Function]}
            onChange={[Function]}
            onFocus={[Function]}
            onKeyDown={[Function]}
            placeholder="Enter Filter Name"
            type="text"
            value=""
          />
        </div>
      </div>
      <div
        className="ant-form-item-explain ant-form-item-explain-error"
      >
        <div
          role="alert"
        >
          Name Already Exists
        </div>
      </div>
    </div>
  </div>
</form>
`;
