import React from "react";
import { Donut<PERSON><PERSON> } from "bizcharts";

export default () => {
  const data = [
    {
      type: "File Area",
      value: 27,
    },
    {
      type: "Compliance",
      value: 25,
    },
    {
      type: "Retention",
      value: 18,
    },
    {
      type: "Portal",
      value: 15,
    },
    {
      type: "Reporting",
      value: 10,
    },
    {
      type: "Routing",
      value: 5,
    },
  ];

  return (
    <>
      <DonutChart
        data={data || []}
        height={320}
        radius={0.8}
        padding="auto"
        angleField="value"
        colorField="type"
        pieStyle={{ stroke: "white", lineWidth: 2 }}
        label={{ visible: true, type: "outer" }}
        statistic={{ visible: false }}
        tooltip={{ visible: true }}
      />
    </>
  );
};
