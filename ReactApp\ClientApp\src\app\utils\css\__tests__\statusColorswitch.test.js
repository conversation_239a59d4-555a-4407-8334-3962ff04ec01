import statusColorSwitch from "../statusColorSwitch";
import {
  PENDING,
  ACTIVE,
  SUSPENDED,
  EXPIRED,
  TERMINATED,
} from "@app/constants/licenseStatuses";

describe("Status Color switch test suite", () => {
  it("should contain statusColorSwitch method", () => {
    expect(typeof statusColorSwitch).toBe("function");
  });
  it("should return a valid color when  PENDING,ACTIVE,SUSPENDED,EXPIRED,TERMINATED to statusColorSwitch method", () => {
    expect(statusColorSwitch(PENDING)).toBe("#ffa940");
    expect(statusColorSwitch(ACTIVE)).toBe("#73d13d");
    expect(statusColorSwitch(SUSPENDED)).toBe("#663A8C");
    expect(statusColorSwitch(EXPIRED)).toBe("#666666");
    expect(statusColorSwitch(TERMINATED)).toBe("#ff4d4f");
  });
  it("should return am empty string when value other than PENDING,ACTIVE,SUSPENDED,EXPIRED,TERMINATED to statusColorSwitch method", () => {
    expect(statusColorSwitch("")).toBe("");
    expect(statusColorSwitch(100)).toBe("");
    expect(statusColorSwitch()).toBe("");
    expect(statusColorSwitch(null)).toBe("");
    expect(statusColorSwitch(undefined)).toBe("");
  });
});
