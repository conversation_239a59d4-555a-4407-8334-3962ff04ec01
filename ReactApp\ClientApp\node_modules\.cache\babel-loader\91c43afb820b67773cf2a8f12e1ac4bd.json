{"ast": null, "code": "var _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\Copyright\\\\index.tsx\";\nimport React from \"react\";\nimport Tooltip from \"antd/lib/tooltip\";\nimport { useSelector } from \"react-redux\";\n\nconst useAllowedDomain = () => {\n  var _user$preferred_usern;\n\n  const {\n    user\n  } = useSelector(rootState => rootState.auth);\n  const userName = (user === null || user === void 0 ? void 0 : (_user$preferred_usern = user.preferred_username) === null || _user$preferred_usern === void 0 ? void 0 : _user$preferred_usern.toLowerCase()) || '';\n  const w = window;\n  const allowedDomains = process.env.ALLOWED_EMAIL_DOMAINS || w._env_ && w._env_.ALLOWED_EMAIL_DOMAINS || '@iris.co.uk,@irisglobal.com';\n  const loggedUserDomain = userName.split('@').pop();\n  return loggedUserDomain ? allowedDomains.split(',').includes(`@${loggedUserDomain}`) : false;\n};\n\nconst AuthTooltip = () => {\n  var _env_, _ref, _ref$_env_, _ref2, _ref2$_env_;\n\n  const {\n    tenant\n  } = useSelector(rootState => rootState.auth);\n  const isAllowedDomain = useAllowedDomain();\n  const copyrightYear = new Date().getFullYear();\n  const version = ((_env_ = window._env_) === null || _env_ === void 0 ? void 0 : _env_.REACT_APP_VERSION) || process.env.REACT_APP_VERSION;\n  const rawVersion = ((_ref = window) === null || _ref === void 0 ? void 0 : (_ref$_env_ = _ref._env_) === null || _ref$_env_ === void 0 ? void 0 : _ref$_env_.REACT_APP_VERSION) || process.env.REACT_APP_VERSION;\n  const showVersion = ((_ref2 = window) === null || _ref2 === void 0 ? void 0 : (_ref2$_env_ = _ref2._env_) === null || _ref2$_env_ === void 0 ? void 0 : _ref2$_env_.REACT_APP_VERSION_SHOW) === 'true'; // Version validations\n\n  const isRealVersion = rawVersion && rawVersion.trim() !== '' && rawVersion !== '${VERSION}';\n  return /*#__PURE__*/React.createElement(Tooltip, {\n    placement: \"top\",\n    title: isAllowedDomain && showVersion && isRealVersion ? /*#__PURE__*/React.createElement(\"span\", {\n      style: {\n        marginRight: '3px'\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 38,\n        columnNumber: 11\n      }\n    }, \"Version: \", rawVersion) : '',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 34,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 44,\n      columnNumber: 7\n    }\n  }, \"Copyright \\xA9 IRIS Software Group \", copyrightYear, \" \"), /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 45,\n      columnNumber: 7\n    }\n  }, \"Tenant: \", tenant));\n};\n\nexport default AuthTooltip;", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/components/Copyright/index.tsx"], "names": ["React", "<PERSON><PERSON><PERSON>", "useSelector", "useAllowedDomain", "user", "rootState", "auth", "userName", "preferred_username", "toLowerCase", "w", "window", "allowedDomains", "process", "env", "ALLOWED_EMAIL_DOMAINS", "_env_", "loggedUserDomain", "split", "pop", "includes", "AuthTooltip", "tenant", "isAllowedDomain", "copyrightYear", "Date", "getFullYear", "version", "REACT_APP_VERSION", "rawVersion", "showVersion", "REACT_APP_VERSION_SHOW", "isRealVersion", "trim", "marginRight"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AACA,OAAOC,OAAP,MAAoB,kBAApB;AACA,SAASC,WAAT,QAA4B,aAA5B;;AAGA,MAAMC,gBAAgB,GAAG,MAAe;AAAA;;AACtC,QAAM;AAAEC,IAAAA;AAAF,MAAWF,WAAW,CAAEG,SAAD,IAA0BA,SAAS,CAACC,IAArC,CAA5B;AACA,QAAMC,QAAQ,GAAG,CAAAH,IAAI,SAAJ,IAAAA,IAAI,WAAJ,qCAAAA,IAAI,CAAEI,kBAAN,gFAA0BC,WAA1B,OAA2C,EAA5D;AACA,QAAMC,CAAC,GAAGC,MAAV;AAEA,QAAMC,cAAc,GAClBC,OAAO,CAACC,GAAR,CAAYC,qBAAZ,IACCL,CAAC,CAACM,KAAF,IAAWN,CAAC,CAACM,KAAF,CAAQD,qBADpB,IAEA,6BAHF;AAKA,QAAME,gBAAgB,GAAGV,QAAQ,CAACW,KAAT,CAAe,GAAf,EAAoBC,GAApB,EAAzB;AAEA,SAAOF,gBAAgB,GACnBL,cAAc,CAACM,KAAf,CAAqB,GAArB,EAA0BE,QAA1B,CAAoC,IAAGH,gBAAiB,EAAxD,CADmB,GAEnB,KAFJ;AAGD,CAfD;;AAiBA,MAAMI,WAAqB,GAAG,MAAM;AAAA;;AAClC,QAAM;AAAEC,IAAAA;AAAF,MAAapB,WAAW,CAAEG,SAAD,IAA0BA,SAAS,CAACC,IAArC,CAA9B;AACA,QAAMiB,eAAe,GAAGpB,gBAAgB,EAAxC;AACA,QAAMqB,aAAa,GAAG,IAAIC,IAAJ,GAAWC,WAAX,EAAtB;AACA,QAAMC,OAAO,GAAG,UAAChB,MAAD,CAAgBK,KAAhB,gDAAuBY,iBAAvB,KAA4Cf,OAAO,CAACC,GAAR,CAAYc,iBAAxE;AACA,QAAMC,UAAU,GAAE,SAAClB,MAAD,4DAAiBK,KAAjB,0DAAwBY,iBAAxB,KAA6Cf,OAAO,CAACC,GAAR,CAAYc,iBAA3E;AACA,QAAME,WAAW,GAAG,UAACnB,MAAD,+DAAiBK,KAAjB,4DAAwBe,sBAAxB,MAAmD,MAAvE,CANkC,CAOpC;;AACA,QAAMC,aAAa,GAAEH,UAAU,IAAIA,UAAU,CAACI,IAAX,OAAsB,EAApC,IAA0CJ,UAAU,KAAK,YAA9E;AAEE,sBACE,oBAAC,OAAD;AACE,IAAA,SAAS,EAAC,KADZ;AAEE,IAAA,KAAK,EACHN,eAAe,IAAIO,WAAnB,IAAkCE,aAAlC,gBACE;AAAM,MAAA,KAAK,EAAE;AAAEE,QAAAA,WAAW,EAAE;AAAf,OAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAA+CL,UAA/C,CADF,GAGE,EANN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAUE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4CAAuCL,aAAvC,MAVF,eAWE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAAeF,MAAf,CAXF,CADF;AAeD,CAzBD;;AA2BA,eAAeD,WAAf", "sourcesContent": ["import React from \"react\";\r\nimport Tooltip from \"antd/lib/tooltip\";\r\nimport { useSelector } from \"react-redux\";\r\nimport { RootState } from \"@app/redux/reducers/state\";\r\n\r\nconst useAllowedDomain = (): boolean => {\r\n  const { user } = useSelector((rootState: RootState) => rootState.auth);\r\n  const userName = user?.preferred_username?.toLowerCase() || '';\r\n  const w = window as any;\r\n\r\n  const allowedDomains =\r\n    process.env.ALLOWED_EMAIL_DOMAINS ||\r\n    (w._env_ && w._env_.ALLOWED_EMAIL_DOMAINS) ||\r\n    '@iris.co.uk,@irisglobal.com';\r\n\r\n  const loggedUserDomain = userName.split('@').pop();\r\n\r\n  return loggedUserDomain\r\n    ? allowedDomains.split(',').includes(`@${loggedUserDomain}`)\r\n    : false;\r\n};\r\n\r\nconst AuthTooltip: React.FC = () => {\r\n  const { tenant } = useSelector((rootState: RootState) => rootState.auth);\r\n  const isAllowedDomain = useAllowedDomain();\r\n  const copyrightYear = new Date().getFullYear();\r\n  const version = (window as any)._env_?.REACT_APP_VERSION || process.env.REACT_APP_VERSION;\r\n  const rawVersion =(window as any)?._env_?.REACT_APP_VERSION || process.env.REACT_APP_VERSION;\r\n  const showVersion = (window as any)?._env_?.REACT_APP_VERSION_SHOW === 'true';\r\n// Version validations\r\nconst isRealVersion =rawVersion && rawVersion.trim() !== '' && rawVersion !== '${VERSION}';\r\n\r\n  return (\r\n    <Tooltip\r\n      placement='top'\r\n      title={\r\n        isAllowedDomain && showVersion && isRealVersion ? (\r\n          <span style={{ marginRight: '3px' }}>Version: {rawVersion}</span>\r\n        ) : (\r\n          ''\r\n        )\r\n      }\r\n    >\r\n      <span>Copyright © IRIS Software Group {copyrightYear} </span>\r\n      <span>Tenant: {tenant}</span>\r\n    </Tooltip>\r\n  );\r\n};\r\n\r\nexport default AuthTooltip;\r\n"]}, "metadata": {}, "sourceType": "module"}