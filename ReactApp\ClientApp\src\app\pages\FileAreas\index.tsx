import React, { Fragment, useEffect, useImperative<PERSON><PERSON><PERSON>, useState } from "react";
import { with<PERSON>out<PERSON> } from "react-router-dom";
import { Tooltip, Button, Skeleton, Tag, Alert } from "antd";
import { FolderOpenOutlined, LockOutlined } from "@ant-design/icons";

import PageTitle from "../../components/PageTitle";
import { PageContent } from "../../layouts/MasterLayout";
import GenericDataTable from "../../components/GenericDataTable";
import config, { getApiUrl } from "../../utils/config";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import styles from "./index.module.less";
import NonAuthorized from "@app/components/NonAuthorized";
import { Sorter } from "@app/components/GenericDataTable/util";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from "@app/utils/logger";
import { getAutocompleteOptions, getColumns, getRecords } from "@app/api/genericDataTable";
import debounce from "lodash/debounce";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import { setDynamicBreadcrums } from "@app/redux/actions/configurationActions";
import StatusTag from "@app/features/FileArea/StatusTag";

export interface IFileAres {
  title?: string;
  history?: any;
}

const SORTER: Sorter = {
  value: "siteName",
  order: "ascend",
};

const Page = (props: IFileAres) => {
  const [channelId, setChannelId] = useState<string | undefined>();
  const [isChannelLoaded, setIsChannelLoaded] = useState<boolean>(false);
  const { userPermission } = useSelector((state: RootState) => state.userManagement);
  const reactDispatch = useDispatch();

  //Refresh action button in rerender
  function gTCustomRenderAction(text: any, record: any) {
    return (
      <div className={"yjActionIconWrapper"}>
        <Tooltip title="View">
          <Button
            onClick={() => {
              const encryptedName = encodeURIComponent(record.siteName);
              const url = `/client-file-area/${record.siteId}/${encryptedName}/${channelId}`;
              const encodedUrl = encodeURI(url);
              props.history.push(encodedUrl);
            }}
            icon={<FolderOpenOutlined />}
          />
        </Tooltip>
      </div>
    );
  }

  //Refresh action button in rerender
  function renderClientIdColumn(text: any, record: any) {
    return (
      <div className={"yjActionIconWrapper"}>
        {text} {(record.isFolderExist === "YES") && <Tag color="blue">File Area</Tag>}
      </div>
    );
  }

  //Error in loading GT
  function gTOnErrorLoading(error: any) {
    switch (error.statusCode) {
      case FORBIDDEN_ERROR_CODE:
        props.history.push("/forbidden");
        break;

      default:
        logger.error('FileArea', 'gTOnErrorLoading', error);
        break;
    }
  }
  let fetchData = (state: any, transformFilters: any, queryParams: any) => {
    logger.debug("FileArea", "fetchData", { state, transformFilters, queryParams });
    return getRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel,
      {
        pagination: {
          current: state.pagination.current,
          pageSize: state.pagination.pageSize,
        },
        sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
        filters: transformFilters,
        columns: state.columns.filter((i: any) => i.default === false && i.selected === true).map((i: any) => i.key),
      }, queryParams
    )
  };

  // Debounced API call
  const debouncedApiCall = debounce(
    (props: any, value: string, callback: (data: any) => void) => {

      getAutocompleteOptions(
        config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel,
        props.data.key,
        value,
        props.searchFieldParameters
      )
        .then((data: any) => {
          callback(data.data);
        })
        .catch(() => {
          callback([]);
        });
    },
    config.inputDebounceInterval
  );

  // Wrapper function to return a Promise
  const searchPromiseWrapper = (props: any, value: string, callback: any) => {
    debouncedApiCall(props, value, (data: any) => {
      callback(data)
    });
  };
  const rowClassName = (record:any, index: number): string => {
    return record.status.toLowerCase().replace(/\s+/g, '-').trim();
  };
  //Channel Status Toggler
  function channelLoader(channelId: any, isChannelLoaded: any) {
    const tableKey = "FileAreaLandingPageGrid";
    if (channelId) {
      return (
        <GenericDataTable
          rowClassName={rowClassName}
          // endpoint={getApiUrl( config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel )}
          searchPromise={searchPromiseWrapper}
          dataPromise={(state, transformFilters, queryParams) => fetchData(state, transformFilters, queryParams)}
          columnPromise={getColumns(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel, tableKey)}
          searchFieldParameters={[{ key: "channelId", value: channelId }]}
          searchQueryParameters={[{ key: "channelId", value: channelId }]}
          rowKey={"siteId"}
          tableKey={tableKey}
          sorted={SORTER}
          key={channelId}
          customRender={{
            action: gTCustomRenderAction,
            isFolderExist: (value: any) => {
              return (value ? <Tag color="blue">File Area</Tag> : '')
            },
            // isFolderExist: {
            //   render: (value: any) => {
            //     return (value ? <Tag color="blue">File Area</Tag> : '')
            //   },
            //   width: 550
            // }
            status: (value: string) => {
              return (
                <div style={{ textAlign: 'center' }}>
                  <StatusTag value={value} />
                </div>
              );
            },
          }}
          onErrorLoading={gTOnErrorLoading}
        />
      );
    } else if (isChannelLoaded) {
      return (
        <NonAuthorized
          styleClassName={styles.yjNonAuthorizedFileAreaWrapper}
          title={"You do not have the permission to access any offices"}
          subTitle={"Contact your organization's adminstrator for assistance"}
        />
      );
    } else {
      return <Skeleton active={true} />;
    }
  }

  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    /**
     * Will add the keyvalue if dropdown still visible
     */
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const options = {
      limit: 10,
      offset: page - 1,
      ...transformFilters
    }
    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].channels, options)
      .then((res: any) => {
        logger.info('SideSelection', 'getPaginatedRecords', res.data);
        if (res.data) {
          setChannelId(res.data.records[0].id);
          // TODO it's better to use redux store to store this and use Redux Persist library
          localStorage.setItem('selectedChannelId', res.data.records[0].id);
          return res.data;
        } else {
          logger.error('SideSelection', 'getPaginatedRecords', res.error);
          return []
        }
      })
      .catch((error: any) => {
        logger.error('SideSelection', 'getPaginatedRecords', error);

        return [];
      });
  };

  useEffect(() => {
    getPaginatedRecords(1, 'load', '')

    // Set dynamic breadcrumbs for the current page
    const dynamicBreadcrumbs = [
      { title: "Client File Areas", path: "/client-file-area" },
    ];
    reactDispatch(setDynamicBreadcrums(dynamicBreadcrumbs));
    return () => {
      reactDispatch(setDynamicBreadcrums([]));
    };
  }, [])

  return (
    <Fragment>
      <PageTitle title={props.title}>
        {/* <div className={"yjChannelSelector"}>
          {channelId && <InfinitySelect
            getPaginatedRecords={getPaginatedRecords}
            formatValue={(value) => {
              return `${value.displayText}`;
            }}
            onLoaded={(isLoaded: boolean) => setIsChannelLoaded(isLoaded)}
            isDefault={true}
            notFoundContent="No Offices Available"
            notLoadContent="Failed to load values in office dropdown"
            onChange={(e) => {
              setChannelId(e);
            }}
            placeholder="Office Name"
            defaultValues={channelId}
            disabled={true}
          />}
        </div> */}
      </PageTitle>

      <PageContent>
        {userPermission.privDMSCanViewFileArea && channelLoader(channelId, isChannelLoaded)}
        {userPermission.privDMSCanViewFileArea === false &&
          <Alert
            message="You do not have permission to access the File Area. Contact your organization's administrator for assistance"
            icon={<LockOutlined />}
            type="error"
            showIcon
          />}
        {userPermission.privDMSCanViewFileArea === undefined && (
          <Skeleton />
        )}
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
