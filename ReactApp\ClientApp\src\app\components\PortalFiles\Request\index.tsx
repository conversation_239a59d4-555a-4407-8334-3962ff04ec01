import { Form, Input, Checkbox, TreeSelect, DatePicker, Col } from "antd";
import React, { useCallback, useEffect, useState } from "react";
import TextArea from "antd/lib/input/TextArea";
import { FormInstance } from "antd/lib/form";
import { CheckboxChangeEvent } from "antd/lib/checkbox";
import moment from "moment";

import styles from "./index.module.less";
import {
  max,
  required,
  typeWithPattern,
} from "@app/components/forms/validators";
import { disabledPastDays } from "@app/components/forms/utils/disableDates";
import { formActions } from "@app/types";
import { AvoidWhitespace } from "@app/utils/regex";

export interface IRequest {
  key?: string;
  formRef: FormInstance;
  actionType: formActions | null;
  onFinish: (event: any) => void;
  data?: any;
  onChange: (event: any) => void;
}
const layout = {
  labelCol: { span: 5 },
  wrapperCol: { span: 19 },
};

const { TreeNode } = TreeSelect;

export default (props: IRequest) => {
  const [securityKey, setSecurityKey] = useState(true);
  const [linkExpired, setLinkExpired] = useState(true);
  const [displayExpireValidation, setDisplayExpireValidaton] = useState(false);
  const [disabled, setDisabled] = useState(false);
  const INPUT_VALUE_LENGTH_TEN = 10;
  const INPUT_VALUE_LENGTH_HUNDRED = 100;
  const INPUT_VALUE_LENGTH_THOUSAND = 1000;

  useEffect(() => {
    props.formRef.resetFields();
  }, [props.formRef]);

  const setRequestFormFields = () => {
    props.formRef.setFieldsValue({
      request: props.data.RequestDetails?.name,
      description: props.data.RequestDetails?.description
        ? props.data.RequestDetails?.description
        : null,
      securityKey: props.data.RequestDetails?.secured
        ? props.data.RequestDetails.secured
        : false,
      expireLink: props.data.RequestDetails?.expirationDate ? true : false,
      linkExpireDate: props.data.RequestDetails?.expirationDate
        ? moment(props.data.RequestDetails?.expirationDate)
        : null,
      subject: "",
      body: "",
      securityKeyInput: props.data.SecurityKeyDetails?.securityKey
        ? props.data.SecurityKeyDetails?.securityKey
        : null,
    });
  };

  const validateExpirationDate = () => {
    setDisplayExpireValidaton(
      !disabled &&
        linkExpired &&
        props.formRef.getFieldValue("linkExpireDate") &&
        moment(props.formRef.getFieldValue("linkExpireDate")).isBefore(
          new Date(),
          "D"
        )
    );
  };

  const bindDataToControls = useCallback(() => {
    setRequestFormFields();
    setLinkExpired(
      props.data.RequestDetails.expireLink ||
        !props.data.RequestDetails.expirationDate
    );
    setSecurityKey(!props.data.RequestDetails.secured);
    validateExpirationDate();
  }, [props.data]);

  useEffect(() => {
    if (props.actionType !== null && props.actionType === formActions.VIEW) {
      bindDataToControls();
      setDisabled(true);
    }
    if (props.actionType !== null && props.actionType === formActions.EDIT) {
      bindDataToControls();
    }
  }, []);

  const generateUserTree = () => {
    return (
      <div className="yjMultiSelectOptionSelect">
        <TreeSelect
          key="3"
          disabled={disabled}
          showSearch
          treeCheckable
          style={{ width: "100%" }}
          dropdownStyle={{ maxHeight: 400, overflow: "auto" }}
          placeholder="Please select"
          allowClear
          treeDefaultExpandAll
          getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
          notFoundContent={"This feature is coming soon"}
        >
          {/* <TreeNode value="parent 1" title="All Contacts & Users">
            <TreeNode value="parent 1-0" title="Primary contacts">
              <TreeNode value="leaf1" title="Harry Potter" />
              <TreeNode value="leaf2" title="Ron Weasley" />
            </TreeNode>
            <TreeNode value="parent 1-1" title="Linked Contacts">
              <TreeNode value="leaf3" title="Hermione Granger" />
              <TreeNode value="leaf4" title="Luna Lovegood" />
            </TreeNode>
            <TreeNode value="parent 1-2" title="Internal Users">
              <TreeNode value="leaf5" title="Albus Dumbledore" />
              <TreeNode value="leaf6" title="Severes Snape" />
            </TreeNode>
          </TreeNode> */}
        </TreeSelect>
      </div>
    );
  };

  const onChangeSecurityKey = (e: CheckboxChangeEvent) => {
    if (!e.target.value) {
      props.formRef.setFieldsValue({ securityKeyInput: "" });
    }
    setSecurityKey(!securityKey);
  };

  const onChangeExpireDateOption = (e: CheckboxChangeEvent) => {
    if (!e.target.value) {
      props.formRef.setFieldsValue({ linkExpireDate: "" });
    }
    setLinkExpired(!linkExpired);
    validateExpirationDate();
  };

  const onExpireDateChange = (e: any) => {
    validateExpirationDate();
    props.onChange(e);
  };

  return (
    <div className={styles.yjPortalRequestWrapper}>
      <Form
        form={props.formRef}
        {...layout}
        size="middle"
        className={styles.yjPortalRequestForm}
        onChange={props.onChange}
      >
        <Form.Item
          label="REQUEST"
          name="request"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
          required
          rules={[
            required,
            max(INPUT_VALUE_LENGTH_HUNDRED),
            typeWithPattern("string", AvoidWhitespace),
          ]}
        >
          <Input disabled={disabled} autoComplete="off" />
        </Form.Item>
        <Form.Item
          label="Description"
          name="description"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
          rules={[max(INPUT_VALUE_LENGTH_THOUSAND)]}
        >
          <TextArea disabled={disabled}></TextArea>
        </Form.Item>
        <Form.Item
          label="from"
          name="from"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
        >
          <Input placeholder="This feature is coming soon" disabled={true} />
          <div className={styles.yjPortalRequestCheckbox}>
            <Checkbox disabled={disabled}> BCC Sender </Checkbox>
          </div>
        </Form.Item>
        <Form.Item
          label="to"
          name="to"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
        >
          {generateUserTree()}
        </Form.Item>
        <Form.Item
          label="cc"
          name="cc"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
        >
          {generateUserTree()}
        </Form.Item>
        <Form.Item
          label="bcc"
          name="bcc"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
        >
          {generateUserTree()}
        </Form.Item>
        <Form.Item
          label="subject"
          name="subject"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
          rules={[
            max(INPUT_VALUE_LENGTH_HUNDRED),
            required,
            typeWithPattern("string", AvoidWhitespace),
          ]}
        >
          <Input disabled={disabled} autoComplete="off" />
        </Form.Item>
        <Form.Item
          label="body"
          name="body"
          className={styles.yjPortalRequestFormRowItem}
          colon={false}
          rules={[
            max(INPUT_VALUE_LENGTH_THOUSAND),
            required,
            typeWithPattern("string", AvoidWhitespace),
          ]}
        >
          <TextArea disabled={disabled}></TextArea>
        </Form.Item>

        <Col span={19} offset={5}>
          <Form.Item
            initialValue={false}
            valuePropName="checked"
            name="securityKey"
            className={styles.yjDatePicketRowItemLabel}
          >
            <Checkbox disabled={disabled} onChange={onChangeSecurityKey}>
              Security Key
            </Checkbox>
          </Form.Item>

          <Form.Item
            name="securityKeyInput"
            className={styles.yjDatePicketRowItem}
            colon={false}
            rules={[
              max(INPUT_VALUE_LENGTH_TEN),
              typeWithPattern("string", AvoidWhitespace),
            ]}
          >
            <Input.Password
              maxLength={INPUT_VALUE_LENGTH_TEN}
              disabled={securityKey || disabled}
              autoComplete="off"
            />
          </Form.Item>

          <Form.Item
            valuePropName="checked"
            name="expireLink"
            initialValue={false}
            className={styles.yjDatePicketRowItemLabel}
          >
            <Checkbox disabled={disabled} onChange={onChangeExpireDateOption}>
              Link Expired on
            </Checkbox>
          </Form.Item>

          <Form.Item
            name="linkExpireDate"
            className={styles.yjDatePicketRowItem}
            colon={false}
            validateStatus={displayExpireValidation ? "error" : undefined}
            help={
              displayExpireValidation && !disabled
                ? "This is a past date"
                : null
            }
          >
            <DatePicker
              disabledDate={disabledPastDays}
              onChange={onExpireDateChange}
              disabled={linkExpired || disabled}
              style={{ width: "100%" }}
              getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
              format={moment.localeData().longDateFormat('L')}
            />
          </Form.Item>
        </Col>
      </Form>
    </div>
  );
};
