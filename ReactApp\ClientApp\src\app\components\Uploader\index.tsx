import React, { useState, useRef, useEffect } from "react";
import { notification } from "antd";
import { UploadFile, RcFile } from "antd/lib/upload/interface";
import { useSelector } from "react-redux";

import DragAndDrop from "./DragAndDrop";
import { UploadProgress } from "./UploadProgress";
import { upload, removeFiles } from "@app/api/fileAreaService";
import SelectUploadOptionModal from "./SelectUploadOptionModal";
import confirmDiscard from "./utils/confirmDiscard";
import useUploader from "./hooks/useUploader";
import { validateSize, validateTitle, validateFileName } from "./validators";
import { FileList } from "./types";
import { FileRecord } from "../forms/UploaderSubmit/types";
import FileDetailsModal from "./FileDetailsModal";
import {
  UNSET_UPLOAD,
  URL_UPLOAD,
  SAME_PROPERTY_UPLOAD,
} from "./constants/uploadTypes";
import logger from "@app/utils/logger";
import UrlDetailsModal from "./UrlDetailsModal";
import { RootState } from "@app/redux/reducers/state";
import {FILE_INVALID, FILE_IS_TOO_LARGE, TOO_MANY_FILES,UN_SUPPORTED_FILE_FORMAT} from "./constants/errors";
import { warningNotification } from "@app/utils/antNotifications";
import {ERROR_TYPE_CANCEL} from "@app/utils/http";

type PropTypes = {
  siteId: string;
  binderId:string;
  portalFiles?: any[];
  onCloseModal?: () => void;
  fileAreaSelection?: boolean;
};

export const LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE = "Low file space. Please contact your administrator";
export const RETRYING_MESSAGE = "Operation could not be completed. Retrying...";
export const UPLOAD_FAILED_MESSAGE = "Upload Failed. Retry or Remove";

const MAX_RETRY_COUNT = 3;
export const FULL_PERCENTAGE = 100;
export const MAXIMUM_FILE_COUNT = 20;
export default ({
  siteId,
  binderId,
  portalFiles = [],
  onCloseModal = () => {},
  fileAreaSelection
}: PropTypes) => {
  const { files, action } = useUploader();
  const {
    add,
    updatePercent,
    updateError,
    increaseChunkCounter,
    isCompleted,
    changeToBeProceedStatus,
    incrementRetryCount,
    resetPercent,
    remove,
    removeAll,
    retry,
    retryAll,
    setReferenceNumber,
  } = action;
  const isShowingError = useRef(false);
  const [viewCompleteModal, setViewCompleteModal] = useState(false);
  const [uploadType, setUploadType] = useState(UNSET_UPLOAD);

  const filesCount = Object.keys(files).length;
  const hasUploadingFiles = !!filesCount;
  const hasUrlUploadPermission = useSelector(
    (state: RootState) => state.userManagement.userPermission.privDMSCanUploadFiles
  );

  const allowedExtensions = [
    ".pdf", ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx",
    ".odt", ".ods", ".odp", ".rtf", ".txt", ".jpg", ".jpeg",
    ".png", ".gif", ".bmp", ".tiff", ".csv", ".xml", ".json", ".zip"
  ];

  useEffect(() => {
    if (Object.keys(files).length === 0) {
      setUploadType(UNSET_UPLOAD);
      setViewCompleteModal(false);
    }

    Object.entries(files).forEach(([uid, info]) => {
      if (uploadType === UNSET_UPLOAD && !info.toBeProceed) {
        fileUpload(uid);
      }
    });
  }, [files]);

  useEffect(() => {
    if (portalFiles?.length === 1) {
      setUploadType(SAME_PROPERTY_UPLOAD);
    }
  }, [portalFiles]);

  const fileUpload = (uid: string) => {
    if (!files[uid] || !files[uid].name) {
      logger.error('fileUpload', 'Uploader', `File with uid ${uid} not found or incomplete`);
      return;
    }
  
    if (files[uid].error?.message === FILE_IS_TOO_LARGE || !validateFileName(files[uid].name)) {
      return;
    }
  
    // Make sure uploadOptions exist before trying to access them
    if (!files[uid].uploadOptions) {
      logger.error('fileUpload', 'Uploader', `Upload options for file with uid ${uid} are missing`);
      return;
    }
  
    const { onSuccess, onError, file, onProgress } = files[uid].uploadOptions;
    const upFile = file.slice(
      files[uid].chunkStartSize,
      files[uid].chunkEndSize
    ) as File;
    changeToBeProceedStatus(uid, true);
    upload(
      file.name,
      upFile,
      siteId,
      files[uid].referenceNumber,
      files[uid].chunkCounter,
      files[uid].chunkCount,
      (event) => {
        const currentProgress =
          (event.loaded / event.total / files[uid].chunkCount) *
          FULL_PERCENTAGE;
        const percent = (files[uid].percent ?? 0) + currentProgress;
        onProgress({ percent }, file);
        updatePercent(uid, percent, currentProgress);
      },
      files[uid].cancelTokenSource?.token
    )
      .then((res: any) => {
        if (res.error) throw res.error;
        setReferenceNumber(uid, res.data.referenceNumber);
        if (files[uid].chunkCounter === files[uid].chunkCount) {
          isCompleted(uid, true);
          onSuccess(res.data.success, file);
        } else {
          increaseChunkCounter(uid);
        }
      })
      .catch((error) => {
        // Ignore cancellation errors
        if (error.code === ERROR_TYPE_CANCEL) {
          return;
        }  
        const { statusCode, message = 'Something went wrong.' } = error;
        // Check if the error is a client error (4xx) where we don't want to retry
        if (statusCode >= 400 && statusCode < 500) {
          // No retries for 4xx errors
          resetPercent(uid);
          const errorMessage = statusCode === 409 ? LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE : message || 'An error occurred during upload.';

          updateError(uid, { name: uid, message: errorMessage });

        } else {
          const currentRetryCount = files[uid].retryCount ?? 0;
          
          // Proceed with retry logic for non-4xx errors
          if (currentRetryCount < MAX_RETRY_COUNT) {
            if (currentRetryCount === 1) {
              warningNotification([""], RETRYING_MESSAGE);
            }
            incrementRetryCount(uid);
            changeToBeProceedStatus(uid, false);
          } else {
            resetPercent(uid);
            updateError(uid, { name: uid, message: message || UPLOAD_FAILED_MESSAGE });

          }
        }
        onError(error);
      });
  };

  const requestUpload = (options: any) => {
    const { file } = options;
    const castedFile = (file as unknown) as UploadFile;
    if (!validateSize(file.size)) {
      add(castedFile, options, Error(FILE_IS_TOO_LARGE));
      return false;
    }
    if (!validateFileName(file.name)) {
      add(castedFile, options, Error(FILE_INVALID));
      return false;
    }
    add(castedFile, options);
    return true;
  };

  const onDelete = (uid: string) => {
    if (portalFiles?.length === 0) {
      // Cancel any ongoing uploads
      if (files[uid].cancelTokenSource) {
        try {
          files[uid].cancel?.();
        } catch (err) {
          logger.error('onDelete', 'Uploader', `Error canceling upload for file with uid ${uid}: ${err}`);
        }
      }

      if (!files[uid].completed) {
        removeFiles(siteId, files[uid].referenceNumber ? [files[uid].referenceNumber] : [], true);
      } else {
        removeFiles(siteId, files[uid].referenceNumber ? [files[uid].referenceNumber] : []);
      }
      if (filesCount === 1) {
        setViewCompleteModal(false);
        setUploadType(UNSET_UPLOAD);
      }
      remove(uid);
    }
  };

  const onDeleteAll = () => {
    confirmDiscard(() => {
      if (portalFiles?.length === 0) {
        // Cancel all ongoing uploads
        Object.entries(files).forEach(([uid, fileInfo]) => {
          if (fileInfo.cancelTokenSource) {
            try {
              fileInfo.cancel?.();
            } catch (err) {
              logger.error('onDeleteAll', 'Uploader', `Error canceling upload for file with uid ${uid}: ${err}`);
            }
          }
        });

        const completedReferences = Object.entries(files)
          .filter(([_, info]) => info.completed && info.referenceNumber)
          .map(([_, info]) => info.referenceNumber);
        const chunkedReferences = Object.entries(files)
          .filter(([_, info]) => !info.completed && info.referenceNumber)
          .map(([_, info]) => info.referenceNumber);

        if (completedReferences.length) {
          removeFiles(siteId, completedReferences);
        }
        if (chunkedReferences.length) {
          removeFiles(siteId, chunkedReferences, true);
        }
        setViewCompleteModal(false);
        removeAll();
        setUploadType(UNSET_UPLOAD);
      }
      onCloseModal?.();
    }, portalFiles?.length !== 0);
  };

  const onRetry = (uid: string) => {
    if (files[uid]) {
      if (files[uid].error?.message === FILE_IS_TOO_LARGE) {
        updateError(uid, {
          name: FILE_IS_TOO_LARGE,
          message: FILE_IS_TOO_LARGE,
        });
      } else {
        retry(uid);
        fileUpload(uid);
      }
    }
  };

  const onRetryAll = () => {
    retryAll();
  };

  const onComplete = () => {
    if (uploadType === UNSET_UPLOAD && filesCount === 1) {
      setUploadType(SAME_PROPERTY_UPLOAD);
      return;
    }
    setViewCompleteModal(true);
  };

  const onSaveSuccess = (successedFiles: FileRecord[]) => {
    if (portalFiles?.length === 0) {
      successedFiles.forEach((file) => {
        const [uid] = Object.entries(files).find(([fileUid, info]) => {
          return info.referenceNumber === file.referenceNumber;
        })!;
        remove(uid);
      });

      if (filesCount === successedFiles.length) {
        setUploadType(UNSET_UPLOAD);
        setViewCompleteModal(false);
      }
    }
    onCloseModal?.();
  };

  const validateFiles = (file: RcFile, fileList: RcFile[]) => {
    if (!validateSize(file.size)) {  
      if (!isShowingError.current) {    
      notification.error({
        message: FILE_IS_TOO_LARGE,
        onClose: () => (isShowingError.current = false),
        className: "yjErrorMsg",
      }); 
    }    
      return false;  
    }

    const fileExtension = `.${file.name.split(".").pop()?.toLowerCase()}`;
    if (!allowedExtensions.includes(fileExtension)) { 
        if (!isShowingError.current) {
      notification.error({
        message: UN_SUPPORTED_FILE_FORMAT,
        onClose: () => (isShowingError.current = false),
        className: "yjErrorMsg",
      });  
    }   
      return false;
    }

    if (fileList.length > MAXIMUM_FILE_COUNT) {
      if (!isShowingError.current) {
        isShowingError.current = true;
        notification.error({
          message: TOO_MANY_FILES,
          onClose: () => (isShowingError.current = false),
          className: "yjErrorMsg",
        });
      }
      return false;
    }
    return true;
  };

  const renderDetailsModal = () => {
    if (uploadType === URL_UPLOAD) {
      return (
        <UrlDetailsModal
          uploadType={uploadType}
          siteId={siteId}
          binderId={binderId}
          onClose={() => setUploadType(UNSET_UPLOAD)}
          onFinish={() => setUploadType(UNSET_UPLOAD)}
        />
      );
    }
    if (hasUploadingFiles || portalFiles?.length > 0) {
      return (
        <FileDetailsModal
          uploadType={uploadType}
          siteId={siteId}
          binderId={binderId}
          files={
            portalFiles?.length > 0
              ? portalFiles
              : mapToFileList(files, uploadType)
          }
          onClose={() => onDeleteAll()}
          onRemove={(referenceNumber) => {
            onDelete(
              portalFiles?.length === 0
                ? Object.entries(files).find(
                    ([_, info]) => info.referenceNumber === referenceNumber
                  )?.[0]!
                : referenceNumber
            );
          }}
          onSaveComplete={onSaveSuccess}
          forPortalFiles={portalFiles?.length > 0}
          onLastFile={() => onCloseModal?.()}
          fileAreaSelection={fileAreaSelection}
        />
      );
    }
    return null;
  };

  return (
    <>
      {portalFiles?.length === 0 && (
        <DragAndDrop
          style={{ display: hasUploadingFiles ? "none" : "block" }}
          customRequest={requestUpload}
          beforeUpload={validateFiles}
          onURLOptionSelect={(option) => setUploadType(option)}
          hasUrlUploadPermission={hasUrlUploadPermission}
        />
      )}
      {hasUploadingFiles && portalFiles?.length === 0 && (
        <UploadProgress
          files={files}
          onRetry={onRetry}
          onRetryAll={onRetryAll}
          onDelete={onDelete}
          onDeleteAll={onDeleteAll}
          onComplete={onComplete}
        />
      )}
      <SelectUploadOptionModal
        destroyOnClose={true}
        visible={
          (viewCompleteModal &&
            hasUploadingFiles &&
            uploadType === UNSET_UPLOAD) ||
          portalFiles?.length > 1
        }
        onCancel={onDeleteAll}
        onOptionSelect={(option) => setUploadType(option)}
      />
      {(uploadType !== UNSET_UPLOAD || portalFiles?.length === 1) &&
        renderDetailsModal()}
    </>
  );
};

const mapToFileList = (
  fileList: FileList,
  uploadType: number
): FileRecord[] => {
  return Object.entries(fileList).map(([uid, info]) => {
    return {
      referenceNumber: info.referenceNumber || "",
      title: info.name,
      checked: uploadType === SAME_PROPERTY_UPLOAD ? true : false,
      error: validateTitle(info.name),
    };
  });
};
