import React, { useEffect, useState } from 'react';
import { useParams } from 'react-router-dom';
import { Row, Col, Form, Select, Input, Switch, Button, Drawer } from 'antd';
import logger from '@app/utils/logger';
import renderOptions from '@app/components/forms/utils/renderOptions';
import { FolderTree } from '@app/components/FolderTree';
import { getBinderFileAreaNodes, saveFileArea, updateFileAreaByBinderId } from '@app/api/fileAreaService';
import InfinitySelect, { InfinitySelectGetOptions } from '@app/components/InfinitySelect';
import { getInfiniteRecords } from '@app/api/infiniteRecordsService';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import styles from './index.module.less';
import config from '@app/utils/config';
import { getYears } from '@app/api/fileAreaService';
import { IFolderTreeResponse } from '@app/types/FileAreaFolderTreeTypes';
import { IFolder } from '@app/types/FileAreaFolderTreeTypes';
import { FormInstance } from 'antd/lib/form';
import { required } from '@app/components/forms/validators';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import { CloseOutlined } from '@ant-design/icons';
import SubmitButton from '@app/components/SubmitButton';
import { useDispatch, useSelector } from 'react-redux';
import { RootState } from '@app/redux/reducers/state';
import { UpdateFunctionalFlowDataLoading } from '@app/redux/actions/functionalFlowActions';

export interface CreateUpdateFileAreaProps {
  form: FormInstance;
  onFileFileAreaFieldChange?: (fileAreaDetailsList: any) => void;
  isFormEditBinder: boolean;
  visible: boolean;
  onClose: () => void;
  onSuccess: () => void;
}
const LIMIT = 10;

export default ({ form, isFormEditBinder, visible, onClose, onSuccess }: CreateUpdateFileAreaProps) => {
  const { siteId } = useParams<any>();
  const [years, setYears] = useState<number[]>([]);
  const dispatch = useDispatch();
  const { isLoading } = useSelector((state: RootState) => state.functionalFlow);

  const [selectedTemplateJobType, setSelectedTemplateJobType] = useState('');
  const [binderFileAreaNodes, setBinderFileAreaNodes] = useState<IFolderTreeResponse | null>(null);
  const [fileAreaDetails, setFileAreaDetails] = useState<any>();
  const [jobIdKey, setJobIdKey] = useState('');
  const [templateKey, setTemplateKey] = useState(0);
  const [selectedTemplate, setSelectedTemplate] = useState('');

  // Mapping form field values to variables
  const formId = form.getFieldValue('id');
  const formFolder = form.getFieldValue('folder')
  const formTemplate = form.getFieldValue('template');
  const formFileAreaName = form.getFieldValue('fileAreaName');
  const formJob = form.getFieldValue('jobIds');
  const formYear = form.getFieldValue('year');
  const savedJobs = form.getFieldValue('savedJobDetails') || [];//only for edit binder form

  const defaultFolder = true ? (formFolder ? formFolder.toString() : '1') : '';
  const [selectedFolder, selectedFolders] = useState(defaultFolder);

  const resetBinderCreateUpdateForm = () => {
    form.resetFields();
    onClose();
    setBinderFileAreaNodes(null);
    setFileAreaDetails(null);
  };

  const handleOnCloseForm = () => {
    resetBinderCreateUpdateForm();
  }

  const handleCreateUpdateBinder = () => {
    if (isFormEditBinder && (!fileAreaDetails || Object.keys(fileAreaDetails).length === 0)) {
      return;
    }
    const { template, jobIds, fileAreaName, status, year, id } = fileAreaDetails;
    const statusType = status ? 1 : 2;
    if (isFormEditBinder) {
      const updateData = {
        binderName: fileAreaName,
        binderStatus: statusType,
        binderYear: year,
        binderId: id,
        jobIds: jobIds,
      };
      dispatch(UpdateFunctionalFlowDataLoading(true));
      updateFileAreaByBinderId(updateData)
        .then((res) => {
          if (res.status === 200 || res) {
            successNotification([''], 'File Area Updated Successfully');
            onSuccess();
          } else {
            errorNotification([''], 'File Area Update Failed');
          }
        })
        .catch((error) => {
          errorNotification([''], error?.message ||'File Area Update Failed');
          logger.error('File Management', 'handleCreateUpdateBinder', error);
        })
        .finally(() => {
          dispatch(UpdateFunctionalFlowDataLoading(false));
          resetBinderCreateUpdateForm();
        });
    } else {
      dispatch(UpdateFunctionalFlowDataLoading(true));
      saveFileArea(fileAreaName, siteId, template, jobIds, statusType, year)
        .then((res) => {
          if (res) {
            successNotification([''], 'File Area Created Successfully');
            onSuccess();
          } else {
            errorNotification([''], 'File Area Creation Failed');
          }
        })
        .catch((error) => {
          errorNotification([''], error?.message ||'File Area Creation Failed');
          logger.error('File Management', 'handleCreateUpdateBinder', error);
        })
        .finally(() => {
          dispatch(UpdateFunctionalFlowDataLoading(false));
          resetBinderCreateUpdateForm();
        });
    }
  };

  // Get paginated File Area template records
  const getPaginatedbinderTemplateRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    if (searchValue) {
      transformFilters.search = searchValue;
    }
    const getClientIdParameters = {
      limit: LIMIT,
      offset: page - 1,
      isHidden: false,
      TemplateStatus: 1,
      ...transformFilters,
    };

    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].binderTemplates, getClientIdParameters)
      .then((res: any) => {
        if (res.data) {
          // Auto-select if there's only one template and no selection yet
          if (res.data.records && res.data.records.length === 1 && !selectedTemplate && !isFormEditBinder) {
            const singleTemplate = res.data.records[0];
              setSelectedTemplate(singleTemplate.id);
              const transformedFolders = transformFolders(singleTemplate.parentNodes);
              setBinderFileAreaNodes({
                siteId,
                siteName: formFileAreaName || "--",
                siteStatusId: 1,
                folders: transformedFolders,
              });
              form.setFieldsValue({ template: singleTemplate.id });
              setSelectedTemplateJobType(singleTemplate.binderTemplateJobType?.value);
              setTemplateKey(prevKey => prevKey + 1);
          }
          return res.data;
        } else {
          return [];
        }
      })
      .catch((error: any) => {
        return [];
      });
  };

  // Get paginated jobs records
  const getPaginatedJobsRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const getClientIdParameters = {
      limit: LIMIT,
      clientCode: siteId,
      offset: page - 1,
      ...transformFilters,
    };

    if (selectedTemplateJobType) {
      getClientIdParameters['jobTypeId'] = selectedTemplateJobType;
    }

    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].getAvailableJobs, getClientIdParameters)
      .then((res: any) => {
        if (res.data) {
          let records = res.data.records;
          if (isFormEditBinder) {
            const savedJobsToAdd = savedJobs.filter(
              (savedJob: any) => !records.some((record: any) => record.id === savedJob.id)
            );
            records = [...savedJobsToAdd, ...records];
          }
          return { ...res.data, records };
        } else {
          return [];
        }
      })
      .catch((error: any) => {
        logger.error('Error fetching jobs:', 'getPaginatedJobsRecords', error);
        return [];
      });
  };

  const transformFolders = (folders: any[]): IFolder[] => {
    const primaryFolders = folders.filter((folder) => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);

    return primaryFolders.map((folder) => ({
      id: folder.id,
      name: folder.name,
      subFolders: folder.childNodes.map((child: any) => ({
        id: child.id,
        name: child.name,
        subFolders: [],
        retention: child.retention || 0,
      })),
    }));
  };

  useEffect(() => {
    // Fetching folder structure if its edit File Area form
    const fetchBinderNodes = async () => {
      try {
        const response = await getBinderFileAreaNodes(formId);
        const transformedFolders = transformFolders(response.data.folders);
        setBinderFileAreaNodes({
          siteId,
          siteName: response.data.name,
          siteStatusId: 1,
          folders: transformedFolders,
        });
      } catch (error) {
        logger.error('Error fetching File Area nodes:', 'fetchBinderNodes', error);
      }
    };

    const fetchYears = async () => {
      try {
        getYears().then((response) => {
          if (response?.data) {
            setYears(response.data);
          }
        });
      } catch (e) {
        logger.error('File Area Module', 'Get Years Error', e);
      }
    };

    fetchYears();
    if (isFormEditBinder) fetchBinderNodes();
  }, [isFormEditBinder, formId]);

  useEffect(() => {
    if (visible) {
      // Using Date.now() to generate a unique key for the InfinitySelect component in edit mode
      setJobIdKey(Date.now().toString());

      if (!isFormEditBinder) {
        setSelectedTemplate('');
        setTemplateKey(prevKey => prevKey + 1);
      }
    }
  }, [visible, isFormEditBinder]);


  return (
    <Drawer
      title={isFormEditBinder ? 'Update File Area' : 'Create File Area'}
      placement="right"
      visible={visible}
      className={'yjDrawerPanel'}
      width={700}
      closeIcon={<CloseOutlined data-testid="template-drawer-close-icon" />}
      onClose={handleOnCloseForm}
      footer={[
        <div style={{ textAlign: 'right' }}>
          <Button key="cancel" type="default" onClick={handleOnCloseForm} disabled={isLoading} data-testid="template-drawer-close">
            Cancel
          </Button>
          <SubmitButton
            key="createUpdateBinder"
            type="primary"
            onClick={handleCreateUpdateBinder}
            disabled={!formTemplate || !formFileAreaName || !formYear}
          >
            {isFormEditBinder ? 'Update' : 'Create'}
          </SubmitButton>
        </div>
      ]}
    >
      <div className={styles.yjModalContentWrapper}>
        <Form form={form} key="CreateUpdateBinderForm" layout="vertical" onValuesChange={(_changedValues: any, allValues: any) => {
          const updatedValues = {
            ...allValues,
            template: allValues.template || formTemplate,
            jobIds: allValues.jobIds || formJob,
          };
          setFileAreaDetails(updatedValues);
        }}>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item style={{ display: 'flex' }} label="Template" name="template" rules={[required]}>
                {isFormEditBinder ? (
                  <Input value={formTemplate} disabled />
                ) : (
                  <InfinitySelect
                    key={`template-${templateKey}`}
                    returnObject={true}
                    getPaginatedRecords={(page, method, searchValue) => getPaginatedbinderTemplateRecords(page, method, searchValue)}
                    formatValue={(value) => {
                      return `${value.name}`;
                    }}
                    notFoundContent="No templates Available"
                    notLoadContent="Failed to load values in templates dropdown"
                    onChange={(e: any) => {
                      const transformedFolders = transformFolders(e.parentNodes);
                      setBinderFileAreaNodes({
                        siteId,
                        siteName: formFileAreaName || "--",
                        siteStatusId: 1,
                        folders: transformedFolders,
                      });
                      form.setFieldsValue({ template: e.id });
                      setSelectedTemplateJobType(e.binderTemplateJobType?.value);
                      setSelectedTemplate(e.id);
                    }}
                    placeholder="Please Select a Template"
                    waitCharCount={3}
                    defaultValues={selectedTemplate || undefined}
                  />
                )}
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item label="Job(s)" name="jobIds">
                <InfinitySelect
                  key={isFormEditBinder ? jobIdKey : `job-${selectedTemplateJobType}`}
                  getPaginatedRecords={(page, method, searchValue) => getPaginatedJobsRecords(page, method, searchValue)}
                  formatValue={(value) => {
                    return `${value.displayName}`;
                  }}
                  notFoundContent="No Jobs Available"
                  notLoadContent="Failed to load values in Jobs dropdown"
                  onChange={(e: any) => {
                    form.setFieldsValue({ jobIds: e });
                  }}
                  placeholder="Please Select a Job"
                  waitCharCount={3}
                  mode="multiple"
                  returnObject={true}
                  defaultValues={isFormEditBinder ? formJob : undefined}
                />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item name="fileAreaName" label="File Area Name" rules={[required]}>
                <Input
                  placeholder="Please Enter File Area Name"
                  value={formFileAreaName}
                  onBlur={(event) => {
                    if (binderFileAreaNodes && event.target.value) {
                      setBinderFileAreaNodes({
                        ...binderFileAreaNodes,
                        siteName: event.target.value
                      });
                    }
                  }}
                />
              </Form.Item>
            </Col></Row>
          <Row gutter={24}>
            <Col span={12}>
              <Form.Item valuePropName="checked" label={'Active'} name="status" initialValue={true}>
                <Switch />
              </Form.Item>
            </Col>

            <Col span={12}>
              <Form.Item label="Year" name="year" rules={[required]}>
                <Select style={{ width: '100%' }} getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}>
                  {years && years && renderOptions(years)}
                </Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item label="Folder" name="folder">
                <div className={styles.yjFileFinderWrapper}>
                  <div className={styles.yjFileFinderTreeWrapper}>
                    <FolderTree
                      showTitle={false}
                      maxHeight="255px"
                      data={binderFileAreaNodes}
                      onSelectFolder={(keys, info) => {
                        selectedFolders(keys);
                        form.setFieldsValue({ folder: keys });
                      }}
                      disableRoot={true}
                      controlSelection={true}
                      autoExpandParent={true}
                      selectedKeys={[selectedFolder.toString()]}
                      disabled={false}
                      onClick={() => { }}
                    />
                  </div>
                </div>
              </Form.Item>
            </Col>
          </Row>
          <Form.Item name="id" initialValue={formId} hidden>
            <Input type="hidden" />
          </Form.Item>
        </Form>
      </div>
    </Drawer>
  );
};
