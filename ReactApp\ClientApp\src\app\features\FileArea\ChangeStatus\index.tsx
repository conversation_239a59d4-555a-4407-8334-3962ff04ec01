import React, { useEffect, useState } from "react";
import { Select, Row, Col, Form } from "antd";
import { FormInstance } from "antd/lib/form";

import styles from "./index.module.less";
import { IFile } from "@app/types/fileAreaTypes";
import SelectedFilesGrid, { ColumnConfig } from "../SelectedFilesGrid";
import { getFileStatuses } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { errorNotification } from "@app/utils/antNotifications";

export interface ChangeStatusProps {
  selectedFiles: IFile[];
  onNewStatusSelect: (event: number) => void;
  form: FormInstance;
  onFinish: () => void;
  onFilesChange: (
    fileList: IFile[],
    onMounted?: boolean,
    onAllRemoved?: boolean
  ) => void;
}
const { Option } = Select;
const changeStatusColumnConfigs: ColumnConfig[] = [
  { title: "", dataIndex: "remove", key: "remove", width: 40 },
  { title: "Id", dataIndex: "id", key: "id" },
  { title: "Title", dataIndex: "title", key: "title", ellipsis: true },
  { title: "Current Status", dataIndex: "status", key: "status" },
];
export default (props: ChangeStatusProps) => {
  const [stateListState, setStateListState] = useState<any[]>([]);
  const [selectedFileList, setSelectedFileList] = useState<IFile[]>(
    props.selectedFiles
  );

  useEffect(() => {
    props.onFilesChange(props.selectedFiles, true, false);
    getFileStatuses()
      .then((response) => {
        setStateListState(response.data);
      })
      .catch((error) => {
        logger.error("AssignOption", "Retrieve Status List", error);
        errorNotification([""], "Unable to Retrieve Status List");
      });
  }, []);

  const handleFilesChange = (fileList: any[]) => {
    if (fileList.length > 0) {
      setSelectedFileList(fileList);
      props.onFilesChange(fileList, false, false);
    } else {
      props.onFilesChange([], false, true);
    }
  };

  const filterOption = (input: any, option: any) => {
    return option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  return (
    <>
      <div className={styles.yjModalContentWrapper}>
        <div className={styles.yjChangeStatusGrid}>
          <SelectedFilesGrid
            onFilesChange={handleFilesChange}
            columnConfigs={changeStatusColumnConfigs}
            dataList={selectedFileList}
          />
        </div>

        <div className={styles.yjChangeStatusNew}>
          <Form
            onFinish={props.onFinish}
            form={props.form}
            size="middle"
            name="basic"
            layout="horizontal"
          >
            <Row gutter={24}>
              <Col span={8}></Col>
              <Col span={16}>
                <Form.Item label={"New Status"} name="newStatus" colon={false}>
                  {" "}
                  <Select
                    className={styles.yjChangeStatusSelectNew}
                    onChange={(value: any) => props.onNewStatusSelect(value)}
                    showSearch
                    notFoundContent={`No Results Found`}
                    getPopupContainer={(trigger) =>
                      trigger.parentNode as HTMLElement
                    }
                    filterOption={filterOption}
                  >
                    {stateListState.map((state: any) => (
                      <Option key={state.value} value={state.value}>
                        {state.name}
                      </Option>
                    ))}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Form>
        </div>
      </div>
    </>
  );
};
