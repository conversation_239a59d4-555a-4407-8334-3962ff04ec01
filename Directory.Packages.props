<Project>

  <PropertyGroup>
    <ManagePackageVersionsCentrally>true</ManagePackageVersionsCentrally>
    <CentralPackageTransitivePinningEnabled>true</CentralPackageTransitivePinningEnabled>

    <IrisMcpCore>25.73.10</IrisMcpCore>
    <IrisMcpProviders>25.85.2</IrisMcpProviders>
    <IrisMcpEnterprise>25.85.3</IrisMcpEnterprise>
  </PropertyGroup>

  <ItemGroup>
    <PackageVersion Include="Microsoft.AspNetCore.SpaProxy" Version="8.0.8" />
    <PackageVersion Include="Microsoft.Extensions.Diagnostics.HealthChecks" Version="8.0.8" />
  </ItemGroup>

  <ItemGroup>
    <PackageVersion Include="App.Metrics.AspNetCore" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.AspNetCore.Endpoints" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Formatters.Json" Version="4.3.0" />
    <PackageVersion Include="App.Metrics.Formatters.Prometheus" Version="4.3.0" />
  </ItemGroup>

</Project>