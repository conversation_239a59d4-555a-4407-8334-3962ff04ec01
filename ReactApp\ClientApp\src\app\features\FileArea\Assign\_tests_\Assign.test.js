import React from "react";
import { Form, Input, Select } from "antd";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { Provider } from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import AssignOption from "../index";
import initTestSuite from "@app/utils/config/TestSuite";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const CustomAssignOption = (props) => {
  const INITIAL_STATE = {
    fileArea: {
      fileAreaSettings: {
        internalFileStatusUpdate: true,
      },
    },
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <AssignOption {...props} />
    </ReduxProvider>
  );
};

jest.mock("../index.module.less", () => ({
  yjModalContentWrapper: "yjModalContentWrapper",
  yjAssignFilesFormWrapper: "yjAssignFilesFormWrapper",
  yjAssignFilesForm: "yjAssignFilesForm",
  yjAssignFilesFormRowItem: "yjAssignFilesFormRowItem",
}));

describe("Assign Option Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const aoComponent = mount(<CustomAssignOption />);
    expect(aoComponent.html()).not.toBe(null);
  });

  it("should create and match to snapshot", () => {
    const aoComponent = renderer.create(<CustomAssignOption />).toJSON();
    expect(aoComponent).toMatchSnapshot();
  });

  it("should render with props", () => {
    const aoComponent = mount(
      <CustomAssignOption
        siteId={"xxx"}
        selectedFiles={[]}
        onClosePopup={() => {}}
        onFormEvents={() => {}}
      />
    );
    expect(aoComponent.html()).not.toBe(null);
  });

  it("should render with props are null", () => {
    const aoComponent = mount(
      <CustomAssignOption
        formRef={null}
        siteId={null}
        selectedFiles={null}
        onClosePopup={null}
        onFormEvents={null}
      />
    );
    expect(aoComponent.html()).not.toBe(null);
  });

  it("should render with props are undefined", () => {
    const aoComponent = mount(
      <CustomAssignOption
        siteId={undefined}
        selectedFiles={undefined}
        onClosePopup={undefined}
        onFormEvents={undefined}
      />
    );
    expect(aoComponent.html()).not.toBe(null);
  });

  it("should have a form", () => {
    const component = mount(
      <CustomAssignOption
        siteId={"xxx"}
        selectedFiles={[]}
        onClosePopup={() => {}}
        onFormEvents={() => {}}
      />
    );
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 3 form items", () => {
    const component = mount(
      <CustomAssignOption
        siteId={"xxx"}
        selectedFiles={[]}
        onClosePopup={() => {}}
        onFormEvents={() => {}}
      />
    );
    expect(component.find(Form.Item)).toHaveLength(3);
  });

  it("should have 2 select", () => {
    const component = mount(
      <CustomAssignOption
        siteId={"xxx"}
        selectedFiles={[]}
        onClosePopup={() => {}}
        onFormEvents={() => {}}
      />
    );
    expect(component.find(Select)).toHaveLength(2);
  });

  it("should have 1 text area", () => {
    const component = mount(
      <CustomAssignOption
        siteId={"xxx"}
        selectedFiles={[]}
        onClosePopup={() => {}}
        onFormEvents={() => {}}
      />
    );
    expect(component.find(Input.TextArea)).toHaveLength(1);
  });

  it("should have div elements", () => {
    const component = mount(
      <CustomAssignOption
        siteId={"xxx"}
        selectedFiles={[]}
        onClosePopup={() => {}}
        onFormEvents={() => {}}
      />
    );
    expect(component.find(".yjModalContentWrapper")).toHaveLength(1);
    expect(component.find(".yjAssignFilesFormWrapper")).toHaveLength(1);
    expect(component.find(".yjAssignFilesForm")).toBeDefined();
    expect(component.find(".yjAssignFilesFormRowItem")).toBeDefined();
  });
});
