import http, { httpVerbs } from "@app/utils/http";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";

export const getGenericDashboardRecords = (
  endpoint: string,
  options: any
): Promise<HTTPResponse<any>> => {
  const mappedOptions: any = {
    limit: options.pagination.pageSize,
    offset: options.pagination.current - 1,
  };

  if (options.search) {
    mappedOptions.search = options.search;
  }

  if (options.queryParameters) {
    for (const key in options.queryParameters) {
      if (options.queryParameters.hasOwnProperty(key)) {
        mappedOptions[key] = options.queryParameters[key];
      }
    }
  }

  const requestObject = {
    method: httpVerbs.GET,
    url: `${endpoint}`,
    params: mappedOptions,
    headers: {
      "content-type": "application/json",
    },
    data: options.filters,
  };

  return http(requestObject);
};
