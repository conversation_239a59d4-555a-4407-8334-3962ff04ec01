import React from 'react';
import RouteConfigType from '@app/types/RouteConfigType';
import RouteSwitch from '@app/routes/RouteSwitch';

const FileAreas = React.lazy(() => import('@pages/FileAreas'));

const FileArea = React.lazy(() => import('@pages/FileAreas/FileArea'));

const PortalFiles = React.lazy(() => import('@pages/FileAreas/PortalFiles'));

const PortalFilesHistory = React.lazy(() => import('@pages/FileAreas/PortalFiles/PortalFilesHistory'));
const History = React.lazy(() => import('@pages/FileAreas/FileArea/History'));

const ClientFileAreas = React.lazy(() => import('@pages/FileAreas/ClientFileAreas'));

const fileAreaRoutes: RouteConfigType[] = [
  {
    component: FileAreas,
    path: '/client-file-area',
    title: 'Client File Areas',
    exact: true,
    guard: [],
    useInBreadcrumbs: false,
  },
  {
    component: ClientFileAreas,
    path: '/client-file-area/:siteId/:siteName/:channelId',
    title: '',
    exact: true,
    guard: [],
    useInBreadcrumbs: true,
  },
  {
    component: History,
    path: '/client-file-area/:siteId/file-area/:siteName/:binderId/:binderName/:channelId/history/:fileId',
    title: 'History',
    exact: false,
    useInBreadcrumbs: false,
    guard: [],
  },
  {
    component: PortalFiles,
    path:'/client-file-area/:siteId/file-area/:siteName/:channelId/portal',
    title: '',
    exact: true,
    guard: [],
    useInBreadcrumbs: true,
  },
  {
    component: RouteSwitch,
    path: '/client-file-area/:siteId/file-area/:siteName/:channelId',
    title: `Site`,
    exact: false,
    guard: [],
    useInBreadcrumbs: true,
    dynamicBreadcrumbIndex: 0,
    routes: [
      {
        component: RouteSwitch,
        path: '/client-file-area/:siteId/file-area/:siteName/:binderId/:binderName/:channelId/portal',
        title: 'Portal',
        exact: false,
        guard: [],
        useInBreadcrumbs: true,
        routes: [
          {
            component: PortalFilesHistory,
            path: '/client-file-area/:siteId/file-area/:siteName/:binderId/:binderName/:channelId/portal/history',
            title: 'History',
            exact: true,
            guard: [],
            useInBreadcrumbs: true,
          },
        ],
      },
      {
        component: FileArea,
        path: '/client-file-area/:siteId/file-area/:siteName/:binderId/:binderName/:channelId',
        title: 'File Area',
        exact: true,
        guard: [],
        useInBreadcrumbs: true,
      },
    ],
  },
];

export default fileAreaRoutes;