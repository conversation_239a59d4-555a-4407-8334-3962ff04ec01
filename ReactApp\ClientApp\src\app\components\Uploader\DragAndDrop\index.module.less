@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';
@file-path: '../../../../styles/';

.yjDragDropWrapper {

  .yjDragDrop {
    background: fade(@color-bg-drag-drop-container, 6%);
    border: 1.2px dashed @color-border-drag-drop-container;
    padding: 1px;
    position: relative;
    width: 100%;

    &:hover {
      border: 1.2px dashed @color-primary;
      cursor: pointer;
    }

    .yjDragDropText {
      color: @color-font-drag-drop-container;
      font-size: @font-size-base/1.2;
      line-height: 2;
      text-transform: @yj-transform;

      .font-mixin(@font-primary, @yjff-semibold);
    }

    .yjUploadBrowseButton {
      background: @color-bg-browse-btn;
      color: @color-font-browse-btn;
      height: 28px;
      margin: 5px 0;
    }

    .yjUploadLinkButton {
      border-radius: 5px;
      cursor: pointer;
      font-size: @font-size-base/1.2;
      margin: 5px 0;
      text-decoration: @yj-underline;
      text-transform: @yj-transform;

      .font-mixin(@font-primary, @yjff-bold);
    }
  }
}

.yjMoverArrow {
  align-items: center;
  display: flex;
  justify-content: center;
}

.seperator {
  border-top: 1px solid @color-border;
  padding: 15px;
}

.yjClose {
  background: none;
  border: none;
  box-shadow: none;
  color: @color-primary;
  float: right;
  position: absolute;
  right: 5px;
  top: 5px;

  &:active,
  &:focus,
  &:hover,
  &:visited {
    background: none;
  }
}

.yjBtnMiniFileUploader {
  border: 1px solid @color-accent-secondary;
  color: @color-accent-secondary;
  text-transform: @yj-transform;

  &:active,
  &:focus,
  &:hover,
  &:visited {
    border: 1px solid @color-accent-secondary;
    color: @color-accent-secondary;
  }
}

@media ( max-width: @breakpoint-tablet ) {

  .yjDragDrop {
    box-sizing: content-box;
    height: 24vh;
  }
}
