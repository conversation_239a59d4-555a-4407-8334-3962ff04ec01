import React from "react";
import renderer from "react-test-renderer";
import { DonutChart } from "bizcharts";
import {shallow} from "enzyme";
import 'jest-canvas-mock';

import initTestSuite from "@app/utils/config/TestSuite";
import AllocatedSpaceContent from "../index";


describe("Allocated Space Content Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const ascComponent = shallow(<AllocatedSpaceContent/>);
        expect(ascComponent.html()).not.toBe(null);
    });

    it("should create and match to snapshot", () => {
        const ascComponent = renderer.create(<AllocatedSpaceContent/>).toJSON();
        expect(ascComponent).toMatchSnapshot();
    });

    it("should have a DonutChart component", () => {
        const ascComponent = shallow(<AllocatedSpaceContent/>);
        expect(ascComponent.find(Donut<PERSON>hart)).toHaveLength(1);
    });
});
