import {Form, Row, Select} from 'antd';
import React, { useEffect, useState } from 'react';
import styles  from "./index.module.less";
const { Option } = Select;

const DocumentType_N = ({ index,value="CLNT", onChange }:any) => {
	const onSelectChange = (value:string) => {
		onChange(value);
	};
	return (<Form.Item label={'Document Type'} name={`${index}-DocumentType`} initialValue={value}>
		<Select placeholder="Select Type" onChange={onSelectChange}>
			<Option value="CLNT">CLNT</Option>
			<Option value="ACCT">ACCT</Option>
			<Option value="GOVT">GOVT</Option>
			<Option value="K1">K1</Option>
		</Select>
	</Form.Item >)
};

export default DocumentType_N;