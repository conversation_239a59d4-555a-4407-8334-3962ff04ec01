apiVersion: apps/v1
kind: Deployment
metadata:
    name: {{servicename}}
    labels:
        app: {{servicename}}
spec:
    replicas: {{replicas}}
    selector:
        matchLabels:
            app: {{servicename}}
    template:
        metadata:
            labels:
                app: {{servicename}}
                advertisedaddress: {{fqdn}}
            annotations:
              prometheus.io/path: /metrics
              prometheus.io/port: '80'
              prometheus.io/scrape: 'true'
        spec:
            volumes:
              - name: {{configMapName}}
                configMap:
                  name: {{configMapName}}
            containers:
                - name: {{servicename}}
                  image: {{acrpullendpoint}}/{{containerimage}}
                  resources:
                    requests:
                      cpu: "{{cpurequests}}"
                      memory: "{{memoryrequests}}"
                    limits:
                      memory: "{{memorylimits}}"
                      cpu: '{{cpuLimits}}'
                  imagePullPolicy:
                  ports:
                    - name: http
                      containerPort: 80
                      protocol: TCP
                  env:
                  - name: ASPNETCORE_URLS
                    value: "http://*:80"
                  - name: ASPNETCORE_HTTP_PORT
                    value: "80"
                  - name: DOTNET_SHUTDOWNTIMEOUTSECONDS
                    value: "120"
                  volumeMounts:
                    - name: {{configMapName}}
                      mountPath: {{configMapMountPath}}
                      subPath: {{configMapMountSubPath}}
                  lifecycle:
                    preStop:
                      exec:
                        command:
                          - sleep
                          - "90"
                  readinessProbe:
                    httpGet:
                      path: /health/ready
                      port: 80
                    periodSeconds: 4
                    timeoutSeconds: 5
                    failureThreshold: 3
                    initialDelaySeconds: {{readinessProbeInitialDelaySeconds}}
                  livenessProbe:
                    httpGet:
                      path: /health/live
                      port: 80
                    periodSeconds: 4
                    timeoutSeconds: 5
                    failureThreshold: 3
                    initialDelaySeconds: {{livenessProbeInitialDelaySeconds}}
            terminationGracePeriodSeconds: 180
            nodeSelector:
              kubernetes.io/os: {{akshostos}}
            affinity:
                    podAntiAffinity:
                      preferredDuringSchedulingIgnoredDuringExecution:
                        - weight: 100
                          podAffinityTerm:
                            labelSelector:
                              matchExpressions:
                                - key: app
                                  operator: In
                                  values:
                                  - {{servicename}}
                            topologyKey: "kubernetes.io/hostname"

---

apiVersion: v1
kind: Service
metadata:
    name: {{servicename}}
    labels:
        app: {{servicename}}
spec:
    type: {{containerServiceType}}
    ports:
        - port: 80
          targetPort: 80
          protocol: TCP
          name: http
    selector:
        app: {{servicename}}

---

apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
    name: {{servicename}}
    labels:
        app: {{servicename}}
    annotations:
        kubernetes.io/ingress.class: {{ingressclass}}
        kubernetes.io/tls-acme: "true"
        appgw.ingress.kubernetes.io/cookie-based-affinity: "true"
        appgw.ingress.kubernetes.io/cookie-based-affinity-distinct-name: "true"
        cert-manager.io/cluster-issuer: {{tlsissuer}}
        ingress.kubernetes.io/secure-backends: "true"
        cert-manager.io/acme-challenge-type: dns01
        appgw.ingress.kubernetes.io/ssl-redirect: "true"
        appgw.ingress.kubernetes.io/connection-draining: "true"
        appgw.ingress.kubernetes.io/connection-draining-timeout: "30"
        appgw.ingress.kubernetes.io/request-timeout: "120"
spec:
  tls:
  - hosts:
      - {{fqdn}}
    secretName: aze-{{aksnamespace}}-{{servicename}}-tls-secret
  rules:
   - host: {{fqdn}}
     http:
       paths:
        - path: /
          pathType: Prefix
          backend:
            service:
              name: {{servicename}}
              port:
                number: 80

---

apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  name: {{servicename}}
  namespace: {{aksnamespace}}
spec:
  minReplicas: {{hpaMinReplicas}}
  maxReplicas: {{hpaMaxReplicas}}
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{servicename}}
  metrics:
    - resource:
        name: memory
        target:
          averageUtilization: {{hpaMemoryAverageUtilization}}
          type: Utilization
      type: Resource
    - resource:
        name: cpu
        target:
          averageUtilization: {{hpaCpuAverageUtilization}}
          type: Utilization
      type: Resource
