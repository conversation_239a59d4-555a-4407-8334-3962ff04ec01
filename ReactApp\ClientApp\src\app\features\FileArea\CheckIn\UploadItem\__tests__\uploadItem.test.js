import React from "react";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { Button, Progress } from "antd";
import Page from "../index";
import { DeleteOutlined, PaperClipOutlined } from "@ant-design/icons";

jest.mock("../index.module.less", () => ({
  yjUploadBlock: "yjUploadBlock",
  yjCheckInProgressBar: "yjCheckInProgressBar",
}));

describe("Upload Item Test Suite", () => {
  it("Upload Item Should Render", () => {
    const component = mount(<Page />);
    expect(component.html()).not.toBe(null);
  });
  it("Upload Item component should render and create the snapshot properly", () => {
    const component = renderer.create(<Page />).toJSON();
    expect(component).toMatchSnapshot();
  });
  it(" Upload Item component should have one Buttton", () => {
    const component = mount(<Page />);
    expect(component.find(Button)).toHaveLength(1);
  });
  it(" Upload Item component should have one Progress Bar", () => {
    const component = mount(<Page percent={100} />);
    expect(component.find(Progress)).toHaveLength(1);
  });
  it(" Upload Item component should have one DeleteOutlined icon", () => {
    const component = mount(<Page />);
    expect(component.find(DeleteOutlined)).toHaveLength(1);
  });
  it(" Upload Item component should have one PaperClipOutlined icon", () => {
    const component = mount(<Page />);
    expect(component.find(PaperClipOutlined)).toHaveLength(1);
  });
  it("should contain Upload Block ", () => {
    const component = mount(<Page />);
    expect(component.find(".yjUploadBlock").length).toBe(1);
  });
  it("should contain Progress bar Class ", () => {
    const component = mount(<Page percent={100} />);
    expect(component.find(".yjCheckInProgressBar").length).toBe(1);
  });
});
