import React from "react";
import { Col, Collapse, Row, Tooltip } from "antd";

import "./index.module.less";
import MyRecentDocuments from "@app/components/Dashboard/MyRecentDocuments/";

const ROW_GUTTER_VALUE_TWELVE = 12;

const { Panel } = Collapse;
export default () => {
  const getMyRecentDocumentsContent = (withDefaultFirstActive: boolean) => {
    return (
      <Col span={12}>
        {/* <Row gutter={12}>
          <Col span={24} className="yjCommonAccordian yjPieChartWrapper">
            {withDefaultFirstActive ? (
              <Collapse defaultActiveKey={1} expandIconPosition={"right"}>
                <Panel  header={
            <Tooltip
              placement="topLeft"
              title={"This feature is coming soon"}
              color="#78bf59"
            >
              My Recent Documents
            </Tooltip>
          } key="1">
                  <MyRecentDocuments />
                </Panel>
                
              </Collapse>
            ) : (
              <Collapse expandIconPosition={"right"}>
                <Panel  header={
            <Tooltip
              placement="topLeft"
              title={"This feature is coming soon"}
              color="#78bf59"
            >
              My Recent Documents
            </Tooltip>
          } key="1">
                  <MyRecentDocuments />
                </Panel>
              </Collapse>
            )}
          </Col>
          <Col span={24} className="yjCommonAccordian yjPieChartWrapper">
            <Collapse expandIconPosition={"right"}>
              <Panel  header={
            <Tooltip
              placement="topLeft"
              title={"This feature is coming soon"}
              color="#78bf59"
            >
              My Recent Documents
            </Tooltip>
          } key="2">
                <MyRecentDocuments />
              </Panel>
            </Collapse>
          </Col>
          <Col span={24} className="yjCommonAccordian yjPieChartWrapper">
            <Collapse expandIconPosition={"right"}>
              <Panel  header={
            <Tooltip
              placement="topLeft"
              title={"This feature is coming soon"}
              color="#78bf59"
            >
              My Recent Documents
            </Tooltip>
          } key="3">
                <MyRecentDocuments />
              </Panel>
            </Collapse>
          </Col>
          <Col span={24} className="yjCommonAccordian yjPieChartWrapper">
            <Collapse expandIconPosition={"right"}>
              <Panel  header={
            <Tooltip
              placement="topLeft"
              title={"This feature is coming soon"}
              color="#78bf59"
            >
              My Recent Documents
            </Tooltip>
          } key="4">
                <MyRecentDocuments />
              </Panel>
            </Collapse>
          </Col>
        </Row> */}
      </Col>
    );
  };

  return (
    <>
      <Row gutter={[ROW_GUTTER_VALUE_TWELVE, ROW_GUTTER_VALUE_TWELVE]}>
        {getMyRecentDocumentsContent(true)}
        {getMyRecentDocumentsContent(false)}
      </Row>
    </>
  );
};
