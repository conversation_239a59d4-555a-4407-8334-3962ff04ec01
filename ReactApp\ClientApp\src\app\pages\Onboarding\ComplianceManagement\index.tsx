import React, { Fragment, useState } from "react";
import { But<PERSON> } from "antd";
import { withRouter } from "react-router-dom";

import Modal from "../../../components/Modal";
import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import ComplianceManagement from "../../../components/forms/ComplianceManagement";

const Page = (props: any) => {
  const [showEditModal, setShowEditModal] = useState(false);

  const handleCancel = (e: any) => {
    setShowEditModal(false);
  };

  return (
    <Fragment>
      <Modal
        visible={showEditModal}
        title={"Compliance Management Details"}
        onCancel={handleCancel}
        footer={[
          <Button type="default" key="back" onClick={handleCancel}>
            cancel
          </Button>,
          <Button key="submit" type="primary" onClick={handleCancel}>
            Manage
          </Button>,
        ]}
      >
        <ComplianceManagement />
      </Modal>

      <PageTitle title={props.title} />
      <PageContent></PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
