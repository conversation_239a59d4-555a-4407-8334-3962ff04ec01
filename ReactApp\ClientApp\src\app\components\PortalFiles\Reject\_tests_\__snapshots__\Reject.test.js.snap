// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Portal Files - Reject Test Suite should render and create snapshot properly 1`] = `
Array [
  <p>
    Do you wish to reject these file(s)? They will be deleted from Portal Files and you won’t be able to revert this action.
  </p>,
  <div
    className="ant-table-wrapper"
  >
    <div
      className="ant-spin-nested-loading"
    >
      <div
        className="ant-spin-container"
      >
        <div
          className="ant-table ant-table-empty ant-table-fixed-header"
        >
          <div
            className="ant-table-container"
          >
            <div
              className="ant-table-header"
              style={
                Object {
                  "overflow": "hidden",
                }
              }
            >
              <table
                style={
                  Object {
                    "tableLayout": "fixed",
                    "visibility": null,
                  }
                }
              >
                <colgroup />
                <thead
                  className="ant-table-thead"
                >
                  <tr>
                    <th
                      className="ant-table-cell"
                      colSpan={null}
                      rowSpan={null}
                      style={Object {}}
                    />
                    <th
                      className="ant-table-cell"
                      colSpan={null}
                      rowSpan={null}
                      style={Object {}}
                    >
                      Request Id
                    </th>
                    <th
                      className="ant-table-cell"
                      colSpan={null}
                      rowSpan={null}
                      style={Object {}}
                    >
                      Request Name
                    </th>
                    <th
                      className="ant-table-cell ant-table-cell-ellipsis"
                      colSpan={null}
                      rowSpan={null}
                      style={Object {}}
                      title="File Title"
                    >
                      File Title
                    </th>
                  </tr>
                </thead>
              </table>
            </div>
            <div
              className="ant-table-body"
              onScroll={[Function]}
              style={
                Object {
                  "maxHeight": "31vh",
                  "overflowY": "scroll",
                }
              }
            >
              <table
                style={
                  Object {
                    "tableLayout": "fixed",
                  }
                }
              >
                <colgroup>
                  <col
                    style={
                      Object {
                        "minWidth": 40,
                        "width": 40,
                      }
                    }
                  />
                </colgroup>
                <tbody
                  className="ant-table-tbody"
                >
                  <tr
                    aria-hidden="true"
                    className="ant-table-measure-row"
                    style={
                      Object {
                        "fontSize": 0,
                        "height": 0,
                      }
                    }
                  >
                    <td
                      style={
                        Object {
                          "border": 0,
                          "height": 0,
                          "padding": 0,
                        }
                      }
                    >
                      <div
                        style={
                          Object {
                            "height": 0,
                            "overflow": "hidden",
                          }
                        }
                      >
                         
                      </div>
                    </td>
                    <td
                      style={
                        Object {
                          "border": 0,
                          "height": 0,
                          "padding": 0,
                        }
                      }
                    >
                      <div
                        style={
                          Object {
                            "height": 0,
                            "overflow": "hidden",
                          }
                        }
                      >
                         
                      </div>
                    </td>
                    <td
                      style={
                        Object {
                          "border": 0,
                          "height": 0,
                          "padding": 0,
                        }
                      }
                    >
                      <div
                        style={
                          Object {
                            "height": 0,
                            "overflow": "hidden",
                          }
                        }
                      >
                         
                      </div>
                    </td>
                    <td
                      style={
                        Object {
                          "border": 0,
                          "height": 0,
                          "padding": 0,
                        }
                      }
                    >
                      <div
                        style={
                          Object {
                            "height": 0,
                            "overflow": "hidden",
                          }
                        }
                      >
                         
                      </div>
                    </td>
                  </tr>
                  <tr
                    className="ant-table-placeholder"
                    style={
                      Object {
                        "display": null,
                      }
                    }
                  >
                    <td
                      className="ant-table-cell"
                      colSpan={4}
                      rowSpan={null}
                      style={Object {}}
                    >
                      No files Available
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div>
    <button
      className="ant-btn ant-btn-primary"
      onClick={[Function]}
      type="button"
    >
      <span>
        No
      </span>
    </button>
    <button
      className="ant-btn ant-btn-primary"
      onClick={[Function]}
      type="button"
    >
      <span>
        Yes
      </span>
    </button>
  </div>,
]
`;
