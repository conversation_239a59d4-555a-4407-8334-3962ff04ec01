import React from "react";
import renderer from "react-test-renderer";
import { shallow } from "enzyme";
import DownloadModal from "..";

describe("Cleat Filter Test Suit", () => {
  it("<DownloadModal/> should render", () => {
    const aComponent = shallow(<DownloadModal />);
    expect(aComponent.html()).not.toBeNull();
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer.create(<DownloadModal />).toJSON();
    expect(component).toMatchSnapshot();
  });
});
