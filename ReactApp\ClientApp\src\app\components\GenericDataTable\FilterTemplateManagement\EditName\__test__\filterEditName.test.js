import React from "react";
import renderer from "react-test-renderer";
import { shallow } from "enzyme";
import { Form, Input } from "antd";

import FilterTemplateEditName from "..";
import initTestSuite from "@app/utils/config/TestSuite";

describe("Filter Template Edit Name Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const filterTemplateEditNameComponent = shallow(<FilterTemplateEditName />);
    expect(filterTemplateEditNameComponent.html()).not.toBe(null);
  });

  it("should render and create snapshot properly", () => {
    const tree = renderer.create(<FilterTemplateEditName />).toJSON();
    expect(tree).toMatchSnapshot();
  });
  it("should render with props", () => {
    const filterTemplateEditNameComponent = shallow(
      <FilterTemplateEditName isNameValid={true} />
    );
    expect(filterTemplateEditNameComponent.html()).not.toBe(null);
  });

  it("should render when props are null", () => {
    const filterTemplateEditNameComponent = shallow(
      <FilterTemplateEditName isNameValid={null} />
    );
    expect(filterTemplateEditNameComponent.html()).not.toBe(null);
  });

  it("should render when props are undefined", () => {
    const filterTemplateEditNameComponent = shallow(
      <FilterTemplateEditName isNameValid={undefined} />
    );
    expect(filterTemplateEditNameComponent.html()).not.toBe(null);
  });
  it("should have a form", () => {
    const filterTemplateEditNameComponent = shallow(
      <FilterTemplateEditName siteId={"xxx"} />
    );
    expect(filterTemplateEditNameComponent.find(Form)).toHaveLength(1);
  });

  it("should have one form inputs in the form", () => {
    const filterTemplateEditNameComponent = shallow(
      <FilterTemplateEditName siteId={"xxx"} />
    );
    expect(filterTemplateEditNameComponent.find(Form.Item)).toHaveLength(1);
  });

  it("should have one Input in the form", () => {
    const filterTemplateEditNameComponent = shallow(
      <FilterTemplateEditName siteId={"xxx"} />
    );
    expect(filterTemplateEditNameComponent.find(Input)).toHaveLength(1);
  });
});
