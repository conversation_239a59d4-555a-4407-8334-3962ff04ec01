{"ast": null, "code": "var _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\FileArea\\\\StatusTag\\\\index.tsx\";\nimport React from \"react\";\nimport { LockOutlined, CheckCircleOutlined, StopOutlined } from \"@ant-design/icons\";\nconst statusMap = {\n  \"Active\": {\n    color: \"#0C6A00\",\n    icon: /*#__PURE__*/React.createElement(CheckCircleOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 7,\n        columnNumber: 39\n      }\n    }),\n    label: \"Active\"\n  },\n  \"Inactive\": {\n    color: \"#C8102E\",\n    icon: /*#__PURE__*/React.createElement(StopOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 8,\n        columnNumber: 41\n      }\n    }),\n    label: \"Inactive\"\n  },\n  \"Active - LH\": {\n    color: \"#BF6E00\",\n    icon: /*#__PURE__*/React.createElement(LockOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 9,\n        columnNumber: 44\n      }\n    }),\n    label: \"Active - Legal Hold\"\n  },\n  \"Inactive - LH\": {\n    color: \"#666E76\",\n    icon: /*#__PURE__*/React.createElement(LockOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 10,\n        columnNumber: 46\n      }\n    }),\n    label: \"Inactive - Legal Hold\"\n  }\n};\nexport default (({\n  value\n}) => {\n  const status = statusMap[value];\n  return /*#__PURE__*/React.createElement(\"div\", {\n    style: {\n      textAlign: 'left',\n      color: status.color\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 22,\n      columnNumber: 9\n    }\n  }, status.icon, \"  \", status.label);\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/FileArea/StatusTag/index.tsx"], "names": ["React", "LockOutlined", "CheckCircleOutlined", "StopOutlined", "statusMap", "color", "icon", "label", "value", "status", "textAlign"], "mappings": ";AAAA,OAAOA,KAAP,MAAkB,OAAlB;AAEA,SAASC,YAAT,EAAuBC,mBAAvB,EAA4CC,YAA5C,QAAgE,mBAAhE;AAGA,MAAMC,SAAkE,GAAG;AACvE,YAAU;AAAEC,IAAAA,KAAK,EAAE,SAAT;AAAmBC,IAAAA,IAAI,eAAC,oBAAC,mBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAxB;AAAgDC,IAAAA,KAAK,EAAC;AAAtD,GAD6D;AAEvE,cAAY;AAAEF,IAAAA,KAAK,EAAE,SAAT;AAAmBC,IAAAA,IAAI,eAAC,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAxB;AAAyCC,IAAAA,KAAK,EAAC;AAA/C,GAF2D;AAGvE,iBAAe;AAAEF,IAAAA,KAAK,EAAE,SAAT;AAAmBC,IAAAA,IAAI,eAAC,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAxB;AAAyCC,IAAAA,KAAK,EAAC;AAA/C,GAHwD;AAIvE,mBAAiB;AAAEF,IAAAA,KAAK,EAAE,SAAT;AAAmBC,IAAAA,IAAI,eAAC,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAxB;AAA0CC,IAAAA,KAAK,EAAC;AAAhD;AAJsD,CAA3E;AAWA,gBAAe,CAAC;AAAEC,EAAAA;AAAF,CAAD,KAAuB;AAElC,QAAMC,MAAM,GAAGL,SAAS,CAACI,KAAD,CAAxB;AAEA,sBACI;AAAK,IAAA,KAAK,EAAE;AAAEE,MAAAA,SAAS,EAAE,MAAb;AAAqBL,MAAAA,KAAK,EAAEI,MAAM,CAACJ;AAAnC,KAAZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAECI,MAAM,CAACH,IAFR,QAEgBG,MAAM,CAACF,KAFvB,CADJ;AAOH,CAXD", "sourcesContent": ["import React from \"react\";\r\nimport { Tag } from \"antd\";\r\nimport { LockOutlined, CheckCircleOutlined, StopOutlined } from \"@ant-design/icons\";\r\nimport { stat } from \"fs\";\r\n\r\nconst statusMap: Record<string, { color: string;icon:any;label:string }> = {\r\n    \"Active\": { color: \"#0C6A00\",icon:<CheckCircleOutlined />,label:\"Active\" },\r\n    \"Inactive\": { color: \"#C8102E\",icon:<StopOutlined/> ,label:\"Inactive\"},\r\n    \"Active - LH\": { color: \"#BF6E00\",icon:<LockOutlined />,label:\"Active - Legal Hold\" },\r\n    \"Inactive - LH\": { color: \"#666E76\",icon:<LockOutlined /> ,label:\"Inactive - Legal Hold\" },\r\n};\r\n\r\ninterface IProps {\r\n    value: string\r\n}\r\n\r\nexport default ({ value }: IProps) => {\r\n    \r\n    const status = statusMap[value];\r\n\r\n    return (\r\n        <div style={{ textAlign: 'left', color: status.color }}>\r\n        \r\n        {status.icon}  {status.label} \r\n            \r\n        </div>\r\n    );\r\n}"]}, "metadata": {}, "sourceType": "module"}