import React from 'react';
import { SyncOutlined, DeleteOutlined } from '@ant-design/icons';
import { Progress, Button } from 'antd';

import styles from "./index.module.less"
import { ProgressingFileInfo } from '../types';
import formatSize from '@app/utils/files/formatSize';

type PropTypes = {
  uid: string,
  info: ProgressingFileInfo
  onRetry: (uid: string) => void,
  onDelete: (uid: string) => void
}

const ProgressingFile: React.FC<PropTypes> = ({ uid, info, onRetry, onDelete }) => {
  return (
    <div className={styles.yjProgressWrapperContent}>
      <div className={styles.yjProgressFileDetailsContainer}>
        <div className={styles.yjFileDetailsWrapper}>
          <span className={styles.fileName}>{info.name}</span>
          <span className={styles.fileSize}>{formatSize(info.size)}</span>
          <span className={styles.progressValue}>{Math.round(info.percent || 0)}%</span>
        </div>
        <div className={styles.yjFileDetailsButtonWrapper}>
          <Button icon={<SyncOutlined />} className={styles.refreshIcon} onClick={() => onRetry(uid)} disabled={!info.error} />
          <Button icon={<DeleteOutlined />} className={styles.deleteIcon} onClick={() => onDelete(uid)} />
        </div>

      </div>
      <div className={styles.yjProgressBarContainer}>
        <Progress className={'yjProgressBar'} percent={Math.round(info.percent || 0)} showInfo={false} />
      </div>
      {info.error?.message && <span className={styles.yjUploadFailMessage}>{info.error.message}</span>}
    </div>
  );
};

export default ProgressingFile;
