import React, { useState, useEffect } from "react";
import {
  Row,
  Col,
  Input,
  Select,
  Button,
  Radio,
  Form,
  Collapse,
  Tooltip,
} from "antd";
import TextArea from "antd/lib/input/TextArea";
import { DoubleRightOutlined } from "@ant-design/icons/lib/icons";
import { FormInstance } from "antd/lib/form";
import { Store } from "antd/lib/form/interface";
import { RadioChangeEvent } from "antd/lib/radio";
import { useParams } from "react-router-dom";
import { useDispatch, useSelector } from "react-redux";

import { FolderTree } from "@app/components/FolderTree";
import styles from "./index.module.less";
import { FileDetailsOptions } from "@app/types/FileDetailsOptions";
import renderOptions from "../utils/renderOptions";
import FileList from "./FileList";
import { FileRecord, FileEvents, UrlEvents } from "./types";
import { URL_UPLOAD } from "@app/components/Uploader/constants/uploadTypes";
import { required, max, typeWithPattern } from "../validators";
import { AvoidWhitespace } from "@app/utils/regex";
import { RootState } from "@app/redux/reducers/state";
import { errorNotification } from "@app/utils/antNotifications";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import { IFolder } from "@app/types/FileAreaFolderTreeTypes";
import { UserPermission } from "@app/types/UserPermission";
import { getExistingTags } from "@app/api/tagManagementService";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import config from "@app/utils/config";
import { getBinderFileAreaNodes } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { setFolderTree } from "@app/redux/actions/fileAreaActions";

const { Panel } = Collapse;
const { Option } = Select;

const INPUT_LENGTH_HUNDRED = 100;
const INPUT_LENGTH_FIFTEEN = 15;
const INPUT_LENGTH_TWO_HUNDRED = 200;
const INPUT_LENGTHT_HOUSAND = 1000;

type PropTypes = {
  uploadType: number;
  options: FileDetailsOptions;
  permissions: UserPermission;
  fileList?: FileRecord[];
  form: FormInstance;
  onFinish: (values: Store, fileList: FileRecord[], folderId: number) => void;
  fileEvents?: FileEvents;
  urlEvents?: UrlEvents;
  disabledForm?: boolean;
  forManageFiles?: boolean;
  onFormChange?: (event: any) => void;
  onFolderTreeChange?: (event: any) => void;
  fileAreaSelection?: boolean;
};

export const UploaderSubmit: React.FC<PropTypes> = ({
  uploadType,
  options,
  permissions,
  fileList,
  form,
  onFinish,
  fileEvents,
  urlEvents,
  disabledForm,
  forManageFiles = false,
  onFormChange = () => { },
  onFolderTreeChange = () => { },
  fileAreaSelection = false,
}) => {
  const defaultFolder = forManageFiles
    ? form.getFieldValue("folder")
      ? form.getFieldValue("folder").toString()
      : "1"
    : "";
  let defaultRetention = -1;
  if (defaultFolder) {
    const getRetention = (node: IFolder[]) => {
      node.forEach((folder) => {
        if (folder.id === parseInt(defaultFolder)) {
          defaultRetention = folder.retention;
          return;
        }
        if (folder.subFolders?.length > 0) {
          getRetention(folder.subFolders);
        }
      });
    };
    getRetention(options.folderTree.folders!);
  }
  const [newTags, setNewTags] = useState<string[]>([]);
  const [isPermanent, setIsPermanent] = useState(true);
  const [selectedFolder, setSelectedFolder] = useState(defaultFolder);
  const [selectedFolderRetention, setSelectedFolderRetention] = useState<
    number
  >(defaultRetention);
  const [enableAddNew, setEnableAddNew] = useState(true);
  const disabled = uploadType !== URL_UPLOAD ? !fileList?.filter((v) => v.checked).length || disabledForm : disabledForm;

  const [tagOptions, setTagOptions] = useState<string[]>([]);
  const { binderId, siteId } = useParams<any>();
  const reactDispatch = useDispatch();
  const { user } = useSelector((state: RootState) => state.auth) as any;
  const [fileAreaKey, setFileAreaKey] = useState(0);
  const [selectedFileArea, setSelectedFileArea] = useState<string>("");

  useEffect(() => {
    form.resetFields(["newTagInput"]);
    if(fileAreaSelection){
      reactDispatch(setFolderTree({}));
    }
    return () => {
      if(fileAreaSelection){
        reactDispatch(setFolderTree({}));
      }
    }
  }, []);

  useEffect(() => {
    if (disabled && uploadType !== URL_UPLOAD) {
      form.resetFields();
      setSelectedFolder(defaultFolder);
      onFolderTreeChange(-1);
      setNewTags([]);
      setIsPermanent(true);
    }
  }, [defaultFolder, disabled, form, uploadType]);

  useEffect(() => {
    form.setFieldsValue({
      newTags: newTags,
    });
    if (forManageFiles) {
      setIsPermanent(form.getFieldValue("permanent"));
    }
  }, [form, newTags]);

  useEffect(() => {
    getExistingTags(binderId)
      .then((response) => {
        setTagOptions(response.data as any);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          form.resetFields(["newTagInput"]);
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        }
      });
  }, [newTags, binderId]);

  const onAddNewTag = () => {
    if (enableAddNew) {
      setNewTags((current) => {
        const value: string = form.getFieldValue("newTagInput") || "";
        if (value.trim()) {
          return [...current, value];
        }
        return current;
      });
      form.resetFields(["newTagInput"]);
    }
  };

  const newTagValidator = (rule: any, value: any) => {
    if (
      value &&
      ((tagOptions as any)
        .map((v: any) => v.name.toLowerCase())
        .includes(value.trim().toLowerCase()) ||
        newTags
          .map((newTag) => newTag.toLowerCase())
          .includes(value.trim().toLowerCase()))
    ) {
      return Promise.reject("Name Already Exists");
    }
    return Promise.resolve();
  };

  const handleNewTagInput = (value: string) => {
    form
      .validateFields(["newTagInput"])
      .then((v) => setEnableAddNew(true))
      .catch((e) => setEnableAddNew(false));
  };

  const handleExpireRadionChange = (e: RadioChangeEvent) => {
    if (e.target.value) {
      form.resetFields(["expirationDate"]);
    }
    setIsPermanent(e.target.value);
  };

  const filterOption = (input: any, option: any) => {
    return option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const getRetentionDate = () => {
    let date = forManageFiles
      ? fileList?.[0].created
        ? new Date(fileList[0]?.created)
        : new Date()
      : new Date();
    date.setFullYear(date.getFullYear() + selectedFolderRetention);
    return date.toISOString().split("T")[0];
  };

  const getFileAreaPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const getClientIdParameters = {
      limit: 10,
      offset: page - 1,
      ...transformFilters,
    };

    if (siteId) {
      getClientIdParameters['siteId'] = siteId;
    }

    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList, getClientIdParameters)
      .then((res: any) => {
        if (res.data) {
          res.data.records = res.data.records.map((rec: any) => { return { ...rec, id: rec.binderId } });

          // Auto-select if there's only one file area and no selection yet
          if (res.data.records && res.data.records.length === 1 && !selectedFileArea) {
            const singleFileArea = res.data.records[0];
            setSelectedFileArea(singleFileArea.id);
            onFolderTreeChange(-1);
            getFolderTreeByFileArea(singleFileArea.id);
            form.setFieldsValue({selectedBinderArea: singleFileArea.id});
            setFileAreaKey(prevKey => prevKey + 1);
          }

          return res.data;
        } else {
          logger.error('dropdownSelection', 'getFileAreaPaginatedRecords', res.error);
          return [];
        }
      })
      .catch((error: any) => {
        logger.error('dropdownSelection', 'getFileAreaPaginatedRecords', error);
        return [];
      });
  };


  const transformFolders = (folders: any[]): IFolder[] => {
    const primaryFolders = folders.filter((folder) => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);
    return primaryFolders.map((folder) => ({
      id: folder.id,
      name: folder.name,
      subFolders: folder.childNodes.map((child: any) => ({
        id: child.id,
        name: child.name,
        subFolders: [],
        retention: child.retention || 0,
      })),
    }));
  };
  
  const getFolderTreeByFileArea = (fileAreaId: string) => {
    getBinderFileAreaNodes(fileAreaId)
      .then((response: any) => {
        if (response.data) {
          const transformedFolders = transformFolders(response.data.folders);
          reactDispatch(setFolderTree({
            siteId:siteId,
            siteName: response.data.name||"--",
            siteStatusId: 1,
            folders: transformedFolders,
          }));
          setSelectedFileArea(fileAreaId);
        } else {
          return [];
        }
      })
      .catch((error) => {
        logger.error('Portal Files Area', 'Get Folder Tree By binder id', error);
      });
  };

  return (
    <Form
      size="middle"
      name="fileDetails"
      layout="vertical"
      form={form}
      onFinish={(values) =>
        onFinish(values, fileList || [], parseInt(selectedFolder))
      }
      initialValues={{
        statusId: options.fileStatuses[0].value,
        year: new Date().getFullYear(),
        permanent: true,
      }}
    >
      <Row>
      {fileAreaSelection && <Col span={24}>
          <Form.Item label="File Area" name="fileAreaId">
              <div className={styles.yjChannelSelector}>
                <InfinitySelect
                  key={`client-file-area-${siteId}-${fileAreaKey}`}
                  getPaginatedRecords={(page, method, searchValue) => getFileAreaPaginatedRecords(page, method, searchValue)}
                  formatValue={(value) => {
                    return value.binderName;
                  }}
                  notFoundContent="No file areas Available"
                  notLoadContent="Failed to load values in file areas dropdown"
                  onChange={(e) => {
                    onFolderTreeChange(-1);
                    getFolderTreeByFileArea(e);
                    form.setFieldsValue({selectedBinderArea: e})
                  }}
                  placeholder="Please Select File Areas(s)"
                  waitCharCount={3}
                  defaultValues={selectedFileArea.length > 0 ? selectedFileArea : undefined}
                />
              </div>
          </Form.Item>
        </Col>}
        {uploadType === URL_UPLOAD && (
          <Col span={16}>
            <Form.Item label="File URL" name="fileUrl" rules={[required]}>
              <TextArea
                rows={4}
                onChange={(e) => urlEvents?.onUrlUpdate(e.target.value)}
              />
            </Form.Item>

            <Form.Item
              label="Document Title"
              name="documentTitle"
              rules={[
                required,
                max(INPUT_LENGTH_HUNDRED),
                typeWithPattern("string", AvoidWhitespace),
              ]}
            >
              <Input
                autoComplete="off"
                onChange={(e) => urlEvents?.onTitleUpdate(e.target.value)}
              />
            </Form.Item>
          </Col>
        )}

        {uploadType !== URL_UPLOAD && fileList && !!fileList.length && (
          <Col span={16}>
            <Row gutter={0}>
              <FileList
                disabledForm={disabledForm}
                fileList={fileList}
                fileEvents={fileEvents}
                uploadType={uploadType}
                forManageFiles={forManageFiles}
                onDataChange={onFormChange}
              />
            </Row>
          </Col>
        )}

        <Col span={8} className={styles.yjFolderTreeUrlUploader}>
          <Form.Item label="Select Folder" name="folder">
            <div className={styles.yjFileFinderWrapper}>
              <div className={styles.yjFileFinderTreeWrapper}>
                <FolderTree
                  showTitle={false}
                  maxHeight="255px"
                  data={options.folderTree}
                  onSelectFolder={(keys, info) => {
                    onFolderTreeChange(keys);
                    setSelectedFolder(keys);
                    setSelectedFolderRetention(info.retention);
                    form.setFieldsValue({selectedBinderNodeId: keys})
                  }}
                  disableRoot={true}
                  controlSelection={true}
                  autoExpandParent={true}
                  selectedKeys={[selectedFolder.toString()]}
                  disabled={disabled}
                  onClick={() => {
                    if (disabledForm) {
                      return
                    }
                    onFormChange(true);
                  }}
                />
              </div>
            </div>
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <div className={styles.seperator}></div>
        </Col>

        {
          /**
            *permissions.internalFileStatusUpdate &&
            */
          (
            <Col span={12}>
              <Form.Item label="Status" name="statusId">
                <Select
                  showSearch
                  style={{ width: "100%" }}
                  disabled={disabled}
                  getPopupContainer={(trigger) =>
                    trigger.parentNode as HTMLElement
                  }
                  onChange={() => onFormChange(true)}
                  filterOption={filterOption}
                >
                  {options &&
                    options.fileStatuses &&
                    renderOptions(options.fileStatuses)}
                </Select>
              </Form.Item>
            </Col>
          )}

        <Col span={12}>
          <Form.Item label="Year" name="year">
            <Select
              style={{ width: "100%" }}
              disabled={disabled}
              getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
              onChange={() => onFormChange(true)}
            >
              {options && options.years && renderOptions(options.years)}
            </Select>
          </Form.Item>
        </Col>

        {
          /**
           * permissions.internalFilesAssign &&
           * */
          (
            <Col span={12}>
              <Form.Item label="Assignee" name="assigneeId">
                <Select
                  style={{ width: "100%" }}
                  disabled={disabled}
                  showSearch={true}
                  filterOption={filterOption}
                  getPopupContainer={(trigger) =>
                    trigger.parentNode as HTMLElement
                  }
                  onChange={() => onFormChange(true)}
                >
                  {options && options.users && renderOptions(options.users)}
                </Select>
              </Form.Item>
            </Col>
          )}

        {/*<Col span={12}>*/}
        {/*  {*/}
        {/*    /***/}
        {/*     * permissions.internalFileSetProject &&*/}
        {/*     */}
        {/*    (*/}
        {/*      <Form.Item label="Projects" name="projectIds">*/}
        {/*        <Select*/}
        {/*          className={"yjMultiSelectOptionSelect"}*/}
        {/*          mode="multiple"*/}
        {/*          style={{ width: "100%" }}*/}
        {/*          showArrow={true}*/}
        {/*          disabled={true}*/}
        {/*          filterOption={filterOption}*/}
        {/*          getPopupContainer={(trigger) =>*/}
        {/*            trigger.parentNode as HTMLElement*/}
        {/*          }*/}
        {/*          onChange={() => onFormChange(true)}*/}
        {/*        >*/}
        {/*          {options && options.projects && renderOptions(options.projects)}*/}
        {/*        </Select>*/}
        {/*      </Form.Item>*/}
        {/*    )}*/}
        {/*</Col>*/}

        <Col span={12}>
          <Form.Item label="Tags" name="tags">
            <Select
              className={"yjMultiSelectOptionSelect"}
              mode="multiple"
              style={{ width: "100%" }}
              showArrow={true}
              disabled={disabled}
              getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
              onChange={() => onFormChange(true)}
            >
              {tagOptions && renderOptions(tagOptions, true)}
            </Select>
          </Form.Item>
        </Col>

        {uploadType === URL_UPLOAD && (
          <Col span={12}>
            <Form.Item label="File Type" name="fileType">
              <Select
                style={{ width: "100%" }}
                disabled={disabled}
                showSearch={true}
                getPopupContainer={(trigger) =>
                  trigger.parentNode as HTMLElement
                }
                onChange={() => onFormChange(true)}
              >
                {options &&
                  options.fileTypes &&
                  renderOptions(options.fileTypes)}
              </Select>
            </Form.Item>
          </Col>
        )}

        <Col span={12}>
          <Row>
            <Col span={16}>
              <Form.Item
                name="newTagInput"
                label="New Tags"
                rules={[
                  { validator: newTagValidator },
                  max(INPUT_LENGTH_FIFTEEN),
                ]}
              >
                <Input
                  className={styles.yjInputNewTags}
                  onChange={(e) => {
                    onFormChange(true);
                    handleNewTagInput(e.target.value);
                  }}
                  onPressEnter={onAddNewTag}
                  disabled={disabled}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Button
                type="primary"
                onClick={onAddNewTag}
                className={styles.yjButtonNewTags}
                disabled={!enableAddNew || disabled}
              >
                Add New Tag
              </Button>
            </Col>
          </Row>
        </Col>

        <Col span={24}>
          <Form.Item name="newTags">
            <Select
              mode="tags"
              placeholder="New Tags"
              onDeselect={(value) =>
                setNewTags((current) => {
                  return current.filter((val) => val !== value);
                })
              }
              onInputKeyDown={(e) => e.preventDefault()}
              className={styles.yjTxtNewTags}
              dropdownStyle={{ display: "none" }}
              disabled={disabled}
              getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
              onFocus={(e) => {
                e.preventDefault();
                e.target.blur();
              }}
              onChange={() => onFormChange(true)}
            />
          </Form.Item>
        </Col>

        <Col span={24}>
          <div className={styles.seperator}></div>
        </Col>
      </Row>

      <Row
        gutter={16}
        align="middle"
        className={styles.yjUploaderRetentionSection}
      >
        <Col span={24}>
          <label className={styles.yjModuleSubHeading}>Expiration</label>
        </Col>
        <Col span={3}>
          <Radio
            className={styles.yjUploaderRetentionPermanent}
            disabled={selectedFolderRetention != 0}
            checked={selectedFolderRetention == 0}
          >
            Permanent
          </Radio>
        </Col>
        <Col span={2}>
          <Radio
            className={styles.yjUploaderRetentionExpiring}
            disabled={selectedFolderRetention <= 0}
            checked={selectedFolderRetention > 0}
          >
            Expiring
          </Radio>
        </Col>
        <Col>
          <div
            className={styles.yjUploaderRetentionDate}
            style={selectedFolderRetention > 0 ? {} : { visibility: "hidden" }}
          >
            {selectedFolderRetention +
              (selectedFolderRetention == 1 ? " year " : " years ") +
              "(" +
              getRetentionDate() +
              ")"}
          </div>
        </Col>
      </Row>

      <Row gutter={16}>
        <Col span={24}>
          <Form.Item
            label="Description"
            name="description"
            rules={[max(INPUT_LENGTH_TWO_HUNDRED)]}
          >
            <TextArea
              rows={4}
              disabled={disabled}
              onChange={() => onFormChange(true)}
            />
          </Form.Item>
        </Col>
      </Row>

      <Row gutter={16}>
        {
          /**
           * permissions.internalFilesEmail &&
           * */
          !forManageFiles && (
            <Col span={24}>
              {/* <Tooltip
              placement="topLeft"
              title={"This feature is coming soon"}
              color="#78bf59"
            >
              <div
                className={"yjSecondaryAccordian"}
                style={{ opacity: "0.5" }}
              >
                <Collapse expandIconPosition={"right"}>
                  <Panel showArrow={true} header="Email Options" key="2">
                    <Form.Item
                      style={{ opacity: "0.5" }}
                      label="Additional Information for this email"
                      name="options"
                      rules={[max(INPUT_LENGTHT_HOUSAND)]}
                    >
                      <TextArea
                        rows={4}
                        disabled={disabled}
                        placeholder={"This feature is coming soon"}
                      />
                    </Form.Item>

                    <Form.Item
                      label="Notify Users"
                      name="users"
                      style={{
                        display: "inline-block",
                        width: "calc(50% - 8px)",
                      }}
                    >
                      <Select
                        notFoundContent={"This feature is coming soon"}
                        className={"yjMultiSelectOptionSelect"}
                        mode="multiple"
                        style={{ width: "100%" }}
                        placeholder="Users"
                        disabled={disabled}
                        getPopupContainer={(trigger) =>
                          trigger.parentNode as HTMLElement
                        }
                        filterOption={filterOption}
                      > */}
              {/* <Option value="harry">Harry Potter</Option>
                      <Option value="ron">Ron Weasley</Option>
                      <Option value="hermione">Hermione Granger</Option>
                      <Option value="luna">Luna Lovegood</Option>
                      <Option value="neville">Neville Longbottom</Option> */}
              {/* </Select>
                    </Form.Item>
                    <Form.Item
                      name="contacts"
                      style={{
                        display: "inline-block",
                        width: "calc(50% - 8px)",
                        margin: "31px 8px 0",
                      }}
                    >
                      <Select
                        notFoundContent={"This feature is coming soon"}
                        className={"yjMultiSelectOptionSelect"}
                        style={{ width: "100%" }}
                        placeholder="Contacts"
                        disabled={disabled}
                        getPopupContainer={(trigger) =>
                          trigger.parentNode as HTMLElement
                        }
                        mode="multiple"
                        filterOption={filterOption}
                      > */}
              {/* <Option value="draco">Draco Malfoy</Option>
                      <Option value="lucius">Lucius Malfoy</Option>
                      <Option value="Bellatrix">Bellatrix Lestrange</Option>
                      <Option value="Igor">Igor Karkaroff</Option> */}
              {/* </Select>
                    </Form.Item>
                  </Panel>
                </Collapse>
              </div>
            </Tooltip> */}
            </Col>
          )}
      </Row>
    </Form>
  );
};
