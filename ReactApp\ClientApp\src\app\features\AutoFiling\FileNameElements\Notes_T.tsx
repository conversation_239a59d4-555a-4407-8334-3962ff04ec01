import React, { useEffect, useRef } from 'react';
import {Form, Input} from 'antd';
import LengthField_N from "@app/features/AutoFiling/FileNameElements/LengthField_N";

const Notes_T = ({index, value = '', onChange}: any) => {
	const inputRef = useRef<any>(null);
	useEffect(() => {
		if (inputRef.current) {
			// or, if Input component in your ref, then use input property like:
			// inputRef.current.input.focus();
			inputRef.current.focus();
		}
	}, [inputRef]);
	return (<Form.Item label={'Notes'} name={`${index}-Notes`} initialValue={value}>
			<Input ref={inputRef} placeholder={"Notes"} />
		</Form.Item>
	);
};

export default Notes_T;
