// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`My Recent Documents Test Suite should create and match to snapshot 1`] = `
<div
  className="ant-table-wrapper"
>
  <div
    className="ant-spin-nested-loading"
  >
    <div
      className="ant-spin-container"
    >
      <div
        className="ant-table ant-table-fixed-header"
      >
        <div
          className="ant-table-container"
        >
          <div
            className="ant-table-header"
            style={
              Object {
                "overflow": "hidden",
              }
            }
          >
            <table
              style={
                Object {
                  "tableLayout": "fixed",
                  "visibility": "hidden",
                }
              }
            >
              <colgroup />
              <thead
                className="ant-table-thead"
              >
                <tr>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    File Id
                  </th>
                  <th
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="Title"
                  >
                    Title
                  </th>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Type
                  </th>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Status
                  </th>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Assignee
                  </th>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Site
                  </th>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Last Accessed
                  </th>
                  <th
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  />
                </tr>
              </thead>
            </table>
          </div>
          <div
            className="ant-table-body"
            onScroll={[Function]}
            style={
              Object {
                "maxHeight": "31vh",
                "overflowY": "scroll",
              }
            }
          >
            <table
              style={
                Object {
                  "tableLayout": "fixed",
                }
              }
            >
              <colgroup />
              <tbody
                className="ant-table-tbody"
              >
                <tr
                  aria-hidden="true"
                  className="ant-table-measure-row"
                  style={
                    Object {
                      "fontSize": 0,
                      "height": 0,
                    }
                  }
                >
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                  <td
                    style={
                      Object {
                        "border": 0,
                        "height": 0,
                        "padding": 0,
                      }
                    }
                  >
                    <div
                      style={
                        Object {
                          "height": 0,
                          "overflow": "hidden",
                        }
                      }
                    >
                       
                    </div>
                  </td>
                </tr>
                <tr
                  className="ant-table-row ant-table-row-level-0 table-row-light"
                  onClick={[Function]}
                  style={Object {}}
                >
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    12FWE24
                  </td>
                  <td
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="PR Tax file 1"
                  >
                    PR Tax file 1
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    pdf
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Draft
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Menakad
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Active
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    01/01/2020
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    <button
                      className="ant-btn ant-btn-primary ant-btn-icon-only"
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="folder"
                        className="anticon anticon-folder"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="folder"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"
                          />
                        </svg>
                      </span>
                    </button>
                  </td>
                </tr>
                <tr
                  className="ant-table-row ant-table-row-level-0 table-row-dark"
                  onClick={[Function]}
                  style={Object {}}
                >
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    12FWE25
                  </td>
                  <td
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="PR Tax file 2"
                  >
                    PR Tax file 2
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    doc
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Draft
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Menakad
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Active
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    01/01/2020
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    <button
                      className="ant-btn ant-btn-primary ant-btn-icon-only"
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="folder"
                        className="anticon anticon-folder"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="folder"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"
                          />
                        </svg>
                      </span>
                    </button>
                  </td>
                </tr>
                <tr
                  className="ant-table-row ant-table-row-level-0 table-row-light"
                  onClick={[Function]}
                  style={Object {}}
                >
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    12FWE26
                  </td>
                  <td
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="PR Tax file 3"
                  >
                    PR Tax file 3
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    doc
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Draft
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Menakad
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Active
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    01/01/2020
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    <button
                      className="ant-btn ant-btn-primary ant-btn-icon-only"
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="folder"
                        className="anticon anticon-folder"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="folder"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"
                          />
                        </svg>
                      </span>
                    </button>
                  </td>
                </tr>
                <tr
                  className="ant-table-row ant-table-row-level-0 table-row-dark"
                  onClick={[Function]}
                  style={Object {}}
                >
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    12FWE27
                  </td>
                  <td
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="PR Tax file 4"
                  >
                    PR Tax file 4
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    pdf
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Draft
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Menakad
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Active
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    01/01/2020
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    <button
                      className="ant-btn ant-btn-primary ant-btn-icon-only"
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="folder"
                        className="anticon anticon-folder"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="folder"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"
                          />
                        </svg>
                      </span>
                    </button>
                  </td>
                </tr>
                <tr
                  className="ant-table-row ant-table-row-level-0 table-row-light"
                  onClick={[Function]}
                  style={Object {}}
                >
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    12FWE28
                  </td>
                  <td
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="PR Tax file 5"
                  >
                    PR Tax file 5
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    pdf
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Draft
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Menakad
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Active
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    01/01/2020
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    <button
                      className="ant-btn ant-btn-primary ant-btn-icon-only"
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="folder"
                        className="anticon anticon-folder"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="folder"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"
                          />
                        </svg>
                      </span>
                    </button>
                  </td>
                </tr>
                <tr
                  className="ant-table-row ant-table-row-level-0 table-row-dark"
                  onClick={[Function]}
                  style={Object {}}
                >
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    12FWE29
                  </td>
                  <td
                    className="ant-table-cell ant-table-cell-ellipsis"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                    title="PR Tax file 6"
                  >
                    PR Tax file 6
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    pdf
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Draft
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Menakad
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    Active
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    01/01/2020
                  </td>
                  <td
                    className="ant-table-cell"
                    colSpan={null}
                    rowSpan={null}
                    style={Object {}}
                  >
                    <button
                      className="ant-btn ant-btn-primary ant-btn-icon-only"
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="folder"
                        className="anticon anticon-folder"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="folder"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M880 298.4H521L403.7 186.2a8.15 8.15 0 00-5.5-2.2H144c-17.7 0-32 14.3-32 32v592c0 17.7 14.3 32 32 32h736c17.7 0 32-14.3 32-32V330.4c0-17.7-14.3-32-32-32zM840 768H184V256h188.5l119.6 114.4H840V768z"
                          />
                        </svg>
                      </span>
                    </button>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
