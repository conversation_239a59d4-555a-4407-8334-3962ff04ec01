import React from "react";
import { Card, Col, Row } from "antd";

export default () => {
  const noOfColumns = 12;
  return (
    <>
      <Row gutter={[noOfColumns, noOfColumns]}>
        <Col span={8}>
          <Card
            title="User Management"
            bordered={false}
            className="yjLicenedModuleCard"
          >
            <p>Users</p>
            <p>User Groups</p>
            <p>Permission</p>
          </Card>
        </Col>
        <Col span={8}>
          <Card
            title="Master Data"
            bordered={false}
            className="yjLicenedModuleCard"
          ></Card>
        </Col>
        <Col span={8}>
          <Card
            title="File Area"
            bordered={false}
            className="yjLicenedModuleCard"
          >
            <p>Assign</p>
            <p>Status</p>
            <p>Move</p>
          </Card>
        </Col>
      </Row>
    </>
  );
};
