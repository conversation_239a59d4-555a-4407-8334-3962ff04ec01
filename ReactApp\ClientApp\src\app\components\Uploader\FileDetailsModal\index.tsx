import React, { useState, useEffect } from "react";
import UploaderSubmitContainer from "@app/components/forms/UploaderSubmit/UploaderSubmitContainer";
import {
  FileRecord,
  FileEvents,
} from "@app/components/forms/UploaderSubmit/types";
import {
  SAME_PROPERTY_UPLOAD,
  DIFFERENT_PROPERTY_UPLOAD,
} from "../constants/uploadTypes";
import { Form } from "antd";
import useFileList from "@app/components/Uploader/hooks/useFileList";
import confirmDiscard from "../utils/confirmDiscard";
import Modal from "@app/components/Modal";

type PropTypes = {
  uploadType: number;
  siteId: string;
  binderId: string;
  files: FileRecord[];
  onClose?: () => void;
  onRemove?: (referenceNumber: string) => void;
  onSaveComplete?: (succeedFiles: FileRecord[]) => void;
  forPortalFiles?: boolean;
  onLastFile?: () => void;
  fileAreaSelection?: boolean;
};

const FileDetailsModal: React.FC<PropTypes> = ({
  uploadType,
  siteId,
  binderId,
  files,
  onClose,
  onRemove,
  onSaveComplete,
  forPortalFiles = false,
  onLastFile = () => {},
  fileAreaSelection
}) => {
  const [form] = Form.useForm();
  const { fileList, actions } = useFileList({ files });
  const {
    changeTitle,
    changeSelection,
    changeSelectAll,
    removeFile,
    removeFiles,
  } = actions;
  const [isFileTableValid, setIsFileTableValid] = useState(false);
  const [folderTreeChange, setFolderTreeChange] = useState(false);

  const onTitleUpdate = (value: string, index: number) => {
    changeTitle(value, index);
  };

  const onFileSelect = (value: boolean, index: number) => {
    changeSelection(value, index);
  };

  const onFileSelectAll = (value: boolean) => {
    changeSelectAll(value);
  };

  const onFileRemove = (referenceNumber: string) => {
    confirmDiscard(() => {
      deleteFile(referenceNumber);
    });
  };

  const onSaveSuccess = (inputFileList: FileRecord[]) => {
    onSaveComplete?.(inputFileList);
    removeFiles(inputFileList);
  };

  const deleteFile = (referenceNumber: string) => {
    onRemove?.(referenceNumber);
    removeFile(referenceNumber);
  };

  const fileEvents: FileEvents = {
    onTitleUpdate,
    onFileSelect,
    onFileSelectAll,
    onFileRemove,
    onSaveSuccess,
  };

  useEffect(() => {
    if (fileList?.length === 0) {
      onLastFile?.();
    }
    const checkedLength = fileList?.filter((v) => v.checked)?.length;
    const validFileTable =
      !!checkedLength &&
      !fileList?.filter((v) => v.checked && (!v.title.trim() || !!v.error))
        .length;
    setIsFileTableValid(validFileTable);
  }, [fileList]);

  return (
    <>
      <Modal
        width="70%"
        style={{ top: 20 }}
        title={forPortalFiles ? "Accept File(s)" : "Upload File(s)"}
        visible={
          !!fileList?.length &&
          (uploadType === SAME_PROPERTY_UPLOAD ||
            uploadType === DIFFERENT_PROPERTY_UPLOAD)
        }
        destroyOnClose={true}
        okText="DONE"
        onOk={() => form.submit()}
        onCancel={onClose}
        okButtonProps={{ disabled: !isFileTableValid || !folderTreeChange }}
      >
        <UploaderSubmitContainer
          uploadType={uploadType}
          siteId={siteId}
          binderId={binderId}
          fileList={fileList}
          form={form}
          fileEvents={fileEvents}
          forPortalFiles={forPortalFiles}
          onFolderTreeChange={(isValid) => setFolderTreeChange(isValid >= 0)}
          fileAreaSelection={fileAreaSelection}
        />
      </Modal>
    </>
  );
};

export default FileDetailsModal;
