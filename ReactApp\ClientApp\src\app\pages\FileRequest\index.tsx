import { useParams, withRouter } from "react-router-dom";
import React from "react";

import FileRequestLayout from "@app/layouts/FileRequestLayout";
import RequestFiles from "@app/components/PortalFiles/RequestFiles";

const Page = (props: any) => {
  const { id } = useParams();
  return (
    <FileRequestLayout>
      <RequestFiles requestId={decodeURIComponent(id)} {...props} />
    </FileRequestLayout>
  );
};

export default withRouter(Page);
