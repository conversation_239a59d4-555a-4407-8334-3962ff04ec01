import React, { forwardRef, useEffect, useImperativeHandle, useRef, useState } from 'react';
import { Form } from 'antd';
import { useForm } from 'antd/lib/form/Form';
import ClientRef_N from './ClientRef_N';
import Notes_T from './Notes_T';
import ClientName_T from './ClientName_T';
import FormCode_N from './FormCode_N';
import DocumentType_N from './DocumentType_N';
import Year_N from './Year_N';
import Separator_N from './Separator_N';
import OtherCharacters_N from './OtherCharacters_N';
import Version_N from './Version_N';
import DocumentTypeFormat_T from './DocumentTypeFormat_T';
import FormCodeFormat_T from "./FormCodeFormat_T";
import DateProcessed_T from "./DateProcessed_T";
import Version_T from "@app/features/AutoFiling/FileNameElements/Version_T";
import ClientRef_T from "@app/features/AutoFiling/FileNameElements/ClientRef_T";
import ClientName_N from "@app/features/AutoFiling/FileNameElements/ClientName_N";
import Year_T from "@app/features/AutoFiling/FileNameElements/Year_T";

import logger from '@app/utils/logger';

// Define the type for the components object
type ComponentMap = {
	[key: string]: React.FC<any>;
};

const elements: ComponentMap = {
	ClientRef_T,
	ClientName_N,
	ClientName_T,
	ClientRef_N,
	DateProcessed_T,
	DocumentType_N,
	DocumentTypeFormat_T,
	FormCode_N,
	FormCodeFormat_T,
	Notes_T,
	OtherCharacters_N,
	Separator_N,
	Version_N,
	Version_T,
	Year_N,
	Year_T,
};

// Define the structure of an element in fileNameList (adjust based on your actual data)


// Define the type for the methods that will be exposed via ref
export interface FileNameElementsRef {
	submitForm: () => Promise<any>;
}

const FileNameElements = forwardRef<FileNameElementsRef, { fileNameList: any[], onChange: (values: any) => void }>(
	({ fileNameList, onChange }, ref) => {
		const [formElements, setFormElements] = useState<any[]>([]); // Explicitly type the state

		const [form] = useForm();

		const convertToKeyValueObject = (array: any[]) => {
			return array?.reduce((acc: any, obj: any) => {
				const { index, name, id } = obj;

				let value;
				switch (id) {
					// _N cases first
					case 'ClientRef_N':
						value = null;
						break;
					case 'ClientName_N':
						value = null;
						break;
					case 'DocumentType_N':
						value = obj.documentType || 'CLNT';
						break;
					case 'FormCode_N':
						value = obj.formCode || 'I';
						break;
					case 'OtherCharacters_N':
						value = obj.fixedLength !== undefined ? obj.fixedLength : 0;
						break;
					case 'Separator_N':
						value = obj.separator || '-';
						break;
					case 'Version_N':
						value = obj.fixedLength !== undefined ? obj.fixedLength : 0;
						break;
					case 'Year_N':
						value = obj.yearFormat || 'YY';
						break;

					// _T cases later
					case 'ClientRef_T':
						value = null;
						break;
					case 'ClientName_T':
						value = null;
						break;
					case 'DateProcessed_T':
						value = obj.dateFormat || 'YYYYMMDD';
						break;
					case 'DocumentTypeFormat_T':
						value = obj.documentType || 'Client';
						break;
					case 'FormCodeFormat_T':
						value = obj.formCode || '1040';
						break;
					case 'Notes_T':
						value = obj.notes;
						break;
					case 'Version_T':
						value = null;
						break;
					case 'Year_T':
						value = obj.yearFormat || 'YY';
						break;
				}

				acc[`${index}-${name}`] = value;
				return acc;
			}, {});
		};

		useEffect(() => {
			const result = convertToKeyValueObject(fileNameList);

			form.setFieldsValue(result);
			setFormElements(fileNameList); // Ensure fileNameList is an array of FileNameElement
		}, [fileNameList]);

		// Expose the submitForm method to the parent via ref
		useImperativeHandle(ref, () => ({
			submitForm: async () => {
				try {
					await form.validateFields();
					const values = form.getFieldsValue();
					logger.debug('AutoFiling', 'useImperativeHandle-submitForm', values);
					return values; // Return the form values to the parent
				} catch (errorInfo) {
					logger.error('AutoFiling', 'useImperativeHandle-submit form', errorInfo)
					throw errorInfo; // Return error if form validation fails
				}
			},
		}));
		const inputRef = useRef<any>(null);
		useEffect(() => {
			if (inputRef.current) {
				// or, if Input component in your ref, then use input property like:
				// inputRef.current.input.focus();
				inputRef.current.focus();
			}
		}, [inputRef]);
		const handleChange = (value: any) => {
			console.log('handleChange:', value);
		};

		return (
			<>
				<Form
					form={form}
					layout="horizontal"
					onValuesChange={(changedValues) => {
						onChange(form.getFieldsValue());
					}}
				>
					{formElements.length > 0 &&
						formElements?.map((item: any, index: number) => {
							const props = { ...item.props, index };
							const Component = elements[item?.id];
							return Component ? <Component ref={inputRef} key={index} {...props} onChange={handleChange} /> : null;
						})}
				</Form>
				{/*<code>{JSON.stringify(formElements)}</code>*/}
			</>
		);
	}
);

export default FileNameElements;
