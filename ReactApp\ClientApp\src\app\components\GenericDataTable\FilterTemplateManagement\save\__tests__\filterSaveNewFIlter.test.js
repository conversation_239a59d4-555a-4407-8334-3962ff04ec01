import React from "react";
import renderer from "react-test-renderer";
import { Form, Input } from "antd";
import { shallow } from "enzyme";

import FilterTemplateSave from "..";
import initTestSuite from "@app/utils/config/TestSuite";

describe("Filter Template Save New Filter Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const filterTemplateSaveComponent = shallow(<FilterTemplateSave />);
    expect(filterTemplateSaveComponent.html()).not.toBe(null);
  });

  it("should render and create snapshot properly", () => {
    const tree = renderer.create(<FilterTemplateSave />).toJSON();
    expect(tree).toMatchSnapshot();
  });
  it("should render with props", () => {
    const filterTemplateSaveComponent = shallow(
      <FilterTemplateSave newFilterName={"xxxx"} />
    );
    expect(filterTemplateSaveComponent.html()).not.toBe(null);
  });

  it("should render when props are null", () => {
    const filterTemplateSaveComponent = shallow(
      <FilterTemplateSave newFilterName={null} />
    );
    expect(filterTemplateSaveComponent.html()).not.toBe(null);
  });

  it("should render when props are undefined", () => {
    const filterTemplateSaveComponent = shallow(
      <FilterTemplateSave newFilterName={undefined} />
    );
    expect(filterTemplateSaveComponent.html()).not.toBe(null);
  });
  it("should have a form", () => {
    const filterTemplateSaveComponent = shallow(
      <FilterTemplateSave siteId={"xxx"} />
    );
    expect(filterTemplateSaveComponent.find(Form)).toHaveLength(1);
  });

  it("should have one form inputs in the form", () => {
    const filterTemplateSaveComponent = shallow(
      <FilterTemplateSave siteId={"xxx"} />
    );
    expect(filterTemplateSaveComponent.find(Form.Item)).toHaveLength(1);
  });

  it("should have one Inputs in the form", () => {
    const filterTemplateSaveComponent = shallow(
      <FilterTemplateSave siteId={"xxx"} />
    );
    expect(filterTemplateSaveComponent.find(Input)).toHaveLength(1);
  });
});
