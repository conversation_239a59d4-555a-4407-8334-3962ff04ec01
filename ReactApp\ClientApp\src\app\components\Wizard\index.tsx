import React, {
  forwardRef,
  Fragment,
  useEffect,
  useImperativeHandle,
  useRef,
  useState,
} from "react";
import { But<PERSON>, Modal, Steps, Tooltip } from "antd";
import {
  ExclamationCircleOutlined,
  InfoCircleOutlined,
} from "@ant-design/icons/lib";
import { useDispatch, useSelector } from "react-redux";

import { RootState } from "../../redux/reducers/state";
import { SaveFunctionalFlow } from "../../redux/actions/functionalFlowActions";
import { successNotification } from "../../utils/antNotifications";
import styles from "./index.module.less";
import WizardConfigType from "../../types/WizardConfigType";
import logger from "../../utils/logger";

const scrollToTop = (ref: any) => {
  ref.current.scrollIntoView({ behavior: "smooth", block: "start" });
};

type WizardProps = {
  history: any;
  steps: WizardConfigType[];
  formRef: any;
  licenseId: any;
  licenseModulesTouched: boolean;
  organizationFormTouched: boolean;
  onSave?: () => void | undefined;
  onCancel?: () => void | undefined;
  onDiscardedCancel?: () => void | undefined;
};
const { Step } = Steps;

const WIZARD_COMP_LICENSE_INDEX = 0;
const WIZARD_COMP_ORGANIZATION_INDEX = 1;
const WIZARD_COMP_FUNCTIONAL_FLOW_INDEX = 2;

const redirectUrl = "/onboarding/license-management";

export default forwardRef((props: WizardProps, ref) => {
  const [currentWizardStep, setCurrentWizardStep] = useState(
    WIZARD_COMP_LICENSE_INDEX
  );
  const [isContinueState, setIsContinueState] = useState(false);
  const { confirm } = Modal;
  const topDivRef = useRef(null);
  const dispatch = useDispatch();
  const { modules, savedSuccessfully, isEdited } = useSelector(
    (state: RootState) => state.functionalFlow
  );

  useImperativeHandle(ref, () => ({
    continue: () => {
      next();
    },
  }));

  useEffect(() => {
    if (
      currentWizardStep === WIZARD_COMP_FUNCTIONAL_FLOW_INDEX &&
      savedSuccessfully
    ) {
      successNotification([""], "Successfully Saved");
      next();
    }
  }, [savedSuccessfully]);

  const next = () => {
    if (isContinueState && currentWizardStep !== props.steps.length - 1) {
      const currentStepState = currentWizardStep + 1;
      setCurrentWizardStep(currentStepState);
      scrollToTop(topDivRef);
    } else {
      props.history.push(redirectUrl);
    }
  };

  const previous = () => {
    if (currentWizardStep !== WIZARD_COMP_LICENSE_INDEX) {
      const currentStepState = currentWizardStep - 1;
      setCurrentWizardStep(currentStepState);
    }
  };

  const handleFormSubmit = (isContinue: boolean) => {
    if (currentWizardStep === WIZARD_COMP_FUNCTIONAL_FLOW_INDEX) {
      dispatch(SaveFunctionalFlow(modules, props.licenseId));
    } else {
      props.formRef
        .validateFields()
        .then((values: any) => {
          props.formRef.submit();
          setIsContinueState(isContinue);
        })
        .catch((r: any) => {
          props.formRef.scrollToField(r.errorFields[0].name);
          logger.error("license", "Wizard", r);
        });
    }
  };

  const handleCancel = () => {
    if (currentWizardStep === WIZARD_COMP_LICENSE_INDEX) {
      if (props.formRef.isFieldsTouched() || props.licenseModulesTouched) {
        cancelConfirmPopup();
      } else {
        props.history.push(redirectUrl);
      }
    } else if (currentWizardStep === WIZARD_COMP_ORGANIZATION_INDEX) {
      if (props.formRef.isFieldsTouched() && props.organizationFormTouched) {
        cancelConfirmPopup();
      } else {
        props.history.push(redirectUrl);
      }
    } else if (currentWizardStep === WIZARD_COMP_FUNCTIONAL_FLOW_INDEX) {
      if (isEdited) {
        cancelConfirmPopup();
      } else {
        props.history.push(redirectUrl);
      }
    }
  };

  const cancelConfirmPopup = () => {
    return confirm({
      title: "Are you sure you want to the discard changes?",
      icon: <ExclamationCircleOutlined />,
      okText: "Discard",
      onOk() {
        props.history.push(redirectUrl);
      },
      onCancel() {
        props.onDiscardedCancel && props.onDiscardedCancel();
      },
    });
  };

  return (
    <Fragment>
      <div ref={topDivRef} className={"yjStepper"}>
        <Steps current={currentWizardStep}>
          {props.steps.map((item) => (
            <Step key={item.key} />
          ))}
        </Steps>
      </div>
      <div className={styles.stepContentWrapper}>
        <div className={styles.stepContentHeader}>
          {props.steps[currentWizardStep].name}
          {currentWizardStep === WIZARD_COMP_FUNCTIONAL_FLOW_INDEX && (
            <Tooltip color="#78bf59" title="This feature is coming soon">
              <InfoCircleOutlined
                className={styles.yjInfoIcon}
                style={{ paddingLeft: "10px", top: "0" }}
              />
            </Tooltip>
          )}
        </div>
        {props.steps[currentWizardStep].content}
      </div>
      <div className={styles.stepButtonWrapper}>
        <div>
          <Button
            className={
              currentWizardStep > 0 ? styles.yjWizardPageTransitionButton : ""
            }
            type="primary"
            onClick={() => previous()}
            disabled={currentWizardStep === 0}
          >
            Back
          </Button>
        </div>
        <div className={styles.stepButtonGroupWrapper}>
          <Button
            type="default"
            onClick={() => {
              handleCancel();
              if (props.onCancel) {
                props.onCancel();
              }
            }}
          >
            Cancel
          </Button>
          <Button
            type="primary"
            htmlType="submit"
            onClick={() => {
              handleFormSubmit(false);
              if (props.onSave) {
                props.onSave();
              }
            }}
          >
            Submit
          </Button>
          <Button
            className={styles.yjWizardPageTransitionButton}
            hidden={currentWizardStep === props.steps.length - 1}
            type="primary"
            onClick={() => {
              handleFormSubmit(true);
              if (props.onSave) {
                props.onSave();
              }
            }}
          >
            {currentWizardStep === props.steps.length - 1
              ? "Submit"
              : "Submit and Continue"}
          </Button>
        </div>
      </div>
    </Fragment>
  );
});
