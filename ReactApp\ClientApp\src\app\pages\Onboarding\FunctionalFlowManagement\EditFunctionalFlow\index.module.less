@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../../styles/';

.yjFunctionalMgtModuleWrapper {
  background-color: @color-bg-functional-flow-management-wrapper-edit;
  padding: 1.5em;

  .yjCollapseModules {
    height: 58vh;
    margin-bottom: 10px;
    overflow-x: hidden;
    overflow-y: auto;
  }

  ul {

    li {
      margin-bottom: 5px;

      ul {
        margin-left: 15px;
      }
    }
  }

  .yjEditButtonWrapper {
    border-top: 1px solid @border-color-base;
    padding: 1em;

    button {
      margin: 0 .3em;

      &:last-child {
        margin-right: 0;
      }
    }

    .flex-mixin(center, flex, flex-end);
  }
}
