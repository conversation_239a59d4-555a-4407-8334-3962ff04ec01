import UserGroupWizard from "..";
import initTestSuite from "@app/utils/config/TestSuite";
import { shallow, mount } from "enzyme";
import { Button, Steps } from "antd";
import React from "react";

const { Step } = Steps;

jest.mock("../index.module.less", () => ({
  stepContentWrapper: "stepContentWrapper",
  stepContentHeader: "stepContentHeader",
  stepButtonWrapper: "stepButtonWrapper",
  stepButtonGroupWrapper: "stepButtonGroupWrapper",
}));

const userGroupStep = [
  {
    key: 1,
    name: "Overview",
    content: "",
  },
  {
    key: 2,
    name: "File Area Permissions",
    content: "",
  },
];

const permissonList = [
  { id: 0, channels: [] },
  { id: 1, channels: [] },
];

jest.mock("../index.module.less", () => ({
  yjModalContentWrapper: "yjModalContentWrapper",
  yjAssignFilesFormWrapper: "yjAssignFilesFormWrapper",
  yjAssignFilesForm: "yjAssignFilesForm",
  yjAssignFilesFormRowItem: "yjAssignFilesFormRowItem",
}));

describe("<UserGroupWizard/>", () => {
  beforeAll(() => {
    initTestSuite();
  });
  it("should render <UserGroupWizard/> component", () => {
    const component = shallow(<UserGroupWizard formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <UserGroupWizard/> component if prop formRef is null ", () => {
    const component = shallow(<UserGroupWizard formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <UserGroupWizard/> component if prop steps is null ", () => {
    const component = shallow(<UserGroupWizard steps={null} formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <UserGroupWizard/> component if prop permissonList is null ", () => {
    const component = shallow(
      <UserGroupWizard
        permissonList={[]}
        steps={userGroupStep}
        formRef={null}
      />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should render <UserGroupWizard/> component if prop permissonList is not null ", () => {
    const component = shallow(
      <UserGroupWizard
        permissonList={permissonList}
        steps={userGroupStep}
        formRef={null}
      />
    );
    expect(component.html()).not.toBe(null);
  });

  it(" <UserGroupWizard/> should have 1 Steps object", () => {
    const component = shallow(<UserGroupWizard />);
    expect(component.find(Steps)).toHaveLength(1);
  });

  it(" <UserGroupWizard/> should have a 2 Steps", () => {
    const component = shallow(
      <UserGroupWizard
        permissonList={permissonList}
        steps={userGroupStep}
        formRef={null}
      />
    );
    expect(component.find(Step)).toHaveLength(2);
  });

  it("First step stepNumber should be '1", () => {
    const component = mount(
      <UserGroupWizard
        permissonList={permissonList}
        steps={userGroupStep}
        formRef={null}
      />
    );

    const firstStep = component.find(Step).at(0).props();
    expect(firstStep.stepNumber).toEqual("1");
  });

  it("First step title should be Overview", () => {
    const component = mount(
      <UserGroupWizard
        permissonList={permissonList}
        steps={userGroupStep}
        formRef={null}
      />
    );

    const firstStep = component.find(Step).at(0).props();
    expect(firstStep.title).toEqual("Overview");
  });

  it("should have 3 Buttons", () => {
    const component = mount(<UserGroupWizard />);
    expect(component.find(Button).length).toEqual(3);
  });

  it("1 button type should be primary", () => {
    const component = mount(<UserGroupWizard />);
    const firstButton = component.find(Button).at(0).props();
    expect(firstButton.type).toEqual("primary");
  });

  it("1 button label should be Back", () => {
    const component = mount(<UserGroupWizard />);
    const firstButton = component.find(Button).at(0).props();
    expect(firstButton.children).toEqual("Back");
  });

  it("2 button type should be primary", () => {
    const component = mount(<UserGroupWizard />);
    const firstButton = component.find(Button).at(1).props();
    expect(firstButton.type).toEqual("default");
  });

  it("2 button label should be Back", () => {
    const component = mount(<UserGroupWizard />);
    const firstButton = component.find(Button).at(1).props();
    expect(firstButton.children).toEqual("Cancel");
  });

  it("3 button type should be primary", () => {
    const component = mount(<UserGroupWizard />);
    const firstButton = component.find(Button).at(2).props();
    expect(firstButton.type).toEqual("primary");
  });

  it("3 button label should be Back", () => {
    const component = mount(<UserGroupWizard />);
    const firstButton = component.find(Button).at(2).props();
    expect(firstButton.children).toEqual("Save and Continue");
  });

  it("should have relevent css classes", () => {
    const component = mount(
      <UserGroupWizard
        permissonList={permissonList}
        steps={userGroupStep}
        formRef={null}
      />
    );
    expect(component.find(".yjModalContentWrapper")).toBeDefined();
    expect(component.find(".yjAssignFilesFormWrapper")).toBeDefined();
  });
});
