import React, { useContext, useEffect, useState } from 'react';
import { v4 as uuidv4 } from 'uuid';
import { Col, Row, Table, Tooltip } from 'antd';
import { useDispatch, useSelector } from 'react-redux';
import { TableProps } from 'antd/lib/table';
import { LoadingOutlined, SwapOutlined } from '@ant-design/icons';

import { DataTableContext } from '../DataTableContext';
import { setSelectedSavedFilter, setTableLoading, updateChange, updateDataRecords } from '../DataTableContext/actions';
import logger from '../../../utils/logger';
import RecordCount from '../RecordCount';
import { prepareFilterObject } from '../util';
import { RootState } from '@app/redux/reducers/state';
import { updateGridColumns, updateGridHasUpdates } from '@app/redux/actions/gridsActions';
import { SavedFilterTemplate } from '@app/types/filterTemplateTypes';
import { MdOpenInNew } from 'react-icons/md';
import { VscPinned } from "react-icons/vsc";
import { PaginationProps } from "antd/lib/pagination";
import { DataTablePropType } from "@app/components/GenericDataTable/GenericDataTableBuilder";
import { getRecords } from "@app/api/genericDataTable";

type CustomTableProps = DataTablePropType & {
  filters: any[];
  selectedSavedFilter?: SavedFilterTemplate;
  disablePagination?: boolean;
}

export default (props: TableProps<any> & CustomTableProps) => {
  const ROW_SEPERATER_VALUE = 2;
  const DEFAULT_COLUMN_COUNTER = 5;
  const MINIMUM_RESOLUTION_VALUE = 1367;
  const MAX_SCREEN_WIDTH = 2400;
  const { state, dispatch } = useContext(DataTableContext);
  const { searchQueryParameters, gridFilters, tableKey: gridTablekey } = useSelector((store: RootState) => store.grid);
  const dispatchGrid = useDispatch();
  const [tableKey, setTableKey] = useState<string>('');
  const [tableSorted, setTableSorted] = useState<boolean>(false);
  const [sortedInfo, setSortedInfo] = useState<any>({
    order: props.sorted?.order,
    columnKey: props.sorted?.value,
  });
  //update the table content according to the selection of saved filters by user
  const [generatedColumValues, setGeneratedColumValues] = useState<any>([]);
  useEffect(() => {
    if (props.columns) {
      setGeneratedColumValues(
        props.columns?.map((column) => {
          return {
            ...column,
            sorter: state.records.length > 0 ? column.sorter : false,
          };
        })
      );
      if (!tableSorted) {
        setSortedInfo({
          order: props.sorted?.order,
          columnKey: props.sorted?.value,
        });
      }
    }
  }, [props.columns, state.records, tableSorted]);

  useEffect(() => {
    if (props.selectedSavedFilter !== undefined && props.selectedSavedFilter !== null && Object.keys(props.selectedSavedFilter).length !== 0) {
      const filtersFromSelectedSavedFilter = gridFilters;
      if (!filtersFromSelectedSavedFilter.find((filter: any) => filter === undefined)) {
        dispatch(setSelectedSavedFilter(filtersFromSelectedSavedFilter));
      }
    }
  }, [gridFilters, props.selectedSavedFilter, dispatch]);

  useEffect(() => {
    let canceled = false;
    let queryParams = searchQueryParameters;

    if (props.searchQueryParameters) queryParams = [...props.searchQueryParameters, ...queryParams];

    if (state.columns.length) {
      dispatch(setTableLoading(true));

      const transformFilters = prepareFilterObject(state.filters);
      if (props.filterByIds) {
        transformFilters.fileId = props.filterByIds.split(',');
      }
      logger.debug('Generic DataTable', 'AntTableContainer', {
        transformFilters,
      });


      /**
       * TODO - This getRecords with props.endpoint scenario needs to remove after successfully refactor
       */
      if (!props.dataPromise) {
        logger.error('AntGenericDataTable', 'GenericDataTable', `WARNING! Endpoint property has been deprecated, Please use dataPromise, columnPromise, onGridStateChange etc
          <GenericDataTable `)
        logger.warn('AntGenericDataTable', 'GenericDataTable', `WARNING! Endpoint property has been deprecated, Please use dataPromise, columnPromise, onGridStateChange etc
          <GenericDataTable
          dataPromise={(state) => fetchData(state)}
          columnPromise={
            getColumns(config.api[OperationalServiceTypes.MasterDataService].templates, "siteManagement")
          }
          rowKey={"siteId"}
          scrollColumnCounter={7}
          tableKey={"siteManagement"}
          fixedColumns={["name"]}
          isDraggable={true}
          noRecordsAvilableMessage={"No Sites Available"}
          hasFilterManagement={true}
        />
      `)
      }
      const dataPromise = !props.dataPromise ? getRecords(
        props.endpoint!,
        {
          pagination: {
            current: state.pagination.current,
            pageSize: state.pagination.pageSize,
          },
          sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
          filters: transformFilters,
          columns: state.columns.filter((i) => i.default === false && i.selected === true).map((i) => i.key),
        },
        queryParams
      ) : props.dataPromise(state, transformFilters, queryParams);


      dataPromise.then((i) => {
        if (!canceled) {
          dispatchGrid(updateGridHasUpdates(false));
          dispatch(updateDataRecords(i.data));
          dispatch(setTableLoading(false));
          dispatchGrid(
            updateGridColumns({
              columns: state.columns,
              tableKey: gridTablekey,
            })
          );
        }
      }).catch((e) => {
        props.onErrorLoading && props.onErrorLoading(e);
        logger.error('GenericDataTable', 'GenericDataTableBuilder', e);
      });
    }

    props.onGridStateChange && props.onGridStateChange({ ...state, searchQueryParameters });

    return () => {
      canceled = true;
    };
  }, [state.filters, props.dataPromise, props.endpoint, state.columns, state.sorter, state.pagination.pageSize, dispatch, state.pagination, searchQueryParameters]);

  useEffect(() => {
    if (props.onFiltersChange) {
      props.onFiltersChange(true);
    }
  }, [state.filters]);

  useEffect(() => {
    if (state.records.length === 0) {
      //genarate UUID for fir rerender the Table Componanat
      setTableKey(uuidv4());
    }
  }, [props.columns]);

  useEffect(() => {
    const tableElement = document.getElementsByClassName('ant-table-body');
    if (tableElement[0]) {
      tableElement[0].scrollTo({
        left: 0,
        behavior: 'smooth',
      });
    }
  }, [state.columns]);

  useEffect(() => {
    if (props.updatedGrid) {
      dispatch(updateChange({ current: 1, pageSize: 20, total: state.pagination.total }, {}, {}));
    }
  }, [props.updatedGrid, dispatch, state.pagination.total]);

  const onChange = (pagination: any, filters: any, sorting: any) => {
    setTableSorted(true);
    /**
     * Reset The pagination.current to 1
     * if
     *  Sorter Column Changed or
     *  Sorter Order Changed or
     *  Page Size Change or
     */
    if (sortedInfo.order !== sorting.order || sortedInfo.columnKey !== sorting.field || state.pagination.pageSize !== pagination.pageSize) {
      pagination.current = 1;
    }

    if (sorting.field !== props.sorted?.value && !sorting.column) {
      setSortedInfo({
        order: props.sorted?.order,
        columnKey: props.sorted?.value,
      });
    } else {
      setSortedInfo({
        order: sorting.order,
        columnKey: sorting.field,
      });
    }

    dispatch(updateChange(pagination, filters, sorting));
  };

  const sortedColumValues = [
    ...generatedColumValues.map((col: any) => {
      return {
        ...col,
        sortOrder: sortedInfo.columnKey === col.key ? sortedInfo.order : false,
      };
    }),
  ];

  const paginationConf: PaginationProps = {
    disabled: props.disablePagination,
    className: 'yjGridPagination',
    total: state.pagination.total,
    current: state.pagination.current,
    pageSize: state.pagination.pageSize,
    pageSizeOptions: ['10', '20', '25', '50'],
    style: { display: 'inline-block', position: 'relative', zIndex: 99 },
    showSizeChanger: true,
    locale: {
      items_per_page: 'rows',
    },
  };

  return (
    <>
      <Table
        {...props}
        rowClassName={(record, index) => {
          const defaultClassName = (index % ROW_SEPERATER_VALUE === 0 ? 'table-row-light' : 'table-row-dark');
          const additionalClassName = props.rowClassName ? ` ${props.rowClassName(record, index)}` : '';
          return `${defaultClassName} ${additionalClassName}`;
        }}
        key={tableKey}
        className={'yjTableContainer'}
        columns={sortedColumValues}
        onRow={props.onRow}
        rowSelection={
          props.rowSelection
            ? {
              type: 'checkbox',
              columnWidth: props.showPublishedIcon ? '105px' : '50px',
              ...props.rowSelection,
              preserveSelectedRowKeys: true,
              renderCell: props.showPublishedIcon
                ? (_value: boolean, record: any, _index: number, originNode: React.ReactNode) => {
                  return (
                    <Row>
                      <Col>{originNode}</Col>
                      <Col style={{ paddingLeft: '10px' }}>
                        {record.published && (
                          <Tooltip title={'Published to Portal'}>
                            <MdOpenInNew color="#134A82" size={20} />
                          </Tooltip>
                        )}
                      </Col>
                      <Col style={{ paddingLeft: '5px' }}>
                        {record.pinned && (
                          <Tooltip title={'Pinned'}>
                            <VscPinned color="#134A82" size={20} />
                          </Tooltip>
                        )}
                      </Col>
                      <Col style={{ paddingLeft: '5px' }}>
                        {
                          record.linked && (
                            <Tooltip title={'Linked to ' + record.linkedSiteCount + ' File Area(s)'}>
                              <SwapOutlined color="#134A82" size={20} />
                            </Tooltip>
                          )}
                      </Col>
                    </Row>
                  );
                }
                : undefined,
            }
            : undefined
        }
        loading={{
          spinning: state.loading,
          indicator: <LoadingOutlined style={{ fontSize: 36 }} spin />,
        }}
        dataSource={state.records}
        showSorterTooltip={false}
        onChange={onChange}
        rowKey={(row: any) => {
          return !!props.rowKey ? row[props.rowKey] : row.id;
        }}
        locale={{
          emptyText: Object.keys(state.filters).length > 0 ? 'No Results Found' : props.noRecordsAvilableMessage ? props.noRecordsAvilableMessage : 'No Records Available',
        }}
        scroll={{
          x:
            props.columns?.length && props.columns?.length > (props.scrollColumnCounter !== undefined ? props.scrollColumnCounter : DEFAULT_COLUMN_COUNTER)
              ? MAX_SCREEN_WIDTH
              : undefined,
          y:
            window.screen.width < MINIMUM_RESOLUTION_VALUE
              ? props.lowResolutionWidth
                ? props.lowResolutionWidth
                : 'auto'
              : props.highResolutionWidth
                ? props.highResolutionWidth
                : 'auto',
        }}
        pagination={!props.hidePagination && paginationConf}
      />
      {!props.hidePagination && !state.loading && <RecordCount
        total={state.pagination.total}
        current={state.pagination.current}
        recordsCount={state.records.length}
        pageSize={state.pagination.pageSize}
        selectedRecordCount={props.selectedRecordCount}
      />}
    </>
  );
};
