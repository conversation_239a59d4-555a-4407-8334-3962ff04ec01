{"ast": null, "code": "import \"antd/es/dropdown/style\";\nimport _Dropdown from \"antd/es/dropdown\";\nimport \"antd/es/checkbox/style\";\nimport _Checkbox from \"antd/es/checkbox\";\nimport \"antd/es/drawer/style\";\nimport _Drawer from \"antd/es/drawer\";\nimport \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/menu/style\";\nimport _Menu from \"antd/es/menu\";\nimport \"antd/es/form/style\";\nimport _Form from \"antd/es/form\";\nimport \"antd/es/select/style\";\nimport _Select from \"antd/es/select\";\nimport \"antd/es/modal/style\";\nimport _Modal from \"antd/es/modal\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\FileArea\\\\FileAreaActionPanel\\\\index.tsx\";\nimport React, { useCallback, useEffect, useState } from 'react';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { ApartmentOutlined, AuditOutlined, ContainerOutlined, CopyOutlined, DeleteOutlined, DownloadOutlined, DownOutlined, DragOutlined, EditOutlined, ExclamationCircleOutlined, FileDoneOutlined, FilterOutlined, MenuFoldOutlined, MenuUnfoldOutlined, SettingOutlined, LinkOutlined, DisconnectOutlined, ShareAltOutlined } from '@ant-design/icons';\nimport { useForm } from 'antd/lib/form/Form';\nimport styles from './index.module.less';\nimport AssignOption from '@app/features/FileArea/Assign';\nimport ColumnFilter from '@app/components/ColumnFilter';\nimport ReCategorize from '@app/features/FileArea/ReCategorize';\nimport ClearFilter from '@app/components/ClearFilter';\nimport DownloadModal, { downloadTypes } from '@app/components/DownloadModal';\nimport { updateContextMenuAssignOption, updateContextMenuCheckoutOption, updateContextMenuDeleteOption, updateContextMenuDownloadOption, updateContextMenuPropetiesoption, updateContextMenuPublishFiles, updateContextMenuReCategorizeOption, updateContextMenuMoveFilesOption, updateContextMenuReNameFilesOption, updateContextMenuCopyFilesOption, updateContextMenuStatusOption, updateContextMenuUnpublishFiles, updateLoadGridOption, updateContextMenuLinkFilesOption, updateContextMenuUnlinkFilesOption, updateContextMenuToBeDeleted, updateContextMenuCopyLinkFiles } from '@app/redux/actions/fileAreaActions';\nimport ChangeStatus from '../ChangeStatus';\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\nimport CheckoutOption from '../CheckoutOption';\nimport { deleteFile, recategorizeFiles, updateAssigneeNStatus, updateFileStatus, copyFiles, reNameFiles, linkToSite, unlinkFilesFromBinders } from '@app/api/fileAreaService';\nimport logger from '@app/utils/logger';\nimport DocumentPropeties from '../DocumentPropeties';\nimport FilterTemplateManagementEdit from '@app/components/GenericDataTable/FilterTemplateManagement/edit';\nimport FilterTemplateManagementSave from '@app/components/GenericDataTable/FilterTemplateManagement/save';\nimport { applyASavedFilter, clearGridFilters, getSavedFilters, saveFilterTemplate, updateFilterTemplateSaved, onSelectedSavedFilter, updateGridHasUpdates } from '@app/redux/actions/gridsActions';\nimport { getGridFilterFromIFilterTemplate } from '@app/components/GenericDataTable/FilterTemplateManagement/util';\nimport { LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE } from '@app/components/Uploader';\nimport { checkoutFiles } from './FileAreaActionPanelFunctions/checkoutFiles';\nimport { FORBIDDEN_ERROR_CODE } from '@app/utils';\nimport { GridRefreshIcon } from '@app/components/GenericDataTable/GridRefresh';\nimport { MdOpenInNew } from 'react-icons/md';\nimport { publishFiles } from './FileAreaActionPanelFunctions/publishFiles';\nimport { unpublishFiles } from './FileAreaActionPanelFunctions/unpublishFiles';\nimport { copyToClipboard } from '@app/components/GenericDataTable/util';\nimport ReNameFiles from '../ReNameFiles';\nimport hasPermission from '@app/utils/permission';\nimport MoveFiles from '@app/features/FileAreaActionPanel/MoveFiles';\nimport LinkFilesDrawer from '@app/features/Link/LinkFilesDrawer';\nimport UnlinkFilesDrawer from '@app/features/Unlink/UnlinkFilesDrawer';\nimport { useParams } from \"react-router-dom\";\nimport { UpdateFunctionalFlowDataLoading } from '@app/redux/actions/functionalFlowActions';\nimport UnpublishFilesDrawer from '@app/features/FileArea/UnpublishFiles/UnpublishFilesDrawer';\nimport PublishFilesDrawer from '@app/features/FileArea/PublishFiles/PublishFilesDrawer';\nimport { TagManagementDrawer } from '@app/features/FileArea/TagManagement/TagManagementDrawer';\nconst FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';\nconst {\n  confirm\n} = _Modal;\nconst {\n  Option\n} = _Select;\nconst elementName = 'FilterChangeTrigger';\nconst TABLE_KEY = 'fileArea';\nconst TO_BE_DELETED_STATUS = 'To be Deleted';\nexport const FileAreaActionPanel = ({\n  siteId,\n  toggleIconClicked,\n  selectedFileList,\n  showDownload = false,\n  showStatus = false,\n  showAssign = false,\n  showPropeties = false,\n  showReCategorize = false,\n  showMoveFiles = false,\n  showCheckout = false,\n  fileDownloaded,\n  showDelete = false,\n  showPublish = false,\n  showUnpublish = false,\n  showToBeDelete = false,\n  showCopyLink = false,\n  showLinkFiles = false,\n  showUnlinkFiles = false,\n  showReName = false,\n  showCopy = false,\n  syncGrid,\n  originalFileAreaWithLinked = false,\n  showManageTags = false,\n  showManageCheckin = false,\n  onFolderTreeChange = () => {},\n  additionalActionPanel = null\n}) => {\n  const CHECKOUT_FAILED_ERRORCODE = 400;\n  const [form] = useForm();\n  const [changeStatusForm] = useForm();\n  const [emailForm] = useForm();\n  const [showPanel, setShowPanel] = useState(true);\n  const [showAddNewFilterModal, setShowAddNewFilterModal] = useState(false);\n  const [showEditFiltersModal, setShowEditFiltersModal] = useState(false);\n  const [showTagsManageModal, setShowTagsManageModal] = useState(false);\n  const [showPublishModal, setShowPublishModal] = useState(false);\n  const [showUnpublishModal, setShowUnpublishModal] = useState(false);\n\n  const [assignOptionForm] = _Form.useForm();\n\n  const [selectedUnlinkFiles, setSelectedUnlinkFiles] = useState([]);\n  const [assignOptionDetails, setAssignOptionDetails] = useState({\n    assignee: 0,\n    files: []\n  });\n  const [showAssignModal, setShowAssignModal] = useState(false);\n\n  const handleAddNewFilterCancel = () => {\n    setNewFilterName('');\n    setShowAddNewFilterModal(false);\n    setNameValid(true);\n  };\n\n  const [newFilterName, setNewFilterName] = useState('');\n  const [sortedSavedFilterList, setSortedFilterList] = useState([]);\n  const {\n    showFilter,\n    showFilterSaveButton,\n    gridFilters,\n    savedFilters,\n    columns,\n    filter_template_saved,\n    hasUpdates\n  } = useSelector(state => state.grid);\n  const {\n    download,\n    status,\n    assign,\n    checkout,\n    publish,\n    unpublish,\n    deleteFiles,\n    propeties,\n    reCategorize,\n    moveFiles,\n    renameFiles,\n    copySelectedFiles,\n    linkFiles,\n    unLinkFiles,\n    toBeDeleted,\n    copyLink\n  } = useSelector(state => state.fileArea);\n  const {\n    userPermission\n  } = useSelector(state => state.userManagement);\n  const {\n    folderTree\n  } = useSelector(state => state.fileArea);\n  const {\n    isLoading\n  } = useSelector(state => state.functionalFlow);\n  const [isNameValid, setNameValid] = useState(true);\n  const [showFiltersButton, setShowFiltersButton] = useState(true);\n  const [publishFileList, setPublishFileList] = useState([]);\n  const [unpublishFileList, setUnpublishFileList] = useState([]);\n  const [publishFileExpiration, setPublishFileExpiration] = useState(undefined);\n  const dispatch = useDispatch();\n  const {\n    binderId\n  } = useParams();\n  useEffect(() => {\n    if (savedFilters.length === 0) {\n      setShowEditFiltersModal(false);\n    }\n\n    const sortedList = savedFilters.sort((filter1, filter2) => {\n      return filter1.name.localeCompare(filter2.name);\n    });\n    setSortedFilterList(sortedList);\n  }, [savedFilters]); // use to clear the grid filters on component mount.\n\n  useEffect(() => {\n    dispatch(clearGridFilters());\n  }, []);\n\n  const onClickSavedFilter = value => {\n    const savedFilterIndex = savedFilters.findIndex(filter => filter.id === value);\n    const savedFilter = savedFilters[savedFilterIndex];\n\n    if (savedFilter) {\n      const gridFilterTemplates = getGridFilterFromIFilterTemplate(savedFilter.content, columns);\n      const savedFilterColumns = Object.getOwnPropertyNames(savedFilter.content);\n      const optionalFilterColumns = columns.filter(column => !column.default && savedFilterColumns.includes(column.key)).map(column => column.key);\n      const filterColumns = columns.map(column => {\n        if (optionalFilterColumns.includes(column.key)) {\n          return { ...column,\n            selected: true\n          };\n        } else {\n          return column;\n        }\n      });\n      dispatch(applyASavedFilter({\n        gridFilterTemplates,\n        filterColumns,\n        selected: optionalFilterColumns.length > 0,\n        selectedElement: {\n          name: elementName,\n          checked: true,\n          multiple: true\n        }\n      }));\n      dispatch(onSelectedSavedFilter(savedFilter));\n      setShowFiltersButton(true);\n    }\n  };\n\n  const {\n    options,\n    isOptionsFetched,\n    successedFiles,\n    pendingSave,\n    permissions\n  } = useSelector(state => {\n    return {\n      options: { ...state.fileDetails.options,\n        folderTree\n      },\n      isOptionsFetched: state.fileDetails.isOptionsFetched,\n      successedFiles: state.fileDetails.successedFiles,\n      pendingSave: state.fileDetails.pendingSave,\n      permissions: state.userManagement.userPermission\n    };\n  });\n\n  const onChangeSaveNewFilterName = name => {\n    if (savedFilters.findIndex(filter => {\n      return filter.name.toLowerCase() === name.toLowerCase().trim();\n    }) !== -1) {\n      setNameValid(false);\n      setNewFilterName(name);\n    } else {\n      setNameValid(true);\n      setNewFilterName(name);\n    }\n  };\n\n  const onSearchSavedFilters = (input, option) => {\n    if (typeof (option === null || option === void 0 ? void 0 : option.children) === 'string') {\n      return option.children.toLowerCase().startsWith(input.toLowerCase());\n    } else {\n      return false;\n    }\n  };\n\n  const handleShowTagManageModalCancel = () => {\n    setShowTagsManageModal(false);\n  };\n\n  const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';\n  const [checkingOutFilesCount, setCheckingOutFilesCount] = useState(0);\n  const [checkedOuFilesDownload, setCheckedOuFilesDownload] = useState(undefined);\n\n  const resetAssignModal = () => {\n    setAssignOptionDetails({\n      assignee: 0,\n      files: []\n    });\n    dispatch(updateContextMenuAssignOption(false));\n    setShowAssignModal(false);\n  };\n\n  const cancelAssignModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 312,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        assignOptionForm.resetFields();\n        resetAssignModal();\n      }\n\n    });\n  };\n\n  const cancelPublishModal = () => {\n    setPublishFileExpiration(undefined);\n    setPublishFileList([]);\n    setShowPublishModal(false);\n    dispatch(updateContextMenuPublishFiles(false));\n  };\n\n  const cancelUnpublishModal = () => {\n    setUnpublishFileList([]);\n    setShowUnpublishModal(false);\n    dispatch(updateContextMenuUnpublishFiles(false));\n  };\n\n  const handlePublishFiles = async () => {\n    dispatch(UpdateFunctionalFlowDataLoading(true));\n    const fileIds = publishFileList.filter(e => e.checked).map(e => e.id);\n\n    try {\n      const {\n        data\n      } = await publishFiles(fileIds, siteId, publishFileExpiration === null || publishFileExpiration === void 0 ? void 0 : publishFileExpiration.toDate());\n\n      if (data) {\n        syncGrid(true);\n        const files = publishFileList.filter(e => !e.checked).map(e => {\n          return { ...e,\n            checked: true\n          };\n        });\n\n        if (files.length === 0) {\n          cancelPublishModal();\n          return;\n        }\n\n        setPublishFileList(files);\n        setPublishFileExpiration(undefined);\n      }\n    } catch (e) {\n      errorNotification([''], 'Publishing Failed');\n      logger.error('File Area Module', 'Publish files', e);\n    } finally {\n      dispatch(UpdateFunctionalFlowDataLoading(false));\n    }\n  };\n\n  const handleFileUnlinkSubmit = () => {\n    const unlinkedData = selectedUnlinkFiles.filter(file => file.selectedBinders && file.selectedBinders.length > 0).map(file => ({\n      fileId: file.fileId,\n      binderIds: file.selectedBinders\n    }));\n    dispatch(UpdateFunctionalFlowDataLoading(true));\n    unlinkFilesFromBinders(unlinkedData).then(response => {\n      if (response.data) {\n        successNotification([''], 'File(s) Unlinked Successfully');\n        syncGrid(true);\n        resetUnlinkFilesModal();\n      } else {\n        errorNotification([''], 'File Unlinking Failed');\n      }\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'File Unlinking Failed');\n      }\n\n      logger.error('File Area Module', 'Unlink files', error);\n    }).finally(() => {\n      dispatch(UpdateFunctionalFlowDataLoading(false));\n    });\n  };\n\n  const handleUnpublishFiles = async () => {\n    dispatch(UpdateFunctionalFlowDataLoading(true));\n    const fileIds = unpublishFileList.filter(e => e.checked).map(e => e.id);\n\n    try {\n      const {\n        data\n      } = await unpublishFiles(fileIds, siteId);\n\n      if (data) {\n        syncGrid(true);\n        const files = publishFileList.filter(e => !e.checked);\n\n        if (files.length === 0) {\n          cancelUnpublishModal();\n          return;\n        }\n\n        setUnpublishFileList(files);\n      }\n    } catch (e) {\n      errorNotification([''], 'Unpublishing Failed');\n      logger.error('File Area Module', 'Unpublish files', e);\n    } finally {\n      dispatch(UpdateFunctionalFlowDataLoading(false));\n    }\n  };\n\n  const handleShowAssignModalCancel = (isAllFilesRemoved = false) => {\n    if (assignOptionDetails.assignee > 0 || assignOptionForm.getFieldValue('statusId') > 0 || assignOptionForm.getFieldValue('assignNotes')) {\n      if (isAllFilesRemoved) {\n        assignOptionForm.resetFields();\n        resetAssignModal();\n      } else {\n        cancelAssignModal();\n      }\n    } else {\n      resetAssignModal();\n    }\n  };\n\n  const onAssigneeUpdate = value => {\n    const currentAssignState = { ...assignOptionDetails\n    };\n    currentAssignState.assignee = value;\n    setAssignOptionDetails(currentAssignState);\n  };\n\n  const onFilesChange = fileList => {\n    const currentAssignState = { ...assignOptionDetails\n    };\n    currentAssignState.files = fileList;\n    setAssignOptionDetails(currentAssignState);\n  };\n\n  const handleAssignFormEvents = {\n    onAssigneeUpdate,\n    onFilesChange\n  };\n\n  const onUpdatedAssignee = response => {\n    if (response.data) {\n      const SET_TIMEOUT_ASSIGNEE = 500;\n      successNotification([''], 'Assignment Successful'); // successNotification([''], 'File Assignment Email sent successfully');\n\n      syncGrid(true);\n      dispatch(updateContextMenuAssignOption(false));\n      setTimeout(() => {\n        assignOptionForm.resetFields();\n        setShowAssignModal(false);\n        setAssignOptionDetails({\n          assignee: 0,\n          files: []\n        });\n      }, SET_TIMEOUT_ASSIGNEE);\n    } else {\n      errorNotification([''], 'Assignment Failed');\n    }\n  };\n\n  const handleAssignOptionUpdate = () => {\n    assignOptionForm.validateFields().then(values => {\n      const selectedFileIds = assignOptionDetails.files.length > 0 ? assignOptionDetails.files.map(file => file.id) : selectedFileList.map(file => file.id);\n      updateAssigneeNStatus({\n        assigneeId: values.assigneeId,\n        statusId: values.statusId,\n        fileIds: selectedFileIds,\n        note: values.assignNotes\n      }).then(response => {\n        onUpdatedAssignee(response);\n      }).catch(error => {\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n        } else {\n          errorNotification([''], 'Assignment Failed');\n        }\n\n        logger.error('AssignOption', 'Update Assignee and Status', error);\n      });\n    });\n  };\n\n  const onCancelRecategorizeModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 484,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        resetReCategorizeModal();\n      }\n\n    });\n  };\n\n  const onCancelMoveFilesModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 496,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        resetMoveFilesModal();\n      }\n\n    });\n  };\n\n  const onCancelLinkFilesModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 508,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        resetLinkFilesModal();\n      }\n\n    });\n  };\n\n  const onCancelReNameFilesModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 520,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        assignOptionForm.resetFields();\n        resetReNameFilesModal();\n      }\n\n    });\n  };\n\n  const [showReCategorizeModal, setShowReCategorizeModal] = useState(false);\n  const [showMoveFileModal, setShowMoveFileModal] = useState(false);\n  const [showLinkFileModal, setShowLinkFilesModal] = useState(false);\n  const [showReNameFilesModal, setShowReNameFilesModal] = useState(false);\n  const [showUnlinkFilesModal, setShowUnlinkFilesModal] = useState(false);\n  const [recategorizeDetails, setRecategorizeDetails] = useState();\n  const [moveFileDetails, setMoveFilesDetails] = useState();\n  const [linkFilesDetails, setLinkFilesDetails] = useState();\n  const [reNameFilesDetails, setReNameFilesDetails] = useState();\n\n  const handleShowReCategorizeModalCancel = (hasSelectedFiles = true) => {\n    if (hasSelectedFiles && (recategorizeDetails === null || recategorizeDetails === void 0 ? void 0 : recategorizeDetails.folderId) !== undefined) {\n      onCancelRecategorizeModal();\n    } else {\n      resetReCategorizeModal();\n    }\n  };\n\n  const handleCancel = () => setShowMoveFileModal(false);\n\n  const handleRenameCancel = () => setShowReNameFilesModal(false);\n\n  const handleReCategorizeFileCancel = () => setShowReCategorizeModal(false);\n\n  const handleShowMoveFilesModalCancel = (hasSelectedFiles = true) => {\n    if (hasSelectedFiles && (moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.folderId) !== undefined) {\n      onCancelMoveFilesModal();\n    } else {\n      resetMoveFilesModal();\n    }\n  };\n\n  const handleShowLinkFilesModal = show => {\n    const areToBeDeleted = selectedFileList.filter(file => file.status.name === TO_BE_DELETED_STATUS);\n\n    if (areToBeDeleted.length > 0 && show) {\n      errorNotification([''], 'Unable to link file(s). Please change the Status and retry.');\n      resetLinkFilesModal();\n    } else {\n      setShowLinkFilesModal(show);\n    }\n  };\n\n  const hasLinkedFiles = files => {\n    const areSelectedFilesLinked = files.some(file => file.linked);\n\n    if (areSelectedFilesLinked) {\n      errorNotification([''], 'You cannot perform this action to linked files. Please unlink files first.');\n    }\n\n    return areSelectedFilesLinked;\n  };\n\n  const handleShowLinkFilesModalCancel = () => {\n    resetLinkFilesModal();\n  };\n\n  const handleShowReNameFilesModalCancel = (hasSelectedFiles = true) => {\n    if (hasSelectedFiles) {\n      onCancelReNameFilesModal();\n    } else {\n      resetReNameFilesModal();\n    }\n  };\n\n  const handleShowUnlinkFilesModalCancel = () => {\n    resetUnlinkFilesModal();\n  };\n\n  const resetReCategorizeModal = () => {\n    setRecategorizeDetails({\n      folderId: undefined,\n      fileList: undefined\n    });\n    dispatch(updateContextMenuReCategorizeOption(false));\n    setShowReCategorizeModal(false);\n  };\n\n  const resetMoveFilesModal = () => {\n    setMoveFilesDetails({\n      folderId: undefined,\n      fileList: undefined,\n      sourceClientId: undefined,\n      destinationClientId: undefined\n    });\n    dispatch(updateContextMenuMoveFilesOption(false));\n    setShowMoveFileModal(false);\n  };\n\n  const resetLinkFilesModal = () => {\n    setLinkFilesDetails({\n      binderIds: undefined,\n      fileIds: undefined\n    });\n    dispatch(updateContextMenuLinkFilesOption(false));\n    handleShowLinkFilesModal(false);\n  };\n\n  const resetReNameFilesModal = () => {\n    setReNameFilesDetails({\n      fileList: undefined\n    });\n    dispatch(updateContextMenuReNameFilesOption(false));\n    setShowReNameFilesModal(false);\n    assignOptionForm.resetFields();\n    setRenameButtonEnable(false);\n  };\n\n  const resetUnlinkFilesModal = () => {\n    setSelectedUnlinkFiles([]);\n    dispatch(updateContextMenuUnlinkFilesOption(false));\n    setShowUnlinkFilesModal(false);\n  };\n\n  const handleReCategorizeUpdateDetails = (folderId, fileList) => {\n    setRecategorizeDetails({\n      folderId: folderId,\n      fileList: fileList\n    });\n  };\n\n  const handleLinkFilesUpdateDetails = (fileList, selectedBinderIds) => {\n    setLinkFilesDetails({\n      fileIds: fileList,\n      binderIds: selectedBinderIds\n    });\n  };\n\n  const handleValuesChange = (changedValues, allValues) => {\n    // Check if any input field has a non-empty value\n    const anyRenamedValues = Object.values(allValues).some(value => value);\n    setRenameButtonEnable(anyRenamedValues);\n  };\n\n  const handleReNameFilesUpdateDetails = fileList => {\n    const updatedFileList = fileList.map(file => ({\n      fileId: file.id,\n      title: file.title\n    }));\n    reNameFiles(updatedFileList).then(response => {\n      successNotification([''], 'ReNamed Successfully');\n      syncGrid(true);\n      resetReNameFilesModal();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'ReName Failed');\n      }\n\n      logger.error('File Area Module', 'ReName files', error);\n    });\n  };\n\n  const handleRecategorizeUpdate = event => {\n    recategorizeFiles(recategorizeDetails === null || recategorizeDetails === void 0 ? void 0 : recategorizeDetails.fileList.map(file => file.id), (recategorizeDetails === null || recategorizeDetails === void 0 ? void 0 : recategorizeDetails.folderId) ? recategorizeDetails.folderId : 0, binderId).then(response => {\n      successNotification([''], 'File Re-Categorization Successful');\n      syncGrid(true);\n      resetReCategorizeModal();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'File Re-Categorization Failed');\n      }\n\n      logger.error('File ARea Module', 'File Re-Categorization', error);\n    });\n  };\n\n  const handleLinkFileUpdate = () => {\n    dispatch(UpdateFunctionalFlowDataLoading(true));\n    linkToSite(linkFilesDetails).then(response => {\n      successNotification([''], 'File(s) Linked Successfully');\n      syncGrid(true);\n      resetLinkFilesModal();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'File Linking Failed');\n      }\n\n      logger.error('File Area Module', 'Move files', error);\n    }).finally(() => {\n      dispatch(UpdateFunctionalFlowDataLoading(false));\n    });\n  };\n\n  const handleFileReNameSubmit = () => {\n    assignOptionForm.submit();\n  };\n\n  const cancelChangeStatusModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 747,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        setStatusFileRemoved(false);\n        setSelectedNewStatusState(0);\n        dispatch(updateContextMenuStatusOption(false));\n        setShowStatusChangeModal(false);\n      }\n\n    });\n  };\n\n  const [selectedNewStatusState, setSelectedNewStatusState] = useState(0);\n  const [fileStatusList, setFilesStatusList] = useState();\n  const [showStatusChangeModal, setShowStatusChangeModal] = useState(false);\n  const [statusFileRemoved, setStatusFileRemoved] = useState(false);\n\n  const handleCloseStatusModal = () => {\n    dispatch(updateContextMenuStatusOption(false));\n    setShowStatusChangeModal(false);\n    setFilesStatusList([]);\n    setStatusFileRemoved(false);\n  };\n\n  const handleShowStatusChangeModalCancel = () => {\n    if (selectedNewStatusState > 0 || statusFileRemoved) {\n      cancelChangeStatusModal();\n    } else {\n      handleCloseStatusModal();\n    }\n  };\n\n  const resetFileStatusValues = () => {\n    setFilesStatusList([]);\n    dispatch(updateContextMenuStatusOption(false));\n    setShowStatusChangeModal(false);\n    setSelectedNewStatusState(0);\n    syncGrid(true);\n  };\n\n  const handleNewStatusUpdate = () => {\n    const isDeleteStatus = selectedNewStatusState === 6;\n\n    if (isDeleteStatus) {\n      if (hasLinkedFiles(selectedFileList)) {\n        return;\n      }\n    }\n\n    updateFileStatus(fileStatusList && fileStatusList.length > 0 ? fileStatusList === null || fileStatusList === void 0 ? void 0 : fileStatusList.map(file => file.id) : selectedFileList === null || selectedFileList === void 0 ? void 0 : selectedFileList.map(file => file.id), selectedNewStatusState).then(response => {\n      successNotification([''], 'Status Update Successful');\n      resetFileStatusValues();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'Status Update Failed');\n      }\n\n      logger.error('File ARea Module', 'Update Status', error);\n    });\n    dispatch(updateContextMenuStatusOption(false));\n  };\n\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\n  const [downloadType, setDownloadType] = useState(downloadTypes.individual);\n  const [checkoutZip, setCheckoutZip] = useState(false);\n  const [showCheckoutdModal, setshowCheckoutdModal] = useState(false);\n  const [validatedCheckoutForm, setValidatedCheckoutForm] = useState(true);\n  const [renameButtonEnable, setRenameButtonEnable] = useState(false);\n\n  const renderToggleIcon = () => {\n    return showPanel ? /*#__PURE__*/React.createElement(MenuFoldOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 24\n      }\n    }) : /*#__PURE__*/React.createElement(MenuUnfoldOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 820,\n        columnNumber: 47\n      }\n    });\n  };\n\n  const handleOnToggleClicked = event => {\n    toggleIconClicked(event);\n    setShowPanel(!showPanel);\n  };\n\n  const handleOnDownloadModalCancel = () => {\n    dispatch(updateContextMenuDownloadOption(false));\n    setShowDownloadModal(false);\n  };\n\n  const displaydownloadModal = (downloadTypeInput, display) => {\n    setDownloadType(downloadTypeInput);\n    setShowDownloadModal(display);\n  };\n\n  const [documentPropeties, displayDocumentPropeties] = useState(false);\n  useEffect(() => {\n    setCheckoutZip(false);\n\n    if (checkout) {\n      setCheckingOutFilesCount(selectedFileList.length);\n    }\n\n    setshowCheckoutdModal(checkout);\n  }, [checkout, selectedFileList]);\n  useEffect(() => {\n    displaydownloadModal(downloadTypes.individual, download);\n  }, [download]);\n  useEffect(() => {\n    setShowStatusChangeModal(status);\n  }, [status]);\n  useEffect(() => {\n    setShowAssignModal(assign);\n  }, [assign]);\n  useEffect(() => {\n    displayDocumentPropeties(propeties);\n  }, [propeties]);\n  useEffect(() => {\n    if (reCategorize && !hasLinkedFiles(selectedFileList)) {\n      setShowReCategorizeModal(true);\n    }\n\n    dispatch(updateContextMenuReCategorizeOption(false));\n  }, [reCategorize]);\n  useEffect(() => {\n    if (moveFiles && !hasLinkedFiles(selectedFileList)) {\n      setShowMoveFileModal(true);\n    }\n\n    dispatch(updateContextMenuMoveFilesOption(false));\n  }, [moveFiles]);\n  useEffect(() => {\n    if (toBeDeleted && !hasLinkedFiles(selectedFileList)) {\n      handleToBeDelete(selectedFileList);\n    }\n\n    dispatch(updateContextMenuToBeDeleted(false));\n  }, [toBeDeleted]);\n  useEffect(() => {\n    if (copyLink && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\n      handleCopyLink(selectedFileList);\n    }\n\n    dispatch(updateContextMenuCopyLinkFiles(false));\n  }, [copyLink]);\n  useEffect(() => {\n    setShowReNameFilesModal(renameFiles);\n  }, [renameFiles]);\n  useEffect(() => {\n    handleShowLinkFilesModal(linkFiles);\n  }, [linkFiles]);\n  useEffect(() => {\n    setShowUnlinkFilesModal(unLinkFiles);\n  }, [unLinkFiles]);\n  useEffect(() => {\n    if (copySelectedFiles && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\n      showCopyFilesDialog(copySelectedFiles);\n    }\n\n    dispatch(updateContextMenuCopyFilesOption(false));\n  }, [copySelectedFiles]);\n  useEffect(() => {\n    dispatch(getSavedFilters(TABLE_KEY, siteId));\n  }, []);\n  useEffect(() => {\n    if (publish && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\n      const mapToCheckoutFiles = selectedFiles => {\n        const checkoutFileList = [];\n        selectedFiles === null || selectedFiles === void 0 ? void 0 : selectedFiles.forEach(file => {\n          checkoutFileList.push({\n            id: file.id,\n            checked: true,\n            title: file.title\n          });\n        });\n        return checkoutFileList;\n      };\n\n      setPublishFileList(mapToCheckoutFiles(selectedFileList));\n      setShowPublishModal(true);\n      dispatch(updateContextMenuPublishFiles(false));\n    }\n  }, [publish]);\n  useEffect(() => {\n    if (unpublish && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\n      const mapFiles = selectedFiles => {\n        const fileList = [];\n        selectedFiles === null || selectedFiles === void 0 ? void 0 : selectedFiles.forEach(file => {\n          fileList.push({\n            id: file.id,\n            checked: true,\n            title: file.title\n          });\n        });\n        return fileList;\n      };\n\n      setUnpublishFileList(mapFiles(selectedFileList));\n      setShowUnpublishModal(true);\n      dispatch(updateContextMenuUnpublishFiles(false));\n    }\n  }, [unpublish]);\n\n  const showCopyFilesDialog = value => {\n    if (value) {\n      confirm({\n        title: \"File(s) will be copied. Do you wish to continue?\",\n        icon: /*#__PURE__*/React.createElement(CopyOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 957,\n            columnNumber: 15\n          }\n        }),\n        okText: 'Yes',\n        cancelText: 'No',\n        onOk: () => {\n          handleCopyFiles();\n          dispatch(updateContextMenuCopyFilesOption(false));\n        },\n        onCancel: () => {\n          dispatch(updateContextMenuCopyFilesOption(false));\n        }\n      });\n    }\n  };\n\n  const handleOnCheckoutModalCancel = () => {\n    confirm({\n      title: 'Are you sure you want to discard the changes?',\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 976,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        dispatch(updateContextMenuCheckoutOption(false));\n        setshowCheckoutdModal(false);\n      }\n\n    });\n  };\n\n  const handleFileDelete = response => {\n    if (response) {\n      successNotification([''], 'File(s) Deleted Successfully');\n      syncGrid(true);\n      dispatch(updateContextMenuDeleteOption(false));\n    } else {\n      errorNotification([''], 'Deletion Failed');\n      dispatch(updateContextMenuDeleteOption(false));\n    }\n  };\n\n  const onClickOkDeleteFiles = useCallback(selectedFiles => {\n    const fileIdList = [];\n    selectedFiles.forEach(file => {\n      fileIdList.push(file.id);\n    });\n    deleteFile(fileIdList).then(response => {\n      handleFileDelete(response);\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'Deletion Failed');\n      }\n\n      dispatch(updateContextMenuDeleteOption(false));\n    });\n  }, []);\n\n  const handleToBeDelete = selectedFiles => {\n    updateFileStatus(selectedFiles.map(e => e.id), 6).then(() => syncGrid(true));\n  };\n\n  const handleCopyLink = selectedFiles => {\n    let ids = selectedFiles.map(e => e.id).join(',');\n    copyToClipboard(ids);\n    syncGrid(true);\n  };\n\n  const handleDeleteFiles = useCallback(selectedFiles => {\n    const totalFiles = selectedFiles.length;\n    const publishedCount = selectedFiles.filter(file => file.published).length;\n    const fileLabel = `${totalFiles} ${totalFiles === 1 ? 'file' : 'files'}`;\n    const message = `${fileLabel} will be deleted.${publishedCount > 0 ? ' Any published files would be unpublished when deleted.' : ''} Are you sure you want to proceed?`;\n    confirm({\n      title: message,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 1036,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        onClickOkDeleteFiles(selectedFiles);\n      },\n\n      onCancel() {\n        dispatch(updateContextMenuDeleteOption(false));\n      }\n\n    });\n  }, []);\n  useEffect(() => {\n    if (deleteFiles && selectedFileList.length > 0) {\n      if (deleteFiles && !hasLinkedFiles(selectedFileList)) {\n        handleDeleteFiles(selectedFileList);\n      }\n\n      dispatch(updateContextMenuDeleteOption(false));\n    }\n  }, [deleteFiles, handleDeleteFiles, selectedFileList]);\n\n  const setCheckoutCommonNote = values => {\n    values.files.forEach(value => {\n      value.checkNote = values.commonNote;\n    });\n  };\n\n  const formatReturnDates = values => {\n    const returnCheckoutValues = [];\n    values.files.forEach(value => {\n      var _value$returnDate;\n\n      returnCheckoutValues.push({ ...value,\n        returnDate: value === null || value === void 0 ? void 0 : (_value$returnDate = value.returnDate) === null || _value$returnDate === void 0 ? void 0 : _value$returnDate.format('YYYY-MM-DD')\n      });\n    });\n    return returnCheckoutValues;\n  };\n\n  const handleSavedCheckoutFiles = () => {\n    setshowCheckoutdModal(false);\n    dispatch(updateContextMenuCheckoutOption(false));\n    setDownloadType(checkoutZip ? downloadTypes.checkoutZip : downloadTypes.checkoutIndividual);\n    setShowDownloadModal(true);\n    fileDownloaded(true);\n    dispatch(updateLoadGridOption(true));\n    setTimeout(() => {\n      dispatch(updateLoadGridOption(false));\n    });\n\n    if (emailForm.getFieldsValue().emailUser || emailForm.getFieldsValue().emailContact) {\n      successNotification([''], 'Check-out Email Sent Successfully');\n    }\n  };\n\n  const onCheckoutFiles = () => {\n    form.validateFields().then(async values => {\n      values.files = values.files.filter(file => file.checked);\n\n      if (values.commonNote) {\n        setCheckoutCommonNote(values);\n      }\n\n      setCheckedOuFilesDownload(values.files);\n      const {\n        hasError,\n        errorCode\n      } = await checkoutFiles(formatReturnDates(values));\n\n      if (hasError) {\n        errorCode && errorCode === FORBIDDEN_ERROR_CODE ? errorNotification([''], FORBIDDEN_ERROR_MESSAGE) : errorCode && errorCode === CHECKOUT_FAILED_ERRORCODE ? errorNotification([''], LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE) : errorNotification([''], 'Check-Out Failed');\n      } else {\n        handleSavedCheckoutFiles();\n      }\n    });\n  };\n\n  const downloadOptionsMenu = /*#__PURE__*/React.createElement(_Menu, {\n    className: styles.yjFilterMenuDropdownWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1109,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(_Menu.Item, {\n    hidden: selectedFileList && (selectedFileList === null || selectedFileList === void 0 ? void 0 : selectedFileList.length) <= 0,\n    onClick: () => {\n      displaydownloadModal(downloadTypes.individual, true);\n    },\n    key: \"1\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1110,\n      columnNumber: 7\n    }\n  }, \"Download Files\"), /*#__PURE__*/React.createElement(_Menu.Item, {\n    hidden: selectedFileList && (selectedFileList === null || selectedFileList === void 0 ? void 0 : selectedFileList.length) <= 1,\n    onClick: () => {\n      displaydownloadModal(downloadTypes.zip, true);\n    },\n    key: \"2\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1119,\n      columnNumber: 7\n    }\n  }, \"Download as a zip file\"));\n\n  const onSaveFilterTemplate = () => {\n    dispatch(updateFilterTemplateSaved(true));\n    const createFilterTemplateRequest = {\n      name: newFilterName.trim(),\n      content: {}\n    };\n    gridFilters.forEach(filter => {\n      const field = filter['key'];\n\n      if ((filter === null || filter === void 0 ? void 0 : filter.isArray) && ((filter === null || filter === void 0 ? void 0 : filter.value) || (filter === null || filter === void 0 ? void 0 : filter.value) === 0)) {\n        if (createFilterTemplateRequest.content[field] === undefined) {\n          createFilterTemplateRequest.content[field] = [];\n        }\n\n        createFilterTemplateRequest.content[field] = [...createFilterTemplateRequest.content[field], filter['value']];\n      } else {\n        createFilterTemplateRequest.content[field] = filter['value'];\n      }\n    });\n    dispatch(saveFilterTemplate(createFilterTemplateRequest, TABLE_KEY, siteId));\n    setShowAddNewFilterModal(false);\n    setNewFilterName('');\n  };\n\n  const onCheckoutAction = () => {\n    setCheckoutZip(false);\n    setshowCheckoutdModal(true);\n    setCheckingOutFilesCount(selectedFileList.length);\n  };\n\n  const onRefreshGrid = () => {\n    syncGrid(true);\n    dispatch(updateGridHasUpdates(false));\n  };\n\n  const onPublishAction = () => {\n    dispatch(updateContextMenuPublishFiles(true));\n  };\n\n  const onUnpublishAction = () => {\n    dispatch(updateContextMenuUnpublishFiles(true));\n  };\n\n  const handleMoveFilesActionBar = () => {\n    if (!showMoveFiles) return;\n\n    if (!hasLinkedFiles(selectedFileList)) {\n      setShowMoveFileModal(true);\n    }\n  };\n\n  const handleReCategorizeActionBr = () => {\n    if (!showReCategorize) return;\n\n    if (!hasLinkedFiles(selectedFileList)) {\n      setShowReCategorizeModal(true);\n    }\n  };\n\n  const handleCopyFilesActionBar = () => {\n    if (!showCopy) return;\n\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\n      showCopyFilesDialog(true);\n    }\n  };\n\n  const handleDeleteActionBar = () => {\n    if (!showDelete) return;\n\n    if (!hasLinkedFiles(selectedFileList)) {\n      handleDeleteFiles(selectedFileList);\n    }\n  };\n\n  const handleToBeDeletedActionBar = () => {\n    if (!showToBeDelete) return;\n\n    if (!hasLinkedFiles(selectedFileList)) {\n      handleToBeDelete(selectedFileList);\n    }\n  };\n\n  const handleCopyLinkActionBar = () => {\n    if (!showCopyLink) return;\n\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\n      handleCopyLink(selectedFileList);\n    }\n  };\n\n  const handleManageTags = () => {\n    if (!hasLinkedFiles(selectedFileList)) {\n      setShowTagsManageModal(true);\n    }\n  };\n\n  const handlePublishActionBar = () => {\n    if (!showPublish) return;\n\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\n      onPublishAction();\n    }\n  };\n\n  const handleUnpublishActionBar = () => {\n    if (!showUnpublish) return;\n\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\n      onUnpublishAction();\n    }\n  };\n\n  const SET_TIMEOUT_ASSIGNEE = 500;\n\n  const handleCopyFiles = () => {\n    const selectedFileUploadReferenceList = selectedFileList === null || selectedFileList === void 0 ? void 0 : selectedFileList.map(file => file.uploadReference);\n    copyFiles(selectedFileUploadReferenceList).then(response => {\n      successNotification([''], 'Copied Successfully');\n      setTimeout(() => {\n        syncGrid(true);\n      }, SET_TIMEOUT_ASSIGNEE);\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'Copy Failed');\n      }\n\n      logger.error('File Area Module', 'Copy Files', error);\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DocumentPropeties, {\n    file: selectedFileList[0],\n    onCloseDrawer: () => {\n      displayDocumentPropeties(false);\n      dispatch(updateContextMenuPropetiesoption(false));\n    },\n    displayDrawer: documentPropeties,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1262,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(ReCategorize, {\n    onSuccess: () => syncGrid(true),\n    selectedFiles: selectedFileList,\n    onClosePopup: handleReCategorizeFileCancel,\n    options: options,\n    form: assignOptionForm,\n    onFolderTreeChange: onFolderTreeChange,\n    showReCategorizeModal: showReCategorizeModal // Provide the 'options' prop\n    ,\n    binderId: binderId,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1273,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(MoveFiles, {\n    siteId: siteId,\n    binderId: binderId,\n    onSuccess: () => syncGrid(true),\n    selectedFiles: selectedFileList,\n    onClosePopup: handleCancel,\n    options: options,\n    form: assignOptionForm,\n    onFolderTreeChange: onFolderTreeChange,\n    showMoveFileModal: showMoveFileModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1286,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(ReNameFiles, {\n    onSuccess: () => syncGrid(true),\n    selectedFiles: selectedFileList,\n    onClosePopup: handleRenameCancel,\n    options: options,\n    form: assignOptionForm,\n    showReNameFilesModal: showReNameFilesModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1299,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(_Drawer, {\n    visible: showAddNewFilterModal,\n    title: 'Save as a New Filter',\n    width: 700,\n    onClose: handleAddNewFilterCancel,\n    className: \"yjDrawerPanel\",\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      key: \"back\",\n      type: \"default\",\n      onClick: handleAddNewFilterCancel,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1317,\n        columnNumber: 11\n      }\n    }, \"cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      disabled: newFilterName.trim().length < 1 || !isNameValid,\n      key: \"submit\",\n      type: \"primary\",\n      onClick: () => onSaveFilterTemplate(),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1320,\n        columnNumber: 11\n      }\n    }, \"Save\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1310,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1325,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FilterTemplateManagementSave, {\n    isNameValid: isNameValid,\n    newFilterName: newFilterName,\n    onFilterNameChangeHandler: name => onChangeSaveNewFilterName(name),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1326,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(_Drawer, {\n    visible: showEditFiltersModal,\n    title: 'Manage Filter',\n    width: 700,\n    onClose: () => setShowEditFiltersModal(false),\n    className: \"yjDrawerPanel\",\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      key: \"back\",\n      type: \"default\",\n      onClick: () => setShowEditFiltersModal(false),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1337,\n        columnNumber: 11\n      }\n    }, \"cancel\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1330,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1342,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(FilterTemplateManagementEdit, {\n    tableKey: TABLE_KEY,\n    groupedValue: siteId,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1343,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(AssignOption, {\n    formRef: assignOptionForm,\n    siteId: siteId,\n    selectedFiles: selectedFileList,\n    onClosePopup: () => handleShowAssignModalCancel(true),\n    showAssignModal: showAssignModal,\n    onSuccess: () => syncGrid(true),\n    form: assignOptionForm,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1350,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(TagManagementDrawer, {\n    visible: showTagsManageModal,\n    onClose: handleShowTagManageModalCancel,\n    binderId: binderId,\n    syncGrid: () => syncGrid(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1363,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(_Drawer, {\n    width: 700,\n    visible: showStatusChangeModal,\n    title: 'Update Status',\n    onClose: handleShowStatusChangeModalCancel,\n    className: \"yjDrawerPanel\",\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      key: \"back\",\n      type: \"default\",\n      onClick: handleShowStatusChangeModalCancel,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1378,\n        columnNumber: 11\n      }\n    }, \"cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      key: \"update\",\n      type: \"primary\",\n      onClick: handleNewStatusUpdate,\n      disabled: selectedNewStatusState === 0,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1381,\n        columnNumber: 11\n      }\n    }, \"update\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1371,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1386,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(ChangeStatus, {\n    onFilesChange: (fileList, onMounted, onAllRemoved) => {\n      if (onAllRemoved) {\n        handleCloseStatusModal();\n        return;\n      }\n\n      if (!onMounted && !onAllRemoved) {\n        setStatusFileRemoved(true);\n      }\n\n      setFilesStatusList(fileList);\n    },\n    onFinish: handleNewStatusUpdate,\n    form: changeStatusForm,\n    selectedFiles: selectedFileList,\n    onNewStatusSelect: setSelectedNewStatusState,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1387,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(_Modal, {\n    visible: showDownloadModal,\n    title: 'Download Files',\n    maskClosable: false,\n    destroyOnClose: true,\n    className: \"yjCommonModalSmall\",\n    onCancel: handleOnDownloadModalCancel,\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      onClick: handleOnDownloadModalCancel,\n      key: \"submit\",\n      type: \"primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1415,\n        columnNumber: 11\n      }\n    }, \"Done\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1407,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1420,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(DownloadModal, {\n    hasDownloaded: hasDownloaded => {\n      if (hasDownloaded) {\n        setShowDownloadModal(false);\n      }\n    },\n    selectedFiles: downloadType === downloadTypes.checkoutIndividual || downloadType === downloadTypes.checkoutZip ? checkedOuFilesDownload : selectedFileList,\n    downloadType: downloadType,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1421,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(_Drawer, {\n    destroyOnClose: true,\n    key: 'checkoutModal',\n    visible: showCheckoutdModal,\n    title: 'Check-out Files',\n    onClose: handleOnCheckoutModalCancel,\n    width: 700,\n    className: \"yjDrawerPanel\",\n    footer: [/*#__PURE__*/React.createElement(React.Fragment, null, selectedFileList.length > 1 && checkingOutFilesCount > 1 && /*#__PURE__*/React.createElement(_Checkbox, {\n      key: 1,\n      onChange: e => {\n        setCheckoutZip(e.target.checked);\n      },\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1445,\n        columnNumber: 15\n      }\n    }, \"As ZIP Files\")), /*#__PURE__*/React.createElement(_Button, {\n      key: 'cancelCheckout',\n      onClick: handleOnCheckoutModalCancel,\n      type: \"default\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1456,\n        columnNumber: 11\n      }\n    }, \"Cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      disabled: !validatedCheckoutForm,\n      key: 'openCheckout',\n      onClick: onCheckoutFiles,\n      type: \"primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1459,\n        columnNumber: 11\n      }\n    }, \"check-out\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1434,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1464,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(CheckoutOption, {\n    validatedCheckoutEmail: validated => {\n      setValidatedCheckoutForm(validated);\n    },\n    onCloseModal: closeModal => {\n      if (closeModal) {\n        setshowCheckoutdModal(false);\n        dispatch(updateContextMenuCheckoutOption(false));\n      }\n    },\n    onFileRemoved: fileCount => {\n      setCheckingOutFilesCount(fileCount);\n    },\n    form: form,\n    emailForm: emailForm,\n    onFinish: onCheckoutFiles,\n    fileList: selectedFileList ? selectedFileList : [],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1465,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(PublishFilesDrawer, {\n    isLoading: isLoading,\n    visible: showPublishModal,\n    publishFileList: publishFileList,\n    onClose: cancelPublishModal,\n    onFileListChange: setPublishFileList,\n    onPublish: handlePublishFiles,\n    expirationDate: publishFileExpiration,\n    setExpirationDate: setPublishFileExpiration,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1487,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(UnpublishFilesDrawer, {\n    isLoading: isLoading,\n    visible: showUnpublishModal,\n    unpublishFileList: unpublishFileList,\n    onClose: cancelUnpublishModal,\n    onFileListChange: setUnpublishFileList,\n    onUnpublish: handleUnpublishFiles,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1499,\n      columnNumber: 7\n    }\n  }), showLinkFileModal && /*#__PURE__*/React.createElement(LinkFilesDrawer, {\n    linkFilesLoading: isLoading,\n    onClosePopup: handleShowLinkFilesModalCancel,\n    onItemSelect: handleLinkFilesUpdateDetails,\n    onSuccess: handleLinkFileUpdate,\n    selectedFiles: selectedFileList,\n    showDrawer: showLinkFileModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1510,\n      columnNumber: 9\n    }\n  }), showUnlinkFilesModal && /*#__PURE__*/React.createElement(UnlinkFilesDrawer, {\n    unlinkFilesLoading: isLoading,\n    onUnlinkChange: setSelectedUnlinkFiles,\n    onClosePopup: handleShowUnlinkFilesModalCancel,\n    onSuccess: handleFileUnlinkSubmit,\n    selectedFiles: selectedFileList,\n    showDrawer: showUnlinkFilesModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1517,\n      columnNumber: 9\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjFileAreaMainActionPanel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1521,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjActionListContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1522,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    onClick: handleOnToggleClicked,\n    className: styles.yjFileAreaCollapsibleTriggerWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1523,\n      columnNumber: 11\n    }\n  }, renderToggleIcon()), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanViewFileArea || !hasPermission(folderTree, 'FILE_AREA_DOWNLOAD'),\n    className: `${styles.yjActionListWrapper} ${!showDownload ? styles.disabled : \"\"}`,\n    \"aria-disabled\": !showDownload,\n    tabIndex: showDownload ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1526,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Dropdown, {\n    getPopupContainer: () => document.getElementById('downloadOptionsMenu'),\n    overlay: downloadOptionsMenu,\n    trigger: ['click'],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1527,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1528,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(_Button, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1529,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    id: \"downloadOptionsMenu\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1530,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(DownloadOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1532,\n      columnNumber: 23\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1532,\n      columnNumber: 44\n    }\n  }, \"Download\"))))))), /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"div\", {\n    className: `${styles.yjActionListWrapper} ${!showAssign ? styles.disabled : \"\"}`,\n    onClick: () => showAssign ? setShowAssignModal(true) : undefined,\n    \"aria-disabled\": !showAssign,\n    tabIndex: showAssign ? 0 : -1,\n    hidden: !userPermission.privDMSCanManageFileAssign || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1545,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(AuditOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1546,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1546,\n      columnNumber: 33\n    }\n  }, \"Assign\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${styles.yjActionListWrapper} ${!showStatus ? styles.disabled : \"\"}`,\n    onClick: () => showStatus ? setShowStatusChangeModal(true) : undefined,\n    \"aria-disabled\": !showStatus,\n    tabIndex: showStatus ? 0 : -1,\n    hidden: !userPermission.privDMSCanManageFileStatus || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1548,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(FileDoneOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1549,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1549,\n      columnNumber: 36\n    }\n  }, \"Status\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${styles.yjActionListWrapper} ${!showMoveFiles ? styles.disabled : \"\"}`,\n    onClick: handleMoveFilesActionBar,\n    \"aria-disabled\": !showMoveFiles,\n    tabIndex: showMoveFiles ? 0 : -1,\n    hidden: !userPermission.privDMSCanMoveFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1552,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(DragOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1554,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1554,\n      columnNumber: 32\n    }\n  }, \"Move\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${styles.yjActionListWrapper} ${!showReCategorize ? styles.disabled : \"\"}`,\n    hidden: !userPermission.privDMSCanRecategorizeFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    onClick: handleReCategorizeActionBr,\n    \"aria-disabled\": !showReCategorize,\n    tabIndex: showReCategorize ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1557,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(ApartmentOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1560,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1560,\n      columnNumber: 37\n    }\n  }, \"Re-Categorize\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanRenameFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showReName ? styles.disabled : \"\"}`,\n    onClick: () => showReName ? setShowReNameFilesModal(true) : undefined,\n    \"aria-disabled\": !showReName,\n    tabIndex: showReName ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1562,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(EditOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1564,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1564,\n      columnNumber: 32\n    }\n  }, \"ReName\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanCopyFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showCopy ? styles.disabled : \"\"}`,\n    onClick: handleCopyFilesActionBar,\n    \"aria-disabled\": !showCopy,\n    tabIndex: showCopy ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1567,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(CopyOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1569,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1569,\n      columnNumber: 32\n    }\n  }, \"Copy\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanCheckInCheckOutInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showCheckout ? styles.disabled : \"\"}`,\n    \"aria-disabled\": !showCheckout,\n    tabIndex: showCheckout ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1571,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(_Button, {\n    onClick: () => showCheckout ? onCheckoutAction() : undefined,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1572,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(ContainerOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1573,\n      columnNumber: 17\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1573,\n      columnNumber: 39\n    }\n  }, \"Check-out\"))), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanPublishUnpublishInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showPublish ? styles.disabled : \"\"}`,\n    \"aria-disabled\": !showPublish,\n    tabIndex: showPublish ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1576,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(_Button, {\n    onClick: handlePublishActionBar,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1577,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(MdOpenInNew, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1578,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      marginTop: '-3px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1579,\n      columnNumber: 17\n    }\n  }, \"Publish\"))), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanPublishUnpublishInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showUnpublish ? styles.disabled : \"\"}`,\n    \"aria-disabled\": !showUnpublish,\n    tabIndex: showUnpublish ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1582,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(_Button, {\n    onClick: handleUnpublishActionBar,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1583,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(MdOpenInNew, {\n    style: {\n      transform: 'rotate(180deg)'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1584,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    style: {\n      marginTop: '-3px'\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1585,\n      columnNumber: 17\n    }\n  }, \"Unpublish\"))), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanDeleteFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showDelete ? styles.disabled : \"\"}`,\n    onClick: handleDeleteActionBar,\n    \"aria-disabled\": !showDelete,\n    tabIndex: showDelete ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1588,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(DeleteOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1591,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1591,\n      columnNumber: 34\n    }\n  }, \"Delete\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') || !userPermission.privDMSCanViewFileArea,\n    onClick: () => showPropeties && (displayDocumentPropeties(true), dispatch(updateContextMenuPropetiesoption(true))),\n    \"aria-disabled\": !showPropeties,\n    tabIndex: showPropeties ? 0 : -1,\n    className: `${styles.yjActionListWrapper} ${!showPropeties || !userPermission.privDMSCanViewFileArea ? styles.disabled : \"\"}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1593,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(SettingOutlined, {\n    className: `yJFileAreaRow`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1598,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(\"span\", {\n    className: ` yJFileAreaRow`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1599,\n      columnNumber: 15\n    }\n  }, \"Properties\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanMarkFilesAsToBeDeleted || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!userPermission.privDMSCanMarkFilesAsToBeDeleted || !showToBeDelete ? styles.disabled : \"\"}`,\n    onClick: handleToBeDeletedActionBar,\n    \"aria-disabled\": !showToBeDelete,\n    tabIndex: showToBeDelete ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1601,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(DeleteOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1604,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1604,\n      columnNumber: 34\n    }\n  }, \"To be Deleted\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanViewFileSourceLink,\n    className: `${styles.yjActionListWrapper} ${!userPermission.privDMSCanViewFileSourceLink || !showCopyLink ? styles.disabled : \"\"}`,\n    onClick: handleCopyLinkActionBar,\n    \"aria-disabled\": !showCopyLink,\n    tabIndex: showCopyLink ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1606,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(ShareAltOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1608,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1608,\n      columnNumber: 36\n    }\n  }, \"Copy Link\")), /*#__PURE__*/React.createElement(\"div\", {\n    className: `${styles.yjActionListWrapper} ${!showLinkFiles ? styles.disabled : \"\"}`,\n    onClick: () => showLinkFiles ? handleShowLinkFilesModal(true) : undefined,\n    \"aria-disabled\": !showLinkFiles,\n    tabIndex: showLinkFiles ? 0 : -1,\n    hidden: !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') || !userPermission.privDMSCanLinkUnlinkFiles,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1610,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(LinkOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1612,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1612,\n      columnNumber: 32\n    }\n  }, \"Link Files\")), /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanLinkUnlinkFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS'),\n    className: `${styles.yjActionListWrapper} ${!showUnlinkFiles ? styles.disabled : \"\"}`,\n    onClick: () => showUnlinkFiles ? setShowUnlinkFilesModal(true) : undefined,\n    \"aria-disabled\": !showUnlinkFiles,\n    tabIndex: showUnlinkFiles ? 0 : -1,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1614,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(DisconnectOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1616,\n      columnNumber: 15\n    }\n  }), \" \", /*#__PURE__*/React.createElement(\"span\", {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1616,\n      columnNumber: 38\n    }\n  }, \"Unlink Files\")))), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjActionButtonsContainer,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1620,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjActionButtonsLeftCorner,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1621,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjFileAreaFilterDropdownWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1622,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(GridRefreshIcon, {\n    hasUpdates: hasUpdates,\n    onRefreshGrid: onRefreshGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1623,\n      columnNumber: 15\n    }\n  }), showFiltersButton ? /*#__PURE__*/React.createElement(_Button, {\n    icon: /*#__PURE__*/React.createElement(FilterOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1625,\n        columnNumber: 31\n      }\n    }),\n    className: styles.yjFiltersButton,\n    onClick: () => setShowFiltersButton(false),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1625,\n      columnNumber: 17\n    }\n  }, \"Filters \", /*#__PURE__*/React.createElement(DownOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1626,\n      columnNumber: 27\n    }\n  })) : /*#__PURE__*/React.createElement(_Select, {\n    showSearch: true,\n    style: {\n      width: 200\n    },\n    placeholder: \"Select a filter\",\n    optionFilterProp: \"children\",\n    autoFocus: true,\n    defaultOpen: true,\n    onBlur: () => setShowFiltersButton(true),\n    showArrow: false,\n    onSelect: onClickSavedFilter,\n    notFoundContent: `No Results Found`,\n    filterOption: onSearchSavedFilters,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1629,\n      columnNumber: 17\n    }\n  }, sortedSavedFilterList.length > 0 && /*#__PURE__*/React.createElement(Option, {\n    disabled: true,\n    key: -1,\n    value: 'Manage Filters',\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1643,\n      columnNumber: 21\n    }\n  }, /*#__PURE__*/React.createElement(_Button, {\n    className: styles.yjDropdownManageFilters,\n    type: \"primary\",\n    onClick: () => setShowEditFiltersModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1644,\n      columnNumber: 23\n    }\n  }, \"Manage Filters\")), sortedSavedFilterList && sortedSavedFilterList.map(savedFilter => {\n    return /*#__PURE__*/React.createElement(Option, {\n      key: savedFilter.id,\n      value: savedFilter.id,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1653,\n        columnNumber: 25\n      }\n    }, savedFilter.name);\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjFileAreaFilterActionButtonWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1661,\n      columnNumber: 13\n    }\n  }, showFilterSaveButton && /*#__PURE__*/React.createElement(_Button, {\n    disabled: filter_template_saved,\n    type: \"primary\",\n    className: styles.yjSaveFilterButton,\n    onClick: () => setShowAddNewFilterModal(true),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1663,\n      columnNumber: 17\n    }\n  }, \"Save as a new filter\"))), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjActionButtonsRightCorner,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1669,\n      columnNumber: 11\n    }\n  }, additionalActionPanel, /*#__PURE__*/React.createElement(_Button, {\n    hidden: !userPermission.privDMSCanManageFileTags,\n    disabled: !showManageTags,\n    \"aria-disabled\": !showManageTags,\n    tabIndex: showManageTags ? 0 : -1,\n    onClick: handleManageTags,\n    className: styles.yjManageTagsButton,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1671,\n      columnNumber: 13\n    }\n  }, \"Manage Tags\"), /*#__PURE__*/React.createElement(ColumnFilter, {\n    tableKey: TABLE_KEY,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1680,\n      columnNumber: 13\n    }\n  }), showFilter && /*#__PURE__*/React.createElement(ClearFilter, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1681,\n      columnNumber: 28\n    }\n  })))));\n};", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/FileArea/FileAreaActionPanel/index.tsx"], "names": ["React", "useCallback", "useEffect", "useState", "useDispatch", "useSelector", "ApartmentOutlined", "AuditOutlined", "ContainerOutlined", "CopyOutlined", "DeleteOutlined", "DownloadOutlined", "DownOutlined", "DragOutlined", "EditOutlined", "ExclamationCircleOutlined", "FileDoneOutlined", "FilterOutlined", "MenuFoldOutlined", "MenuUnfoldOutlined", "SettingOutlined", "LinkOutlined", "DisconnectOutlined", "ShareAltOutlined", "useForm", "styles", "AssignOption", "<PERSON>umn<PERSON><PERSON><PERSON>", "ReCategorize", "ClearFilter", "DownloadModal", "downloadTypes", "updateContextMenuAssignOption", "updateContextMenuCheckoutOption", "updateContextMenuDeleteOption", "updateContextMenuDownloadOption", "updateContextMenuPropetiesoption", "updateContextMenuPublishFiles", "updateContextMenuReCategorizeOption", "updateContextMenuMoveFilesOption", "updateContextMenuReNameFilesOption", "updateContextMenuCopyFilesOption", "updateContextMenuStatusOption", "updateContextMenuUnpublishFiles", "updateLoadGridOption", "updateContextMenuLinkFilesOption", "updateContextMenuUnlinkFilesOption", "updateContextMenuToBeDeleted", "updateContextMenuCopyLinkFiles", "ChangeStatus", "errorNotification", "successNotification", "CheckoutOption", "deleteFile", "recategorizeFiles", "updateAssigneeNStatus", "updateFileStatus", "copyFiles", "reNameFiles", "linkToSite", "unlinkFilesFromBinders", "logger", "DocumentPropeties", "FilterTemplateManagementEdit", "FilterTemplateManagementSave", "applyASavedFilter", "clearGridFilters", "getSavedFilters", "saveFilterTemplate", "updateFilterTemplateSaved", "onSelectedSavedFilter", "updateGridHasUpdates", "getGridFilterFromIFilterTemplate", "LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE", "checkoutFiles", "FORBIDDEN_ERROR_CODE", "GridRefreshIcon", "MdOpenInNew", "publishFiles", "unpublishFiles", "copyToClipboard", "ReNameFiles", "hasPermission", "MoveFiles", "LinkFilesDrawer", "UnlinkFilesDrawer", "useParams", "UpdateFunctionalFlowDataLoading", "UnpublishFilesDrawer", "PublishFilesDrawer", "TagManagementDrawer", "FORBIDDEN_ERROR_MESSAGE", "confirm", "Option", "elementName", "TABLE_KEY", "TO_BE_DELETED_STATUS", "FileAreaActionPanel", "siteId", "toggleIconClicked", "selectedFileList", "showDownload", "showStatus", "showAssign", "showPropeties", "showReCategorize", "showMoveFiles", "showCheckout", "fileDownloaded", "showDelete", "showPublish", "showUnpublish", "showToBeDelete", "showCopyLink", "showLinkFiles", "showUnlinkFiles", "showReName", "showCopy", "syncGrid", "originalFileAreaWithLinked", "showManageTags", "showManageCheckin", "onFolderTreeChange", "additionalActionPanel", "CHECKOUT_FAILED_ERRORCODE", "form", "changeStatusForm", "emailForm", "showPanel", "setShowPanel", "showAddNewFilterModal", "setShowAddNewFilterModal", "showEditFiltersModal", "setShowEditFiltersModal", "showTagsManageModal", "setShowTagsManageModal", "showPublishModal", "setShowPublishModal", "showUnpublishModal", "setShowUnpublishModal", "assignOptionForm", "selectedUnlinkFiles", "setSelectedUnlinkFiles", "assignOptionDetails", "setAssignOptionDetails", "assignee", "files", "showAssignModal", "setShowAssignModal", "handleAddNewFilterCancel", "setNewFilterName", "setNameValid", "newFilterName", "sortedSavedFilterList", "setSortedFilterList", "showFilter", "showFilterSaveButton", "gridFilters", "savedFilters", "columns", "filter_template_saved", "hasUpdates", "state", "grid", "download", "status", "assign", "checkout", "publish", "unpublish", "deleteFiles", "propeties", "reCategorize", "moveFiles", "renameFiles", "copySelectedFiles", "linkFiles", "unLinkFiles", "toBeDeleted", "copyLink", "fileArea", "userPermission", "userManagement", "folderTree", "isLoading", "functionalFlow", "isNameValid", "showFiltersButton", "setShowFiltersButton", "publishFileList", "setPublishFileList", "unpublishFileList", "setUnpublishFileList", "publishFileExpiration", "setPublishFileExpiration", "undefined", "dispatch", "binderId", "length", "sortedList", "sort", "filter1", "filter2", "name", "localeCompare", "onClickSavedFilter", "value", "savedFilterIndex", "findIndex", "filter", "id", "savedFilter", "gridFilterTemplates", "content", "savedFilterColumns", "Object", "getOwnPropertyNames", "optionalFilterColumns", "column", "default", "includes", "key", "map", "filterColumns", "selected", "selectedElement", "checked", "multiple", "options", "isOptionsFetched", "successedFiles", "pendingSave", "permissions", "fileDetails", "onChangeSaveNewFilterName", "toLowerCase", "trim", "onSearchSavedFilters", "input", "option", "children", "startsWith", "handleShowTagManageModalCancel", "DISCARD_MESSAGE", "checkingOutFilesCount", "setCheckingOutFilesCount", "checkedOuFilesDownload", "setCheckedOuFilesDownload", "resetAssignModal", "cancelAssignModal", "title", "icon", "okText", "cancelText", "onOk", "resetFields", "cancelPublishModal", "cancelUnpublishModal", "handlePublishFiles", "fileIds", "e", "data", "toDate", "error", "handleFileUnlinkSubmit", "unlinkedData", "file", "<PERSON><PERSON><PERSON><PERSON>", "fileId", "binderIds", "then", "response", "resetUnlinkFilesModal", "catch", "statusCode", "finally", "handleUnpublishFiles", "handleShowAssignModalCancel", "isAllFilesRemoved", "getFieldValue", "onAssigneeUpdate", "currentAssignState", "onFilesChange", "fileList", "handleAssignFormEvents", "onUp<PERSON><PERSON><PERSON><PERSON>", "SET_TIMEOUT_ASSIGNEE", "setTimeout", "handleAssignOptionUpdate", "validateFields", "values", "selectedFileIds", "assigneeId", "statusId", "note", "assignNotes", "onCancelRecategorizeModal", "resetReCategorizeModal", "onCancelMoveFilesModal", "resetMoveFilesModal", "onCancelLinkFilesModal", "resetLinkFilesModal", "onCancelReNameFilesModal", "resetReNameFilesModal", "showReCategorizeModal", "setShowReCategorizeModal", "showMoveFileModal", "setShowMoveFileModal", "showLinkFileModal", "setShowLinkFilesModal", "showReNameFilesModal", "setShowReNameFilesModal", "showUnlinkFilesModal", "setShowUnlinkFilesModal", "recategorizeDetails", "setRecategorizeDetails", "moveFileDetails", "setMoveFilesDetails", "linkFilesDetails", "setLinkFilesDetails", "reNameFilesDetails", "setReNameFilesDetails", "handleShowReCategorizeModalCancel", "hasSelectedFiles", "folderId", "handleCancel", "handleRenameCancel", "handleReCategorizeFileCancel", "handleShowMoveFilesModalCancel", "handleShowLinkFilesModal", "show", "areToBeDeleted", "hasLinkedFiles", "areSelectedFilesLinked", "some", "linked", "handleShowLinkFilesModalCancel", "handleShowReNameFilesModalCancel", "handleShowUnlinkFilesModalCancel", "sourceClientId", "destinationClientId", "setRenameButtonEnable", "handleReCategorizeUpdateDetails", "handleLinkFilesUpdateDetails", "selectedBinderIds", "handleValuesChange", "changedValues", "allValues", "anyRenamedV<PERSON>ues", "handleReNameFilesUpdateDetails", "updatedFileList", "handleRecategorizeUpdate", "event", "handleLinkFileUpdate", "handleFileReNameSubmit", "submit", "cancelChangeStatusModal", "setStatusFileRemoved", "setSelectedNewStatusState", "setShowStatusChangeModal", "selectedNewStatusState", "fileStatusList", "setFilesStatusList", "showStatusChangeModal", "statusFileRemoved", "handleCloseStatusModal", "handleShowStatusChangeModalCancel", "resetFileStatusV<PERSON>ues", "handleNewStatusUpdate", "isDeleteStatus", "showDownloadModal", "setShowDownloadModal", "downloadType", "setDownloadType", "individual", "checkoutZip", "setCheckoutZip", "showCheckoutdModal", "setshowCheckoutdModal", "validatedCheckoutForm", "setValidatedCheckoutForm", "renameButtonEnable", "renderToggleIcon", "handleOnToggleClicked", "handleOnDownloadModalCancel", "displaydownloadModal", "downloadTypeInput", "display", "documentPropeties", "displayDocumentPropeties", "handleToBeDelete", "handleCopyLink", "showCopyFilesDialog", "mapToCheckoutFiles", "selectedFiles", "checkoutFileList", "for<PERSON>ach", "push", "mapFiles", "handleCopyFiles", "onCancel", "handleOnCheckoutModalCancel", "handleFileDelete", "onClickOkDeleteFiles", "fileIdList", "ids", "join", "handleDeleteFiles", "totalFiles", "publishedCount", "published", "fileLabel", "message", "setCheckoutCommonNote", "checkNote", "commonNote", "formatReturnDates", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "returnDate", "format", "handleSavedCheckoutFiles", "checkoutIndividual", "getFieldsValue", "emailUser", "emailContact", "onCheckoutFiles", "<PERSON><PERSON><PERSON><PERSON>", "errorCode", "downloadOptionsMenu", "yjFilterMenuDropdownWrapper", "zip", "onSaveFilterTemplate", "createFilterTemplateRequest", "field", "isArray", "onCheckoutAction", "onRefreshGrid", "onPublishAction", "onUnpublishAction", "handleMoveFilesActionBar", "handleReCategorizeActionBr", "handleCopyFilesActionBar", "handleDeleteActionBar", "handleToBeDeletedActionBar", "handleCopyLinkActionBar", "handleManageTags", "handlePublishActionBar", "handleUnpublishActionBar", "selectedFileUploadReferenceList", "uploadReference", "yjModalContentWrapper", "onMounted", "onAllRemoved", "hasDownloaded", "target", "validated", "closeModal", "fileCount", "yjFileAreaMainActionPanel", "yjActionListContainer", "yjFileAreaCollapsibleTriggerWrapper", "privDMSCanViewFileArea", "yjActionListWrapper", "disabled", "document", "getElementById", "privDMSCanManageFileAssign", "privDMSCanManageFileStatus", "privDMSCanMoveFiles", "privDMSCanRecategorizeFiles", "privDMSCanRenameFiles", "privDMSCanCopyFiles", "privDMSCanCheckInCheckOutInternalFiles", "privDMSCanPublishUnpublishInternalFiles", "marginTop", "transform", "privDMSCanDeleteFiles", "privDMSCanMarkFilesAsToBeDeleted", "privDMSCanViewFileSourceLink", "privDMSCanLinkUnlinkFiles", "yjActionButtonsContainer", "yjActionButtonsLeftCorner", "yjFileAreaFilterDropdownWrapper", "yj<PERSON><PERSON>ersButton", "width", "yjDropdownManageFilters", "yjFileAreaFilterActionButtonWrapper", "yj<PERSON><PERSON><PERSON><PERSON>er<PERSON>utton", "yjActionButtonsRightCorner", "privDMSCanManageFileTags", "yjManageTagsButton"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,WAAhB,EAA6BC,SAA7B,EAAwCC,QAAxC,QAAwD,OAAxD;AACA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,aAAzC;AAEA,SACEC,iBADF,EAEEC,aAFF,EAGEC,iBAHF,EAIEC,YAJF,EAKEC,cALF,EAMEC,gBANF,EAOEC,YAPF,EAQEC,YARF,EASEC,YATF,EAUEC,yBAVF,EAWEC,gBAXF,EAYEC,cAZF,EAaEC,gBAbF,EAcEC,kBAdF,EAeEC,eAfF,EAgBEC,YAhBF,EAiBEC,kBAjBF,EAkBEC,gBAlBF,QAmBO,mBAnBP;AAoBA,SAASC,OAAT,QAAwB,oBAAxB;AAIA,OAAOC,MAAP,MAAmB,qBAAnB;AAEA,OAAOC,YAAP,MAA+C,+BAA/C;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AACA,OAAOC,YAAP,MAAyB,qCAAzB;AACA,OAAOC,WAAP,MAAwB,6BAAxB;AAEA,OAAOC,aAAP,IAAwBC,aAAxB,QAA6C,+BAA7C;AAGA,SACEC,6BADF,EAEEC,+BAFF,EAGEC,6BAHF,EAIEC,+BAJF,EAKEC,gCALF,EAMEC,6BANF,EAOEC,mCAPF,EAQEC,gCARF,EASEC,kCATF,EAUEC,gCAVF,EAWEC,6BAXF,EAYEC,+BAZF,EAaEC,oBAbF,EAcEC,gCAdF,EAeEC,kCAfF,EAgBEC,4BAhBF,EAiBEC,8BAjBF,QAkBO,oCAlBP;AAmBA,OAAOC,YAAP,MAAyB,iBAAzB;AACA,SAASC,iBAAT,EAA4BC,mBAA5B,QAAuD,6BAAvD;AACA,OAAOC,cAAP,MAA8C,mBAA9C;AACA,SAASC,UAAT,EAAqBC,iBAArB,EAAwCC,qBAAxC,EAA+DC,gBAA/D,EAAmGC,SAAnG,EAA8GC,WAA9G,EAA2HC,UAA3H,EAAuIC,sBAAvI,QAAqK,0BAArK;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,OAAOC,iBAAP,MAA8B,sBAA9B;AAEA,OAAOC,4BAAP,MAAyC,gEAAzC;AACA,OAAOC,4BAAP,MAAyC,gEAAzC;AAGA,SACEC,iBADF,EAEEC,gBAFF,EAGEC,eAHF,EAIEC,kBAJF,EAKEC,yBALF,EAMEC,qBANF,EAOEC,oBAPF,QAQO,iCARP;AASA,SAASC,gCAAT,QAAiD,gEAAjD;AAGA,SAASC,mCAAT,QAAoD,0BAApD;AACA,SAASC,aAAT,QAA8B,8CAA9B;AACA,SAASC,oBAAT,QAAqC,YAArC;AACA,SAASC,eAAT,QAAgC,8CAAhC;AAGA,SAASC,WAAT,QAA4B,gBAA5B;AACA,SAASC,YAAT,QAA6B,6CAA7B;AACA,SAASC,cAAT,QAA+B,+CAA/B;AACA,SAASC,eAAT,QAAgC,uCAAhC;AACA,OAAOC,WAAP,MAAwB,gBAAxB;AACA,OAAOC,aAAP,MAA0B,uBAA1B;AACA,OAAOC,SAAP,MAAsB,6CAAtB;AACA,OAAOC,eAAP,MAA4B,oCAA5B;AACA,OAAOC,iBAAP,MAA4C,wCAA5C;AACA,SAASC,SAAT,QAA0B,kBAA1B;AACA,SAASC,+BAAT,QAAgD,0CAAhD;AAEA,OAAOC,oBAAP,MAAiC,4DAAjC;AACA,OAAOC,kBAAP,MAA+B,wDAA/B;AACA,SAASC,mBAAT,QAAoC,0DAApC;AAEA,MAAMC,uBAAuB,GAAG,qFAAhC;AA+BA,MAAM;AAAEC,EAAAA;AAAF,UAAN;AACA,MAAM;AAAEC,EAAAA;AAAF,WAAN;AACA,MAAMC,WAAW,GAAG,qBAApB;AACA,MAAMC,SAAS,GAAG,UAAlB;AACA,MAAMC,oBAAoB,GAAG,eAA7B;AAEA,OAAO,MAAMC,mBAAmD,GAAG,CAAC;AAClEC,EAAAA,MADkE;AAElEC,EAAAA,iBAFkE;AAGlEC,EAAAA,gBAHkE;AAIlEC,EAAAA,YAAY,GAAG,KAJmD;AAKlEC,EAAAA,UAAU,GAAG,KALqD;AAMlEC,EAAAA,UAAU,GAAG,KANqD;AAOlEC,EAAAA,aAAa,GAAG,KAPkD;AAQlEC,EAAAA,gBAAgB,GAAG,KAR+C;AASlEC,EAAAA,aAAa,GAAG,KATkD;AAUlEC,EAAAA,YAAY,GAAG,KAVmD;AAWlEC,EAAAA,cAXkE;AAYlEC,EAAAA,UAAU,GAAG,KAZqD;AAalEC,EAAAA,WAAW,GAAG,KAboD;AAclEC,EAAAA,aAAa,GAAG,KAdkD;AAelEC,EAAAA,cAAc,GAAG,KAfiD;AAgBlEC,EAAAA,YAAY,GAAG,KAhBmD;AAiBlEC,EAAAA,aAAa,GAAG,KAjBkD;AAkBlEC,EAAAA,eAAe,GAAG,KAlBgD;AAmBlEC,EAAAA,UAAU,GAAG,KAnBqD;AAoBlEC,EAAAA,QAAQ,GAAG,KApBuD;AAqBlEC,EAAAA,QArBkE;AAsBlEC,EAAAA,0BAA0B,GAAG,KAtBqC;AAuBlEC,EAAAA,cAAc,GAAG,KAvBiD;AAwBlEC,EAAAA,iBAAiB,GAAG,KAxB8C;AAyBlEC,EAAAA,kBAAkB,GAAG,MAAM,CAAG,CAzBoC;AA0BlEC,EAAAA,qBAAqB,GAAG;AA1B0C,CAAD,KA2B7D;AACJ,QAAMC,yBAAyB,GAAG,GAAlC;AACA,QAAM,CAACC,IAAD,IAASrG,OAAO,EAAtB;AACA,QAAM,CAACsG,gBAAD,IAAqBtG,OAAO,EAAlC;AACA,QAAM,CAACuG,SAAD,IAAcvG,OAAO,EAA3B;AACA,QAAM,CAACwG,SAAD,EAAYC,YAAZ,IAA4B9H,QAAQ,CAAC,IAAD,CAA1C;AACA,QAAM,CAAC+H,qBAAD,EAAwBC,wBAAxB,IAAoDhI,QAAQ,CAAC,KAAD,CAAlE;AACA,QAAM,CAACiI,oBAAD,EAAuBC,uBAAvB,IAAkDlI,QAAQ,CAAC,KAAD,CAAhE;AACA,QAAM,CAACmI,mBAAD,EAAsBC,sBAAtB,IAAgDpI,QAAQ,CAAC,KAAD,CAA9D;AACA,QAAM,CAACqI,gBAAD,EAAmBC,mBAAnB,IAA0CtI,QAAQ,CAAC,KAAD,CAAxD;AACA,QAAM,CAACuI,kBAAD,EAAqBC,qBAArB,IAA8CxI,QAAQ,CAAC,KAAD,CAA5D;;AACA,QAAM,CAACyI,gBAAD,IAAqB,MAAKpH,OAAL,EAA3B;;AACA,QAAM,CAACqH,mBAAD,EAAsBC,sBAAtB,IAAgD3I,QAAQ,CAAa,EAAb,CAA9D;AACA,QAAM,CAAC4I,mBAAD,EAAsBC,sBAAtB,IAAgD7I,QAAQ,CAG3D;AAAE8I,IAAAA,QAAQ,EAAE,CAAZ;AAAeC,IAAAA,KAAK,EAAE;AAAtB,GAH2D,CAA9D;AAIA,QAAM,CAACC,eAAD,EAAkBC,kBAAlB,IAAwCjJ,QAAQ,CAAC,KAAD,CAAtD;;AACA,QAAMkJ,wBAAwB,GAAG,MAAM;AACrCC,IAAAA,gBAAgB,CAAC,EAAD,CAAhB;AACAnB,IAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACAoB,IAAAA,YAAY,CAAC,IAAD,CAAZ;AACD,GAJD;;AAKA,QAAM,CAACC,aAAD,EAAgBF,gBAAhB,IAAoCnJ,QAAQ,CAAC,EAAD,CAAlD;AACA,QAAM,CAACsJ,qBAAD,EAAwBC,mBAAxB,IAA+CvJ,QAAQ,CAAC,EAAD,CAA7D;AACA,QAAM;AAAEwJ,IAAAA,UAAF;AAAcC,IAAAA,oBAAd;AAAoCC,IAAAA,WAApC;AAAiDC,IAAAA,YAAjD;AAA+DC,IAAAA,OAA/D;AAAwEC,IAAAA,qBAAxE;AAA+FC,IAAAA;AAA/F,MAA8G5J,WAAW,CAAE6J,KAAD,IAAsBA,KAAK,CAACC,IAA7B,CAA/H;AACA,QAAM;AAAEC,IAAAA,QAAF;AAAYC,IAAAA,MAAZ;AAAoBC,IAAAA,MAApB;AAA4BC,IAAAA,QAA5B;AAAsCC,IAAAA,OAAtC;AAA+CC,IAAAA,SAA/C;AAA0DC,IAAAA,WAA1D;AAAuEC,IAAAA,SAAvE;AAAkFC,IAAAA,YAAlF;AAAgGC,IAAAA,SAAhG;AAA2GC,IAAAA,WAA3G;AAAwHC,IAAAA,iBAAxH;AAA2IC,IAAAA,SAA3I;AAAsJC,IAAAA,WAAtJ;AAAmKC,IAAAA,WAAnK;AAAgLC,IAAAA;AAAhL,MAA6L9K,WAAW,CAAE6J,KAAD,IAAsBA,KAAK,CAACkB,QAA7B,CAA9M;AACA,QAAM;AAAEC,IAAAA;AAAF,MAAqBhL,WAAW,CAAE6J,KAAD,IAAsBA,KAAK,CAACoB,cAA7B,CAAtC;AACA,QAAM;AAAEC,IAAAA;AAAF,MAAiBlL,WAAW,CAAE6J,KAAD,IAAsBA,KAAK,CAACkB,QAA7B,CAAlC;AACA,QAAM;AAAEI,IAAAA;AAAF,MAAgBnL,WAAW,CAAE6J,KAAD,IAAsBA,KAAK,CAACuB,cAA7B,CAAjC;AAEA,QAAM,CAACC,WAAD,EAAcnC,YAAd,IAA8BpJ,QAAQ,CAAC,IAAD,CAA5C;AACA,QAAM,CAACwL,iBAAD,EAAoBC,oBAApB,IAA4CzL,QAAQ,CAAC,IAAD,CAA1D;AACA,QAAM,CAAC0L,eAAD,EAAkBC,kBAAlB,IAAwC3L,QAAQ,CAAkB,EAAlB,CAAtD;AACA,QAAM,CAAC4L,iBAAD,EAAoBC,oBAApB,IAA4C7L,QAAQ,CAAoB,EAApB,CAA1D;AACA,QAAM,CAAC8L,qBAAD,EAAwBC,wBAAxB,IAAoD/L,QAAQ,CAA4BgM,SAA5B,CAAlE;AACA,QAAMC,QAAQ,GAAGhM,WAAW,EAA5B;AACA,QAAM;AAAEiM,IAAAA;AAAF,MAAe/G,SAAS,EAA9B;AAEApF,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI4J,YAAY,CAACwC,MAAb,KAAwB,CAA5B,EAA+B;AAC7BjE,MAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACD;;AACD,UAAMkE,UAAU,GAAIzC,YAAD,CAAwB0C,IAAxB,CAA6B,CAACC,OAAD,EAAeC,OAAf,KAAgC;AAC9E,aAAOD,OAAO,CAACE,IAAR,CAAaC,aAAb,CAA2BF,OAAO,CAACC,IAAnC,CAAP;AACD,KAFkB,CAAnB;AAGAjD,IAAAA,mBAAmB,CAAC6C,UAAD,CAAnB;AACD,GARQ,EAQN,CAACzC,YAAD,CARM,CAAT,CAvCI,CAiDJ;;AACA5J,EAAAA,SAAS,CAAC,MAAM;AACdkM,IAAAA,QAAQ,CAAClI,gBAAgB,EAAjB,CAAR;AACD,GAFQ,EAEN,EAFM,CAAT;;AAKA,QAAM2I,kBAAkB,GAAIC,KAAD,IAAgB;AACzC,UAAMC,gBAAgB,GAAGjD,YAAY,CAACkD,SAAb,CAAwBC,MAAD,IAAiCA,MAAM,CAACC,EAAP,KAAcJ,KAAtE,CAAzB;AACA,UAAMK,WAAgC,GAAGrD,YAAY,CAACiD,gBAAD,CAArD;;AACA,QAAII,WAAJ,EAAiB;AACf,YAAMC,mBAAmB,GAAG5I,gCAAgC,CAAC2I,WAAW,CAACE,OAAb,EAAsBtD,OAAtB,CAA5D;AACA,YAAMuD,kBAAkB,GAAGC,MAAM,CAACC,mBAAP,CAA2BL,WAAW,CAACE,OAAvC,CAA3B;AACA,YAAMI,qBAAqB,GAAG1D,OAAO,CAACkD,MAAR,CAAgBS,MAAD,IAAiB,CAACA,MAAM,CAACC,OAAR,IAAmBL,kBAAkB,CAACM,QAAnB,CAA4BF,MAAM,CAACG,GAAnC,CAAnD,EAA4FC,GAA5F,CAAiGJ,MAAD,IAAiBA,MAAM,CAACG,GAAxH,CAA9B;AACA,YAAME,aAAa,GAAGhE,OAAO,CAAC+D,GAAR,CAAaJ,MAAD,IAAiB;AACjD,YAAID,qBAAqB,CAACG,QAAtB,CAA+BF,MAAM,CAACG,GAAtC,CAAJ,EAAgD;AAC9C,iBAAO,EAAE,GAAGH,MAAL;AAAaM,YAAAA,QAAQ,EAAE;AAAvB,WAAP;AACD,SAFD,MAEO;AACL,iBAAON,MAAP;AACD;AACF,OANqB,CAAtB;AAQAtB,MAAAA,QAAQ,CACNnI,iBAAiB,CAAC;AAChBmJ,QAAAA,mBADgB;AAEhBW,QAAAA,aAFgB;AAGhBC,QAAAA,QAAQ,EAAEP,qBAAqB,CAACnB,MAAtB,GAA+B,CAHzB;AAIhB2B,QAAAA,eAAe,EAAE;AACftB,UAAAA,IAAI,EAAE7G,WADS;AAEfoI,UAAAA,OAAO,EAAE,IAFM;AAGfC,UAAAA,QAAQ,EAAE;AAHK;AAJD,OAAD,CADX,CAAR;AAYA/B,MAAAA,QAAQ,CAAC9H,qBAAqB,CAAC6I,WAAD,CAAtB,CAAR;AACAvB,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACD;AACF,GA9BD;;AAgCA,QAAM;AACJwC,IAAAA,OADI;AAEJC,IAAAA,gBAFI;AAGJC,IAAAA,cAHI;AAIJC,IAAAA,WAJI;AAKJC,IAAAA;AALI,MAMFnO,WAAW,CAAE6J,KAAD,IAAsB;AACpC,WAAO;AACLkE,MAAAA,OAAO,EAAE,EACP,GAAGlE,KAAK,CAACuE,WAAN,CAAkBL,OADd;AAEP7C,QAAAA;AAFO,OADJ;AAOL8C,MAAAA,gBAAgB,EAAEnE,KAAK,CAACuE,WAAN,CAAkBJ,gBAP/B;AAQLC,MAAAA,cAAc,EAAEpE,KAAK,CAACuE,WAAN,CAAkBH,cAR7B;AASLC,MAAAA,WAAW,EAAErE,KAAK,CAACuE,WAAN,CAAkBF,WAT1B;AAULC,MAAAA,WAAW,EAAEtE,KAAK,CAACoB,cAAN,CAAqBD;AAV7B,KAAP;AAYD,GAbc,CANf;;AAqBA,QAAMqD,yBAAyB,GAAI/B,IAAD,IAAkB;AAClD,QACE7C,YAAY,CAACkD,SAAb,CAAwBC,MAAD,IAAiC;AACtD,aAAOA,MAAM,CAACN,IAAP,CAAYgC,WAAZ,OAA8BhC,IAAI,CAACgC,WAAL,GAAmBC,IAAnB,EAArC;AACD,KAFD,MAEO,CAAC,CAHV,EAIE;AACArF,MAAAA,YAAY,CAAC,KAAD,CAAZ;AACAD,MAAAA,gBAAgB,CAACqD,IAAD,CAAhB;AACD,KAPD,MAOO;AACLpD,MAAAA,YAAY,CAAC,IAAD,CAAZ;AACAD,MAAAA,gBAAgB,CAACqD,IAAD,CAAhB;AACD;AACF,GAZD;;AAcA,QAAMkC,oBAAoB,GAAG,CAACC,KAAD,EAAaC,MAAb,KAA6B;AACxD,QAAI,QAAOA,MAAP,aAAOA,MAAP,uBAAOA,MAAM,CAAEC,QAAf,MAA4B,QAAhC,EAA0C;AACxC,aAAOD,MAAM,CAACC,QAAP,CAAgBL,WAAhB,GAA8BM,UAA9B,CAAyCH,KAAK,CAACH,WAAN,EAAzC,CAAP;AACD,KAFD,MAEO;AACL,aAAO,KAAP;AACD;AACF,GAND;;AAQA,QAAMO,8BAA8B,GAAG,MAAM;AAC3C3G,IAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACD,GAFD;;AAGA,QAAM4G,eAAe,GAAG,+CAAxB;AACA,QAAM,CAACC,qBAAD,EAAwBC,wBAAxB,IAAoDlP,QAAQ,CAAC,CAAD,CAAlE;AACA,QAAM,CAACmP,sBAAD,EAAyBC,yBAAzB,IAAsDpP,QAAQ,CAAsBgM,SAAtB,CAApE;;AAEA,QAAMqD,gBAAgB,GAAG,MAAM;AAC7BxG,IAAAA,sBAAsB,CAAC;AAAEC,MAAAA,QAAQ,EAAE,CAAZ;AAAeC,MAAAA,KAAK,EAAE;AAAtB,KAAD,CAAtB;AACAkD,IAAAA,QAAQ,CAACpK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACAoH,IAAAA,kBAAkB,CAAC,KAAD,CAAlB;AACD,GAJD;;AAMA,QAAMqG,iBAAiB,GAAG,MAAM;AAC9B7J,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEP,eADD;AAENQ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLlH,QAAAA,gBAAgB,CAACmH,WAAjB;AACAP,QAAAA,gBAAgB;AACjB;;AARK,KAAD,CAAP;AAUD,GAXD;;AAaA,QAAMQ,kBAAkB,GAAG,MAAM;AAC/B9D,IAAAA,wBAAwB,CAACC,SAAD,CAAxB;AACAL,IAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACArD,IAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACA2D,IAAAA,QAAQ,CAAC/J,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD,GALD;;AAMA,QAAM4N,oBAAoB,GAAG,MAAM;AACjCjE,IAAAA,oBAAoB,CAAC,EAAD,CAApB;AACArD,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACAyD,IAAAA,QAAQ,CAACzJ,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD,GAJD;;AAMA,QAAMuN,kBAAkB,GAAG,YAAY;AACrC9D,IAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,IAAD,CAAhC,CAAR;AACA,UAAM4K,OAAO,GAAGtE,eAAe,CAACoB,MAAhB,CAAwBmD,CAAD,IAAOA,CAAC,CAAClC,OAAhC,EAAyCJ,GAAzC,CAA8CsC,CAAD,IAAOA,CAAC,CAAClD,EAAtD,CAAhB;;AAEA,QAAI;AACF,YAAM;AAAEmD,QAAAA;AAAF,UAAW,MAAMvL,YAAY,CAACqL,OAAD,EAAUjK,MAAV,EAAkB+F,qBAAlB,aAAkBA,qBAAlB,uBAAkBA,qBAAqB,CAAEqE,MAAvB,EAAlB,CAAnC;;AACA,UAAID,IAAJ,EAAU;AACR/I,QAAAA,QAAQ,CAAC,IAAD,CAAR;AACA,cAAM4B,KAAK,GAAG2C,eAAe,CAC1BoB,MADW,CACHmD,CAAD,IAAO,CAACA,CAAC,CAAClC,OADN,EAEXJ,GAFW,CAENsC,CAAD,IAAO;AACV,iBAAO,EAAE,GAAGA,CAAL;AAAQlC,YAAAA,OAAO,EAAE;AAAjB,WAAP;AACD,SAJW,CAAd;;AAKA,YAAIhF,KAAK,CAACoD,MAAN,KAAiB,CAArB,EAAwB;AACtB0D,UAAAA,kBAAkB;AAClB;AACD;;AACDlE,QAAAA,kBAAkB,CAAC5C,KAAD,CAAlB;AACAgD,QAAAA,wBAAwB,CAACC,SAAD,CAAxB;AACD;AACF,KAhBD,CAgBE,OAAOiE,CAAP,EAAU;AACVlN,MAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,mBAAP,CAAjB;AACAW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,eAAjC,EAAkDH,CAAlD;AACD,KAnBD,SAmBU;AACRhE,MAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD;AACF,GA1BD;;AA4BA,QAAMiL,sBAAsB,GAAG,MAAM;AACnC,UAAMC,YAAY,GAAG5H,mBAAmB,CACrCoE,MADkB,CACXyD,IAAI,IAAIA,IAAI,CAACC,eAAL,IAAwBD,IAAI,CAACC,eAAL,CAAqBrE,MAArB,GAA8B,CADnD,EAElBwB,GAFkB,CAEd4C,IAAI,KAAK;AACZE,MAAAA,MAAM,EAAEF,IAAI,CAACE,MADD;AAEZC,MAAAA,SAAS,EAAEH,IAAI,CAACC;AAFJ,KAAL,CAFU,CAArB;AAOAvE,IAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,IAAD,CAAhC,CAAR;AACA3B,IAAAA,sBAAsB,CAAC6M,YAAD,CAAtB,CACGK,IADH,CACSC,QAAD,IAAc;AAClB,UAAIA,QAAQ,CAACV,IAAb,EAAmB;AACjBlN,QAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,+BAAP,CAAnB;AACAmE,QAAAA,QAAQ,CAAC,IAAD,CAAR;AACA0J,QAAAA,qBAAqB;AACtB,OAJD,MAIO;AACL9N,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,uBAAP,CAAjB;AACD;AACF,KATH,EAUG+N,KAVH,CAUUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,uBAAP,CAAjB;AACD;;AACDW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,cAAjC,EAAiDA,KAAjD;AACD,KAjBH,EAiBKY,OAjBL,CAiBa,MAAM;AACf/E,MAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD,KAnBH;AAoBD,GA7BD;;AA+BA,QAAM6L,oBAAoB,GAAG,YAAY;AACvChF,IAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,IAAD,CAAhC,CAAR;AACA,UAAM4K,OAAO,GAAGpE,iBAAiB,CAACkB,MAAlB,CAA0BmD,CAAD,IAAOA,CAAC,CAAClC,OAAlC,EAA2CJ,GAA3C,CAAgDsC,CAAD,IAAOA,CAAC,CAAClD,EAAxD,CAAhB;;AACA,QAAI;AACF,YAAM;AAAEmD,QAAAA;AAAF,UAAW,MAAMtL,cAAc,CAACoL,OAAD,EAAUjK,MAAV,CAArC;;AACA,UAAImK,IAAJ,EAAU;AACR/I,QAAAA,QAAQ,CAAC,IAAD,CAAR;AACA,cAAM4B,KAAK,GAAG2C,eAAe,CAACoB,MAAhB,CAAwBmD,CAAD,IAAO,CAACA,CAAC,CAAClC,OAAjC,CAAd;;AACA,YAAIhF,KAAK,CAACoD,MAAN,KAAiB,CAArB,EAAwB;AACtB2D,UAAAA,oBAAoB;AACpB;AACD;;AACDjE,QAAAA,oBAAoB,CAAC9C,KAAD,CAApB;AACD;AACF,KAXD,CAWE,OAAOkH,CAAP,EAAU;AACVlN,MAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,qBAAP,CAAjB;AACAW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,iBAAjC,EAAoDH,CAApD;AACD,KAdD,SAcU;AACRhE,MAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD;AACF,GApBD;;AAsBA,QAAM8L,2BAA2B,GAAG,CAACC,iBAAiB,GAAG,KAArB,KAA+B;AACjE,QAAIvI,mBAAmB,CAACE,QAApB,GAA+B,CAA/B,IAAoCL,gBAAgB,CAAC2I,aAAjB,CAA+B,UAA/B,IAA6C,CAAjF,IAAsF3I,gBAAgB,CAAC2I,aAAjB,CAA+B,aAA/B,CAA1F,EAAyI;AACvI,UAAID,iBAAJ,EAAuB;AACrB1I,QAAAA,gBAAgB,CAACmH,WAAjB;AACAP,QAAAA,gBAAgB;AACjB,OAHD,MAGO;AACLC,QAAAA,iBAAiB;AAClB;AACF,KAPD,MAOO;AACLD,MAAAA,gBAAgB;AACjB;AACF,GAXD;;AAYA,QAAMgC,gBAAgB,GAAI1E,KAAD,IAAgB;AACvC,UAAM2E,kBAAkB,GAAG,EAAE,GAAG1I;AAAL,KAA3B;AACA0I,IAAAA,kBAAkB,CAACxI,QAAnB,GAA8B6D,KAA9B;AACA9D,IAAAA,sBAAsB,CAACyI,kBAAD,CAAtB;AACD,GAJD;;AAKA,QAAMC,aAAa,GAAIC,QAAD,IAAuB;AAC3C,UAAMF,kBAAkB,GAAG,EAAE,GAAG1I;AAAL,KAA3B;AACA0I,IAAAA,kBAAkB,CAACvI,KAAnB,GAA2ByI,QAA3B;AACA3I,IAAAA,sBAAsB,CAACyI,kBAAD,CAAtB;AACD,GAJD;;AAKA,QAAMG,sBAAwC,GAAG;AAC/CJ,IAAAA,gBAD+C;AAE/CE,IAAAA;AAF+C,GAAjD;;AAKA,QAAMG,iBAAiB,GAAId,QAAD,IAAiC;AACzD,QAAIA,QAAQ,CAACV,IAAb,EAAmB;AACjB,YAAMyB,oBAAoB,GAAG,GAA7B;AACA3O,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,uBAAP,CAAnB,CAFiB,CAGjB;;AACAmE,MAAAA,QAAQ,CAAC,IAAD,CAAR;AACA8E,MAAAA,QAAQ,CAACpK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACA+P,MAAAA,UAAU,CAAC,MAAM;AACfnJ,QAAAA,gBAAgB,CAACmH,WAAjB;AACA3G,QAAAA,kBAAkB,CAAC,KAAD,CAAlB;AACAJ,QAAAA,sBAAsB,CAAC;AAAEC,UAAAA,QAAQ,EAAE,CAAZ;AAAeC,UAAAA,KAAK,EAAE;AAAtB,SAAD,CAAtB;AACD,OAJS,EAIP4I,oBAJO,CAAV;AAKD,KAXD,MAWO;AACL5O,MAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,mBAAP,CAAjB;AACD;AACF,GAfD;;AAiBA,QAAM8O,wBAAwB,GAAG,MAAM;AACrCpJ,IAAAA,gBAAgB,CAACqJ,cAAjB,GAAkCnB,IAAlC,CAAwCoB,MAAD,IAAY;AACjD,YAAMC,eAAe,GAAGpJ,mBAAmB,CAACG,KAApB,CAA0BoD,MAA1B,GAAmC,CAAnC,GAAuCvD,mBAAmB,CAACG,KAApB,CAA0B4E,GAA1B,CAA+B4C,IAAD,IAAUA,IAAI,CAACxD,EAA7C,CAAvC,GAA0F9G,gBAAgB,CAAC0H,GAAjB,CAAsB4C,IAAD,IAAUA,IAAI,CAACxD,EAApC,CAAlH;AACA3J,MAAAA,qBAAqB,CAAC;AACpB6O,QAAAA,UAAU,EAAEF,MAAM,CAACE,UADC;AAEpBC,QAAAA,QAAQ,EAAEH,MAAM,CAACG,QAFG;AAGpBlC,QAAAA,OAAO,EAAEgC,eAHW;AAIpBG,QAAAA,IAAI,EAAEJ,MAAM,CAACK;AAJO,OAAD,CAArB,CAMGzB,IANH,CAMSC,QAAD,IAAc;AAClBc,QAAAA,iBAAiB,CAACd,QAAD,CAAjB;AACD,OARH,EASGE,KATH,CASUV,KAAD,IAAW;AAChB,YAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,UAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,SAFD,MAEO;AACLzC,UAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,mBAAP,CAAjB;AACD;;AACDW,QAAAA,MAAM,CAAC0M,KAAP,CAAa,cAAb,EAA6B,4BAA7B,EAA2DA,KAA3D;AACD,OAhBH;AAiBD,KAnBD;AAoBD,GArBD;;AAsBA,QAAMiC,yBAAyB,GAAG,MAAM;AACtC5M,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEP,eADD;AAENQ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACL2C,QAAAA,sBAAsB;AACvB;;AAPK,KAAD,CAAP;AASD,GAVD;;AAYA,QAAMC,sBAAsB,GAAG,MAAM;AACnC9M,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEP,eADD;AAENQ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACL6C,QAAAA,mBAAmB;AACpB;;AAPK,KAAD,CAAP;AASD,GAVD;;AAYA,QAAMC,sBAAsB,GAAG,MAAM;AACnChN,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEP,eADD;AAENQ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACL+C,QAAAA,mBAAmB;AACpB;;AAPK,KAAD,CAAP;AASD,GAVD;;AAYA,QAAMC,wBAAwB,GAAG,MAAM;AACrClN,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEP,eADD;AAENQ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLlH,QAAAA,gBAAgB,CAACmH,WAAjB;AACAgD,QAAAA,qBAAqB;AACtB;;AARK,KAAD,CAAP;AAUD,GAXD;;AAaA,QAAM,CAACC,qBAAD,EAAwBC,wBAAxB,IAAoD9S,QAAQ,CAAC,KAAD,CAAlE;AACA,QAAM,CAAC+S,iBAAD,EAAoBC,oBAApB,IAA4ChT,QAAQ,CAAC,KAAD,CAA1D;AACA,QAAM,CAACiT,iBAAD,EAAoBC,qBAApB,IAA6ClT,QAAQ,CAAC,KAAD,CAA3D;AACA,QAAM,CAACmT,oBAAD,EAAuBC,uBAAvB,IAAkDpT,QAAQ,CAAC,KAAD,CAAhE;AACA,QAAM,CAACqT,oBAAD,EAAuBC,uBAAvB,IAAkDtT,QAAQ,CAAC,KAAD,CAAhE;AACA,QAAM,CAACuT,mBAAD,EAAsBC,sBAAtB,IAAgDxT,QAAQ,EAA9D;AAKA,QAAM,CAACyT,eAAD,EAAkBC,mBAAlB,IAAyC1T,QAAQ,EAAvD;AAOA,QAAM,CAAC2T,gBAAD,EAAmBC,mBAAnB,IAA0C5T,QAAQ,EAAxD;AAKA,QAAM,CAAC6T,kBAAD,EAAqBC,qBAArB,IAA8C9T,QAAQ,EAA5D;;AAIA,QAAM+T,iCAAiC,GAAG,CAACC,gBAAgB,GAAG,IAApB,KAA6B;AACrE,QAAIA,gBAAgB,IAAI,CAAAT,mBAAmB,SAAnB,IAAAA,mBAAmB,WAAnB,YAAAA,mBAAmB,CAAEU,QAArB,MAAkCjI,SAA1D,EAAqE;AACnEqG,MAAAA,yBAAyB;AAC1B,KAFD,MAEO;AACLC,MAAAA,sBAAsB;AACvB;AACF,GAND;;AAQA,QAAM4B,YAAY,GAAG,MAAMlB,oBAAoB,CAAC,KAAD,CAA/C;;AACA,QAAMmB,kBAAkB,GAAG,MAAMf,uBAAuB,CAAC,KAAD,CAAxD;;AACA,QAAMgB,4BAA4B,GAAG,MAAMtB,wBAAwB,CAAC,KAAD,CAAnE;;AACA,QAAMuB,8BAA8B,GAAG,CAACL,gBAAgB,GAAG,IAApB,KAA6B;AAClE,QAAIA,gBAAgB,IAAI,CAAAP,eAAe,SAAf,IAAAA,eAAe,WAAf,YAAAA,eAAe,CAAEQ,QAAjB,MAA8BjI,SAAtD,EAAiE;AAC/DuG,MAAAA,sBAAsB;AACvB,KAFD,MAEO;AACLC,MAAAA,mBAAmB;AACpB;AACF,GAND;;AAQA,QAAM8B,wBAAwB,GAAIC,IAAD,IAAmB;AAClD,UAAMC,cAAc,GAAGvO,gBAAgB,CAAC6G,MAAjB,CAAyByD,IAAD,IAAUA,IAAI,CAACrG,MAAL,CAAYsC,IAAZ,KAAqB3G,oBAAvD,CAAvB;;AACA,QAAI2O,cAAc,CAACrI,MAAf,GAAwB,CAAxB,IAA6BoI,IAAjC,EAAuC;AACrCxR,MAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,6DAAP,CAAjB;AACA2P,MAAAA,mBAAmB;AACpB,KAHD,MAGO;AACLQ,MAAAA,qBAAqB,CAACqB,IAAD,CAArB;AACD;AACF,GARD;;AAUA,QAAME,cAAc,GAAI1L,KAAD,IAA6B;AAClD,UAAM2L,sBAAsB,GAAG3L,KAAK,CAAC4L,IAAN,CAAYpE,IAAD,IAAUA,IAAI,CAACqE,MAA1B,CAA/B;;AACA,QAAIF,sBAAJ,EAA4B;AAC1B3R,MAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,4EAAP,CAAjB;AACD;;AACD,WAAO2R,sBAAP;AACD,GAND;;AAQA,QAAMG,8BAA8B,GAAG,MAAM;AAC3CnC,IAAAA,mBAAmB;AACpB,GAFD;;AAIA,QAAMoC,gCAAgC,GAAG,CAACd,gBAAgB,GAAG,IAApB,KAA6B;AACpE,QAAIA,gBAAJ,EAAsB;AACpBrB,MAAAA,wBAAwB;AACzB,KAFD,MAEO;AACLC,MAAAA,qBAAqB;AACtB;AACF,GAND;;AAQA,QAAMmC,gCAAgC,GAAG,MAAM;AAC7ClE,IAAAA,qBAAqB;AACtB,GAFD;;AAIA,QAAMyB,sBAAsB,GAAG,MAAM;AACnCkB,IAAAA,sBAAsB,CAAC;AACrBS,MAAAA,QAAQ,EAAEjI,SADW;AAErBwF,MAAAA,QAAQ,EAAExF;AAFW,KAAD,CAAtB;AAIAC,IAAAA,QAAQ,CAAC9J,mCAAmC,CAAC,KAAD,CAApC,CAAR;AACA2Q,IAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACD,GAPD;;AASA,QAAMN,mBAAmB,GAAG,MAAM;AAChCkB,IAAAA,mBAAmB,CAAC;AAClBO,MAAAA,QAAQ,EAAEjI,SADQ;AAElBwF,MAAAA,QAAQ,EAAExF,SAFQ;AAGlBgJ,MAAAA,cAAc,EAAEhJ,SAHE;AAIlBiJ,MAAAA,mBAAmB,EAAEjJ;AAJH,KAAD,CAAnB;AAMAC,IAAAA,QAAQ,CAAC7J,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACA4Q,IAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD,GATD;;AAWA,QAAMN,mBAAmB,GAAG,MAAM;AAChCkB,IAAAA,mBAAmB,CAAC;AAClBlD,MAAAA,SAAS,EAAE1E,SADO;AAElBgE,MAAAA,OAAO,EAAEhE;AAFS,KAAD,CAAnB;AAIAC,IAAAA,QAAQ,CAACvJ,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACA4R,IAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACD,GAPD;;AASA,QAAM1B,qBAAqB,GAAG,MAAM;AAClCkB,IAAAA,qBAAqB,CAAC;AACpBtC,MAAAA,QAAQ,EAAExF;AADU,KAAD,CAArB;AAGAC,IAAAA,QAAQ,CAAC5J,kCAAkC,CAAC,KAAD,CAAnC,CAAR;AACA+Q,IAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACA3K,IAAAA,gBAAgB,CAACmH,WAAjB;AACAsF,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD,GARD;;AAUA,QAAMrE,qBAAqB,GAAG,MAAM;AAClClI,IAAAA,sBAAsB,CAAC,EAAD,CAAtB;AACAsD,IAAAA,QAAQ,CAACtJ,kCAAkC,CAAC,KAAD,CAAnC,CAAR;AACA2Q,IAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACD,GAJD;;AAMA,QAAM6B,+BAA+B,GAAG,CAAClB,QAAD,EAAmBzC,QAAnB,KAAyC;AAC/EgC,IAAAA,sBAAsB,CAAC;AAAES,MAAAA,QAAQ,EAAEA,QAAZ;AAAsBzC,MAAAA,QAAQ,EAAEA;AAAhC,KAAD,CAAtB;AACD,GAFD;;AAIA,QAAM4D,4BAA4B,GAAG,CAAC5D,QAAD,EAAqB6D,iBAArB,KAAqD;AACxFzB,IAAAA,mBAAmB,CAAC;AAAE5D,MAAAA,OAAO,EAAEwB,QAAX;AAAqBd,MAAAA,SAAS,EAAE2E;AAAhC,KAAD,CAAnB;AACD,GAFD;;AAIA,QAAMC,kBAAkB,GAAG,CAACC,aAAD,EAAqBC,SAArB,KAAwC;AACjE;AACA,UAAMC,gBAAgB,GAAGrI,MAAM,CAAC2E,MAAP,CAAcyD,SAAd,EAAyBb,IAAzB,CAA8BhI,KAAK,IAAIA,KAAvC,CAAzB;AACAuI,IAAAA,qBAAqB,CAACO,gBAAD,CAArB;AACD,GAJD;;AAMA,QAAMC,8BAA8B,GAAIlE,QAAD,IAAqB;AAE1D,UAAMmE,eAAe,GAAGnE,QAAQ,CAAC7D,GAAT,CAAc4C,IAAD,KAAkB;AACrDE,MAAAA,MAAM,EAAEF,IAAI,CAACxD,EADwC;AAErDwC,MAAAA,KAAK,EAAEgB,IAAI,CAAChB;AAFyC,KAAlB,CAAb,CAAxB;AAKAhM,IAAAA,WAAW,CACToS,eADS,CAAX,CAGGhF,IAHH,CAGSC,QAAD,IAAc;AAClB5N,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,sBAAP,CAAnB;AACAmE,MAAAA,QAAQ,CAAC,IAAD,CAAR;AACAyL,MAAAA,qBAAqB;AACtB,KAPH,EAQG9B,KARH,CAQUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,eAAP,CAAjB;AACD;;AACDW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,cAAjC,EAAiDA,KAAjD;AACD,KAfH;AAiBD,GAxBD;;AA0BA,QAAMwF,wBAAwB,GAAIC,KAAD,IAAsD;AACrF1S,IAAAA,iBAAiB,CACfoQ,mBADe,aACfA,mBADe,uBACfA,mBAAmB,CAAE/B,QAArB,CAA8B7D,GAA9B,CAAmC4C,IAAD,IAAUA,IAAI,CAACxD,EAAjD,CADe,EAEf,CAAAwG,mBAAmB,SAAnB,IAAAA,mBAAmB,WAAnB,YAAAA,mBAAmB,CAAEU,QAArB,IAAgCV,mBAAmB,CAACU,QAApD,GAA+D,CAFhD,EAGf/H,QAHe,CAAjB,CAKGyE,IALH,CAKSC,QAAD,IAAc;AAClB5N,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,mCAAP,CAAnB;AACAmE,MAAAA,QAAQ,CAAC,IAAD,CAAR;AACAmL,MAAAA,sBAAsB;AACvB,KATH,EAUGxB,KAVH,CAUUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,+BAAP,CAAjB;AACD;;AACDW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,wBAAjC,EAA2DA,KAA3D;AACD,KAjBH;AAkBD,GAnBD;;AAsBA,QAAM0F,oBAAoB,GAAG,MAAM;AACjC7J,IAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,IAAD,CAAhC,CAAR;AAEA5B,IAAAA,UAAU,CACRmQ,gBADQ,CAAV,CAGGhD,IAHH,CAGSC,QAAD,IAAc;AAClB5N,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,6BAAP,CAAnB;AACAmE,MAAAA,QAAQ,CAAC,IAAD,CAAR;AACAuL,MAAAA,mBAAmB;AACpB,KAPH,EAQG5B,KARH,CAQUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,qBAAP,CAAjB;AACD;;AACDW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,YAAjC,EAA+CA,KAA/C;AACD,KAfH,EAeKY,OAfL,CAea,MAAM;AACf/E,MAAAA,QAAQ,CAAC7G,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD,KAjBH;AAkBD,GArBD;;AAuBA,QAAM2Q,sBAAsB,GAAG,MAAM;AACnCtN,IAAAA,gBAAgB,CAACuN,MAAjB;AACD,GAFD;;AAKA,QAAMC,uBAAuB,GAAG,MAAM;AACpCxQ,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEP,eADD;AAENQ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLuG,QAAAA,oBAAoB,CAAC,KAAD,CAApB;AACAC,QAAAA,yBAAyB,CAAC,CAAD,CAAzB;AACAlK,QAAAA,QAAQ,CAAC1J,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACA6T,QAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACD;;AAVK,KAAD,CAAP;AAYD,GAbD;;AAeA,QAAM,CAACC,sBAAD,EAAyBF,yBAAzB,IAAsDnW,QAAQ,CAAC,CAAD,CAApE;AACA,QAAM,CAACsW,cAAD,EAAiBC,kBAAjB,IAAuCvW,QAAQ,EAArD;AACA,QAAM,CAACwW,qBAAD,EAAwBJ,wBAAxB,IAAoDpW,QAAQ,CAAC,KAAD,CAAlE;AACA,QAAM,CAACyW,iBAAD,EAAoBP,oBAApB,IAA4ClW,QAAQ,CAAC,KAAD,CAA1D;;AAEA,QAAM0W,sBAAsB,GAAG,MAAM;AACnCzK,IAAAA,QAAQ,CAAC1J,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACA6T,IAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACAG,IAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACAL,IAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD,GALD;;AAOA,QAAMS,iCAAiC,GAAG,MAAM;AAC9C,QAAIN,sBAAsB,GAAG,CAAzB,IAA8BI,iBAAlC,EAAqD;AACnDR,MAAAA,uBAAuB;AACxB,KAFD,MAEO;AACLS,MAAAA,sBAAsB;AACvB;AACF,GAND;;AAQA,QAAME,qBAAqB,GAAG,MAAM;AAClCL,IAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACAtK,IAAAA,QAAQ,CAAC1J,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACA6T,IAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACAD,IAAAA,yBAAyB,CAAC,CAAD,CAAzB;AACAhP,IAAAA,QAAQ,CAAC,IAAD,CAAR;AACD,GAND;;AAQA,QAAM0P,qBAAqB,GAAG,MAAM;AAClC,UAAMC,cAAc,GAAGT,sBAAsB,KAAK,CAAlD;;AAEA,QAAIS,cAAJ,EAAoB;AAClB,UAAIrC,cAAc,CAACxO,gBAAD,CAAlB,EAAsC;AACpC;AACD;AACF;;AAED5C,IAAAA,gBAAgB,CAACiT,cAAc,IAAIA,cAAc,CAACnK,MAAf,GAAwB,CAA1C,GAA8CmK,cAA9C,aAA8CA,cAA9C,uBAA8CA,cAAc,CAAE3I,GAAhB,CAAqB4C,IAAD,IAAUA,IAAI,CAACxD,EAAnC,CAA9C,GAAuF9G,gBAAvF,aAAuFA,gBAAvF,uBAAuFA,gBAAgB,CAAE0H,GAAlB,CAAuB4C,IAAD,IAAUA,IAAI,CAACxD,EAArC,CAAxF,EAAkIsJ,sBAAlI,CAAhB,CACG1F,IADH,CACSC,QAAD,IAAc;AAClB5N,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,0BAAP,CAAnB;AACA4T,MAAAA,qBAAqB;AACtB,KAJH,EAKG9F,KALH,CAKUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,sBAAP,CAAjB;AACD;;AACDW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,eAAjC,EAAkDA,KAAlD;AACD,KAZH;AAcAnE,IAAAA,QAAQ,CAAC1J,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD,GAxBD;;AA0BA,QAAM,CAACwU,iBAAD,EAAoBC,oBAApB,IAA4ChX,QAAQ,CAAC,KAAD,CAA1D;AACA,QAAM,CAACiX,YAAD,EAAeC,eAAf,IAAkClX,QAAQ,CAA4B4B,aAAa,CAACuV,UAA1C,CAAhD;AACA,QAAM,CAACC,WAAD,EAAcC,cAAd,IAAgCrX,QAAQ,CAAC,KAAD,CAA9C;AACA,QAAM,CAACsX,kBAAD,EAAqBC,qBAArB,IAA8CvX,QAAQ,CAAC,KAAD,CAA5D;AACA,QAAM,CAACwX,qBAAD,EAAwBC,wBAAxB,IAAoDzX,QAAQ,CAAC,IAAD,CAAlE;AACA,QAAM,CAAC0X,kBAAD,EAAqBxC,qBAArB,IAA8ClV,QAAQ,CAAC,KAAD,CAA5D;;AACA,QAAM2X,gBAAgB,GAAG,MAAM;AAC7B,WAAO9P,SAAS,gBAAG,oBAAC,gBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAH,gBAA0B,oBAAC,kBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAA1C;AACD,GAFD;;AAGA,QAAM+P,qBAAqB,GAAI/B,KAAD,IAAyD;AACrF7P,IAAAA,iBAAiB,CAAC6P,KAAD,CAAjB;AACA/N,IAAAA,YAAY,CAAC,CAACD,SAAF,CAAZ;AACD,GAHD;;AAKA,QAAMgQ,2BAA2B,GAAG,MAAM;AACxC5L,IAAAA,QAAQ,CAACjK,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACAgV,IAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD,GAHD;;AAKA,QAAMc,oBAAoB,GAAG,CAACC,iBAAD,EAAmCC,OAAnC,KAAwD;AACnFd,IAAAA,eAAe,CAACa,iBAAD,CAAf;AACAf,IAAAA,oBAAoB,CAACgB,OAAD,CAApB;AACD,GAHD;;AAKA,QAAM,CAACC,iBAAD,EAAoBC,wBAApB,IAAgDlY,QAAQ,CAAC,KAAD,CAA9D;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACdsX,IAAAA,cAAc,CAAC,KAAD,CAAd;;AACA,QAAIjN,QAAJ,EAAc;AACZ8E,MAAAA,wBAAwB,CAACjJ,gBAAgB,CAACkG,MAAlB,CAAxB;AACD;;AACDoL,IAAAA,qBAAqB,CAACnN,QAAD,CAArB;AACD,GANQ,EAMN,CAACA,QAAD,EAAWnE,gBAAX,CANM,CAAT;AAQAlG,EAAAA,SAAS,CAAC,MAAM;AACd+X,IAAAA,oBAAoB,CAAClW,aAAa,CAACuV,UAAf,EAA2BlN,QAA3B,CAApB;AACD,GAFQ,EAEN,CAACA,QAAD,CAFM,CAAT;AAIAlK,EAAAA,SAAS,CAAC,MAAM;AACdqW,IAAAA,wBAAwB,CAAClM,MAAD,CAAxB;AACD,GAFQ,EAEN,CAACA,MAAD,CAFM,CAAT;AAIAnK,EAAAA,SAAS,CAAC,MAAM;AACdkJ,IAAAA,kBAAkB,CAACkB,MAAD,CAAlB;AACD,GAFQ,EAEN,CAACA,MAAD,CAFM,CAAT;AAIApK,EAAAA,SAAS,CAAC,MAAM;AACdmY,IAAAA,wBAAwB,CAAC1N,SAAD,CAAxB;AACD,GAFQ,EAEN,CAACA,SAAD,CAFM,CAAT;AAIAzK,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI0K,YAAY,IAAI,CAACgK,cAAc,CAACxO,gBAAD,CAAnC,EAAuD;AACrD6M,MAAAA,wBAAwB,CAAC,IAAD,CAAxB;AACD;;AACD7G,IAAAA,QAAQ,CAAC9J,mCAAmC,CAAC,KAAD,CAApC,CAAR;AACD,GALQ,EAKN,CAACsI,YAAD,CALM,CAAT;AAOA1K,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI2K,SAAS,IAAI,CAAC+J,cAAc,CAACxO,gBAAD,CAAhC,EAAoD;AAClD+M,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACD;;AACD/G,IAAAA,QAAQ,CAAC7J,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACD,GALQ,EAKN,CAACsI,SAAD,CALM,CAAT;AAOA3K,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIgL,WAAW,IAAI,CAAC0J,cAAc,CAACxO,gBAAD,CAAlC,EAAsD;AACpDkS,MAAAA,gBAAgB,CAAClS,gBAAD,CAAhB;AACD;;AACDgG,IAAAA,QAAQ,CAACrJ,4BAA4B,CAAC,KAAD,CAA7B,CAAR;AACD,GALQ,EAKN,CAACmI,WAAD,CALM,CAAT;AAOAhL,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIiL,QAAQ,KAAK5D,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAlD,CAAZ,EAAmF;AACjFmS,MAAAA,cAAc,CAACnS,gBAAD,CAAd;AACD;;AACDgG,IAAAA,QAAQ,CAACpJ,8BAA8B,CAAC,KAAD,CAA/B,CAAR;AACD,GALQ,EAKN,CAACmI,QAAD,CALM,CAAT;AAOAjL,EAAAA,SAAS,CAAC,MAAM;AACdqT,IAAAA,uBAAuB,CAACzI,WAAD,CAAvB;AACD,GAFQ,EAEN,CAACA,WAAD,CAFM,CAAT;AAIA5K,EAAAA,SAAS,CAAC,MAAM;AACduU,IAAAA,wBAAwB,CAACzJ,SAAD,CAAxB;AACD,GAFQ,EAEN,CAACA,SAAD,CAFM,CAAT;AAIA9K,EAAAA,SAAS,CAAC,MAAM;AACduT,IAAAA,uBAAuB,CAACxI,WAAD,CAAvB;AACD,GAFQ,EAEN,CAACA,WAAD,CAFM,CAAT;AAIA/K,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI6K,iBAAiB,KAAKxD,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAlD,CAArB,EAA4F;AAC1FoS,MAAAA,mBAAmB,CAACzN,iBAAD,CAAnB;AACD;;AACDqB,IAAAA,QAAQ,CAAC3J,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACD,GALQ,EAKN,CAACsI,iBAAD,CALM,CAAT;AAOA7K,EAAAA,SAAS,CAAC,MAAM;AACdkM,IAAAA,QAAQ,CAACjI,eAAe,CAAC4B,SAAD,EAAYG,MAAZ,CAAhB,CAAR;AACD,GAFQ,EAEN,EAFM,CAAT;AAIAhG,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIsK,OAAO,KAAKjD,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAlD,CAAX,EAAkF;AAChF,YAAMqS,kBAAkB,GAAIC,aAAD,IAA6C;AACtE,cAAMC,gBAAgB,GAAG,EAAzB;AACAD,QAAAA,aAAa,SAAb,IAAAA,aAAa,WAAb,YAAAA,aAAa,CAAEE,OAAf,CAAwBlI,IAAD,IAAU;AAC/BiI,UAAAA,gBAAgB,CAACE,IAAjB,CAAsB;AACpB3L,YAAAA,EAAE,EAAEwD,IAAI,CAACxD,EADW;AAEpBgB,YAAAA,OAAO,EAAE,IAFW;AAGpBwB,YAAAA,KAAK,EAAEgB,IAAI,CAAChB;AAHQ,WAAtB;AAKD,SAND;AAOA,eAAOiJ,gBAAP;AACD,OAVD;;AAWA7M,MAAAA,kBAAkB,CAAC2M,kBAAkB,CAACrS,gBAAD,CAAnB,CAAlB;AACAqC,MAAAA,mBAAmB,CAAC,IAAD,CAAnB;AACA2D,MAAAA,QAAQ,CAAC/J,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD;AACF,GAjBQ,EAiBN,CAACmI,OAAD,CAjBM,CAAT;AAmBAtK,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIuK,SAAS,KAAKlD,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAlD,CAAb,EAAoF;AAClF,YAAM0S,QAAQ,GAAIJ,aAAD,IAA+C;AAC9D,cAAM/G,QAAQ,GAAG,EAAjB;AACA+G,QAAAA,aAAa,SAAb,IAAAA,aAAa,WAAb,YAAAA,aAAa,CAAEE,OAAf,CAAwBlI,IAAD,IAAU;AAC/BiB,UAAAA,QAAQ,CAACkH,IAAT,CAAc;AACZ3L,YAAAA,EAAE,EAAEwD,IAAI,CAACxD,EADG;AAEZgB,YAAAA,OAAO,EAAE,IAFG;AAGZwB,YAAAA,KAAK,EAAEgB,IAAI,CAAChB;AAHA,WAAd;AAKD,SAND;AAOA,eAAOiC,QAAP;AACD,OAVD;;AAWA3F,MAAAA,oBAAoB,CAAC8M,QAAQ,CAAC1S,gBAAD,CAAT,CAApB;AACAuC,MAAAA,qBAAqB,CAAC,IAAD,CAArB;AACAyD,MAAAA,QAAQ,CAACzJ,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD;AACF,GAjBQ,EAiBN,CAAC8H,SAAD,CAjBM,CAAT;;AAoBA,QAAM+N,mBAAmB,GAAI1L,KAAD,IAAoB;AAC9C,QAAIA,KAAJ,EAAW;AACTlH,MAAAA,OAAO,CAAC;AACN8J,QAAAA,KAAK,EAAE,kDADD;AAENC,QAAAA,IAAI,eAAE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAFA;AAGNC,QAAAA,MAAM,EAAE,KAHF;AAINC,QAAAA,UAAU,EAAE,IAJN;AAKNC,QAAAA,IAAI,EAAE,MAAM;AACViJ,UAAAA,eAAe;AACf3M,UAAAA,QAAQ,CAAC3J,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AAED,SATK;AAUNuW,QAAAA,QAAQ,EAAE,MAAM;AACd5M,UAAAA,QAAQ,CAAC3J,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACD;AAZK,OAAD,CAAP;AAeD;AACF,GAlBD;;AAoBA,QAAMwW,2BAA2B,GAAG,MAAM;AACxCrT,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAE,+CADD;AAENC,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACL1D,QAAAA,QAAQ,CAACnK,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACAyV,QAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD;;AARK,KAAD,CAAP;AAUD,GAXD;;AAaA,QAAMwB,gBAAgB,GAAInI,QAAD,IAAiC;AACxD,QAAIA,QAAJ,EAAc;AACZ5N,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,8BAAP,CAAnB;AACAmE,MAAAA,QAAQ,CAAC,IAAD,CAAR;AACA8E,MAAAA,QAAQ,CAAClK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD,KAJD,MAIO;AACLgB,MAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,iBAAP,CAAjB;AACAkJ,MAAAA,QAAQ,CAAClK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD;AACF,GATD;;AAWA,QAAMiX,oBAAoB,GAAGlZ,WAAW,CAAEyY,aAAD,IAA4B;AACnE,UAAMU,UAAiB,GAAG,EAA1B;AACAV,IAAAA,aAAa,CAACE,OAAd,CAAuBlI,IAAD,IAAU;AAC9B0I,MAAAA,UAAU,CAACP,IAAX,CAAgBnI,IAAI,CAACxD,EAArB;AACD,KAFD;AAGA7J,IAAAA,UAAU,CAAC+V,UAAD,CAAV,CACGtI,IADH,CACSC,QAAD,IAAc;AAClBmI,MAAAA,gBAAgB,CAACnI,QAAD,CAAhB;AACD,KAHH,EAIGE,KAJH,CAIUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,iBAAP,CAAjB;AACD;;AACDkJ,MAAAA,QAAQ,CAAClK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD,KAXH;AAYD,GAjBuC,EAiBrC,EAjBqC,CAAxC;;AAmBA,QAAMoW,gBAAgB,GAAII,aAAD,IAA4B;AACnDlV,IAAAA,gBAAgB,CACdkV,aAAa,CAAC5K,GAAd,CAAmBsC,CAAD,IAAOA,CAAC,CAAClD,EAA3B,CADc,EAEd,CAFc,CAAhB,CAGE4D,IAHF,CAGO,MAAMxJ,QAAQ,CAAC,IAAD,CAHrB;AAID,GALD;;AAOA,QAAMiR,cAAc,GAAIG,aAAD,IAA4B;AACjD,QAAIW,GAAG,GAAGX,aAAa,CAAC5K,GAAd,CAAmBsC,CAAD,IAAOA,CAAC,CAAClD,EAA3B,EAA+BoM,IAA/B,CAAoC,GAApC,CAAV;AACAtU,IAAAA,eAAe,CAACqU,GAAD,CAAf;AACA/R,IAAAA,QAAQ,CAAC,IAAD,CAAR;AACD,GAJD;;AAMA,QAAMiS,iBAAiB,GAAGtZ,WAAW,CAAEyY,aAAD,IAA4B;AAChE,UAAMc,UAAU,GAAGd,aAAa,CAACpM,MAAjC;AACA,UAAMmN,cAAc,GAAGf,aAAa,CAACzL,MAAd,CAAsByD,IAAD,IAAUA,IAAI,CAACgJ,SAApC,EAA+CpN,MAAtE;AACA,UAAMqN,SAAS,GAAI,GAAEH,UAAW,IAAGA,UAAU,KAAK,CAAf,GAAmB,MAAnB,GAA4B,OAAQ,EAAvE;AACA,UAAMI,OAAO,GAAI,GAAED,SAAU,oBAAmBF,cAAc,GAAG,CAAjB,GAAqB,yDAArB,GAAiF,EAAG,oCAApI;AACA7T,IAAAA,OAAO,CAAC;AACN8J,MAAAA,KAAK,EAAEkK,OADD;AAENjK,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLqJ,QAAAA,oBAAoB,CAACT,aAAD,CAApB;AACD,OAPK;;AAQNM,MAAAA,QAAQ,GAAG;AACT5M,QAAAA,QAAQ,CAAClK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD;;AAVK,KAAD,CAAP;AAYD,GAjBoC,EAiBlC,EAjBkC,CAArC;AAmBAhC,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIwK,WAAW,IAAItE,gBAAgB,CAACkG,MAAjB,GAA0B,CAA7C,EAAgD;AAC9C,UAAI5B,WAAW,IAAI,CAACkK,cAAc,CAACxO,gBAAD,CAAlC,EAAsD;AACpDmT,QAAAA,iBAAiB,CAACnT,gBAAD,CAAjB;AACD;;AACDgG,MAAAA,QAAQ,CAAClK,6BAA6B,CAAC,KAAD,CAA9B,CAAR;AACD;AACF,GAPQ,EAON,CAACwI,WAAD,EAAc6O,iBAAd,EAAiCnT,gBAAjC,CAPM,CAAT;;AASA,QAAMyT,qBAAqB,GAAI3H,MAAD,IAAmB;AAC/CA,IAAAA,MAAM,CAAChJ,KAAP,CAAa0P,OAAb,CAAsB9L,KAAD,IAA0B;AAC7CA,MAAAA,KAAK,CAACgN,SAAN,GAAkB5H,MAAM,CAAC6H,UAAzB;AACD,KAFD;AAGD,GAJD;;AAMA,QAAMC,iBAAiB,GAAI9H,MAAD,IAAoC;AAC5D,UAAM+H,oBAAoB,GAAG,EAA7B;AACA/H,IAAAA,MAAM,CAAChJ,KAAP,CAAa0P,OAAb,CAAsB9L,KAAD,IAA0B;AAAA;;AAC7CmN,MAAAA,oBAAoB,CAACpB,IAArB,CAA0B,EACxB,GAAG/L,KADqB;AAExBoN,QAAAA,UAAU,EAAEpN,KAAF,aAAEA,KAAF,4CAAEA,KAAK,CAAEoN,UAAT,sDAAE,kBAAmBC,MAAnB,CAA0B,YAA1B;AAFY,OAA1B;AAID,KALD;AAMA,WAAOF,oBAAP;AACD,GATD;;AAUA,QAAMG,wBAAwB,GAAG,MAAM;AACrC1C,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACAtL,IAAAA,QAAQ,CAACnK,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACAoV,IAAAA,eAAe,CAACE,WAAW,GAAGxV,aAAa,CAACwV,WAAjB,GAA+BxV,aAAa,CAACsY,kBAAzD,CAAf;AACAlD,IAAAA,oBAAoB,CAAC,IAAD,CAApB;AACAvQ,IAAAA,cAAc,CAAC,IAAD,CAAd;AACAwF,IAAAA,QAAQ,CAACxJ,oBAAoB,CAAC,IAAD,CAArB,CAAR;AACAmP,IAAAA,UAAU,CAAC,MAAM;AACf3F,MAAAA,QAAQ,CAACxJ,oBAAoB,CAAC,KAAD,CAArB,CAAR;AACD,KAFS,CAAV;;AAGA,QAAImF,SAAS,CAACuS,cAAV,GAA2BC,SAA3B,IAAwCxS,SAAS,CAACuS,cAAV,GAA2BE,YAAvE,EAAqF;AACnFrX,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,mCAAP,CAAnB;AACD;AACF,GAbD;;AAeA,QAAMsX,eAAe,GAAG,MAAM;AAC5B5S,IAAAA,IAAI,CAACoK,cAAL,GAAsBnB,IAAtB,CAA2B,MAAOoB,MAAP,IAAkB;AAC3CA,MAAAA,MAAM,CAAChJ,KAAP,GAAegJ,MAAM,CAAChJ,KAAP,CAAa+D,MAAb,CAAqByD,IAAD,IAAyBA,IAAI,CAACxC,OAAlD,CAAf;;AACA,UAAIgE,MAAM,CAAC6H,UAAX,EAAuB;AACrBF,QAAAA,qBAAqB,CAAC3H,MAAD,CAArB;AACD;;AACD3C,MAAAA,yBAAyB,CAAC2C,MAAM,CAAChJ,KAAR,CAAzB;AACA,YAAM;AAAEwR,QAAAA,QAAF;AAAYC,QAAAA;AAAZ,UAA0B,MAAMjW,aAAa,CAACsV,iBAAiB,CAAC9H,MAAD,CAAlB,CAAnD;;AACA,UAAIwI,QAAJ,EAAc;AACZC,QAAAA,SAAS,IAAIA,SAAS,KAAKhW,oBAA3B,GACIzB,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CADrB,GAEIgV,SAAS,IAAIA,SAAS,KAAK/S,yBAA3B,GACE1E,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOuB,mCAAP,CADnB,GAEEvB,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,kBAAP,CAJvB;AAKD,OAND,MAMO;AACLkX,QAAAA,wBAAwB;AACzB;AACF,KAhBD;AAiBD,GAlBD;;AAoBA,QAAMQ,mBAAmB,gBACvB;AAAM,IAAA,SAAS,EAAEnZ,MAAM,CAACoZ,2BAAxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,0BAAM,IAAN;AACE,IAAA,MAAM,EAAEzU,gBAAgB,IAAI,CAAAA,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAEkG,MAAlB,KAA4B,CAD1D;AAEE,IAAA,OAAO,EAAE,MAAM;AACb2L,MAAAA,oBAAoB,CAAClW,aAAa,CAACuV,UAAf,EAA2B,IAA3B,CAApB;AACD,KAJH;AAKE,IAAA,GAAG,EAAC,GALN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADF,eAUE,0BAAM,IAAN;AACE,IAAA,MAAM,EAAElR,gBAAgB,IAAI,CAAAA,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAEkG,MAAlB,KAA4B,CAD1D;AAEE,IAAA,OAAO,EAAE,MAAM;AACb2L,MAAAA,oBAAoB,CAAClW,aAAa,CAAC+Y,GAAf,EAAoB,IAApB,CAApB;AACD,KAJH;AAKE,IAAA,GAAG,EAAC,GALN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BAVF,CADF;;AAuBA,QAAMC,oBAAoB,GAAG,MAAM;AACjC3O,IAAAA,QAAQ,CAAC/H,yBAAyB,CAAC,IAAD,CAA1B,CAAR;AAEA,UAAM2W,2BAAwD,GAAG;AAC/DrO,MAAAA,IAAI,EAAEnD,aAAa,CAACoF,IAAd,EADyD;AAE/DvB,MAAAA,OAAO,EAAE;AAFsD,KAAjE;AAIAxD,IAAAA,WAAW,CAAC+O,OAAZ,CAAqB3L,MAAD,IAA2B;AAC7C,YAAMgO,KAAK,GAAGhO,MAAM,CAAC,KAAD,CAApB;;AACA,UAAI,CAAAA,MAAM,SAAN,IAAAA,MAAM,WAAN,YAAAA,MAAM,CAAEiO,OAAR,MAAoB,CAAAjO,MAAM,SAAN,IAAAA,MAAM,WAAN,YAAAA,MAAM,CAAEH,KAAR,KAAiB,CAAAG,MAAM,SAAN,IAAAA,MAAM,WAAN,YAAAA,MAAM,CAAEH,KAAR,MAAkB,CAAvD,CAAJ,EAA+D;AAC7D,YAAKkO,2BAA2B,CAAC3N,OAA5B,CAAoC4N,KAApC,CAAD,KAAmF9O,SAAvF,EAAkG;AAC/F6O,UAAAA,2BAA2B,CAAC3N,OAA5B,CAAoC4N,KAApC,CAAD,GAAiF,EAAjF;AACD;;AACAD,QAAAA,2BAA2B,CAAC3N,OAA5B,CAAoC4N,KAApC,CAAD,GAAiF,CAC/E,GAAID,2BAA2B,CAAC3N,OAA5B,CAAoC4N,KAApC,CAD2E,EAE/EhO,MAAM,CAAC,OAAD,CAFyE,CAAjF;AAID,OARD,MAQO;AACL+N,QAAAA,2BAA2B,CAAC3N,OAA5B,CAAoC4N,KAApC,IAAsEhO,MAAM,CAAC,OAAD,CAA5E;AACD;AACF,KAbD;AAcAb,IAAAA,QAAQ,CAAChI,kBAAkB,CAAC4W,2BAAD,EAA8BjV,SAA9B,EAAyCG,MAAzC,CAAnB,CAAR;AACAiC,IAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACAmB,IAAAA,gBAAgB,CAAC,EAAD,CAAhB;AACD,GAxBD;;AA0BA,QAAM6R,gBAAgB,GAAG,MAAM;AAC7B3D,IAAAA,cAAc,CAAC,KAAD,CAAd;AACAE,IAAAA,qBAAqB,CAAC,IAAD,CAArB;AACArI,IAAAA,wBAAwB,CAACjJ,gBAAgB,CAACkG,MAAlB,CAAxB;AACD,GAJD;;AAMA,QAAM8O,aAAa,GAAG,MAAM;AAC1B9T,IAAAA,QAAQ,CAAC,IAAD,CAAR;AACA8E,IAAAA,QAAQ,CAAC7H,oBAAoB,CAAC,KAAD,CAArB,CAAR;AACD,GAHD;;AAIA,QAAM8W,eAAe,GAAG,MAAM;AAC5BjP,IAAAA,QAAQ,CAAC/J,6BAA6B,CAAC,IAAD,CAA9B,CAAR;AACD,GAFD;;AAGA,QAAMiZ,iBAAiB,GAAG,MAAM;AAC9BlP,IAAAA,QAAQ,CAACzJ,+BAA+B,CAAC,IAAD,CAAhC,CAAR;AACD,GAFD;;AAIA,QAAM4Y,wBAAwB,GAAG,MAAM;AACrC,QAAI,CAAC7U,aAAL,EAAoB;;AACpB,QAAI,CAACkO,cAAc,CAACxO,gBAAD,CAAnB,EAAuC;AACrC+M,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACD;AACF,GALD;;AAOA,QAAMqI,0BAA0B,GAAG,MAAM;AACvC,QAAI,CAAC/U,gBAAL,EAAuB;;AACvB,QAAI,CAACmO,cAAc,CAACxO,gBAAD,CAAnB,EAAuC;AACrC6M,MAAAA,wBAAwB,CAAC,IAAD,CAAxB;AACD;AACF,GALD;;AAOA,QAAMwI,wBAAwB,GAAG,MAAM;AACrC,QAAI,CAACpU,QAAL,EAAe;;AACf,QAAIE,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAjD,EAAqE;AACnEoS,MAAAA,mBAAmB,CAAC,IAAD,CAAnB;AACD;AACF,GALD;;AAOA,QAAMkD,qBAAqB,GAAG,MAAM;AAClC,QAAI,CAAC7U,UAAL,EAAiB;;AACjB,QAAI,CAAC+N,cAAc,CAACxO,gBAAD,CAAnB,EAAuC;AACrCmT,MAAAA,iBAAiB,CAACnT,gBAAD,CAAjB;AACD;AACF,GALD;;AAOA,QAAMuV,0BAA0B,GAAG,MAAM;AACvC,QAAI,CAAC3U,cAAL,EAAqB;;AACrB,QAAI,CAAC4N,cAAc,CAACxO,gBAAD,CAAnB,EAAuC;AACrCkS,MAAAA,gBAAgB,CAAClS,gBAAD,CAAhB;AACD;AACF,GALD;;AAOA,QAAMwV,uBAAuB,GAAG,MAAM;AACpC,QAAI,CAAC3U,YAAL,EAAmB;;AACnB,QAAIM,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAjD,EAAqE;AACnEmS,MAAAA,cAAc,CAACnS,gBAAD,CAAd;AACD;AACF,GALD;;AAOA,QAAMyV,gBAAgB,GAAG,MAAM;AAC7B,QAAI,CAACjH,cAAc,CAACxO,gBAAD,CAAnB,EAAuC;AACrCmC,MAAAA,sBAAsB,CAAC,IAAD,CAAtB;AACD;AACF,GAJD;;AAMA,QAAMuT,sBAAsB,GAAG,MAAM;AACnC,QAAI,CAAChV,WAAL,EAAkB;;AAClB,QAAIS,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAjD,EAAqE;AACnEiV,MAAAA,eAAe;AAChB;AACF,GALD;;AAOA,QAAMU,wBAAwB,GAAG,MAAM;AACrC,QAAI,CAAChV,aAAL,EAAoB;;AACpB,QAAIQ,0BAA0B,IAAI,CAACqN,cAAc,CAACxO,gBAAD,CAAjD,EAAqE;AACnEkV,MAAAA,iBAAiB;AAClB;AACF,GALD;;AAOA,QAAMxJ,oBAAoB,GAAG,GAA7B;;AACA,QAAMiH,eAAe,GAAG,MAAM;AAC5B,UAAMiD,+BAA+B,GAAG5V,gBAAH,aAAGA,gBAAH,uBAAGA,gBAAgB,CAAE0H,GAAlB,CAAuB4C,IAAD,IAAiBA,IAAI,CAACuL,eAA5C,CAAxC;AAEAxY,IAAAA,SAAS,CACPuY,+BADO,CAAT,CAGGlL,IAHH,CAGSC,QAAD,IAAc;AAClB5N,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,qBAAP,CAAnB;AACA4O,MAAAA,UAAU,CAAC,MAAM;AACfzK,QAAAA,QAAQ,CAAC,IAAD,CAAR;AAED,OAHS,EAGPwK,oBAHO,CAAV;AAID,KATH,EAUGb,KAVH,CAUUV,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACW,UAAN,KAAqBvM,oBAAzB,EAA+C;AAC7CzB,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyC,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLzC,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,aAAP,CAAjB;AACD;;AACDW,MAAAA,MAAM,CAAC0M,KAAP,CAAa,kBAAb,EAAiC,YAAjC,EAA+CA,KAA/C;AACD,KAjBH;AAkBD,GArBD;;AAuBA,sBACE,uDACE,oBAAC,iBAAD;AACE,IAAA,IAAI,EAAEnK,gBAAgB,CAAC,CAAD,CADxB;AAEE,IAAA,aAAa,EAAE,MAAM;AACnBiS,MAAAA,wBAAwB,CAAC,KAAD,CAAxB;AACAjM,MAAAA,QAAQ,CAAChK,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACD,KALH;AAME,IAAA,aAAa,EAAEgW,iBANjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAYE,oBAAC,YAAD;AACE,IAAA,SAAS,EAAE,MAAM9Q,QAAQ,CAAC,IAAD,CAD3B;AAEE,IAAA,aAAa,EAAElB,gBAFjB;AAGE,IAAA,YAAY,EAAEmO,4BAHhB;AAIE,IAAA,OAAO,EAAEnG,OAJX;AAKE,IAAA,IAAI,EAAExF,gBALR;AAME,IAAA,kBAAkB,EAAElB,kBANtB;AAOE,IAAA,qBAAqB,EAAEsL,qBAPzB,CAOuD;AAPvD;AAQE,IAAA,QAAQ,EAAE3G,QARZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAZF,eAyBE,oBAAC,SAAD;AACE,IAAA,MAAM,EAAEnG,MADV;AAEE,IAAA,QAAQ,EAAEmG,QAFZ;AAGE,IAAA,SAAS,EAAE,MAAM/E,QAAQ,CAAC,IAAD,CAH3B;AAIE,IAAA,aAAa,EAAElB,gBAJjB;AAKE,IAAA,YAAY,EAAEiO,YALhB;AAME,IAAA,OAAO,EAAEjG,OANX;AAOE,IAAA,IAAI,EAAExF,gBAPR;AAQE,IAAA,kBAAkB,EAAElB,kBARtB;AASE,IAAA,iBAAiB,EAAEwL,iBATrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAzBF,eAsCE,oBAAC,WAAD;AACE,IAAA,SAAS,EAAE,MAAM5L,QAAQ,CAAC,IAAD,CAD3B;AAEE,IAAA,aAAa,EAAElB,gBAFjB;AAGE,IAAA,YAAY,EAAEkO,kBAHhB;AAIE,IAAA,OAAO,EAAElG,OAJX;AAKE,IAAA,IAAI,EAAExF,gBALR;AAME,IAAA,oBAAoB,EAAE0K,oBANxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAtCF,eAiDE;AACE,IAAA,OAAO,EAAEpL,qBADX;AAEE,IAAA,KAAK,EAAE,sBAFT;AAGE,IAAA,KAAK,EAAE,GAHT;AAIE,IAAA,OAAO,EAAEmB,wBAJX;AAKE,IAAA,SAAS,EAAE,eALb;AAME,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,GAAG,EAAC,MAAZ;AAAmB,MAAA,IAAI,EAAC,SAAxB;AAAkC,MAAA,OAAO,EAAEA,wBAA3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADM,eAIN;AAAQ,MAAA,QAAQ,EAAEG,aAAa,CAACoF,IAAd,GAAqBtC,MAArB,GAA8B,CAA9B,IAAmC,CAACZ,WAAtD;AAAmE,MAAA,GAAG,EAAC,QAAvE;AAAgF,MAAA,IAAI,EAAC,SAArF;AAA+F,MAAA,OAAO,EAAE,MAAMqP,oBAAoB,EAAlI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAJM,CANV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAeE;AAAK,IAAA,SAAS,EAAEtZ,MAAM,CAACya,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,4BAAD;AAA8B,IAAA,WAAW,EAAExQ,WAA3C;AAAwD,IAAA,aAAa,EAAElC,aAAvE;AAAsF,IAAA,yBAAyB,EAAGmD,IAAD,IAAkB+B,yBAAyB,CAAC/B,IAAD,CAA5J;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CAfF,CAjDF,eAqEE;AACE,IAAA,OAAO,EAAEvE,oBADX;AAEE,IAAA,KAAK,EAAE,eAFT;AAGE,IAAA,KAAK,EAAE,GAHT;AAIE,IAAA,OAAO,EAAE,MAAMC,uBAAuB,CAAC,KAAD,CAJxC;AAKE,IAAA,SAAS,EAAE,eALb;AAME,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,GAAG,EAAC,MAAZ;AAAmB,MAAA,IAAI,EAAC,SAAxB;AAAkC,MAAA,OAAO,EAAE,MAAMA,uBAAuB,CAAC,KAAD,CAAxE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADM,CANV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAYE;AAAK,IAAA,SAAS,EAAE5G,MAAM,CAACya,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,4BAAD;AAA8B,IAAA,QAAQ,EAAEnW,SAAxC;AAAmD,IAAA,YAAY,EAAEG,MAAjE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CAZF,CArEF,eAyFE,oBAAC,YAAD;AACE,IAAA,OAAO,EAAE0C,gBADX;AAEE,IAAA,MAAM,EAAE1C,MAFV;AAGE,IAAA,aAAa,EAAEE,gBAHjB;AAIE,IAAA,YAAY,EAAE,MAAMiL,2BAA2B,CAAC,IAAD,CAJjD;AAKE,IAAA,eAAe,EAAElI,eALnB;AAME,IAAA,SAAS,EAAE,MAAM7B,QAAQ,CAAC,IAAD,CAN3B;AAOE,IAAA,IAAI,EAAEsB,gBAPR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAzFF,eAsGE,oBAAC,mBAAD;AACE,IAAA,OAAO,EAAEN,mBADX;AAEE,IAAA,OAAO,EAAE4G,8BAFX;AAGE,IAAA,QAAQ,EAAE7C,QAHZ;AAIE,IAAA,QAAQ,EAAE,MAAM/E,QAAQ,CAAC,IAAD,CAJ1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAtGF,eA8GE;AACE,IAAA,KAAK,EAAE,GADT;AAEE,IAAA,OAAO,EAAEqP,qBAFX;AAGE,IAAA,KAAK,EAAE,eAHT;AAIE,IAAA,OAAO,EAAEG,iCAJX;AAKE,IAAA,SAAS,EAAE,eALb;AAME,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,GAAG,EAAC,MAAZ;AAAmB,MAAA,IAAI,EAAC,SAAxB;AAAkC,MAAA,OAAO,EAAEA,iCAA3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADM,eAIN;AAAQ,MAAA,GAAG,EAAC,QAAZ;AAAqB,MAAA,IAAI,EAAC,SAA1B;AAAoC,MAAA,OAAO,EAAEE,qBAA7C;AAAoE,MAAA,QAAQ,EAAER,sBAAsB,KAAK,CAAzG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJM,CANV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAeE;AAAK,IAAA,SAAS,EAAE/U,MAAM,CAACya,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,YAAD;AACE,IAAA,aAAa,EAAE,CAACvK,QAAD,EAAWwK,SAAX,EAAsBC,YAAtB,KAAuC;AACpD,UAAIA,YAAJ,EAAkB;AAChBvF,QAAAA,sBAAsB;AACtB;AACD;;AACD,UAAI,CAACsF,SAAD,IAAc,CAACC,YAAnB,EAAiC;AAC/B/F,QAAAA,oBAAoB,CAAC,IAAD,CAApB;AACD;;AACDK,MAAAA,kBAAkB,CAAC/E,QAAD,CAAlB;AACD,KAVH;AAWE,IAAA,QAAQ,EAAEqF,qBAXZ;AAYE,IAAA,IAAI,EAAElP,gBAZR;AAaE,IAAA,aAAa,EAAE1B,gBAbjB;AAcE,IAAA,iBAAiB,EAAEkQ,yBAdrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CAfF,CA9GF,eAkJE;AACE,IAAA,OAAO,EAAEY,iBADX;AAEE,IAAA,KAAK,EAAE,gBAFT;AAGE,IAAA,YAAY,EAAE,KAHhB;AAIE,IAAA,cAAc,EAAE,IAJlB;AAKE,IAAA,SAAS,EAAC,oBALZ;AAME,IAAA,QAAQ,EAAEc,2BANZ;AAOE,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,OAAO,EAAEA,2BAAjB;AAA8C,MAAA,GAAG,EAAC,QAAlD;AAA2D,MAAA,IAAI,EAAC,SAAhE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADM,CAPV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAaE;AAAK,IAAA,SAAS,EAAEvW,MAAM,CAACya,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,aAAD;AACE,IAAA,aAAa,EAAGG,aAAD,IAA4B;AACzC,UAAIA,aAAJ,EAAmB;AACjBlF,QAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD;AACF,KALH;AAME,IAAA,aAAa,EAAEC,YAAY,KAAKrV,aAAa,CAACsY,kBAA/B,IAAqDjD,YAAY,KAAKrV,aAAa,CAACwV,WAApF,GAAkGjI,sBAAlG,GAA2HlJ,gBAN5I;AAOE,IAAA,YAAY,EAAEgR,YAPhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CAbF,CAlJF,eA6KE;AACE,IAAA,cAAc,EAAE,IADlB;AAEE,IAAA,GAAG,EAAE,eAFP;AAGE,IAAA,OAAO,EAAEK,kBAHX;AAIE,IAAA,KAAK,EAAE,iBAJT;AAKE,IAAA,OAAO,EAAEwB,2BALX;AAME,IAAA,KAAK,EAAE,GANT;AAOE,IAAA,SAAS,EAAE,eAPb;AAQE,IAAA,MAAM,EAAE,cACN,0CACG7S,gBAAgB,CAACkG,MAAjB,GAA0B,CAA1B,IAA+B8C,qBAAqB,GAAG,CAAvD,iBACC;AACE,MAAA,GAAG,EAAE,CADP;AAEE,MAAA,QAAQ,EAAGgB,CAAD,IAA4B;AACpCoH,QAAAA,cAAc,CAACpH,CAAC,CAACkM,MAAF,CAASpO,OAAV,CAAd;AACD,OAJH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBAFJ,CADM,eAcN;AAAQ,MAAA,GAAG,EAAE,gBAAb;AAA+B,MAAA,OAAO,EAAE+K,2BAAxC;AAAqE,MAAA,IAAI,EAAC,SAA1E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAdM,eAiBN;AAAQ,MAAA,QAAQ,EAAE,CAACtB,qBAAnB;AAA0C,MAAA,GAAG,EAAE,cAA/C;AAA+D,MAAA,OAAO,EAAE8C,eAAxE;AAAyF,MAAA,IAAI,EAAC,SAA9F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAjBM,CARV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA8BE;AAAK,IAAA,SAAS,EAAEhZ,MAAM,CAACya,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,cAAD;AACE,IAAA,sBAAsB,EAAGK,SAAD,IAAe;AACrC3E,MAAAA,wBAAwB,CAAC2E,SAAD,CAAxB;AACD,KAHH;AAIE,IAAA,YAAY,EAAGC,UAAD,IAAyB;AACrC,UAAIA,UAAJ,EAAgB;AACd9E,QAAAA,qBAAqB,CAAC,KAAD,CAArB;AACAtL,QAAAA,QAAQ,CAACnK,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACD;AACF,KATH;AAUE,IAAA,aAAa,EAAGwa,SAAD,IAAuB;AACpCpN,MAAAA,wBAAwB,CAACoN,SAAD,CAAxB;AACD,KAZH;AAaE,IAAA,IAAI,EAAE5U,IAbR;AAcE,IAAA,SAAS,EAAEE,SAdb;AAeE,IAAA,QAAQ,EAAE0S,eAfZ;AAgBE,IAAA,QAAQ,EAAErU,gBAAgB,GAAGA,gBAAH,GAAsB,EAhBlD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CA9BF,CA7KF,eAkOE,oBAAC,kBAAD;AACE,IAAA,SAAS,EAAEoF,SADb;AAEE,IAAA,OAAO,EAAEhD,gBAFX;AAGE,IAAA,eAAe,EAAEqD,eAHnB;AAIE,IAAA,OAAO,EAAEmE,kBAJX;AAKE,IAAA,gBAAgB,EAAElE,kBALpB;AAME,IAAA,SAAS,EAAEoE,kBANb;AAOE,IAAA,cAAc,EAAEjE,qBAPlB;AAQE,IAAA,iBAAiB,EAAEC,wBARrB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAlOF,eA8OE,oBAAC,oBAAD;AACE,IAAA,SAAS,EAAEV,SADb;AAEE,IAAA,OAAO,EAAE9C,kBAFX;AAGE,IAAA,iBAAiB,EAAEqD,iBAHrB;AAIE,IAAA,OAAO,EAAEkE,oBAJX;AAKE,IAAA,gBAAgB,EAAEjE,oBALpB;AAME,IAAA,WAAW,EAAEoF,oBANf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA9OF,EAwPGgC,iBAAiB,iBAChB,oBAAC,eAAD;AAAiB,IAAA,gBAAgB,EAAE5H,SAAnC;AAA8C,IAAA,YAAY,EAAEwJ,8BAA5D;AACE,IAAA,YAAY,EAAEO,4BADhB;AAC8C,IAAA,SAAS,EAAEU,oBADzD;AAEE,IAAA,aAAa,EAAE7P,gBAFjB;AAEmC,IAAA,UAAU,EAAEgN,iBAF/C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAzPJ,EA+PGI,oBAAoB,iBACnB,oBAAC,iBAAD;AAAmB,IAAA,kBAAkB,EAAEhI,SAAvC;AAAkD,IAAA,cAAc,EAAE1C,sBAAlE;AAA0F,IAAA,YAAY,EAAEoM,gCAAxG;AACE,IAAA,SAAS,EAAE1E,sBADb;AACqC,IAAA,aAAa,EAAEpK,gBADpD;AACsE,IAAA,UAAU,EAAEoN,oBADlF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAhQJ,eAoQE;AAAK,IAAA,SAAS,EAAE/R,MAAM,CAACib,yBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,SAAS,EAAEjb,MAAM,CAACkb,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,OAAO,EAAE5E,qBAAd;AAAqC,IAAA,SAAS,EAAEtW,MAAM,CAACmb,mCAAvD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACG9E,gBAAgB,EADnB,CADF,eAIE;AAAK,IAAA,MAAM,EAAE,CAACzM,cAAc,CAACwR,sBAAhB,IAA0C,CAAC3X,aAAa,CAACqG,UAAD,EAAa,oBAAb,CAArE;AAAyG,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACzW,YAAD,GAAgB5E,MAAM,CAACsb,QAAvB,GAAkC,EAAG,EAA1L;AAA6L,qBAAe,CAAC1W,YAA7M;AAA2N,IAAA,QAAQ,EAAEA,YAAY,GAAG,CAAH,GAAO,CAAC,CAAzP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAU,IAAA,iBAAiB,EAAE,MAAM2W,QAAQ,CAACC,cAAT,CAAwB,qBAAxB,CAAnC;AAAkG,IAAA,OAAO,EAAErC,mBAA3G;AAAgI,IAAA,OAAO,EAAE,CAAC,OAAD,CAAzI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,EAAE,EAAC,qBAAR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,uDACE,oBAAC,gBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,oBACuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADvB,CADF,CADF,CADF,CADF,CADF,CAJF,eAiBG,uDAMC;AAAK,IAAA,SAAS,EAAG,GAAEnZ,MAAM,CAACqb,mBAAoB,IAAG,CAACvW,UAAD,GAAc9E,MAAM,CAACsb,QAArB,GAAgC,EAAG,EAApF;AAAuF,IAAA,OAAO,EAAE,MAAMxW,UAAU,GAAG6C,kBAAkB,CAAC,IAAD,CAArB,GAA8B+C,SAA9I;AAAyJ,qBAAe,CAAC5F,UAAzK;AAAqL,IAAA,QAAQ,EAAEA,UAAU,GAAG,CAAH,GAAO,CAAC,CAAjN;AAAoN,IAAA,MAAM,EAAE,CAAC8E,cAAc,CAAC6R,0BAAhB,IAA8C,CAAChY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAxR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,aAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,oBACoB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADpB,CAND,eASC;AAAK,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACxW,UAAD,GAAc7E,MAAM,CAACsb,QAArB,GAAgC,EAAG,EAApF;AAAuF,IAAA,OAAO,EAAE,MAAMzW,UAAU,GAAGiQ,wBAAwB,CAAC,IAAD,CAA3B,GAAoCpK,SAApJ;AAA+J,qBAAe,CAAC7F,UAA/K;AAA2L,IAAA,QAAQ,EAAEA,UAAU,GAAG,CAAH,GAAO,CAAC,CAAvN;AAA0N,IAAA,MAAM,EAAE,CAAC+E,cAAc,CAAC8R,0BAAhB,IAA8C,CAACjY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAA9R;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,gBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,oBACuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADvB,CATD,eAaC;AAAK,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACpW,aAAD,GAAiBjF,MAAM,CAACsb,QAAxB,GAAmC,EAAG,EAAvF;AAA0F,IAAA,OAAO,EAAExB,wBAAnG;AAA6H,qBAAe,CAAC7U,aAA7I;AAA4J,IAAA,QAAQ,EAAEA,aAAa,GAAG,CAAH,GAAO,CAAC,CAA3L;AACE,IAAA,MAAM,EAAE,CAAC2E,cAAc,CAAC+R,mBAAhB,IAAuC,CAAClY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAD/D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAFF,oBAEmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAFnB,CAbD,eAkBC;AAAK,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACrW,gBAAD,GAAoBhF,MAAM,CAACsb,QAA3B,GAAsC,EAAG,EAA1F;AAA6F,IAAA,MAAM,EAAE,CAAC1R,cAAc,CAACgS,2BAAhB,IAA+C,CAACnY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAlK;AACE,IAAA,OAAO,EAAEiQ,0BADX;AACuC,qBAAe,CAAC/U,gBADvD;AACyE,IAAA,QAAQ,EAAEA,gBAAgB,GAAG,CAAH,GAAO,CAAC,CAD3G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAGE,oBAAC,iBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAHF,oBAGwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAHxB,CAlBD,eAuBC;AAAK,IAAA,MAAM,EAAE,CAAC4E,cAAc,CAACiS,qBAAhB,IAAyC,CAACpY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAApE;AAA6G,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAAC1V,UAAD,GAAc3F,MAAM,CAACsb,QAArB,GAAgC,EAAG,EAA5L;AACE,IAAA,OAAO,EAAE,MAAM3V,UAAU,GAAGmM,uBAAuB,CAAC,IAAD,CAA1B,GAAmCpH,SAD9D;AACyE,qBAAe,CAAC/E,UADzF;AACqG,IAAA,QAAQ,EAAEA,UAAU,GAAG,CAAH,GAAO,CAAC,CADjI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAFF,oBAEmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAFnB,CAvBD,eA4BC;AAAK,IAAA,MAAM,EAAE,CAACiE,cAAc,CAACkS,mBAAhB,IAAuC,CAACrY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAlE;AAA2G,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACzV,QAAD,GAAY5F,MAAM,CAACsb,QAAnB,GAA8B,EAAG,EAAxL;AACE,IAAA,OAAO,EAAEtB,wBADX;AACqC,qBAAe,CAACpU,QADrD;AAC+D,IAAA,QAAQ,EAAEA,QAAQ,GAAG,CAAH,GAAO,CAAC,CADzF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAFF,oBAEmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAFnB,CA5BD,eAgCC;AAAK,IAAA,MAAM,EAAE,CAACgE,cAAc,CAACmS,sCAAhB,IAA0D,CAACtY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAArF;AAA8H,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACnW,YAAD,GAAgBlF,MAAM,CAACsb,QAAvB,GAAkC,EAAG,EAA/M;AAAkN,qBAAe,CAACpW,YAAlO;AAAgP,IAAA,QAAQ,EAAEA,YAAY,GAAG,CAAH,GAAO,CAAC,CAA9Q;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAQ,IAAA,OAAO,EAAE,MAAMA,YAAY,GAAGwU,gBAAgB,EAAnB,GAAwBhP,SAA3D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,iBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,oBACwB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBADxB,CADF,CAhCD,eAqCC;AAAK,IAAA,MAAM,EAAE,CAACd,cAAc,CAACoS,uCAAhB,IAA2D,CAACvY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAtF;AAA+H,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAAChW,WAAD,GAAerF,MAAM,CAACsb,QAAtB,GAAiC,EAAG,EAA/M;AAAkN,qBAAe,CAACjW,WAAlO;AAA+O,IAAA,QAAQ,EAAEA,WAAW,GAAG,CAAH,GAAO,CAAC,CAA5Q;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAQ,IAAA,OAAO,EAAEgV,sBAAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,WAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAEE;AAAM,IAAA,KAAK,EAAE;AAAE4B,MAAAA,SAAS,EAAE;AAAb,KAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,eAFF,CADF,CArCD,eA2CC;AAAK,IAAA,MAAM,EAAE,CAACrS,cAAc,CAACoS,uCAAhB,IAA2D,CAACvY,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAtF;AAA+H,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAAC/V,aAAD,GAAiBtF,MAAM,CAACsb,QAAxB,GAAmC,EAAG,EAAjN;AAAoN,qBAAe,CAAChW,aAApO;AAAmP,IAAA,QAAQ,EAAEA,aAAa,GAAG,CAAH,GAAO,CAAC,CAAlR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAQ,IAAA,OAAO,EAAEgV,wBAAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,WAAD;AAAa,IAAA,KAAK,EAAE;AAAE4B,MAAAA,SAAS,EAAE;AAAb,KAApB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAEE;AAAM,IAAA,KAAK,EAAE;AAAED,MAAAA,SAAS,EAAE;AAAb,KAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAFF,CADF,CA3CD,eAiDC;AAAK,IAAA,MAAM,EAAE,CAACrS,cAAc,CAACuS,qBAAhB,IAAyC,CAAC1Y,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAApE;AACE,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAACjW,UAAD,GAAcpF,MAAM,CAACsb,QAArB,GAAgC,EAAG,EADjF;AAEE,IAAA,OAAO,EAAErB,qBAFX;AAEkC,qBAAe,CAAC7U,UAFlD;AAE8D,IAAA,QAAQ,EAAEA,UAAU,GAAG,CAAH,GAAO,CAAC,CAF1F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAGE,oBAAC,cAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAHF,oBAGqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAHrB,CAjDD,eAsDC;AACE,IAAA,MAAM,EAAE,CAAC3B,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAd,IAAyD,CAACF,cAAc,CAACwR,sBADnF;AAEE,IAAA,OAAO,EAAE,MAAMrW,aAAa,KAAK6R,wBAAwB,CAAC,IAAD,CAAxB,EAAgCjM,QAAQ,CAAChK,gCAAgC,CAAC,IAAD,CAAjC,CAA7C,CAF9B;AAEsH,qBAAe,CAACoE,aAFtI;AAEqJ,IAAA,QAAQ,EAAEA,aAAa,GAAG,CAAH,GAAO,CAAC,CAFpL;AAGE,IAAA,SAAS,EAAG,GAAE/E,MAAM,CAACqb,mBAAoB,IAAI,CAACtW,aAAD,IAAkB,CAAC6E,cAAc,CAACwR,sBAAnC,GAA6Dpb,MAAM,CAACsb,QAApE,GAA+E,EAAG,EAHhI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAKE,oBAAC,eAAD;AAAiB,IAAA,SAAS,EAAG,eAA7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IALF,eAME;AAAM,IAAA,SAAS,EAAG,gBAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBANF,CAtDD,eA8DC;AAAK,IAAA,MAAM,EAAE,CAAC1R,cAAc,CAACwS,gCAAhB,IAAoD,CAAC3Y,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAA/E;AACE,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAI,CAACzR,cAAc,CAACwS,gCAAhB,IAAoD,CAAC7W,cAAtD,GAAwEvF,MAAM,CAACsb,QAA/E,GAA0F,EAAG,EAD3I;AAEE,IAAA,OAAO,EAAEpB,0BAFX;AAEuC,qBAAe,CAAC3U,cAFvD;AAEuE,IAAA,QAAQ,EAAEA,cAAc,GAAG,CAAH,GAAO,CAAC,CAFvG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAGE,oBAAC,cAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAHF,oBAGqB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,qBAHrB,CA9DD,eAmEC;AAAK,IAAA,MAAM,EAAE,CAACqE,cAAc,CAACyS,4BAA7B;AACE,IAAA,SAAS,EAAG,GAAErc,MAAM,CAACqb,mBAAoB,IAAI,CAACzR,cAAc,CAACyS,4BAAhB,IAAgD,CAAC7W,YAAlD,GAAkExF,MAAM,CAACsb,QAAzE,GAAoF,EAAG,EADrI;AACwI,IAAA,OAAO,EAAEnB,uBADjJ;AAC0K,qBAAe,CAAC3U,YAD1L;AACwM,IAAA,QAAQ,EAAEA,YAAY,GAAG,CAAH,GAAO,CAAC,CADtO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE,oBAAC,gBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAFF,oBAEuB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,iBAFvB,CAnED,eAuEC;AAAK,IAAA,SAAS,EAAG,GAAExF,MAAM,CAACqb,mBAAoB,IAAG,CAAC5V,aAAD,GAAiBzF,MAAM,CAACsb,QAAxB,GAAmC,EAAG,EAAvF;AAA0F,IAAA,OAAO,EAAE,MAAM7V,aAAa,GAAGuN,wBAAwB,CAAC,IAAD,CAA3B,GAAoCtI,SAA1J;AAAqK,qBAAe,CAACjF,aAArL;AAAoM,IAAA,QAAQ,EAAEA,aAAa,GAAG,CAAH,GAAO,CAAC,CAAnO;AACE,IAAA,MAAM,EAAE,CAAChC,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAd,IAAyD,CAACF,cAAc,CAAC0S,yBADnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAFF,oBAEmB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAFnB,CAvED,eA2EC;AAAK,IAAA,MAAM,EAAE,CAAC1S,cAAc,CAAC0S,yBAAhB,IAA6C,CAAC7Y,aAAa,CAACqG,UAAD,EAAa,yBAAb,CAAxE;AACE,IAAA,SAAS,EAAG,GAAE9J,MAAM,CAACqb,mBAAoB,IAAG,CAAC3V,eAAD,GAAmB1F,MAAM,CAACsb,QAA1B,GAAqC,EAAG,EADtF;AACyF,IAAA,OAAO,EAAE,MAAM5V,eAAe,GAAGsM,uBAAuB,CAAC,IAAD,CAA1B,GAAmCtH,SAD1J;AACqK,qBAAe,CAAChF,eADrL;AACsM,IAAA,QAAQ,EAAEA,eAAe,GAAG,CAAH,GAAO,CAAC,CADvO;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE,oBAAC,kBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAFF,oBAEyB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAFzB,CA3ED,CAjBH,CADF,eAmGE;AAAK,IAAA,SAAS,EAAE1F,MAAM,CAACuc,wBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,SAAS,EAAEvc,MAAM,CAACwc,yBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,SAAS,EAAExc,MAAM,CAACyc,+BAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,eAAD;AAAiB,IAAA,UAAU,EAAEjU,UAA7B;AAAyC,IAAA,aAAa,EAAEmR,aAAxD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,EAEGzP,iBAAiB,gBAChB;AAAQ,IAAA,IAAI,eAAE,oBAAC,cAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAd;AAAkC,IAAA,SAAS,EAAElK,MAAM,CAAC0c,eAApD;AAAqE,IAAA,OAAO,EAAE,MAAMvS,oBAAoB,CAAC,KAAD,CAAxG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,8BACU,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADV,CADgB,gBAKhB;AACE,IAAA,UAAU,MADZ;AAEE,IAAA,KAAK,EAAE;AAAEwS,MAAAA,KAAK,EAAE;AAAT,KAFT;AAGE,IAAA,WAAW,EAAC,iBAHd;AAIE,IAAA,gBAAgB,EAAC,UAJnB;AAKE,IAAA,SAAS,EAAE,IALb;AAME,IAAA,WAAW,EAAE,IANf;AAOE,IAAA,MAAM,EAAE,MAAMxS,oBAAoB,CAAC,IAAD,CAPpC;AAQE,IAAA,SAAS,EAAE,KARb;AASE,IAAA,QAAQ,EAAEiB,kBATZ;AAUE,IAAA,eAAe,EAAG,kBAVpB;AAWE,IAAA,YAAY,EAAEgC,oBAXhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAaGpF,qBAAqB,CAAC6C,MAAtB,GAA+B,CAA/B,iBACC,oBAAC,MAAD;AAAQ,IAAA,QAAQ,EAAE,IAAlB;AAAwB,IAAA,GAAG,EAAE,CAAC,CAA9B;AAAiC,IAAA,KAAK,EAAE,gBAAxC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAQ,IAAA,SAAS,EAAE7K,MAAM,CAAC4c,uBAA1B;AAAmD,IAAA,IAAI,EAAC,SAAxD;AAAkE,IAAA,OAAO,EAAE,MAAMhW,uBAAuB,CAAC,IAAD,CAAxG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,sBADF,CAdJ,EAqBGoB,qBAAqB,IACpBA,qBAAqB,CAACqE,GAAtB,CAA2BX,WAAD,IAAsC;AAC9D,wBACE,oBAAC,MAAD;AAAQ,MAAA,GAAG,EAAEA,WAAW,CAACD,EAAzB;AAA6B,MAAA,KAAK,EAAEC,WAAW,CAACD,EAAhD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OACGC,WAAW,CAACR,IADf,CADF;AAKD,GAND,CAtBJ,CAPJ,CADF,eAwCE;AAAK,IAAA,SAAS,EAAElL,MAAM,CAAC6c,mCAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACG1U,oBAAoB,iBACnB;AAAQ,IAAA,QAAQ,EAAEI,qBAAlB;AAAyC,IAAA,IAAI,EAAC,SAA9C;AAAwD,IAAA,SAAS,EAAEvI,MAAM,CAAC8c,kBAA1E;AAA8F,IAAA,OAAO,EAAE,MAAMpW,wBAAwB,CAAC,IAAD,CAArI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BAFJ,CAxCF,CADF,eAiDE;AAAK,IAAA,SAAS,EAAE1G,MAAM,CAAC+c,0BAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACC7W,qBADD,eAEE;AACE,IAAA,MAAM,EAAE,CAAC0D,cAAc,CAACoT,wBAD1B;AAEE,IAAA,QAAQ,EAAE,CAACjX,cAFb;AAGE,qBAAe,CAACA,cAHlB;AAIE,IAAA,QAAQ,EAAEA,cAAc,GAAG,CAAH,GAAO,CAAC,CAJlC;AAKE,IAAA,OAAO,EAAEqU,gBALX;AAME,IAAA,SAAS,EAAEpa,MAAM,CAACid,kBANpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAFF,eAWE,oBAAC,YAAD;AAAc,IAAA,QAAQ,EAAE3Y,SAAxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAXF,EAYG4D,UAAU,iBAAI,oBAAC,WAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAZjB,CAjDF,CAnGF,CApQF,CADF;AA2aD,CA5gDM", "sourcesContent": ["import React, { useCallback, useEffect, useState } from 'react';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { Button, Checkbox, Dropdown, Form, Menu, Modal as AntModal, Select, Drawer, Modal } from 'antd';\r\nimport {\r\n  ApartmentOutlined,\r\n  AuditOutlined,\r\n  ContainerOutlined,\r\n  CopyOutlined,\r\n  DeleteOutlined,\r\n  DownloadOutlined,\r\n  DownOutlined,\r\n  DragOutlined,\r\n  EditOutlined,\r\n  ExclamationCircleOutlined,\r\n  FileDoneOutlined,\r\n  FilterOutlined,\r\n  MenuFoldOutlined,\r\n  MenuUnfoldOutlined,\r\n  SettingOutlined,\r\n  LinkOutlined,\r\n  DisconnectOutlined,\r\n  ShareAltOutlined\r\n} from '@ant-design/icons';\r\nimport { useForm } from 'antd/lib/form/Form';\r\nimport { CheckboxChangeEvent } from 'antd/lib/checkbox';\r\nimport { Store } from 'antd/lib/form/interface';\r\n\r\nimport styles from './index.module.less';\r\nimport TagManagement from '@app/features/FileArea/TagManagement';\r\nimport AssignOption, { AssignFormEvents } from '@app/features/FileArea/Assign';\r\nimport ColumnFilter from '@app/components/ColumnFilter';\r\nimport ReCategorize from '@app/features/FileArea/ReCategorize';\r\nimport ClearFilter from '@app/components/ClearFilter';\r\nimport { RootState } from '@app/redux/reducers/state';\r\nimport DownloadModal, { downloadTypes } from '@app/components/DownloadModal';\r\nimport { IFile } from '@app/types/fileAreaTypes';\r\n\r\nimport {\r\n  updateContextMenuAssignOption,\r\n  updateContextMenuCheckoutOption,\r\n  updateContextMenuDeleteOption,\r\n  updateContextMenuDownloadOption,\r\n  updateContextMenuPropetiesoption,\r\n  updateContextMenuPublishFiles,\r\n  updateContextMenuReCategorizeOption,\r\n  updateContextMenuMoveFilesOption,\r\n  updateContextMenuReNameFilesOption,\r\n  updateContextMenuCopyFilesOption,\r\n  updateContextMenuStatusOption,\r\n  updateContextMenuUnpublishFiles,\r\n  updateLoadGridOption,\r\n  updateContextMenuLinkFilesOption,\r\n  updateContextMenuUnlinkFilesOption,\r\n  updateContextMenuToBeDeleted,\r\n  updateContextMenuCopyLinkFiles,\r\n} from '@app/redux/actions/fileAreaActions';\r\nimport ChangeStatus from '../ChangeStatus';\r\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\r\nimport CheckoutOption, { ICheckoutFile } from '../CheckoutOption';\r\nimport { deleteFile, recategorizeFiles, updateAssigneeNStatus, updateFileStatus, moveFilesService, copyFiles, reNameFiles, linkToSite, unlinkFilesFromBinders } from '@app/api/fileAreaService';\r\nimport logger from '@app/utils/logger';\r\nimport DocumentPropeties from '../DocumentPropeties';\r\n\r\nimport FilterTemplateManagementEdit from '@app/components/GenericDataTable/FilterTemplateManagement/edit';\r\nimport FilterTemplateManagementSave from '@app/components/GenericDataTable/FilterTemplateManagement/save';\r\nimport { GenericFilter } from '@app/components/GenericDataTable/types';\r\nimport { CreateFilterTemplateRequest, IFilterTemplate, SavedFilterTemplate } from '@app/types/filterTemplateTypes';\r\nimport {\r\n  applyASavedFilter,\r\n  clearGridFilters,\r\n  getSavedFilters,\r\n  saveFilterTemplate,\r\n  updateFilterTemplateSaved,\r\n  onSelectedSavedFilter,\r\n  updateGridHasUpdates,\r\n} from '@app/redux/actions/gridsActions';\r\nimport { getGridFilterFromIFilterTemplate } from '@app/components/GenericDataTable/FilterTemplateManagement/util';\r\nimport HTTPResponse from '@app/utils/http/interfaces/HttpResponse';\r\n\r\nimport { LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE } from '@app/components/Uploader';\r\nimport { checkoutFiles } from './FileAreaActionPanelFunctions/checkoutFiles';\r\nimport { FORBIDDEN_ERROR_CODE } from '@app/utils';\r\nimport { GridRefreshIcon } from '@app/components/GenericDataTable/GridRefresh';\r\nimport PublishFiles, { IPublishFiles } from '../PublishFiles/PublishFilesDrawerContent';\r\nimport UnpublishFiles, { IUnpublishFiles } from '../UnpublishFiles/UnpublishFilesDrawerContent';\r\nimport { MdOpenInNew } from 'react-icons/md';\r\nimport { publishFiles } from './FileAreaActionPanelFunctions/publishFiles';\r\nimport { unpublishFiles } from './FileAreaActionPanelFunctions/unpublishFiles';\r\nimport { copyToClipboard } from '@app/components/GenericDataTable/util';\r\nimport ReNameFiles from '../ReNameFiles';\r\nimport hasPermission from '@app/utils/permission';\r\nimport MoveFiles from '@app/features/FileAreaActionPanel/MoveFiles';\r\nimport LinkFilesDrawer from '@app/features/Link/LinkFilesDrawer';\r\nimport UnlinkFilesDrawer, { FileLink } from '@app/features/Unlink/UnlinkFilesDrawer';\r\nimport { useParams } from \"react-router-dom\";\r\nimport { UpdateFunctionalFlowDataLoading } from '@app/redux/actions/functionalFlowActions';\r\nimport SubmitButton from '@app/components/SubmitButton';\r\nimport UnpublishFilesDrawer from '@app/features/FileArea/UnpublishFiles/UnpublishFilesDrawer';\r\nimport PublishFilesDrawer from '@app/features/FileArea/PublishFiles/PublishFilesDrawer';\r\nimport { TagManagementDrawer } from '@app/features/FileArea/TagManagement/TagManagementDrawer';\r\n\r\nconst FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';\r\n\r\nexport interface IfileAreaActionPanel {\r\n  siteId: string;\r\n  selectedFileList: IFile[];\r\n  showDownload?: boolean;\r\n  showStatus?: boolean;\r\n  showAssign?: boolean;\r\n  showCheckout?: boolean;\r\n  fileDownloaded?: any;\r\n  showPropeties?: boolean;\r\n  showDelete?: boolean;\r\n  showReCategorize?: boolean;\r\n  showMoveFiles?: boolean;\r\n  syncGrid?: any;\r\n  showPublish?: boolean;\r\n  showUnpublish?: boolean;\r\n  showToBeDelete?: boolean;\r\n  showCopyLink?: boolean;\r\n  showReName?: boolean;\r\n  showCopy?: boolean;\r\n  showLinkFiles?: boolean;\r\n  showUnlinkFiles?: boolean;\r\n  originalFileAreaWithLinked?: boolean;\r\n  showManageTags?: boolean;\r\n  showManageCheckin?: boolean;\r\n  toggleIconClicked: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;\r\n  onFolderTreeChange?: (event: number) => void;\r\n  additionalActionPanel?: JSX.Element;\r\n}\r\n\r\nconst { confirm } = AntModal;\r\nconst { Option } = Select;\r\nconst elementName = 'FilterChangeTrigger';\r\nconst TABLE_KEY = 'fileArea';\r\nconst TO_BE_DELETED_STATUS = 'To be Deleted';\r\n\r\nexport const FileAreaActionPanel: React.FC<IfileAreaActionPanel> = ({\r\n  siteId,\r\n  toggleIconClicked,\r\n  selectedFileList,\r\n  showDownload = false,\r\n  showStatus = false,\r\n  showAssign = false,\r\n  showPropeties = false,\r\n  showReCategorize = false,\r\n  showMoveFiles = false,\r\n  showCheckout = false,\r\n  fileDownloaded,\r\n  showDelete = false,\r\n  showPublish = false,\r\n  showUnpublish = false,\r\n  showToBeDelete = false,\r\n  showCopyLink = false,\r\n  showLinkFiles = false,\r\n  showUnlinkFiles = false,\r\n  showReName = false,\r\n  showCopy = false,\r\n  syncGrid,\r\n  originalFileAreaWithLinked = false,\r\n  showManageTags = false,\r\n  showManageCheckin = false,\r\n  onFolderTreeChange = () => { },\r\n  additionalActionPanel = null,\r\n}) => {\r\n  const CHECKOUT_FAILED_ERRORCODE = 400;\r\n  const [form] = useForm();\r\n  const [changeStatusForm] = useForm();\r\n  const [emailForm] = useForm();\r\n  const [showPanel, setShowPanel] = useState(true);\r\n  const [showAddNewFilterModal, setShowAddNewFilterModal] = useState(false);\r\n  const [showEditFiltersModal, setShowEditFiltersModal] = useState(false);\r\n  const [showTagsManageModal, setShowTagsManageModal] = useState(false);\r\n  const [showPublishModal, setShowPublishModal] = useState(false);\r\n  const [showUnpublishModal, setShowUnpublishModal] = useState(false);\r\n  const [assignOptionForm] = Form.useForm();\r\n  const [selectedUnlinkFiles, setSelectedUnlinkFiles] = useState<FileLink[]>([]);\r\n  const [assignOptionDetails, setAssignOptionDetails] = useState<{\r\n    assignee: number;\r\n    files: IFile[];\r\n  }>({ assignee: 0, files: [] });\r\n  const [showAssignModal, setShowAssignModal] = useState(false);\r\n  const handleAddNewFilterCancel = () => {\r\n    setNewFilterName('');\r\n    setShowAddNewFilterModal(false);\r\n    setNameValid(true);\r\n  };\r\n  const [newFilterName, setNewFilterName] = useState('');\r\n  const [sortedSavedFilterList, setSortedFilterList] = useState([] as any[]);\r\n  const { showFilter, showFilterSaveButton, gridFilters, savedFilters, columns, filter_template_saved, hasUpdates } = useSelector((state: RootState) => state.grid);\r\n  const { download, status, assign, checkout, publish, unpublish, deleteFiles, propeties, reCategorize, moveFiles, renameFiles, copySelectedFiles, linkFiles, unLinkFiles, toBeDeleted, copyLink } = useSelector((state: RootState) => state.fileArea);\r\n  const { userPermission } = useSelector((state: RootState) => state.userManagement);\r\n  const { folderTree } = useSelector((state: RootState) => state.fileArea);\r\n  const { isLoading } = useSelector((state: RootState) => state.functionalFlow);\r\n\r\n  const [isNameValid, setNameValid] = useState(true);\r\n  const [showFiltersButton, setShowFiltersButton] = useState(true);\r\n  const [publishFileList, setPublishFileList] = useState<IPublishFiles[]>([]);\r\n  const [unpublishFileList, setUnpublishFileList] = useState<IUnpublishFiles[]>([]);\r\n  const [publishFileExpiration, setPublishFileExpiration] = useState<moment.Moment | undefined>(undefined);\r\n  const dispatch = useDispatch();\r\n  const { binderId } = useParams<any>();\r\n\r\n  useEffect(() => {\r\n    if (savedFilters.length === 0) {\r\n      setShowEditFiltersModal(false);\r\n    }\r\n    const sortedList = (savedFilters as any[]).sort((filter1: any, filter2: any) => {\r\n      return filter1.name.localeCompare(filter2.name);\r\n    });\r\n    setSortedFilterList(sortedList);\r\n  }, [savedFilters]);\r\n\r\n  // use to clear the grid filters on component mount.\r\n  useEffect(() => {\r\n    dispatch(clearGridFilters());\r\n  }, []);\r\n\r\n\r\n  const onClickSavedFilter = (value: any) => {\r\n    const savedFilterIndex = savedFilters.findIndex((filter: SavedFilterTemplate) => filter.id === value);\r\n    const savedFilter: SavedFilterTemplate = savedFilters[savedFilterIndex];\r\n    if (savedFilter) {\r\n      const gridFilterTemplates = getGridFilterFromIFilterTemplate(savedFilter.content, columns);\r\n      const savedFilterColumns = Object.getOwnPropertyNames(savedFilter.content);\r\n      const optionalFilterColumns = columns.filter((column: any) => !column.default && savedFilterColumns.includes(column.key)).map((column: any) => column.key);\r\n      const filterColumns = columns.map((column: any) => {\r\n        if (optionalFilterColumns.includes(column.key)) {\r\n          return { ...column, selected: true };\r\n        } else {\r\n          return column;\r\n        }\r\n      });\r\n\r\n      dispatch(\r\n        applyASavedFilter({\r\n          gridFilterTemplates,\r\n          filterColumns,\r\n          selected: optionalFilterColumns.length > 0,\r\n          selectedElement: {\r\n            name: elementName,\r\n            checked: true,\r\n            multiple: true,\r\n          },\r\n        })\r\n      );\r\n      dispatch(onSelectedSavedFilter(savedFilter));\r\n      setShowFiltersButton(true);\r\n    }\r\n  };\r\n\r\n  const {\r\n    options,\r\n    isOptionsFetched,\r\n    successedFiles,\r\n    pendingSave,\r\n    permissions,\r\n  } = useSelector((state: RootState) => {\r\n    return {\r\n      options: {\r\n        ...state.fileDetails.options,\r\n        folderTree,\r\n      },\r\n\r\n\r\n      isOptionsFetched: state.fileDetails.isOptionsFetched,\r\n      successedFiles: state.fileDetails.successedFiles,\r\n      pendingSave: state.fileDetails.pendingSave,\r\n      permissions: state.userManagement.userPermission,\r\n    };\r\n  });\r\n\r\n  const onChangeSaveNewFilterName = (name: string) => {\r\n    if (\r\n      savedFilters.findIndex((filter: SavedFilterTemplate) => {\r\n        return filter.name.toLowerCase() === name.toLowerCase().trim();\r\n      }) !== -1\r\n    ) {\r\n      setNameValid(false);\r\n      setNewFilterName(name);\r\n    } else {\r\n      setNameValid(true);\r\n      setNewFilterName(name);\r\n    }\r\n  };\r\n\r\n  const onSearchSavedFilters = (input: any, option: any) => {\r\n    if (typeof option?.children === 'string') {\r\n      return option.children.toLowerCase().startsWith(input.toLowerCase());\r\n    } else {\r\n      return false;\r\n    }\r\n  };\r\n\r\n  const handleShowTagManageModalCancel = () => {\r\n    setShowTagsManageModal(false);\r\n  };\r\n  const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';\r\n  const [checkingOutFilesCount, setCheckingOutFilesCount] = useState(0);\r\n  const [checkedOuFilesDownload, setCheckedOuFilesDownload] = useState<IFile[] | undefined>(undefined);\r\n\r\n  const resetAssignModal = () => {\r\n    setAssignOptionDetails({ assignee: 0, files: [] });\r\n    dispatch(updateContextMenuAssignOption(false));\r\n    setShowAssignModal(false);\r\n  };\r\n\r\n  const cancelAssignModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        assignOptionForm.resetFields();\r\n        resetAssignModal();\r\n      },\r\n    });\r\n  };\r\n\r\n  const cancelPublishModal = () => {\r\n    setPublishFileExpiration(undefined);\r\n    setPublishFileList([]);\r\n    setShowPublishModal(false);\r\n    dispatch(updateContextMenuPublishFiles(false));\r\n  };\r\n  const cancelUnpublishModal = () => {\r\n    setUnpublishFileList([]);\r\n    setShowUnpublishModal(false);\r\n    dispatch(updateContextMenuUnpublishFiles(false));\r\n  };\r\n\r\n  const handlePublishFiles = async () => {\r\n    dispatch(UpdateFunctionalFlowDataLoading(true));\r\n    const fileIds = publishFileList.filter((e) => e.checked).map((e) => e.id);\r\n\r\n    try {\r\n      const { data } = await publishFiles(fileIds, siteId, publishFileExpiration?.toDate());\r\n      if (data) {\r\n        syncGrid(true);\r\n        const files = publishFileList\r\n          .filter((e) => !e.checked)\r\n          .map((e) => {\r\n            return { ...e, checked: true };\r\n          });\r\n        if (files.length === 0) {\r\n          cancelPublishModal();\r\n          return;\r\n        }\r\n        setPublishFileList(files);\r\n        setPublishFileExpiration(undefined);\r\n      }\r\n    } catch (e) {\r\n      errorNotification([''], 'Publishing Failed');\r\n      logger.error('File Area Module', 'Publish files', e);\r\n    } finally {\r\n      dispatch(UpdateFunctionalFlowDataLoading(false));\r\n    }\r\n  };\r\n\r\n  const handleFileUnlinkSubmit = () => {\r\n    const unlinkedData = selectedUnlinkFiles\r\n      .filter(file => file.selectedBinders && file.selectedBinders.length > 0)\r\n      .map(file => ({\r\n        fileId: file.fileId,\r\n        binderIds: file.selectedBinders\r\n      }));\r\n\r\n    dispatch(UpdateFunctionalFlowDataLoading(true));\r\n    unlinkFilesFromBinders(unlinkedData)\r\n      .then((response) => {\r\n        if (response.data) {\r\n          successNotification([''], 'File(s) Unlinked Successfully');\r\n          syncGrid(true);\r\n          resetUnlinkFilesModal();\r\n        } else {\r\n          errorNotification([''], 'File Unlinking Failed');\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'File Unlinking Failed');\r\n        }\r\n        logger.error('File Area Module', 'Unlink files', error);\r\n      }).finally(() => {\r\n        dispatch(UpdateFunctionalFlowDataLoading(false));\r\n      });\r\n  };\r\n\r\n  const handleUnpublishFiles = async () => {\r\n    dispatch(UpdateFunctionalFlowDataLoading(true));\r\n    const fileIds = unpublishFileList.filter((e) => e.checked).map((e) => e.id);\r\n    try {\r\n      const { data } = await unpublishFiles(fileIds, siteId);\r\n      if (data) {\r\n        syncGrid(true);\r\n        const files = publishFileList.filter((e) => !e.checked);\r\n        if (files.length === 0) {\r\n          cancelUnpublishModal();\r\n          return;\r\n        }\r\n        setUnpublishFileList(files);\r\n      }\r\n    } catch (e) {\r\n      errorNotification([''], 'Unpublishing Failed');\r\n      logger.error('File Area Module', 'Unpublish files', e);\r\n    } finally {\r\n      dispatch(UpdateFunctionalFlowDataLoading(false));\r\n    }\r\n  };\r\n\r\n  const handleShowAssignModalCancel = (isAllFilesRemoved = false) => {\r\n    if (assignOptionDetails.assignee > 0 || assignOptionForm.getFieldValue('statusId') > 0 || assignOptionForm.getFieldValue('assignNotes')) {\r\n      if (isAllFilesRemoved) {\r\n        assignOptionForm.resetFields();\r\n        resetAssignModal();\r\n      } else {\r\n        cancelAssignModal();\r\n      }\r\n    } else {\r\n      resetAssignModal();\r\n    }\r\n  };\r\n  const onAssigneeUpdate = (value: any) => {\r\n    const currentAssignState = { ...assignOptionDetails };\r\n    currentAssignState.assignee = value;\r\n    setAssignOptionDetails(currentAssignState);\r\n  };\r\n  const onFilesChange = (fileList: IFile[]) => {\r\n    const currentAssignState = { ...assignOptionDetails };\r\n    currentAssignState.files = fileList;\r\n    setAssignOptionDetails(currentAssignState);\r\n  };\r\n  const handleAssignFormEvents: AssignFormEvents = {\r\n    onAssigneeUpdate,\r\n    onFilesChange,\r\n  };\r\n\r\n  const onUpdatedAssignee = (response: HTTPResponse<any>) => {\r\n    if (response.data) {\r\n      const SET_TIMEOUT_ASSIGNEE = 500;\r\n      successNotification([''], 'Assignment Successful');\r\n      // successNotification([''], 'File Assignment Email sent successfully');\r\n      syncGrid(true);\r\n      dispatch(updateContextMenuAssignOption(false));\r\n      setTimeout(() => {\r\n        assignOptionForm.resetFields();\r\n        setShowAssignModal(false);\r\n        setAssignOptionDetails({ assignee: 0, files: [] });\r\n      }, SET_TIMEOUT_ASSIGNEE);\r\n    } else {\r\n      errorNotification([''], 'Assignment Failed');\r\n    }\r\n  };\r\n\r\n  const handleAssignOptionUpdate = () => {\r\n    assignOptionForm.validateFields().then((values) => {\r\n      const selectedFileIds = assignOptionDetails.files.length > 0 ? assignOptionDetails.files.map((file) => file.id) : selectedFileList.map((file) => file.id);\r\n      updateAssigneeNStatus({\r\n        assigneeId: values.assigneeId,\r\n        statusId: values.statusId,\r\n        fileIds: selectedFileIds,\r\n        note: values.assignNotes,\r\n      })\r\n        .then((response) => {\r\n          onUpdatedAssignee(response);\r\n        })\r\n        .catch((error) => {\r\n          if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n            errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n          } else {\r\n            errorNotification([''], 'Assignment Failed');\r\n          }\r\n          logger.error('AssignOption', 'Update Assignee and Status', error);\r\n        });\r\n    });\r\n  };\r\n  const onCancelRecategorizeModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        resetReCategorizeModal();\r\n      },\r\n    });\r\n  };\r\n\r\n  const onCancelMoveFilesModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        resetMoveFilesModal();\r\n      },\r\n    });\r\n  };\r\n\r\n  const onCancelLinkFilesModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        resetLinkFilesModal();\r\n      },\r\n    });\r\n  };\r\n\r\n  const onCancelReNameFilesModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        assignOptionForm.resetFields();\r\n        resetReNameFilesModal();\r\n      },\r\n    });\r\n  };\r\n\r\n  const [showReCategorizeModal, setShowReCategorizeModal] = useState(false);\r\n  const [showMoveFileModal, setShowMoveFileModal] = useState(false);\r\n  const [showLinkFileModal, setShowLinkFilesModal] = useState(false);\r\n  const [showReNameFilesModal, setShowReNameFilesModal] = useState(false);\r\n  const [showUnlinkFilesModal, setShowUnlinkFilesModal] = useState(false);\r\n  const [recategorizeDetails, setRecategorizeDetails] = useState<{\r\n    fileList: IFile[];\r\n    folderId: number;\r\n\r\n  }>();\r\n  const [moveFileDetails, setMoveFilesDetails] = useState<{\r\n    fileList: IFile[];\r\n    folderId: number;\r\n    sourceClientId: string;\r\n    destinationClientId: number;\r\n  }>();\r\n\r\n  const [linkFilesDetails, setLinkFilesDetails] = useState<{\r\n    fileIds: string[];\r\n    binderIds: string[];\r\n  }>();\r\n\r\n  const [reNameFilesDetails, setReNameFilesDetails] = useState<{\r\n    fileList: IFile[];\r\n  }>();\r\n\r\n  const handleShowReCategorizeModalCancel = (hasSelectedFiles = true) => {\r\n    if (hasSelectedFiles && recategorizeDetails?.folderId !== undefined) {\r\n      onCancelRecategorizeModal();\r\n    } else {\r\n      resetReCategorizeModal();\r\n    }\r\n  };\r\n\r\n  const handleCancel = () => setShowMoveFileModal(false);\r\n  const handleRenameCancel = () => setShowReNameFilesModal(false);\r\n  const handleReCategorizeFileCancel = () => setShowReCategorizeModal(false);\r\n  const handleShowMoveFilesModalCancel = (hasSelectedFiles = true) => {\r\n    if (hasSelectedFiles && moveFileDetails?.folderId !== undefined) {\r\n      onCancelMoveFilesModal();\r\n    } else {\r\n      resetMoveFilesModal();\r\n    }\r\n  };\r\n\r\n  const handleShowLinkFilesModal = (show: boolean) => {\r\n    const areToBeDeleted = selectedFileList.filter((file) => file.status.name === TO_BE_DELETED_STATUS);\r\n    if (areToBeDeleted.length > 0 && show) {\r\n      errorNotification([''], 'Unable to link file(s). Please change the Status and retry.');\r\n      resetLinkFilesModal();\r\n    } else {\r\n      setShowLinkFilesModal(show);\r\n    }\r\n  }\r\n\r\n  const hasLinkedFiles = (files: IFile[]): boolean => {\r\n    const areSelectedFilesLinked = files.some((file) => file.linked);\r\n    if (areSelectedFilesLinked) {\r\n      errorNotification([''], 'You cannot perform this action to linked files. Please unlink files first.');\r\n    }\r\n    return areSelectedFilesLinked;\r\n  };\r\n\r\n  const handleShowLinkFilesModalCancel = () => {\r\n    resetLinkFilesModal();\r\n  };\r\n\r\n  const handleShowReNameFilesModalCancel = (hasSelectedFiles = true) => {\r\n    if (hasSelectedFiles) {\r\n      onCancelReNameFilesModal();\r\n    } else {\r\n      resetReNameFilesModal();\r\n    }\r\n  };\r\n\r\n  const handleShowUnlinkFilesModalCancel = () => {\r\n    resetUnlinkFilesModal();\r\n  };\r\n\r\n  const resetReCategorizeModal = () => {\r\n    setRecategorizeDetails({\r\n      folderId: undefined as any,\r\n      fileList: undefined as any,\r\n    });\r\n    dispatch(updateContextMenuReCategorizeOption(false));\r\n    setShowReCategorizeModal(false);\r\n  };\r\n\r\n  const resetMoveFilesModal = () => {\r\n    setMoveFilesDetails({\r\n      folderId: undefined as any,\r\n      fileList: undefined as any,\r\n      sourceClientId: undefined as any,\r\n      destinationClientId: undefined as any,\r\n    });\r\n    dispatch(updateContextMenuMoveFilesOption(false));\r\n    setShowMoveFileModal(false);\r\n  };\r\n\r\n  const resetLinkFilesModal = () => {\r\n    setLinkFilesDetails({\r\n      binderIds: undefined as any,\r\n      fileIds: undefined as any,\r\n    });\r\n    dispatch(updateContextMenuLinkFilesOption(false));\r\n    handleShowLinkFilesModal(false);\r\n  };\r\n\r\n  const resetReNameFilesModal = () => {\r\n    setReNameFilesDetails({\r\n      fileList: undefined as any\r\n    });\r\n    dispatch(updateContextMenuReNameFilesOption(false));\r\n    setShowReNameFilesModal(false);\r\n    assignOptionForm.resetFields();\r\n    setRenameButtonEnable(false);\r\n  };\r\n\r\n  const resetUnlinkFilesModal = () => {\r\n    setSelectedUnlinkFiles([]);\r\n    dispatch(updateContextMenuUnlinkFilesOption(false));\r\n    setShowUnlinkFilesModal(false);\r\n  };\r\n\r\n  const handleReCategorizeUpdateDetails = (folderId: number, fileList: IFile[]) => {\r\n    setRecategorizeDetails({ folderId: folderId, fileList: fileList });\r\n  };\r\n\r\n  const handleLinkFilesUpdateDetails = (fileList: string[], selectedBinderIds: string[]) => {\r\n    setLinkFilesDetails({ fileIds: fileList, binderIds: selectedBinderIds });\r\n  };\r\n\r\n  const handleValuesChange = (changedValues: any, allValues: any) => {\r\n    // Check if any input field has a non-empty value\r\n    const anyRenamedValues = Object.values(allValues).some(value => value);\r\n    setRenameButtonEnable(anyRenamedValues);\r\n  };\r\n\r\n  const handleReNameFilesUpdateDetails = (fileList: any[]) => {\r\n\r\n    const updatedFileList = fileList.map((file: IFile) => ({\r\n      fileId: file.id,\r\n      title: file.title\r\n    }));\r\n\r\n    reNameFiles(\r\n      updatedFileList\r\n    )\r\n      .then((response) => {\r\n        successNotification([''], 'ReNamed Successfully');\r\n        syncGrid(true);\r\n        resetReNameFilesModal();\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'ReName Failed');\r\n        }\r\n        logger.error('File Area Module', 'ReName files', error);\r\n      });\r\n\r\n  };\r\n\r\n  const handleRecategorizeUpdate = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {\r\n    recategorizeFiles(\r\n      recategorizeDetails?.fileList.map((file) => file.id),\r\n      recategorizeDetails?.folderId ? recategorizeDetails.folderId : 0,\r\n      binderId\r\n    )\r\n      .then((response) => {\r\n        successNotification([''], 'File Re-Categorization Successful');\r\n        syncGrid(true);\r\n        resetReCategorizeModal();\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'File Re-Categorization Failed');\r\n        }\r\n        logger.error('File ARea Module', 'File Re-Categorization', error);\r\n      });\r\n  };\r\n\r\n\r\n  const handleLinkFileUpdate = () => {\r\n    dispatch(UpdateFunctionalFlowDataLoading(true));\r\n\r\n    linkToSite(\r\n      linkFilesDetails\r\n    )\r\n      .then((response) => {\r\n        successNotification([''], 'File(s) Linked Successfully');\r\n        syncGrid(true);\r\n        resetLinkFilesModal();\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'File Linking Failed');\r\n        }\r\n        logger.error('File Area Module', 'Move files', error);\r\n      }).finally(() => {\r\n        dispatch(UpdateFunctionalFlowDataLoading(false));\r\n      });\r\n  };\r\n\r\n  const handleFileReNameSubmit = () => {\r\n    assignOptionForm.submit();\r\n  };\r\n\r\n\r\n  const cancelChangeStatusModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        setStatusFileRemoved(false);\r\n        setSelectedNewStatusState(0);\r\n        dispatch(updateContextMenuStatusOption(false));\r\n        setShowStatusChangeModal(false);\r\n      },\r\n    });\r\n  };\r\n\r\n  const [selectedNewStatusState, setSelectedNewStatusState] = useState(0);\r\n  const [fileStatusList, setFilesStatusList] = useState<IFile[]>();\r\n  const [showStatusChangeModal, setShowStatusChangeModal] = useState(false);\r\n  const [statusFileRemoved, setStatusFileRemoved] = useState(false);\r\n\r\n  const handleCloseStatusModal = () => {\r\n    dispatch(updateContextMenuStatusOption(false));\r\n    setShowStatusChangeModal(false);\r\n    setFilesStatusList([]);\r\n    setStatusFileRemoved(false);\r\n  };\r\n\r\n  const handleShowStatusChangeModalCancel = () => {\r\n    if (selectedNewStatusState > 0 || statusFileRemoved) {\r\n      cancelChangeStatusModal();\r\n    } else {\r\n      handleCloseStatusModal();\r\n    }\r\n  };\r\n\r\n  const resetFileStatusValues = () => {\r\n    setFilesStatusList([]);\r\n    dispatch(updateContextMenuStatusOption(false));\r\n    setShowStatusChangeModal(false);\r\n    setSelectedNewStatusState(0);\r\n    syncGrid(true);\r\n  };\r\n\r\n  const handleNewStatusUpdate = () => {\r\n    const isDeleteStatus = selectedNewStatusState === 6;\r\n\r\n    if (isDeleteStatus) {\r\n      if (hasLinkedFiles(selectedFileList)) {\r\n        return;\r\n      }\r\n    }\r\n\r\n    updateFileStatus(fileStatusList && fileStatusList.length > 0 ? fileStatusList?.map((file) => file.id) : selectedFileList?.map((file) => file.id), selectedNewStatusState)\r\n      .then((response) => {\r\n        successNotification([''], 'Status Update Successful');\r\n        resetFileStatusValues();\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'Status Update Failed');\r\n        }\r\n        logger.error('File ARea Module', 'Update Status', error);\r\n      });\r\n\r\n    dispatch(updateContextMenuStatusOption(false));\r\n  };\r\n\r\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\r\n  const [downloadType, setDownloadType] = useState<downloadTypes | undefined>(downloadTypes.individual);\r\n  const [checkoutZip, setCheckoutZip] = useState(false);\r\n  const [showCheckoutdModal, setshowCheckoutdModal] = useState(false);\r\n  const [validatedCheckoutForm, setValidatedCheckoutForm] = useState(true);\r\n  const [renameButtonEnable, setRenameButtonEnable] = useState(false);\r\n  const renderToggleIcon = () => {\r\n    return showPanel ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />;\r\n  };\r\n  const handleOnToggleClicked = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {\r\n    toggleIconClicked(event);\r\n    setShowPanel(!showPanel);\r\n  };\r\n\r\n  const handleOnDownloadModalCancel = () => {\r\n    dispatch(updateContextMenuDownloadOption(false));\r\n    setShowDownloadModal(false);\r\n  };\r\n\r\n  const displaydownloadModal = (downloadTypeInput: downloadTypes, display: boolean) => {\r\n    setDownloadType(downloadTypeInput);\r\n    setShowDownloadModal(display);\r\n  };\r\n\r\n  const [documentPropeties, displayDocumentPropeties] = useState(false);\r\n\r\n  useEffect(() => {\r\n    setCheckoutZip(false);\r\n    if (checkout) {\r\n      setCheckingOutFilesCount(selectedFileList.length);\r\n    }\r\n    setshowCheckoutdModal(checkout);\r\n  }, [checkout, selectedFileList]);\r\n\r\n  useEffect(() => {\r\n    displaydownloadModal(downloadTypes.individual, download);\r\n  }, [download]);\r\n\r\n  useEffect(() => {\r\n    setShowStatusChangeModal(status);\r\n  }, [status]);\r\n\r\n  useEffect(() => {\r\n    setShowAssignModal(assign);\r\n  }, [assign]);\r\n\r\n  useEffect(() => {\r\n    displayDocumentPropeties(propeties);\r\n  }, [propeties]);\r\n\r\n  useEffect(() => {\r\n    if (reCategorize && !hasLinkedFiles(selectedFileList)) {\r\n      setShowReCategorizeModal(true);\r\n    }\r\n    dispatch(updateContextMenuReCategorizeOption(false));\r\n  }, [reCategorize]);\r\n\r\n  useEffect(() => {\r\n    if (moveFiles && !hasLinkedFiles(selectedFileList)) {\r\n      setShowMoveFileModal(true);\r\n    }\r\n    dispatch(updateContextMenuMoveFilesOption(false));\r\n  }, [moveFiles]);\r\n\r\n  useEffect(() => {\r\n    if (toBeDeleted && !hasLinkedFiles(selectedFileList)) {\r\n      handleToBeDelete(selectedFileList);\r\n    }\r\n    dispatch(updateContextMenuToBeDeleted(false));\r\n  }, [toBeDeleted]);\r\n\r\n  useEffect(() => {\r\n    if (copyLink && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\r\n      handleCopyLink(selectedFileList);\r\n    }\r\n    dispatch(updateContextMenuCopyLinkFiles(false));\r\n  }, [copyLink]);\r\n\r\n  useEffect(() => {\r\n    setShowReNameFilesModal(renameFiles);\r\n  }, [renameFiles]);\r\n\r\n  useEffect(() => {\r\n    handleShowLinkFilesModal(linkFiles);\r\n  }, [linkFiles]);\r\n\r\n  useEffect(() => {\r\n    setShowUnlinkFilesModal(unLinkFiles);\r\n  }, [unLinkFiles]);\r\n\r\n  useEffect(() => {\r\n    if (copySelectedFiles && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\r\n      showCopyFilesDialog(copySelectedFiles);\r\n    }\r\n    dispatch(updateContextMenuCopyFilesOption(false));\r\n  }, [copySelectedFiles]);\r\n\r\n  useEffect(() => {\r\n    dispatch(getSavedFilters(TABLE_KEY, siteId));\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (publish && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\r\n      const mapToCheckoutFiles = (selectedFiles: IFile[]): IPublishFiles[] => {\r\n        const checkoutFileList = [] as IPublishFiles[];\r\n        selectedFiles?.forEach((file) => {\r\n          checkoutFileList.push({\r\n            id: file.id,\r\n            checked: true,\r\n            title: file.title,\r\n          });\r\n        });\r\n        return checkoutFileList;\r\n      };\r\n      setPublishFileList(mapToCheckoutFiles(selectedFileList));\r\n      setShowPublishModal(true);\r\n      dispatch(updateContextMenuPublishFiles(false));\r\n    }\r\n  }, [publish]);\r\n\r\n  useEffect(() => {\r\n    if (unpublish && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {\r\n      const mapFiles = (selectedFiles: IFile[]): IUnpublishFiles[] => {\r\n        const fileList = [] as IUnpublishFiles[];\r\n        selectedFiles?.forEach((file) => {\r\n          fileList.push({\r\n            id: file.id,\r\n            checked: true,\r\n            title: file.title,\r\n          });\r\n        });\r\n        return fileList;\r\n      };\r\n      setUnpublishFileList(mapFiles(selectedFileList));\r\n      setShowUnpublishModal(true);\r\n      dispatch(updateContextMenuUnpublishFiles(false));\r\n    }\r\n  }, [unpublish]);\r\n\r\n\r\n  const showCopyFilesDialog = (value: boolean) => {\r\n    if (value) {\r\n      confirm({\r\n        title: \"File(s) will be copied. Do you wish to continue?\",\r\n        icon: <CopyOutlined />,\r\n        okText: 'Yes',\r\n        cancelText: 'No',\r\n        onOk: () => {\r\n          handleCopyFiles();\r\n          dispatch(updateContextMenuCopyFilesOption(false));\r\n\r\n        },\r\n        onCancel: () => {\r\n          dispatch(updateContextMenuCopyFilesOption(false));\r\n        }\r\n\r\n      });\r\n    }\r\n  }\r\n\r\n  const handleOnCheckoutModalCancel = () => {\r\n    confirm({\r\n      title: 'Are you sure you want to discard the changes?',\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        dispatch(updateContextMenuCheckoutOption(false));\r\n        setshowCheckoutdModal(false);\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleFileDelete = (response: HTTPResponse<any>) => {\r\n    if (response) {\r\n      successNotification([''], 'File(s) Deleted Successfully');\r\n      syncGrid(true);\r\n      dispatch(updateContextMenuDeleteOption(false));\r\n    } else {\r\n      errorNotification([''], 'Deletion Failed');\r\n      dispatch(updateContextMenuDeleteOption(false));\r\n    }\r\n  };\r\n\r\n  const onClickOkDeleteFiles = useCallback((selectedFiles: IFile[]) => {\r\n    const fileIdList: any[] = [];\r\n    selectedFiles.forEach((file) => {\r\n      fileIdList.push(file.id);\r\n    });\r\n    deleteFile(fileIdList)\r\n      .then((response) => {\r\n        handleFileDelete(response);\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'Deletion Failed');\r\n        }\r\n        dispatch(updateContextMenuDeleteOption(false));\r\n      });\r\n  }, []);\r\n\r\n  const handleToBeDelete = (selectedFiles: IFile[]) => {\r\n    updateFileStatus(\r\n      selectedFiles.map((e) => e.id),\r\n      6\r\n    ).then(() => syncGrid(true));\r\n  };\r\n\r\n  const handleCopyLink = (selectedFiles: IFile[]) => {\r\n    let ids = selectedFiles.map((e) => e.id).join(',');\r\n    copyToClipboard(ids);\r\n    syncGrid(true);\r\n  };\r\n\r\n  const handleDeleteFiles = useCallback((selectedFiles: IFile[]) => {\r\n    const totalFiles = selectedFiles.length;\r\n    const publishedCount = selectedFiles.filter((file) => file.published).length;\r\n    const fileLabel = `${totalFiles} ${totalFiles === 1 ? 'file' : 'files'}`;\r\n    const message = `${fileLabel} will be deleted.${publishedCount > 0 ? ' Any published files would be unpublished when deleted.' : ''} Are you sure you want to proceed?`;\r\n    confirm({\r\n      title: message,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        onClickOkDeleteFiles(selectedFiles);\r\n      },\r\n      onCancel() {\r\n        dispatch(updateContextMenuDeleteOption(false));\r\n      },\r\n    });\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    if (deleteFiles && selectedFileList.length > 0) {\r\n      if (deleteFiles && !hasLinkedFiles(selectedFileList)) {\r\n        handleDeleteFiles(selectedFileList);\r\n      }\r\n      dispatch(updateContextMenuDeleteOption(false));\r\n    }\r\n  }, [deleteFiles, handleDeleteFiles, selectedFileList]);\r\n\r\n  const setCheckoutCommonNote = (values: Store) => {\r\n    values.files.forEach((value: ICheckoutFile) => {\r\n      value.checkNote = values.commonNote;\r\n    });\r\n  };\r\n\r\n  const formatReturnDates = (values: Store): ICheckoutFile[] => {\r\n    const returnCheckoutValues = [] as ICheckoutFile[];\r\n    values.files.forEach((value: ICheckoutFile) => {\r\n      returnCheckoutValues.push({\r\n        ...value,\r\n        returnDate: value?.returnDate?.format('YYYY-MM-DD'),\r\n      });\r\n    });\r\n    return returnCheckoutValues;\r\n  };\r\n  const handleSavedCheckoutFiles = () => {\r\n    setshowCheckoutdModal(false);\r\n    dispatch(updateContextMenuCheckoutOption(false));\r\n    setDownloadType(checkoutZip ? downloadTypes.checkoutZip : downloadTypes.checkoutIndividual);\r\n    setShowDownloadModal(true);\r\n    fileDownloaded(true);\r\n    dispatch(updateLoadGridOption(true));\r\n    setTimeout(() => {\r\n      dispatch(updateLoadGridOption(false));\r\n    });\r\n    if (emailForm.getFieldsValue().emailUser || emailForm.getFieldsValue().emailContact) {\r\n      successNotification([''], 'Check-out Email Sent Successfully');\r\n    }\r\n  };\r\n\r\n  const onCheckoutFiles = () => {\r\n    form.validateFields().then(async (values) => {\r\n      values.files = values.files.filter((file: ICheckoutFile) => file.checked);\r\n      if (values.commonNote) {\r\n        setCheckoutCommonNote(values);\r\n      }\r\n      setCheckedOuFilesDownload(values.files);\r\n      const { hasError, errorCode } = await checkoutFiles(formatReturnDates(values));\r\n      if (hasError) {\r\n        errorCode && errorCode === FORBIDDEN_ERROR_CODE\r\n          ? errorNotification([''], FORBIDDEN_ERROR_MESSAGE)\r\n          : errorCode && errorCode === CHECKOUT_FAILED_ERRORCODE\r\n            ? errorNotification([''], LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE)\r\n            : errorNotification([''], 'Check-Out Failed');\r\n      } else {\r\n        handleSavedCheckoutFiles();\r\n      }\r\n    });\r\n  };\r\n\r\n  const downloadOptionsMenu = (\r\n    <Menu className={styles.yjFilterMenuDropdownWrapper}>\r\n      <Menu.Item\r\n        hidden={selectedFileList && selectedFileList?.length <= 0}\r\n        onClick={() => {\r\n          displaydownloadModal(downloadTypes.individual, true);\r\n        }}\r\n        key=\"1\"\r\n      >\r\n        Download Files\r\n      </Menu.Item>\r\n      <Menu.Item\r\n        hidden={selectedFileList && selectedFileList?.length <= 1}\r\n        onClick={() => {\r\n          displaydownloadModal(downloadTypes.zip, true);\r\n        }}\r\n        key=\"2\"\r\n      >\r\n        Download as a zip file\r\n      </Menu.Item>\r\n    </Menu>\r\n  );\r\n\r\n  const onSaveFilterTemplate = () => {\r\n    dispatch(updateFilterTemplateSaved(true));\r\n\r\n    const createFilterTemplateRequest: CreateFilterTemplateRequest = {\r\n      name: newFilterName.trim(),\r\n      content: {},\r\n    };\r\n    gridFilters.forEach((filter: GenericFilter) => {\r\n      const field = filter['key'];\r\n      if (filter?.isArray && (filter?.value || filter?.value === 0)) {\r\n        if ((createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) === undefined) {\r\n          (createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) = [];\r\n        }\r\n        (createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) = [\r\n          ...(createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]),\r\n          filter['value'],\r\n        ];\r\n      } else {\r\n        createFilterTemplateRequest.content[field as keyof IFilterTemplate] = filter['value'];\r\n      }\r\n    });\r\n    dispatch(saveFilterTemplate(createFilterTemplateRequest, TABLE_KEY, siteId));\r\n    setShowAddNewFilterModal(false);\r\n    setNewFilterName('');\r\n  };\r\n\r\n  const onCheckoutAction = () => {\r\n    setCheckoutZip(false);\r\n    setshowCheckoutdModal(true);\r\n    setCheckingOutFilesCount(selectedFileList.length);\r\n  };\r\n\r\n  const onRefreshGrid = () => {\r\n    syncGrid(true);\r\n    dispatch(updateGridHasUpdates(false));\r\n  };\r\n  const onPublishAction = () => {\r\n    dispatch(updateContextMenuPublishFiles(true));\r\n  };\r\n  const onUnpublishAction = () => {\r\n    dispatch(updateContextMenuUnpublishFiles(true));\r\n  };\r\n\r\n  const handleMoveFilesActionBar = () => {\r\n    if (!showMoveFiles) return;\r\n    if (!hasLinkedFiles(selectedFileList)) {\r\n      setShowMoveFileModal(true);\r\n    }\r\n  };\r\n\r\n  const handleReCategorizeActionBr = () => {\r\n    if (!showReCategorize) return;\r\n    if (!hasLinkedFiles(selectedFileList)) {\r\n      setShowReCategorizeModal(true);\r\n    }\r\n  };\r\n\r\n  const handleCopyFilesActionBar = () => {\r\n    if (!showCopy) return;\r\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\r\n      showCopyFilesDialog(true);\r\n    }\r\n  };\r\n\r\n  const handleDeleteActionBar = () => {\r\n    if (!showDelete) return;\r\n    if (!hasLinkedFiles(selectedFileList)) {\r\n      handleDeleteFiles(selectedFileList);\r\n    }\r\n  };\r\n\r\n  const handleToBeDeletedActionBar = () => {\r\n    if (!showToBeDelete) return;\r\n    if (!hasLinkedFiles(selectedFileList)) {\r\n      handleToBeDelete(selectedFileList);\r\n    }\r\n  };\r\n\r\n  const handleCopyLinkActionBar = () => {\r\n    if (!showCopyLink) return;\r\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\r\n      handleCopyLink(selectedFileList);\r\n    }\r\n  };\r\n\r\n  const handleManageTags = () => {\r\n    if (!hasLinkedFiles(selectedFileList)) {\r\n      setShowTagsManageModal(true);\r\n    }\r\n  };\r\n\r\n  const handlePublishActionBar = () => {\r\n    if (!showPublish) return;\r\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\r\n      onPublishAction();\r\n    }\r\n  };\r\n\r\n  const handleUnpublishActionBar = () => {\r\n    if (!showUnpublish) return;\r\n    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {\r\n      onUnpublishAction();\r\n    }\r\n  };\r\n\r\n  const SET_TIMEOUT_ASSIGNEE = 500;\r\n  const handleCopyFiles = () => {\r\n    const selectedFileUploadReferenceList = selectedFileList?.map((file: IFile) => file.uploadReference);\r\n\r\n    copyFiles(\r\n      selectedFileUploadReferenceList\r\n    )\r\n      .then((response) => {\r\n        successNotification([''], 'Copied Successfully');\r\n        setTimeout(() => {\r\n          syncGrid(true);\r\n\r\n        }, SET_TIMEOUT_ASSIGNEE);\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], 'Copy Failed');\r\n        }\r\n        logger.error('File Area Module', 'Copy Files', error);\r\n      });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <DocumentPropeties\r\n        file={selectedFileList[0]}\r\n        onCloseDrawer={() => {\r\n          displayDocumentPropeties(false);\r\n          dispatch(updateContextMenuPropetiesoption(false));\r\n        }}\r\n        displayDrawer={documentPropeties}\r\n      />\r\n\r\n      {/* Re-Categorize option Drawer */}\r\n\r\n      <ReCategorize\r\n        onSuccess={() => syncGrid(true)}\r\n        selectedFiles={selectedFileList}\r\n        onClosePopup={handleReCategorizeFileCancel}\r\n        options={options}\r\n        form={assignOptionForm}\r\n        onFolderTreeChange={onFolderTreeChange}\r\n        showReCategorizeModal={showReCategorizeModal}        // Provide the 'options' prop\r\n        binderId={binderId}\r\n      />\r\n\r\n\r\n      {/* Move Files drawyer */}\r\n      <MoveFiles\r\n        siteId={siteId}\r\n        binderId={binderId}\r\n        onSuccess={() => syncGrid(true)}\r\n        selectedFiles={selectedFileList}\r\n        onClosePopup={handleCancel}\r\n        options={options}\r\n        form={assignOptionForm}\r\n        onFolderTreeChange={onFolderTreeChange}\r\n        showMoveFileModal={showMoveFileModal}\r\n      />\r\n\r\n      {/* File ReName Drawer */}\r\n      <ReNameFiles\r\n        onSuccess={() => syncGrid(true)}\r\n        selectedFiles={selectedFileList}\r\n        onClosePopup={handleRenameCancel}\r\n        options={options}\r\n        form={assignOptionForm}\r\n        showReNameFilesModal={showReNameFilesModal}\r\n      />\r\n\r\n\r\n      {/* Add New Filter Drawer */}\r\n      <Drawer\r\n        visible={showAddNewFilterModal}\r\n        title={'Save as a New Filter'}\r\n        width={700}\r\n        onClose={handleAddNewFilterCancel}\r\n        className={\"yjDrawerPanel\"}\r\n        footer={[\r\n          <Button key=\"back\" type=\"default\" onClick={handleAddNewFilterCancel}>\r\n            cancel\r\n          </Button>,\r\n          <Button disabled={newFilterName.trim().length < 1 || !isNameValid} key=\"submit\" type=\"primary\" onClick={() => onSaveFilterTemplate()}>\r\n            Save\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <FilterTemplateManagementSave isNameValid={isNameValid} newFilterName={newFilterName} onFilterNameChangeHandler={(name: string) => onChangeSaveNewFilterName(name)} />\r\n        </div>\r\n      </Drawer>\r\n      {/* Manage existing Filters Drawer */}\r\n      <Drawer\r\n        visible={showEditFiltersModal}\r\n        title={'Manage Filter'}\r\n        width={700}\r\n        onClose={() => setShowEditFiltersModal(false)}\r\n        className={\"yjDrawerPanel\"}\r\n        footer={[\r\n          <Button key=\"back\" type=\"default\" onClick={() => setShowEditFiltersModal(false)}>\r\n            cancel\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <FilterTemplateManagementEdit tableKey={TABLE_KEY} groupedValue={siteId} />\r\n        </div>\r\n      </Drawer>\r\n\r\n      {/* Assign option Drawer */}\r\n\r\n\r\n      <AssignOption\r\n        formRef={assignOptionForm}\r\n        siteId={siteId}\r\n        selectedFiles={selectedFileList}\r\n        onClosePopup={() => handleShowAssignModalCancel(true)}\r\n        showAssignModal={showAssignModal}\r\n        onSuccess={() => syncGrid(true)}\r\n        form={assignOptionForm}\r\n      />\r\n\r\n\r\n\r\n      {/*  Tag Management Drawer */}\r\n      <TagManagementDrawer\r\n        visible={showTagsManageModal}\r\n        onClose={handleShowTagManageModalCancel}\r\n        binderId={binderId}\r\n        syncGrid={() => syncGrid(true)}\r\n      />\r\n\r\n      {/*  Status Change Drawer */}\r\n      <Drawer\r\n        width={700}\r\n        visible={showStatusChangeModal}\r\n        title={'Update Status'}\r\n        onClose={handleShowStatusChangeModalCancel}\r\n        className={\"yjDrawerPanel\"}\r\n        footer={[\r\n          <Button key=\"back\" type=\"default\" onClick={handleShowStatusChangeModalCancel}>\r\n            cancel\r\n          </Button>,\r\n          <Button key=\"update\" type=\"primary\" onClick={handleNewStatusUpdate} disabled={selectedNewStatusState === 0}>\r\n            update\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <ChangeStatus\r\n            onFilesChange={(fileList, onMounted, onAllRemoved) => {\r\n              if (onAllRemoved) {\r\n                handleCloseStatusModal();\r\n                return;\r\n              }\r\n              if (!onMounted && !onAllRemoved) {\r\n                setStatusFileRemoved(true);\r\n              }\r\n              setFilesStatusList(fileList);\r\n            }}\r\n            onFinish={handleNewStatusUpdate}\r\n            form={changeStatusForm}\r\n            selectedFiles={selectedFileList}\r\n            onNewStatusSelect={setSelectedNewStatusState}\r\n          />\r\n        </div>\r\n      </Drawer>\r\n\r\n      {/*  Download Option Menu Drawer */}\r\n      <Modal\r\n        visible={showDownloadModal}\r\n        title={'Download Files'}\r\n        maskClosable={false}\r\n        destroyOnClose={true}\r\n        className=\"yjCommonModalSmall\"\r\n        onCancel={handleOnDownloadModalCancel}\r\n        footer={[\r\n          <Button onClick={handleOnDownloadModalCancel} key=\"submit\" type=\"primary\">\r\n            Done\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <DownloadModal\r\n            hasDownloaded={(hasDownloaded: boolean) => {\r\n              if (hasDownloaded) {\r\n                setShowDownloadModal(false);\r\n              }\r\n            }}\r\n            selectedFiles={downloadType === downloadTypes.checkoutIndividual || downloadType === downloadTypes.checkoutZip ? checkedOuFilesDownload : selectedFileList}\r\n            downloadType={downloadType}\r\n          />\r\n        </div>\r\n      </Modal>\r\n\r\n      {/*  Checkout Option Menu Drawer */}\r\n      <Drawer\r\n        destroyOnClose={true}\r\n        key={'checkoutModal'}\r\n        visible={showCheckoutdModal}\r\n        title={'Check-out Files'}\r\n        onClose={handleOnCheckoutModalCancel}\r\n        width={700}\r\n        className={\"yjDrawerPanel\"}\r\n        footer={[\r\n          <>\r\n            {selectedFileList.length > 1 && checkingOutFilesCount > 1 && (\r\n              <Checkbox\r\n                key={1}\r\n                onChange={(e: CheckboxChangeEvent) => {\r\n                  setCheckoutZip(e.target.checked);\r\n                }}\r\n              >\r\n                As ZIP Files\r\n              </Checkbox>\r\n            )}\r\n          </>,\r\n\r\n          <Button key={'cancelCheckout'} onClick={handleOnCheckoutModalCancel} type=\"default\">\r\n            Cancel\r\n          </Button>,\r\n          <Button disabled={!validatedCheckoutForm} key={'openCheckout'} onClick={onCheckoutFiles} type=\"primary\">\r\n            check-out\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <CheckoutOption\r\n            validatedCheckoutEmail={(validated) => {\r\n              setValidatedCheckoutForm(validated);\r\n            }}\r\n            onCloseModal={(closeModal: boolean) => {\r\n              if (closeModal) {\r\n                setshowCheckoutdModal(false);\r\n                dispatch(updateContextMenuCheckoutOption(false));\r\n              }\r\n            }}\r\n            onFileRemoved={(fileCount: number) => {\r\n              setCheckingOutFilesCount(fileCount);\r\n            }}\r\n            form={form}\r\n            emailForm={emailForm}\r\n            onFinish={onCheckoutFiles}\r\n            fileList={selectedFileList ? selectedFileList : []}\r\n          />\r\n        </div>\r\n      </Drawer>\r\n\r\n      {/*  publish files Drawer */}\r\n      <PublishFilesDrawer\r\n        isLoading={isLoading}\r\n        visible={showPublishModal}\r\n        publishFileList={publishFileList}\r\n        onClose={cancelPublishModal}\r\n        onFileListChange={setPublishFileList}\r\n        onPublish={handlePublishFiles}\r\n        expirationDate={publishFileExpiration}\r\n        setExpirationDate={setPublishFileExpiration}\r\n      />\r\n\r\n      {/*  unpublish files Drawer */}\r\n      <UnpublishFilesDrawer\r\n        isLoading={isLoading}\r\n        visible={showUnpublishModal}\r\n        unpublishFileList={unpublishFileList}\r\n        onClose={cancelUnpublishModal}\r\n        onFileListChange={setUnpublishFileList}\r\n        onUnpublish={handleUnpublishFiles}\r\n      />\r\n\r\n      {/* File Link Drawer */}\r\n      {showLinkFileModal && (\r\n        <LinkFilesDrawer linkFilesLoading={isLoading} onClosePopup={handleShowLinkFilesModalCancel}\r\n          onItemSelect={handleLinkFilesUpdateDetails} onSuccess={handleLinkFileUpdate}\r\n          selectedFiles={selectedFileList} showDrawer={showLinkFileModal} />\r\n      )}\r\n\r\n      {/* File Unlink Drawer */}\r\n      {showUnlinkFilesModal && (\r\n        <UnlinkFilesDrawer unlinkFilesLoading={isLoading} onUnlinkChange={setSelectedUnlinkFiles} onClosePopup={handleShowUnlinkFilesModalCancel}\r\n          onSuccess={handleFileUnlinkSubmit} selectedFiles={selectedFileList} showDrawer={showUnlinkFilesModal} />\r\n      )}\r\n\r\n      <div className={styles.yjFileAreaMainActionPanel}>\r\n        <div className={styles.yjActionListContainer}>\r\n          <div onClick={handleOnToggleClicked} className={styles.yjFileAreaCollapsibleTriggerWrapper}>\r\n            {renderToggleIcon()}\r\n          </div>\r\n          <div hidden={!userPermission.privDMSCanViewFileArea || !hasPermission(folderTree, 'FILE_AREA_DOWNLOAD')} className={`${styles.yjActionListWrapper} ${!showDownload ? styles.disabled : \"\"}`} aria-disabled={!showDownload} tabIndex={showDownload ? 0 : -1}>\r\n            <Dropdown getPopupContainer={() => document.getElementById('downloadOptionsMenu') as HTMLElement} overlay={downloadOptionsMenu} trigger={['click']}>\r\n              <div>\r\n                <Button>\r\n                  <div id=\"downloadOptionsMenu\">\r\n                    <>\r\n                      <DownloadOutlined /> <span>Download</span>\r\n                    </>\r\n                  </div>\r\n                </Button>\r\n              </div>\r\n            </Dropdown>\r\n          </div>\r\n          {<>\r\n            {/* <Tooltip placement=\"topLeft\" title={'This feature is coming soon'} color=\"#78bf59\">\r\n            <div hidden={!userPermission.privDMSCanViewFileArea} className={styles.yjActionListWrapper}>\r\n              <MailOutlined /> <span>Email</span>\r\n            </div>\r\n          </Tooltip> */}\r\n            <div className={`${styles.yjActionListWrapper} ${!showAssign ? styles.disabled : \"\"}`} onClick={() => showAssign ? setShowAssignModal(true) : undefined} aria-disabled={!showAssign} tabIndex={showAssign ? 0 : -1} hidden={!userPermission.privDMSCanManageFileAssign || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} >\r\n              <AuditOutlined /> <span>Assign</span>\r\n            </div>\r\n            <div className={`${styles.yjActionListWrapper} ${!showStatus ? styles.disabled : \"\"}`} onClick={() => showStatus ? setShowStatusChangeModal(true) : undefined} aria-disabled={!showStatus} tabIndex={showStatus ? 0 : -1} hidden={!userPermission.privDMSCanManageFileStatus || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} >\r\n              <FileDoneOutlined /> <span>Status</span>\r\n            </div>\r\n\r\n            <div className={`${styles.yjActionListWrapper} ${!showMoveFiles ? styles.disabled : \"\"}`} onClick={handleMoveFilesActionBar} aria-disabled={!showMoveFiles} tabIndex={showMoveFiles ? 0 : -1}\r\n              hidden={!userPermission.privDMSCanMoveFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} >\r\n              <DragOutlined /> <span>Move</span>\r\n            </div>\r\n\r\n            <div className={`${styles.yjActionListWrapper} ${!showReCategorize ? styles.disabled : \"\"}`} hidden={!userPermission.privDMSCanRecategorizeFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}\r\n              onClick={handleReCategorizeActionBr} aria-disabled={!showReCategorize} tabIndex={showReCategorize ? 0 : -1}\r\n            >\r\n              <ApartmentOutlined /> <span>Re-Categorize</span>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanRenameFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showReName ? styles.disabled : \"\"}`}\r\n              onClick={() => showReName ? setShowReNameFilesModal(true) : undefined} aria-disabled={!showReName} tabIndex={showReName ? 0 : -1}>\r\n              <EditOutlined /> <span>ReName</span>\r\n\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanCopyFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showCopy ? styles.disabled : \"\"}`}\r\n              onClick={handleCopyFilesActionBar} aria-disabled={!showCopy} tabIndex={showCopy ? 0 : -1}>\r\n              <CopyOutlined /> <span>Copy</span>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanCheckInCheckOutInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showCheckout ? styles.disabled : \"\"}`} aria-disabled={!showCheckout} tabIndex={showCheckout ? 0 : -1}>\r\n              <Button onClick={() => showCheckout ? onCheckoutAction() : undefined}>\r\n                <ContainerOutlined /> <span>Check-out</span>\r\n              </Button>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanPublishUnpublishInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showPublish ? styles.disabled : \"\"}`} aria-disabled={!showPublish} tabIndex={showPublish ? 0 : -1}>\r\n              <Button onClick={handlePublishActionBar}>\r\n                <MdOpenInNew />\r\n                <span style={{ marginTop: '-3px' }}>Publish</span>\r\n              </Button>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanPublishUnpublishInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showUnpublish ? styles.disabled : \"\"}`} aria-disabled={!showUnpublish} tabIndex={showUnpublish ? 0 : -1}>\r\n              <Button onClick={handleUnpublishActionBar}>\r\n                <MdOpenInNew style={{ transform: 'rotate(180deg)' }} />\r\n                <span style={{ marginTop: '-3px' }}>Unpublish</span>\r\n              </Button>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanDeleteFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}\r\n              className={`${styles.yjActionListWrapper} ${!showDelete ? styles.disabled : \"\"}`}\r\n              onClick={handleDeleteActionBar} aria-disabled={!showDelete} tabIndex={showDelete ? 0 : -1}>\r\n              <DeleteOutlined /> <span>Delete</span>\r\n            </div>\r\n            <div\r\n              hidden={!hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') || !userPermission.privDMSCanViewFileArea}\r\n              onClick={() => showPropeties && (displayDocumentPropeties(true), dispatch(updateContextMenuPropetiesoption(true)))} aria-disabled={!showPropeties} tabIndex={showPropeties ? 0 : -1}\r\n              className={`${styles.yjActionListWrapper} ${(!showPropeties || !userPermission.privDMSCanViewFileArea) ? styles.disabled : \"\"}`}\r\n            >\r\n              <SettingOutlined className={`yJFileAreaRow`} />\r\n              <span className={` yJFileAreaRow`}>Properties</span>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanMarkFilesAsToBeDeleted || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}\r\n              className={`${styles.yjActionListWrapper} ${(!userPermission.privDMSCanMarkFilesAsToBeDeleted || !showToBeDelete) ? styles.disabled : \"\"}`}\r\n              onClick={handleToBeDeletedActionBar} aria-disabled={!showToBeDelete} tabIndex={showToBeDelete ? 0 : -1}>\r\n              <DeleteOutlined /> <span>To be Deleted</span>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanViewFileSourceLink}\r\n              className={`${styles.yjActionListWrapper} ${(!userPermission.privDMSCanViewFileSourceLink || !showCopyLink) ? styles.disabled : \"\"}`} onClick={handleCopyLinkActionBar} aria-disabled={!showCopyLink} tabIndex={showCopyLink ? 0 : -1}>\r\n              <ShareAltOutlined /> <span>Copy Link</span>\r\n            </div>\r\n            <div className={`${styles.yjActionListWrapper} ${!showLinkFiles ? styles.disabled : \"\"}`} onClick={() => showLinkFiles ? handleShowLinkFilesModal(true) : undefined} aria-disabled={!showLinkFiles} tabIndex={showLinkFiles ? 0 : -1}\r\n              hidden={!hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') || !userPermission.privDMSCanLinkUnlinkFiles} >\r\n              <LinkOutlined /> <span>Link Files</span>\r\n            </div>\r\n            <div hidden={!userPermission.privDMSCanLinkUnlinkFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}\r\n              className={`${styles.yjActionListWrapper} ${!showUnlinkFiles ? styles.disabled : \"\"}`} onClick={() => showUnlinkFiles ? setShowUnlinkFilesModal(true) : undefined} aria-disabled={!showUnlinkFiles} tabIndex={showUnlinkFiles ? 0 : -1}>\r\n              <DisconnectOutlined /> <span>Unlink Files</span>\r\n            </div>\r\n          </>}\r\n        </div>\r\n        <div className={styles.yjActionButtonsContainer}>\r\n          <div className={styles.yjActionButtonsLeftCorner}>\r\n            <div className={styles.yjFileAreaFilterDropdownWrapper}>\r\n              <GridRefreshIcon hasUpdates={hasUpdates} onRefreshGrid={onRefreshGrid} />\r\n              {showFiltersButton ? (\r\n                <Button icon={<FilterOutlined />} className={styles.yjFiltersButton} onClick={() => setShowFiltersButton(false)}>\r\n                  Filters <DownOutlined />\r\n                </Button>\r\n              ) : (\r\n                <Select\r\n                  showSearch\r\n                  style={{ width: 200 }}\r\n                  placeholder=\"Select a filter\"\r\n                  optionFilterProp=\"children\"\r\n                  autoFocus={true}\r\n                  defaultOpen={true}\r\n                  onBlur={() => setShowFiltersButton(true)}\r\n                  showArrow={false}\r\n                  onSelect={onClickSavedFilter}\r\n                  notFoundContent={`No Results Found`}\r\n                  filterOption={onSearchSavedFilters}\r\n                >\r\n                  {sortedSavedFilterList.length > 0 && (\r\n                    <Option disabled={true} key={-1} value={'Manage Filters'}>\r\n                      <Button className={styles.yjDropdownManageFilters} type=\"primary\" onClick={() => setShowEditFiltersModal(true)}>\r\n                        Manage Filters\r\n                      </Button>\r\n                    </Option>\r\n                  )}\r\n\r\n                  {sortedSavedFilterList &&\r\n                    sortedSavedFilterList.map((savedFilter: SavedFilterTemplate) => {\r\n                      return (\r\n                        <Option key={savedFilter.id} value={savedFilter.id}>\r\n                          {savedFilter.name}\r\n                        </Option>\r\n                      );\r\n                    })}\r\n                </Select>\r\n              )}\r\n            </div>\r\n            <div className={styles.yjFileAreaFilterActionButtonWrapper}>\r\n              {showFilterSaveButton && (\r\n                <Button disabled={filter_template_saved} type=\"primary\" className={styles.yjSaveFilterButton} onClick={() => setShowAddNewFilterModal(true)}>\r\n                  Save as a new filter\r\n                </Button>\r\n              )}\r\n            </div>\r\n          </div>\r\n          <div className={styles.yjActionButtonsRightCorner}>\r\n          {additionalActionPanel}\r\n            <Button \r\n              hidden={!userPermission.privDMSCanManageFileTags} \r\n              disabled={!showManageTags} \r\n              aria-disabled={!showManageTags}\r\n              tabIndex={showManageTags ? 0 : -1}\r\n              onClick={handleManageTags}\r\n              className={styles.yjManageTagsButton}>\r\n              Manage Tags\r\n            </Button>\r\n            <ColumnFilter tableKey={TABLE_KEY} />\r\n            {showFilter && <ClearFilter />}\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n"]}, "metadata": {}, "sourceType": "module"}