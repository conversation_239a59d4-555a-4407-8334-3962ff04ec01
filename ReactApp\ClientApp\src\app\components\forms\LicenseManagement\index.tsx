import React, { useEffect, useRef } from "react";
import { DatePicker, Row, Col, Form, Input, Select, Modal } from "antd";
import { FormInstance } from "antd/lib/form";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import { Store } from "antd/lib/form/interface";
import moment from "moment";
import { useTranslation } from "react-i18next";

import styles from "./index.module.less";
import { required, max, min, typeWithPattern } from "../validators";
import { LicenseDetailsOptions } from "@app/types/LicenseDetailsOptions";
import { ValueType } from "@app/types/ValueType";
import { errorNotification } from "@app/utils/antNotifications";
import { TERMINATED, ACTIVE, PENDING } from "@app/constants/licenseStatuses";
import {
  disabledDatesBeforeToday,
  genrateDateOnly,
  disabledYear,
} from "../utils/disableDates";
import { AvoidWhitespace } from "@app/utils/regex";
import {
  prevenPrecedingSpaces,
  prevenPrecedingSpacesOnPaste,
} from "@app/utils/forms";

const { Option } = Select;

const MAX_LENGTH_TWO_HUNDRED = 200;
const MIN_LENGTH_THREE = 3;

type PropTypes = {
  details: any;
  options?: LicenseDetailsOptions;
  disabled?: boolean;
  form?: FormInstance;
  setVertical: (v: number) => void;
  onFinish?: (values: Store) => void;
  isEdit?: boolean;
  onChangeLicenceForm?: (event: any) => void;
};

const layout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

export default ({
  details,
  options,
  disabled = false,
  setVertical,
  form,
  onFinish,
  isEdit,
  onChangeLicenceForm,
}: PropTypes) => {
  const { t } = useTranslation();
  if (!form) {
    [form] = Form.useForm();
  }

  const expirationRef = useRef<any>();

  useEffect(() => {
    form?.resetFields();
  }, [form]);

  const handleVerticleChange = (vertical: number) => {
    setVertical(vertical);
    if (onChangeLicenceForm) {
      onChangeLicenceForm(vertical);
    }
  };

  const handleStatusChange = (id: number) => {
    const today = moment().set({
      hour: 0,
      minute: 0,
      second: 0,
      millisecond: 0,
    });
    if (id === ACTIVE && today > form?.getFieldValue("expirationDate")) {
      errorNotification(
        [""],
        "Unable to proceed. Amend expiration date forward"
      );
      form?.setFieldsValue({ status: details.status });
      expirationRef.current?.focus();
    }

    if (id === TERMINATED) {
      Modal.confirm({
        title: (
          <>
            <span>Are you sure you want to terminate?</span> <br />{" "}
            <span>A terminated license cannot be revoked</span>
          </>
        ),
        icon: <ExclamationCircleOutlined />,
        okText: "Yes",
        cancelText: "No",
        onCancel: () => form?.setFieldsValue({ status: details.status }),
      });
    }
    onChangeLicenceForm?.(id);
  };

  const filterOption = (input: any, option: any) => {
    return option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

  const getPopupContainer = (trigger: any) => {
    return trigger.parentNode as HTMLElement;
  };

  const renderDropdownOptions = (option: ValueType) => {
    return (
      <Option key={option.value} value={option.value}>
        {option.name}
      </Option>
    );
  };

  const disabledDateForExpired = (current: any) => {
    if (
      moment(new Date()).isSame(form?.getFieldValue("effectiveDate"), "day") ||
      !form?.getFieldValue("effectiveDate")
    ) {
      return disabledDatesBeforeToday(current);
    } else {
      return (
        genrateDateOnly(current?._d) <
          genrateDateOnly(form?.getFieldValue("effectiveDate")?._d) ||
        disabledYear(current)
      );
    }
  };
  
  return (
    <div className={styles.yjLicenseManagementDetails}>
      <Form
        onChange={onChangeLicenceForm}
        size="middle"
        {...layout}
        name="basic"
        layout="horizontal"
        form={form}
        onFinish={onFinish}
        initialValues={{ ...details, vertical: 1 }}
        hideRequiredMark={disabled}
      >
        <Row
          className={styles.yjLicenseManagementDetailsInputWrapper}
          gutter={16}
        >
          <Col span={8}>
            <Form.Item
              label={t("Company Name")}
              name="companyName"
              rules={[
                required,
                max(MAX_LENGTH_TWO_HUNDRED),
                min(MIN_LENGTH_THREE),
                typeWithPattern("string", AvoidWhitespace),
              ]}
            >
              <Input
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                autoComplete="off"
                disabled={disabled}
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item label="Vertical" name="vertical" rules={[required]}>
              <Select
                style={{ width: "100%" }}
                disabled={disabled}
                onChange={(v: number) => handleVerticleChange(v)}
                getPopupContainer={getPopupContainer}
              >
                {options && options.verticals
                  ? (options.verticals as ValueType[]).map((option) =>
                      renderDropdownOptions(option)
                    )
                  : null}
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label={t("Status")}
              name="status"
              style={{ width: "100%" }}
            >
              <Select
                onChange={handleStatusChange}
                style={{ width: "100%" }}
                disabled={disabled || !isEdit || !options?.statuses.isEnabled}
                getPopupContainer={getPopupContainer}
              >
                {options && options.statuses.statuses
                  ? (options.statuses.statuses as ValueType[]).map((option) =>
                      renderDropdownOptions(option)
                    )
                  : null}
              </Select>
            </Form.Item>
          </Col>

          <Col span={8}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={t("Start Date")}
                  name="effectiveDate"
                  style={{ width: "100%" }}
                  rules={[required]}
                >
                  <DatePicker
                    disabledDate={(current: any) => {
                      return (
                        disabledDatesBeforeToday(current) ||
                        disabledYear(current)
                      );  
                    }}
                    format={moment.localeData().longDateFormat('L')}
                    onChange={() =>
                      form?.setFieldsValue({ expirationDate: null })
                    }
                    style={{ width: "100%" }}
                    disabled={
                      disabled || (isEdit && details.status !== PENDING)
                    }
                    getPopupContainer={getPopupContainer}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={t("Expiration Date")}
                  name="expirationDate"
                  style={{ width: "100%" }}
                  rules={[required]}
                >
                  <DatePicker
                    ref={expirationRef}
                    disabledDate={(current: any) =>
                      disabledDateForExpired(current)
                    }
                    style={{ width: "100%" }}
                    disabled={disabled}
                    getPopupContainer={getPopupContainer}
                    format={moment.localeData().longDateFormat('L')}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Col>

          <Col span={8}>
            <Form.Item
              label={t("Compliances")}
              name="compliances"
              style={{ width: "100%" }}
            >
              <Select
                className={"yjMultiSelectOptionSelect"}
                onChange={onChangeLicenceForm}
                showArrow
                mode="multiple"
                style={{ width: "100%" }}
                disabled={disabled}
                filterOption={filterOption}
                getPopupContainer={getPopupContainer}
              >
                {options && options.compliances
                  ? (options.compliances as ValueType[]).map((option) =>
                      renderDropdownOptions(option)
                    )
                  : null}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={t("License ID")}
              name="licenseId"
              style={{ width: "100%" }}
            >
              <Input
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                autoComplete="off"
                disabled
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Row gutter={16}>
              <Col span={12}>
                <Form.Item
                  label={t("Allocated Space")}
                  name="allocatedSpace"
                  style={{ width: "100%" }}
                  rules={[required]}
                >
                  <Select
                    onChange={onChangeLicenceForm}
                    style={{ width: "100%" }}
                    disabled={disabled}
                    getPopupContainer={getPopupContainer}
                  >
                    {options && options.storages
                      ? (options.storages as ValueType[]).map((option) =>
                          renderDropdownOptions(option)
                        )
                      : null}
                  </Select>
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  label={t("Internal User Count")}
                  name="userCount"
                  style={{ width: "100%" }}
                  rules={[required]}
                >
                  <Select
                    onChange={onChangeLicenceForm}
                    style={{ width: "100%" }}
                    disabled={disabled}
                    getPopupContainer={getPopupContainer}
                  >
                    {options && options.userCounts
                      ? (options.userCounts as ValueType[]).map((option) =>
                          renderDropdownOptions(option)
                        )
                      : null}
                  </Select>
                </Form.Item>
              </Col>
            </Row>
          </Col>
          <Col span={8}>
            <Form.Item
              label={t("Support Level")}
              name="supportLevel"
              style={{ width: "100%" }}
            >
              <Select
                onChange={onChangeLicenceForm}
                style={{ width: "100%" }}
                disabled={disabled}
                getPopupContainer={getPopupContainer}
              >
                {options && options.supportLevels
                  ? (options.supportLevels as ValueType[]).map((option) =>
                      renderDropdownOptions(option)
                    )
                  : null}
              </Select>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={t("Integrations")}
              name="integrations"
              style={{ width: "100%" }}
            >
              <Select
                className={"yjMultiSelectOptionSelect"}
                onChange={onChangeLicenceForm}
                showArrow
                mode={"multiple"}
                style={{ width: "100%" }}
                disabled={disabled}
                filterOption={filterOption}
                getPopupContainer={getPopupContainer}
              >
                {options && options.integrations
                  ? (options.integrations as ValueType[]).map((option) =>
                      renderDropdownOptions(option)
                    )
                  : null}
              </Select>
            </Form.Item>
          </Col>
        </Row>
      </Form>
    </div>
  );
};
