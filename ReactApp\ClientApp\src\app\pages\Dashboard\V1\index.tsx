import React, { useEffect, useState, useCallback } from "react";
import { Col, Collapse, Row, Skeleton, Tooltip } from "antd";

import StatsContainer from "@app/components/Dashboard/StatsContent/";
import MyRecentDocuments from "@app/components/Dashboard/MyRecentDocuments";
import FullfiledFilesTableContainer from "@app/components/Dashboard/FullfiledFilesTableContainer";
import { getDashboardPermissonDetails } from "@app/api/dashboardService";
import NonAuthorized from "@app/components/NonAuthorized";
import styles from "./index.module.less";
import STR from "@app/constants/strings"

const { Panel } = Collapse;

const GUTTER_VALUE_TWELEVE = 12;
const GUTTER_VALUE_TWENTY_FOUR = 24;
const PENDING_FILES = "PendingFiles";
const FULLFILED_FILES = "FulFilledFiles";

export default (props: any) => {
  const [dashboardPermitedd, setDashboardPermitted] = useState<boolean>(false);
  const [allowedPendindFiles, setAllowedPendingFiles] = useState<boolean>(
    false
  );
  const [allowedFullfiledFiles, setAllowedFullfiledFiles] = useState<boolean>(
    false
  );

  useEffect(() => {
    setDashboardPermitted(false);
    getDashboardPermissonDetails(1, props.channelId).then((response) => {
      const permisson = response.data;
      const showPendingFiles = permisson.find(
        (permit: any) => permit.name === PENDING_FILES
      );
      const showFullfiledFiles = permisson.find(
        (permit: any) => permit.name === FULLFILED_FILES
      );
      setAllowedPendingFiles(showPendingFiles ? true : false);
      setAllowedFullfiledFiles(showFullfiledFiles ? true : false);
      setDashboardPermitted(true);
    });
  }, [props.channelId]);

  function renderPendingFileStatus(allowedPendindFiles: boolean) {
    if (allowedPendindFiles) {
      return (<MyRecentDocuments {...props} />);
    } else {
      return (
        <NonAuthorized
          styleClassName={styles.yjNonAuthorizedDashboardWrapper}
          title={STR.NOT_AUTHORIZED_WIDGET}
          subTitle={STR.NOT_AUTHORIZED_CONTACT_ADMIN}
        />
      )
    }
  }

  function renderFulfilledFileStatus(allowedFullfiledFiles: boolean) {
    if (allowedFullfiledFiles) {
      return (<FullfiledFilesTableContainer {...props} />);
    } else {
      return (
        <NonAuthorized
          styleClassName={styles.yjNonAuthorizedDashboardWrapper}
          title={STR.NOT_AUTHORIZED_WIDGET}
          subTitle={STR.NOT_AUTHORIZED_CONTACT_ADMIN}
        />
      )
    }
  }

  const renderRecentDocument = useCallback(
    (className: string) => {
      return (
        <Col span={24} className={className}>
          <Collapse
            defaultActiveKey={["recentDocumets"]}
            expandIconPosition={"right"}
          >
            <Panel header="Requests with pending files" key="recentDocumets">
              {dashboardPermitedd ? renderPendingFileStatus(allowedPendindFiles) : (<Skeleton active />)}
            </Panel>
          </Collapse>
        </Col>
      );
    },
    [dashboardPermitedd, allowedPendindFiles]
  );

  const renderPieChartDocuments = () => {
    return renderRecentDocument("yjCommonAccordian yjPieChartWrapper");
  };

  const getMyRecentDocumentContent = () => {
    return (
      <Col span={12}>
        <Row gutter={12}>{renderPieChartDocuments()}</Row>
      </Col>
    );
  };

  const renderFullfilledfiles = useCallback(
    (className: string) => {
      return (
        <Col span={24} className={className}>
          <Collapse
            defaultActiveKey={["fulfilledFiles"]}
            expandIconPosition={"right"}
          >
            <Panel header="FULFILLED FILES STATUS" key="fulfilledFiles">
              {dashboardPermitedd ? renderFulfilledFileStatus(allowedFullfiledFiles) : (<Skeleton active />)}
            </Panel>
          </Collapse>
        </Col>
      );
    },
    [dashboardPermitedd, allowedFullfiledFiles]
  );

  const renderFullfiledGridContent = () => {
    return renderFullfilledfiles("yjCommonAccordian yjPieChartWrapper");
  };

  const getFullfiledGridContent = () => {
    return (
      <Col span={12}>
        <Row gutter={12}>{renderFullfiledGridContent()}</Row>
      </Col>
    );
  };

  return (
    <div className="yjDashboardWrapper">
      <Row gutter={[GUTTER_VALUE_TWENTY_FOUR, GUTTER_VALUE_TWENTY_FOUR]}>
        <Col span={24}>
          <StatsContainer />
        </Col>
      </Row>

      <Row gutter={[GUTTER_VALUE_TWELEVE, GUTTER_VALUE_TWELEVE]}>
        {getFullfiledGridContent()}
        {getMyRecentDocumentContent()}
      </Row>
    </div>
  );
};
