import config from '@app/utils/config';
import http, { httpVerbs } from '@app/utils/http';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import { getParameterizedUrlWith } from "@app/utils";

export const getTemplates = (data:any) => {
  return http({
    method: httpVerbs.GET,
    url: config.api[OperationalServiceTypes.FileManagementService].binderTemplates,
    params: {
      ...data
    },
  });
};

export const checkTemplateNameExists = (name: string) => {
  return http({
    method: httpVerbs.HEAD,
    url: config.api[OperationalServiceTypes.FileManagementService].binderTemplates,
    params: {
      name,
    },
  });
};

export const createTemplate = (data: any) => {
  return http({
    method: httpVerbs.POST,
    url: config.api[OperationalServiceTypes.FileManagementService].binderTemplates,
    data,
  });
};

export const updateTemplate = (data: any) => {
  return http({
    method: httpVerbs.PUT,
    url: getParameterizedUrlWith( config.api[OperationalServiceTypes.FileManagementService].updateBinderTemplates, [ { name: "templateId", value: data.id } ] ),
    data,
  });
};