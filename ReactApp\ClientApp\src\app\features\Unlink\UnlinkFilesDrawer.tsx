import React, { useEffect, useState } from "react";
import { Row, Col, Checkbox, Button, Tooltip, Modal as AntModal, Drawer } from "antd";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import { getLinksWithBinders, FileLinksResponse } from "@app/api/fileAreaService";
import { IFile } from "@app/types/fileAreaTypes";
import logger from "@app/utils/logger";
import SubmitButton from "@app/components/SubmitButton";

export interface FileLink extends FileLinksResponse {
  checked: boolean;
  selectedBinders: string[];
}

interface UnlinkFilesProps {
  selectedFiles: IFile[];
  onUnlinkChange?: (files: FileLink[]) => void;
  onClosePopup: () => void;
  showDrawer: boolean;
  unlinkFilesLoading: boolean;
  onSuccess: () => void;
}

const { confirm } = AntModal;

const UnlinkFilesDrawer: React.FC<UnlinkFilesProps> = ({
  selectedFiles,
  onUnlinkChange,
  onClosePopup,
  onSuccess,
  showDrawer,
  unlinkFilesLoading,
}) => {
  const [fileLinks, setFileLinks] = useState<FileLink[]>([]);

  useEffect(() => {
    const fetchFileLinks = async () => {
      try {
        const fileIds = selectedFiles.map(file => file.id);
        const response = await getLinksWithBinders(fileIds);

        const initializedLinks = response.data.map(link => ({
          ...link,
          checked: true,
          selectedBinders: link.binders.map(binder => binder.id),
        }));
        setFileLinks(initializedLinks);
        onUnlinkChange?.(initializedLinks);
      } catch (error) {
        logger.error("Unlink File Component", "fetchFileLinks", error);
      }
    };

    if (selectedFiles.length > 0 && showDrawer) {
      fetchFileLinks();
    }
  }, [selectedFiles, showDrawer]);

  const handleSelectAllChange = (checked: boolean): void => {
    const updatedLinks = fileLinks.map(file => ({
      ...file,
      checked,
      selectedBinders: checked ? file.selectedBinders : [],
    }));
    setFileLinks(updatedLinks);
    onUnlinkChange?.(updatedLinks);
  };

  const handleFileCheckboxChange = (fileId: string, checked: boolean): void => {
    const updatedLinks = fileLinks.map(file => {
      if (file.fileId === fileId) {
        return {
          ...file,
          checked,
          selectedBinders: checked ? file.selectedBinders : [],
        };
      }
      return file;
    });
    setFileLinks(updatedLinks);
    onUnlinkChange?.(updatedLinks);
  };

  const handleCloseDrawer = () => {
    setFileLinks([]);
    onClosePopup();
  };

  const handleBinderCheckboxChange = (fileId: string, binderId: string, checked: boolean): void => {
    const updatedLinks = fileLinks.map(file => {
      if (file.fileId === fileId) {
        const selectedBinders = checked
          ? [...(file.selectedBinders || []), binderId]
          : (file.selectedBinders || []).filter(id => id !== binderId);

        return {
          ...file,
          selectedBinders,
          checked: selectedBinders.length === file.binders.length,
        };
      }
      return file;
    });
    setFileLinks(updatedLinks);
    onUnlinkChange?.(updatedLinks);
  };

  const handleRemoveFile = (fileId: string): void => {
    const updatedLinks = fileLinks.filter(file => file.fileId !== fileId);
    setFileLinks(updatedLinks);
    onUnlinkChange?.(updatedLinks);
    if (updatedLinks.length === 0) handleCloseDrawer();
  };

  const isAllSelected =
    fileLinks.length > 0 &&
    fileLinks.every(file => file.checked && file.selectedBinders.length === file.binders.length);

  return (
    <Drawer
      title={"Unlink File(s)"}
      placement="right"
      visible={showDrawer}
      className={"yjDrawerPanel"}
      width={700}
      closeIcon={<CloseOutlined data-testid="template-drawer-close-icon" />}
      onClose={handleCloseDrawer}
      footer={[
        <>
          <Button
            key="cancel"
            type="default"
            onClick={handleCloseDrawer}
            disabled={unlinkFilesLoading}
            data-testid="template-drawer-close"
          >
            Cancel
          </Button>

          <SubmitButton
            key="unlinkFiles"
            type="primary"
            onClick={onSuccess}
            disabled={!fileLinks.length || unlinkFilesLoading}
          >
            Unlink
          </SubmitButton>
        </>,
      ]}
    >
        <Row gutter={24}>
          <Col span={24}>
                <div className={styles.yjUnlinkFilesFormHeader}>
                  <Row gutter={24}>
                    <Col span={1} style={{ marginTop: "8px" }}>
                      <Checkbox
                        checked={isAllSelected}
                        onChange={e => handleSelectAllChange(e.target.checked)}
                        indeterminate={
                          fileLinks.some(file => file.checked || file.selectedBinders.length > 0) &&
                          !isAllSelected
                        }
                      ></Checkbox>
                    </Col>
                    <Col span={23}>
                      <h3>File(s) to Unlink</h3>
                    </Col>
                  </Row>
                </div>

                <div className={styles.yjUnlinkFilesFormGrid}>
                  {fileLinks.map(file => (
                    <div className={styles.yjUnlinkFilesLinks} key={file.fileId}>
                      <Row gutter={20} className={styles.yjUnlinkFileRow}>
                        <Col>
                          <Checkbox
                            checked={file.checked}
                            onChange={e => handleFileCheckboxChange(file.fileId, e.target.checked)}
                            indeterminate={
                              !file.checked &&
                              file.selectedBinders.length > 0 &&
                              file.selectedBinders.length < file.binders.length
                            }
                          ></Checkbox>
                          <Tooltip title="Remove File">
                            <Button
                              type="primary"
                              icon={<CloseOutlined />}
                              className={styles.yjDeteleFile}
                              onClick={() => {
                                confirm({
                                  title: `File(s) will be removed. Do you wish to continue?`,
                                  icon: <ExclamationCircleOutlined />,
                                  okText: "Yes",
                                  cancelText: "No",
                                  onOk() {
                                    handleRemoveFile(file.fileId);
                                  },
                                });
                              }}
                            />
                          </Tooltip>
                        </Col>
                        <Col span={20}>
                          <div className={styles.yjUnlinkFileName}>{file.fileName}</div>
                        </Col>
                      </Row>

                      <Row gutter={24}>
                        <Col span={24}>
                          <div className={styles.yjUnlinkSitesList}>
                            {file.binders.map(binder => (
                              <Row key={binder.id} className={styles.yjUnlinkSiteRow}>
                                <Col span={24}>
                                  <Checkbox
                                    checked={file.selectedBinders.includes(binder.id)}
                                    onChange={e =>
                                      handleBinderCheckboxChange(
                                        file.fileId,
                                        binder.id,
                                        e.target.checked
                                      )
                                    }
                                  >
                                    {binder.displayText}
                                  </Checkbox>
                                </Col>
                              </Row>
                            ))}
                          </div>
                        </Col>
                      </Row>
                    </div>
                  ))}
                </div>
          </Col>
        </Row>
    </Drawer>
  );
};

export default UnlinkFilesDrawer;
