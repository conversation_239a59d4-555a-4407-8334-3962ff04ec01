import React, { Fragment } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>ton, Skeleton, Toolt<PERSON> } from "antd";
import { FileWordOutlined } from "@ant-design/icons/lib";

import styles from "./index.module.less";
import { IFile } from "@app/types/fileAreaTypes";
import Checkout from "./Checkout";
import Assignment from "./Assignment";
import Detail from "./Detail";
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";

export const DATA_RETRIEVE_ERROR_MESSAGE = "Failed to load";
const { TabPane } = Tabs;

const dataVersions = [
  {
    title: "Versioned by",
    Value: "User 1",
  },
  {
    title: "Version",
    Value: "Version 1",
  },
  {
    title: "User",
    Value: "John",
  },
];

export interface IDocumentPropeties {
  key?: string;
  displayDrawer: boolean;
  onCloseDrawer: any;
  file: IFile;
}

export default (props: IDocumentPropeties) => {
  const { userPermission } = useSelector(
    (state: RootState) => state.userManagement
  );

  const renderVersionDetails = (item: any) => {
    return (
      <List.Item className={styles.yjPropertiesVersionsListItem}>
        <List.Item.Meta
          title={
            <p className={styles.yjPropertiesVersionsTitle}>{item.title}</p>
          }
          description={
            <span className={styles.yjPropertiesVersionsDescription}>
              {item.Value}
            </span>
          }
        />
      </List.Item>
    );
  };

  return (
    <Fragment>
      <div
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <Drawer
          mask={false}
          title={
            <>
              <FileWordOutlined />
              {props.file?.title}
            </>
          }
          onClose={props.onCloseDrawer}
          placement="right"
          visible={props.displayDrawer}
          destroyOnClose={true}
          className={"yjDrawerPanel"}
        >
          <div className={"yjCommonTabs"}>
            <Tabs type="card">
              {userPermission.privDMSCanViewFileArea && (
                <TabPane tab="Details" key="1">
                  {props.file ? (
                    <Detail fileId={props.file.id} />
                  ) : (
                    <Skeleton active={true} />
                  )}
                </TabPane>
              )}

              {userPermission.privDMSCanViewFileHistory && (
                <TabPane tab="Assignments" key="2">
                  {props.file ? (
                    <Assignment fileId={props.file.id} />
                  ) : (
                    <Skeleton active={true} />
                  )}
                </TabPane>
              )}
              {userPermission.privDMSCanViewFileHistory && (
                <TabPane tab="Checkout" key="3">
                  {props.file ? (
                    <Checkout fileId={props.file.id} />
                  ) : (
                    <Skeleton active={true} />
                  )}
                </TabPane>
              )}
              {userPermission.privDMSCanViewFileHistory && (
                <TabPane
                  tab={
                    <></>
                    // <Tooltip
                    //   placement="topLeft"
                    //   title={"This feature is coming soon"}
                    //   color="#78bf59"
                    // >
                    //   Versions
                    // </Tooltip>
                  }
                  key="4"
                >
                  {/* <Tooltip
                    placement="topLeft"
                    title={"This feature is coming soon"}
                    color="#78bf59"
                  >
                    <div
                      className={styles.yjPropertiesVersionsTab}
                      style={{ opacity: "0.5" }}
                    >
                      <p className={styles.yjPropertiesVersionsNotifications}>
                        01 Version
                      </p>
                      <div className={styles.yjPropertiesVersionsList}>
                        <p className={styles.yjPropertiesVersionsDateTime}>
                          05/05/2020 22:23
                        </p>
                        <List
                          itemLayout="horizontal"
                          dataSource={dataVersions}
                          renderItem={(item) => renderVersionDetails(item)}
                        />
                        <div className={styles.yjPropertiesVersionsButtonPanel}>
                          <Button type="primary">Restore</Button>
                          <Button type="primary">Make a New File</Button>
                        </div>
                      </div>
                    </div>
                  </Tooltip> */}
                </TabPane>
              )}
            </Tabs>
          </div>
        </Drawer>
      </div>
    </Fragment>
  );
};
