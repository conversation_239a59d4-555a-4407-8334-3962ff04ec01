import licenceReducer, { initialState } from "../licenseReducer";
import * as actions from "../../actionTypes/LicenseActionTypes";

describe("Licence reducer test suite", () => {
  it("Should return the initial state", () => {
    expect(licenceReducer(undefined, {})).toBe(initialState);
  });
  it("should handle SET_LICENSE_DETAILS action", () => {
    const action = {
      type: actions.default.SET_LICENSE_DETAILS,
      payload: { id: 1, name: "license1" },
    };
    expect(licenceReducer({}, action)).toEqual({
      liceseDetails: { id: 1, name: "license1" },
    });
  });
  it("should handle DATA_FETCH_SUCESSFUL action", () => {
    const action = {
      type: actions.default.DATA_FETCH_SUCESSFUL,
      payload: true,
    };
    expect(licenceReducer({}, action)).toEqual({
      isDataFetched: true,
    });
    const actionFalse = {
      type: actions.default.DATA_FETCH_SUCESSFUL,
      payload: false,
    };
    expect(licenceReducer({}, actionFalse)).toEqual({
      isDataFetched: false,
    });
  });

  it("should handle OPTIONS_FETCH_SUCCESSFUL action", () => {
    const action = {
      type: actions.default.OPTIONS_FETCH_SUCCESSFUL,
      payload: true,
    };
    expect(licenceReducer({}, action)).toEqual({
      isOptionsFetched: true,
    });
    const actionFalse = {
      type: actions.default.OPTIONS_FETCH_SUCCESSFUL,
      payload: false,
    };
    expect(licenceReducer({}, actionFalse)).toEqual({
      isOptionsFetched: false,
    });
  });

  it("should handle SET_COMPLIANCES action", () => {
    const action = {
      type: actions.default.SET_COMPLIANCES,
      payload: ["aa"],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { compliances: ["aa"] },
    });
  });

  it("should handle SET_STATUSES action", () => {
    const action = {
      type: actions.default.SET_STATUSES,
      payload: ["aa"],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { statuses: ["aa"] },
    });
  });

  it("should handle SET_VERTICALS action", () => {
    const action = {
      type: actions.default.SET_VERTICALS,
      payload: ["aa"],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { verticals: ["aa"] },
    });
  });

  it("should handle SET_STORAGES action", () => {
    const action = {
      type: actions.default.SET_STORAGES,
      payload: ["aa"],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { storages: ["aa"] },
    });
  });

  it("should handle SET_USER_COUNTS action", () => {
    const action = {
      type: actions.default.SET_USER_COUNTS,
      payload: [3, 4],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { userCounts: [3, 4] },
    });
  });

  it("should handle SET_SUPPORT_LEVELS action", () => {
    const action = {
      type: actions.default.SET_SUPPORT_LEVELS,
      payload: [3, 4],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { supportLevels: [3, 4] },
    });
  });

  it("should handle SET_INTEGRATIONS action", () => {
    const action = {
      type: actions.default.SET_INTEGRATIONS,
      payload: [3, 4],
    };
    expect(licenceReducer({}, action)).toEqual({
      options: { integrations: [3, 4] },
    });
  });

  it("should handle HAS_ERRORED action", () => {
    const action = {
      type: actions.default.HAS_ERRORED,
      payload: ["error"],
    };
    expect(licenceReducer({}, action)).toEqual({
      error: ["error"],
    });
  });

  it("should handle HAS_ERRORED action", () => {
    const action = {
      type: actions.default.SAVE_SUCCESSED,
      payload: true,
    };
    expect(licenceReducer({}, action)).toEqual({
      saveSuccessed: true,
    });
  });
});
