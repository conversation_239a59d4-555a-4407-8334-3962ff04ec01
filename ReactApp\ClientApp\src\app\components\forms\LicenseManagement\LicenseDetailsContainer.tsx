import React, { useEffect, useState } from "react";
import LicenseManagementDetails from ".";
import { useDispatch, useSelector } from "react-redux";
import {
  fetchLicenseDetailsById,
  fetchOptions,
  saveLicense,
  saveSuccessed,
  updateLicense,
  hasErrored,
  fetchStatuses,
} from "../../../redux/actions/licenseActions";
import {
  errorNotification,
  successNotification,
} from "../../../utils/antNotifications";
import { Skeleton } from "antd";
import transformDetails from "./utils/transformDetails";
import { RootState } from "../../../redux/reducers/state";
import Modules from "./Modules";
import mapModules from "./utils/mapModules";
import { FormInstance } from "antd/lib/form";
import { Store } from "antd/lib/form/interface";
import flattenModules from "./utils/flattenModules";
import { Module } from "../../../types/Modules";

import {
  getUtcByShortFormat
} from "@app/utils";

type PropTypes = {
  licenseId?: string;
  disabled?: boolean;
  form?: FormInstance;
  onSaveSuccessed?: (id?: string) => void | any;
  isModulesTouched?: (isTouched: boolean) => void;
  isEdit?: boolean;
  onChangeLicenceForm?: () => void | undefined;
};

const dateFormat = "YYYY-MM-DD";

let moduleTree: any;

export default ({
  licenseId,
  disabled = false,
  form,
  onSaveSuccessed,
  isModulesTouched,
  isEdit,
  onChangeLicenceForm,
}: PropTypes) => {
  const [verticalId, setVertical] = useState(1);

  const dispatch = useDispatch();

  const {
    details,
    modules,
    options,
    isDetailsFetched,
    isOptionsFetched,
    isModulesFetched,
    error,
    isSaveSuccessed,
  } = useSelector((state: RootState) => {
    return {
      details: state.licenseManagement.liceseDetails,
      modules: state.licenseManagement.liceseDetails?.modules
        ? mapModules(
          state.licenseManagement.liceseDetails?.modules,
          state.vertical.modules
        )
        : state.vertical.modules,
      error: state.licenseManagement.error,
      isDetailsFetched: state.licenseManagement.isDataFetched,
      isModulesFetched: state.vertical.isModulesFetched,
      options: state.licenseManagement.options,
      isOptionsFetched: state.licenseManagement.isOptionsFetched,
      isSaveSuccessed: state.licenseManagement.saveSuccessed,
    };
  });

  useEffect(() => {
    return () => {
      moduleTree = [];
    };
  }, []);

  useEffect(() => {
    if (error) {
      errorNotification([""], error?.title || "Request Failed");
      dispatch(hasErrored(null));
    }
  }, [dispatch, error]);

  useEffect(() => {
    if (isSaveSuccessed) {
      successNotification([""], "Successfully Saved");
      dispatch(saveSuccessed(false));
      onSaveSuccessed?.(details?.id);
    }
  }, [details, dispatch, isSaveSuccessed, onSaveSuccessed]);

  useEffect(() => {
    dispatch(fetchOptions());
    if (licenseId) {
      dispatch(fetchStatuses(licenseId));
    }
    dispatch(fetchLicenseDetailsById(verticalId, licenseId));
  }, [dispatch, licenseId, verticalId]);


  const onFinish = (values: Store) => {
    const integrations = values.integrations?.map((i: number) => {
      return { value: i };
    });

    const effectiveDate = getUtcByShortFormat(values?.effectiveDate?.format(dateFormat));
    const expirationDate = getUtcByShortFormat(values?.expirationDate?.format(dateFormat));

    const flattenedModules = flattenModules(
      moduleTree && moduleTree.length > 0 ? moduleTree : modules
    );

    let dispatchOptions = {
      ...values,
      effectiveDate,
      expirationDate,
      integrations,
      modules: flattenedModules
    }

    if (licenseId) {
      dispatch(
        updateLicense(licenseId, dispatchOptions)
      );
    } else {
      dispatch(
        saveLicense(dispatchOptions)
      );
    }
  };

  const onModulesChange = (e: any) => {
    isModulesTouched?.(true);
    moduleTree = updateModulesTree(
      moduleTree && moduleTree.length > 0 ? moduleTree : modules,
      e
    );
  };

  return (
    <>
      {!isDetailsFetched || !isOptionsFetched ? (
        <Skeleton active />
      ) : (
          <>
            <LicenseManagementDetails
              details={transformDetails(details)}
              options={options}
              disabled={disabled}
              form={form}
              setVertical={setVertical}
              onFinish={onFinish}
              isEdit={isEdit}
              onChangeLicenceForm={onChangeLicenceForm}
            />

            {!isModulesFetched ? (
              <Skeleton active />
            ) : (
                <Modules
                  modules={modules}
                  onChange={onModulesChange}
                  disabled={disabled}
                />
              )}
          </>
        )}
    </>
  );
};

const updateModulesTree = (
  currentTree: Module[] | undefined,
  updateEvent: any
) => {
  return currentTree?.map((subModule) => {
    if (subModule.id === updateEvent.parentId) {
      return {
        ...subModule,
        checked: updateEvent.parentChecked,
        subModules: updateSubModules(subModule?.subModules, updateEvent),
      };
    } else {
      return {
        ...subModule,
      };
    }
  });
}

const updateSubModules = (subModules: Module[] | undefined, updateEvent: any) => {
  if (subModules) {
    if (updateEvent.isSubModuleUpdated) {
      return subModules?.map((subModule) => {
        const eventSubModule = updateEvent.subModules.find(
          (m: any) => m.moduleId === subModule.id
        );
        return {
          ...subModule,
          checked: eventSubModule.checked,
          functions: updateFunctions(subModule.functions, eventSubModule),
        };
      });
    } else {
      return subModules;
    }
  } else {
    return [];
  }
}

const updateFunctions = (functions: any, eventSubModule: any) => {
  if (functions) {
    return functions.map((func: any) => {
      if (eventSubModule?.functions?.includes(func.id)) {
        return {
          ...func,
          checked: true,
        };
      } else {
        return {
          ...func,
          checked: false,
        };
      }
    });
  } else {
    return [];
  }
}
