import * as types from "@app/redux/actionTypes/fileDetailsActionTypes";
import * as actions from "@app/redux/actions/fileDetailsActions";

describe("File Details Actions", () => {
  it("should generate a valid action to set setFileStatuses Data", () => {
    const data = [
      { name: "test1", value: "test1" },
      { name: "test2", value: "test2" },
    ];
    const expectedAction = {
      type: types.SET_FILE_STATUSES,
      fileStatuses: data,
    };
    expect(actions.setFileStatuses(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setYears Data", () => {
    const data = [1990, 2000, 2001];
    const expectedAction = {
      type: types.SET_FILE_YEARS,
      years: data,
    };
    expect(actions.setYears(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setProjects Data", () => {
    const data = [
      { name: "test1", value: "test1" },
      { name: "test2", value: "test2" },
    ];
    const expectedAction = {
      type: types.SET_PROJECTS,
      projects: data,
    };
    expect(actions.setProjects(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setTags Data", () => {
    const data = [
      { name: "test1", value: "test1" },
      { name: "test2", value: "test2" },
    ];
    const expectedAction = {
      type: types.SET_TAGS,
      tags: data,
    };
    expect(actions.setTags(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setUsers Data", () => {
    const data = [
      { name: "test1", value: "test1" },
      { name: "test2", value: "test2" },
    ];
    const expectedAction = {
      type: types.SET_USERS,
      users: data,
    };
    expect(actions.setUsers(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setSuccessedFiles Data", () => {
    const data = [
      {
        referenceNumber: "6565vvv3373737",
        title: "testFile1",
        checked: true,
        error: null,
      },
      {
        referenceNumber: "uyuy8858585858",
        title: "testFile2",
        checked: false,
        error: "Invalid Characters",
      },
    ];
    const expectedAction = {
      type: types.SET_SUCCESSED_FILES,
      successedFiles: data,
    };
    expect(actions.setSuccessedFiles(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setPendingSave Data", () => {
    const expectedAction = {
      type: types.SET_PENDING_SAVE,
      pendingSave: true,
    };
    expect(actions.setPendingSave(true)).toEqual(expectedAction);
  });

  it("should generate a valid action to set setFileTypes Data", () => {
    const data = ["PNG", "JPEG", "EXE"];
    const expectedAction = {
      type: types.SET_FILE_TYPES,
      fileTypes: data,
    };
    expect(actions.setFileTypes(data)).toEqual(expectedAction);
  });

  it("should generate a valid action to set isOptionsFetched Data", () => {
    const expectedAction = {
      type: types.SET_IS_OPTIONS_FETCHED,
      isOptionsFetched: true,
    };
    expect(actions.isOptionsFetched(true)).toEqual(expectedAction);
  });
});
