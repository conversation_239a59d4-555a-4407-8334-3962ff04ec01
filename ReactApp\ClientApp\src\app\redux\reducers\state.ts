import { IFunctionalFlowState } from "../types/functionalFlow/functionalFlow";
import { VerticalState } from "@app/redux/types/VerticalState";
import { IAuthState } from "../types/authTypes/auth";
import { LicenseManagementState } from "../types/LicenseManagementState";
import { FileDetailsState } from "../types/FileDetailsState";
import { IGridView } from "../types/gridView";
import { IFileArea } from "../types/fileAreaTypes";
import { IUserManagement } from "../types/userManagement";
import { ConfigState } from "../types/ConfigState";
import { IFirm } from "../types/FirmState";
import { ITemplates } from "@app/redux/reducers/templateReducer";

export interface RootState {
  auth: IAuthState;
  licenseManagement: LicenseManagementState;
  vertical: VerticalState;
  functionalFlow: IFunctionalFlowState;
  fileDetails: FileDetailsState;
  grid: IGridView;
  fileArea: IFileArea;
  template: ITemplates;
  userManagement: IUserManagement;
  configuration: ConfigState;
  firm: IFirm;
}

export namespace RootState {}
