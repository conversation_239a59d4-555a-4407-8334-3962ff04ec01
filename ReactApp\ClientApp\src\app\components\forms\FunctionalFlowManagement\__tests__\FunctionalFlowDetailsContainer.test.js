import React from 'react';
import { Provider } from 'react-redux';
import { mount } from 'enzyme';
import thunk from 'redux-thunk';
import configureMockStore from 'redux-mock-store';
import { Skeleton } from "antd";

import FunctionalFlowDetailsContainer from '../FunctionalFlowDetailsContainer';
import FunctionalFlowManagement from '../index';
// required to facilitate missing window events
import '../../../../../unit-test-utils';


const ReduxProvider = ({ children, store }) => (
    <Provider store={store}>
        {children}
    </Provider>
);
const midllewares = [thunk]
const mockStore = configureMockStore(midllewares);

const FunctionalFlowManagementContainerWrapper = (props) => {
    const { store, ...compProps } = { ...props };
    return (
        <ReduxProvider store={store}>
            <FunctionalFlowDetailsContainer
                {...compProps} />
        </ReduxProvider>
    );
}

describe('Functional FlowDetails Container', () => {
    it('should display without crashing if it is not loading', () => {
        const INITIAL_STATE = {
            functionalFlow: {
                isLoading: false,
                modules: [],
                editModule: null
            }
        };
        const store = mockStore(INITIAL_STATE);

        const component = mount(
            <FunctionalFlowManagementContainerWrapper
                actionType="add"
                licenceId="LC-00024"
                key="1"
                store={store} />);

        expect(component.find(FunctionalFlowManagement)).toHaveLength(1)
    });

    it('should display the Skelaton without crashing if loading is true', () => {
        const INITIAL_STATE = {
            functionalFlow: {
                isLoading: true,
                modules: [],
                editModule: null
            }
        };
        const store = mockStore(INITIAL_STATE);

        const component = mount(
            <FunctionalFlowManagementContainerWrapper
                actionType="add"
                licenceId="LC-00024"
                key="1"
                store={store} />);

        expect(component.find(Skeleton)).toHaveLength(1)
    });
})

