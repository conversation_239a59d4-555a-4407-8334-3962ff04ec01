import { shallow, mount } from "enzyme";
import React from "react";
import renderer from "react-test-renderer";
import { Form, Input, Select } from "antd";

import User from "..";
import initTestSuite from "@app/utils/config/TestSuite";
import InfinitySelect from "@app/components/InfinitySelect";

describe("<User/>", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render <User/> component", () => {
    const component = shallow(<User formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer.create(<User formRef={null} />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("should render <User/> component if prop formRef is null ", () => {
    const component = shallow(<User formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <User/> component if action is view  ", () => {
    const component = shallow(<User action="view" />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <User/>  ", () => {
    const component = shallow(<User action="save" />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <User/> component if action is save", () => {
    const component = shallow(<User action="save" />);
    expect(component.html()).not.toBe(null);
  });

  it("should have a form", () => {
    const component = shallow(<User />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 6 form input fields", () => {
    const component = shallow(<User action="save" />);
    expect(component.find(Form.Item)).toHaveLength(6);
  });
  it('should have label: "First Name"', () => {
    const component = shallow(<User action="save" />);
    expect(component.find(Form.Item).at(0).props().label).toEqual("First Name");
  });
  it('should have label: "Last Name"', () => {
    const component = shallow(<User action="save" />);
    expect(component.find(Form.Item).at(1).props().label).toEqual("Last Name");
  });
  it('should have label: "Email Address"', () => {
    const component = shallow(<User action="save" />);
    expect(component.find(Form.Item).at(2).props().label).toEqual("Email Address");
  });
  it('should have label: "User role"', () => {
    const component = shallow(<User action="save" />);
    expect(component.find(Form.Item).at(3).props().label).toEqual("User role");
  });
  it('should have label: "Status"', () => {
    const component = shallow(<User action="save" />);
    expect(component.find(Form.Item).at(4).props().label).toEqual("Status");
  });
  it("should have 1 InfinitySelect input Field", () => {
    const component = mount(<User action="save" />);
    expect(component.find(InfinitySelect).length).toEqual(1);
  });
  it("should have 3 Input Fields", () => {
    const component = mount(<User action="save" />);
    expect(component.find(Input).length).toEqual(3);
  });
  it("should have 3 Select Fields", () => {
    const component = mount(<User action="save" />);
    expect(component.find(Select).length).toEqual(3);
  });
  it("should have a H2 tag", () => {
    const component = mount(<User />);
    expect(component.find("h2").length).toEqual(1);
  });
  it("HR tag should be titled as Basic Information", () => {
    const component = mount(<User />);
    expect(component.find("h2").text()).toEqual("Basic Information");
  });
});
