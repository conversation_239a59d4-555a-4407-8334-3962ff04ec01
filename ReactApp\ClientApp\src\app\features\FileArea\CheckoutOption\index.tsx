import React, { useEffect, useState } from "react";
import {
  Input,
  Form,
  DatePicker,
  Row,
  Col,
  Radio,
  Button,
  Modal as AntModal,
} from "antd";
import moment from "moment";
import { useSelector } from "react-redux";
import FormItem from "antd/lib/form/FormItem";
import { FormInstance } from "antd/lib/form";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";

import styles from "./index.module.less";
import EmailDocuments from "../EmailDocuments";
import { IFile } from "@app/types/fileAreaTypes";
import { RootState } from "@app/redux/reducers/state";

const { confirm } = AntModal;

export interface ICheckOutOption {
  fileList: IFile[];
  onFinish: any;
  form: FormInstance;
  validatedCheckoutEmail: (validated: boolean) => void;
  onCloseModal?: any;
  onFileRemoved?: any;
  emailForm: FormInstance;
}

export interface ICheckoutFile {
  id: string;
  checked: boolean;
  title: string | null;
  returnDate: any;
  checkNote: string | null;
}

const layout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

export const mapToCheckoutFiles = (selectedFiles: IFile[]): ICheckoutFile[] => {
  const checkoutFileList = [] as ICheckoutFile[];
  selectedFiles?.forEach((file) => {
    checkoutFileList.push({
      id: file.id,
      checked: true,
      checkNote: null,
      returnDate: null,
      title: file.title,
    });
  });
  return checkoutFileList;
};

const disabledDatesBeforeToday = (current: moment.Moment) =>
  current < moment().endOf("day").subtract(1, "days");

export default ({
  fileList,
  onFinish,
  form,
  onCloseModal,
  onFileRemoved,
  validatedCheckoutEmail,
  emailForm,
}: ICheckOutOption) => {
  const [individualNote, setIndividualNote] = useState(true);
  const [commonNote, setCommonNote] = useState(false);
  const { permissions } = useSelector((state: RootState) => {
    return { permissions: state.fileArea.fileAreaSettings };
  });
  useEffect(() => {
    const files = mapToCheckoutFiles(fileList);
    form?.setFieldsValue({ files, commonNote: null });
  }, [fileList, form]);

  const onRemoveCheckoutItem = (remove: any, field: any) => {
    confirm({
      title: "File(s) will be removed. Do you wish to continue",
      icon: <ExclamationCircleOutlined />,
      okText: "Yes",
      cancelText: "No",
      onOk() {
        remove(field.name);
        onFileRemoved(form.getFieldValue("files").length);
        if (form.getFieldValue("files").length === 0) {
          onCloseModal(true);
        }
      },
    });
  };

  const renderDynamicCheckoutFields = (fields: any, { add, remove }: any) => {
    return (
      <div>
        {fields.map((field: any) => (
          <Row
            key={field.name}
            gutter={24}
            className={styles.yjCheckoutActionFormRow}
          >
            <Col span={2}>
              <FormItem
                valuePropName="checked"
                name={[field.name, "checked"]}
                className={styles.yjCheckoutActionFormItem}
              >
                <Button
                  onClick={() => onRemoveCheckoutItem(remove, field)}
                  type="primary"
                  icon={<CloseOutlined />}
                  className={styles.yjDeteleFile}
                />
              </FormItem>
            </Col>
            <Col span={4}>
              <Form.Item
                name={[field.name, "id"]}
                colon={false}
                className={styles.yjCheckoutActionFormItem}
              >
                <Input className={styles.yjCheckoutId} readOnly={true} />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={[field.name, "title"]}
                colon={false}
                className={styles.yjCheckoutActionFormItem}
              >
                <Input className={styles.yjCheckoutFileName} readOnly={true} />
              </Form.Item>
            </Col>

            <Col span={5}>
              <Form.Item
                name={[field.name, "returnDate"]}
                className={styles.yjCheckoutActionFormItem}
              >
                <DatePicker
                  disabledDate={disabledDatesBeforeToday}
                  className={styles.yjCheckoutActionDatePicker}
                  format={moment.localeData().longDateFormat('L')}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                name={[field.name, "checkNote"]}
                className={styles.yjCheckoutActionFormItem}
              >
                <Input.TextArea
                  maxLength={100}
                  disabled={commonNote}
                  className={styles.yjCheckoutActionTextArea}
                />
              </Form.Item>
            </Col>
          </Row>
        ))}
      </div>
    );
  };

  const onCheckoutModeChange = (isIndividualNote: boolean) => {
    setIndividualNote(isIndividualNote);
    setCommonNote(!isIndividualNote);
    if (isIndividualNote) {
      form.resetFields(["commonNote"]);
    } else {
      form.getFieldValue("files").forEach((file: any, index: any) => {
        form.resetFields([["files", index, "checkNote"]]);
      });
    }
  };

  const onChangeEmailForm = () => {
    emailForm
      .validateFields()
      .then((values) => {
        validatedCheckoutEmail(true);
      })
      .catch(() => {
        validatedCheckoutEmail(false);
      });
  };

  return (
    <>
      <div className={styles.yjModalContentWrapper}>
        <div className={styles.yjCheckoutActionWrapper}>
          <div className={styles.yjCheckoutActionUpperSection}>
            <Form
              {...layout}
              form={form}
              onFinish={onFinish}
              autoComplete="off"
              className={styles.yjCheckoutActionForm}
            >
              <Row gutter={24}>
                <Col span={18}>
                  <div className={styles.yjCheckoutActionFormHeader}>
                    <Row gutter={24}>
                      <Col
                        span={2}
                        className={styles.yjCheckoutActionColumn}
                      ></Col>
                      <Col span={4} className={styles.yjCheckoutActionColumn}>
                        <Form.Item
                          children={null}
                          label="ID"
                          colon={false}
                        ></Form.Item>
                      </Col>
                      <Col span={6} className={styles.yjCheckoutActionColumn}>
                        <Form.Item
                          children={null}
                          label="File Title"
                          colon={false}
                        ></Form.Item>
                      </Col>
                      <Col span={5} className={styles.yjCheckoutActionColumn}>
                        <Form.Item
                          children={null}
                          label="Return Date"
                          colon={false}
                        ></Form.Item>
                      </Col>
                      <Col span={5} className={styles.yjCheckoutActionColumn}>
                        <Form.Item>
                          <Radio
                            onChange={() => onCheckoutModeChange(true)}
                            checked={individualNote}
                          >
                            Individual Notes
                          </Radio>
                        </Form.Item>
                      </Col>
                    </Row>
                  </div>
                  <div className={styles.yjCheckoutActionFormGrid}>
                    <Form.List name="files">
                      {(fields: any, { add, remove }: any) => {
                        return renderDynamicCheckoutFields(fields, {
                          add,
                          remove,
                        });
                      }}
                    </Form.List>
                  </div>
                </Col>
                <Col span={6}>
                  <Row gutter={24}>
                    <Col
                      span={24}
                      className={styles.yjCheckoutCommonNotesLabel}
                    >
                      <Form.Item>
                        <Radio
                          onChange={() => onCheckoutModeChange(false)}
                          checked={commonNote}
                        >
                          Common Notes
                        </Radio>
                      </Form.Item>
                    </Col>
                  </Row>
                  <Row gutter={24}>
                    <Col span={24}>
                      <Form.Item name={"commonNote"}>
                        <div className={styles.yjCheckoutCommonNote}>
                          <Input.TextArea
                            maxLength={100}
                            disabled={individualNote}
                            className={styles.txtNotes}
                            autoSize={{ minRows: 3, maxRows: 7 }}
                          />
                        </div>
                      </Form.Item>
                    </Col>
                  </Row>
                </Col>
              </Row>
              {permissions.internalFilesEmail && (
                <EmailDocuments
                  form={emailForm}
                  onChangeEmailForm={onChangeEmailForm}
                />
              )}
            </Form>
          </div>
        </div>
      </div>
    </>
  );
};
