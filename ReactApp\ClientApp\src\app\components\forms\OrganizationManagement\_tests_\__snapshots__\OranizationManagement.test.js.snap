// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<OrganizationManagement/> should create and match to snapshot 1`] = `
<div>
  <form
    className="ant-form ant-form-horizontal ant-form-middle"
    id="basic"
    onChange={[Function]}
    onReset={[Function]}
    onSubmit={[Function]}
  >
    <div
      className="ant-row"
      style={
        Object {
          "marginLeft": -8,
          "marginRight": -8,
        }
      }
    >
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className="ant-form-item-required"
              htmlFor="basic_dba"
              title="DBA"
            >
              DBA
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <input
                  autoComplete="off"
                  className="ant-input"
                  disabled={false}
                  id="basic_dba"
                  maxLength={200}
                  onBlur={[Function]}
                  onChange={[Function]}
                  onFocus={[Function]}
                  onInput={[Function]}
                  onKeyDown={[Function]}
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className=""
              htmlFor="basic_employeeCount"
              title="EMPLOYEE COUNT"
            >
              EMPLOYEE COUNT
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <input
                  autoComplete="off"
                  className="ant-input"
                  disabled={false}
                  id="basic_employeeCount"
                  maxLength={8}
                  onBlur={[Function]}
                  onChange={[Function]}
                  onFocus={[Function]}
                  onInput={[Function]}
                  onKeyDown={[Function]}
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className=""
              htmlFor="basic_webSite"
              title="WEBSITE"
            >
              WEBSITE
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <input
                  autoComplete="off"
                  className="ant-input"
                  disabled={false}
                  id="basic_webSite"
                  maxLength={2100}
                  onBlur={[Function]}
                  onChange={[Function]}
                  onFocus={[Function]}
                  onInput={[Function]}
                  onKeyDown={[Function]}
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-row"
      style={Object {}}
    >
      <div
        className="ant-col ant-col-24"
        style={Object {}}
      >
        <div>
          Addresses
        </div>
      </div>
    </div>
    <div
      data-block="addressBlock"
    >
      <div
        className="ant-row"
        style={Object {}}
      >
        <div
          className="ant-col"
          style={Object {}}
        >
          Address Details - Default
        </div>
      </div>
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -8,
            "marginRight": -8,
          }
        }
      >
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_primaryAddress_country"
                  title="COUNTRY"
                >
                  COUNTRY
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <div
                      className="ant-select ant-select-single ant-select-show-arrow ant-select-show-search"
                      onBlur={[Function]}
                      onFocus={[Function]}
                      onKeyDown={[Function]}
                      onKeyUp={[Function]}
                      onMouseDown={[Function]}
                    >
                      <div
                        className="ant-select-selector"
                        onClick={[Function]}
                        onMouseDown={[Function]}
                      >
                        <span
                          className="ant-select-selection-search"
                        >
                          <input
                            aria-activedescendant="basic_primaryAddress_country_list_0"
                            aria-autocomplete="list"
                            aria-controls="basic_primaryAddress_country_list"
                            aria-haspopup="listbox"
                            aria-owns="basic_primaryAddress_country_list"
                            autoComplete="off"
                            className="ant-select-selection-search-input"
                            disabled={false}
                            id="basic_primaryAddress_country"
                            onChange={[Function]}
                            onCompositionEnd={[Function]}
                            onCompositionStart={[Function]}
                            onKeyDown={[Function]}
                            onMouseDown={[Function]}
                            onPaste={[Function]}
                            readOnly={false}
                            role="combobox"
                            style={
                              Object {
                                "opacity": null,
                              }
                            }
                            type="search"
                            unselectable={null}
                            value=""
                          />
                        </span>
                        <span
                          className="ant-select-selection-placeholder"
                        />
                      </div>
                      <span
                        aria-hidden={true}
                        className="ant-select-arrow"
                        onMouseDown={[Function]}
                        style={
                          Object {
                            "WebkitUserSelect": "none",
                            "userSelect": "none",
                          }
                        }
                        unselectable="on"
                      >
                        <span
                          aria-label="down"
                          className="anticon anticon-down ant-select-suffix"
                          role="img"
                        >
                          <svg
                            aria-hidden="true"
                            data-icon="down"
                            fill="currentColor"
                            focusable="false"
                            height="1em"
                            viewBox="64 64 896 896"
                            width="1em"
                          >
                            <path
                              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                            />
                          </svg>
                        </span>
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_primaryAddress_streetAddress"
                  title="ADDRESS"
                >
                  ADDRESS
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_primaryAddress_streetAddress"
                      maxLength={300}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_primaryAddress_city"
                  title="CITY"
                >
                  CITY
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_primaryAddress_city"
                      maxLength={100}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -8,
            "marginRight": -8,
          }
        }
      >
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_primaryAddress_state"
                  title="STATE/PROVINCE/REGION"
                >
                  STATE/PROVINCE/REGION
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_primaryAddress_state"
                      maxLength={100}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_primaryAddress_zipCode"
                  title="ZIP CODE"
                >
                  ZIP CODE
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_primaryAddress_zipCode"
                      maxLength={15}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-row"
      style={
        Object {
          "marginLeft": -8,
          "marginRight": -8,
        }
      }
    >
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <button
          className="ant-btn ant-btn-primary"
          hidden={false}
          onClick={[Function]}
          onMouseEnter={[Function]}
          onMouseLeave={[Function]}
          type="button"
        >
          <span
            aria-label="plus-circle"
            className="anticon anticon-plus-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="plus-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M696 480H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"
              />
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
            </svg>
          </span>
          <span>
            Add New Address 
          </span>
        </button>
      </div>
    </div>
    <div
      className="ant-divider ant-divider-horizontal"
      role="separator"
    />
    <div
      className="ant-row"
      style={Object {}}
    >
      <div
        className="ant-col ant-col-24"
        style={Object {}}
      >
        <div>
          PRIMARY ADMIN DETAILS
        </div>
      </div>
    </div>
    <div
      data-contactlistblock="contactList"
    >
      <div
        className="ant-row"
        style={Object {}}
      >
        <div
          className="ant-col"
          style={Object {}}
        >
          Primary Admin - Default
        </div>
      </div>
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -8,
            "marginRight": -8,
          }
        }
      >
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_defaultPrimaryAdmin_firstName"
                  title="FIRST NAME"
                >
                  FIRST NAME
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_defaultPrimaryAdmin_firstName"
                      maxLength={50}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_defaultPrimaryAdmin_lastName"
                  title="LAST NAME"
                >
                  LAST NAME
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_defaultPrimaryAdmin_lastName"
                      maxLength={50}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_defaultPrimaryAdmin_emailAddress"
                  title="Email Address"
                >
                  Email Address
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      id="basic_defaultPrimaryAdmin_emailAddress"
                      maxLength={254}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      placeholder="<EMAIL>"
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className="ant-form-item-required"
                  htmlFor="basic_defaultPrimaryAdmin_primaryContactNumber"
                  title="PRIMARY CONTACT NUMBER"
                >
                  PRIMARY CONTACT NUMBER
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_defaultPrimaryAdmin_primaryContactNumber"
                      maxLength={20}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 8,
              "paddingRight": 8,
            }
          }
        >
          <div>
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-col-24 ant-form-item-label"
                style={Object {}}
              >
                <label
                  className=""
                  htmlFor="basic_defaultPrimaryAdmin_secondaryContactNumber"
                  title="SECONDARY CONTACT NUMBER"
                >
                  SECONDARY CONTACT NUMBER
                </label>
              </div>
              <div
                className="ant-col ant-col-24 ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <input
                      autoComplete="off"
                      className="ant-input"
                      disabled={false}
                      id="basic_defaultPrimaryAdmin_secondaryContactNumber"
                      maxLength={20}
                      onBlur={[Function]}
                      onChange={[Function]}
                      onFocus={[Function]}
                      onInput={[Function]}
                      onKeyDown={[Function]}
                      type="text"
                      value=""
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-row"
      style={
        Object {
          "marginLeft": -8,
          "marginRight": -8,
        }
      }
    >
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <button
          className="ant-btn ant-btn-primary"
          hidden={false}
          onClick={[Function]}
          onMouseEnter={[Function]}
          onMouseLeave={[Function]}
          type="button"
        >
          <span
            aria-label="plus-circle"
            className="anticon anticon-plus-circle"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="plus-circle"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M696 480H544V328c0-4.4-3.6-8-8-8h-48c-4.4 0-8 3.6-8 8v152H328c-4.4 0-8 3.6-8 8v48c0 4.4 3.6 8 8 8h152v152c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V544h152c4.4 0 8-3.6 8-8v-48c0-4.4-3.6-8-8-8z"
              />
              <path
                d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
              />
            </svg>
          </span>
          <span>
            ADD ANOTHER PRIMARY ADMIN
          </span>
        </button>
      </div>
    </div>
  </form>
</div>
`;
