@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjManageTagsWrapper {

  .yjManageTagsContent {
    max-height: 45vh;
    overflow: hidden auto;

    .yjManageTagsBox {
      margin: 0 10px 20px 0;

      button {
        border-radius: 0;
        box-shadow: none;
        margin: 0;
      }

      .yjCheckButton {
        background: @color-accent-secondary;
        border: none;
        box-shadow: none;
        color: @color-font-white;
      }

      .yjCloseButton {
        background: @color-danger;
        border: none;
        box-shadow: none;
        color: @color-font-white;
      }

      .yjManageTagName {
        background: transparent;
        border: none;
        color: @color-font;
        margin: 0;
        padding: 0;

        p {
          background: @color-tag;
          margin: 0;
          overflow: hidden;
          padding: 5px 7px;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 150px;
        }

        .flex-display(inline-flex);
      }

      .yjManageTagNameEdit {
        margin-bottom: 0;
        width: 150px;

        span {
          padding-left: 0;
        }
      }

      .flex-display(inline-flex);
    }
  }

  .yjManageTagsHeader {
    border-bottom: 1px solid fade(@color-accent-border, 10%);
    margin-bottom: 10px;
    padding: 20px 0;

    .yjManageTagsForm {
      width: 100%;

      .yjManageTagsFormItem {
        margin: 0;

        button {
          width: 100%;
        }
      }
    }
  }
}
