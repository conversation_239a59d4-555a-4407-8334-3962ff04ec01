@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

// Details Tab

.yjPropertiesDetailTab {

  .yjPropertiesDetailPreview {
    background: url(../../../../styles/assets/images/docpreview.png.jpg) no-repeat center;
    background-size: cover;
    border: 1px solid @color-primary;
    height: 200px;
    margin-bottom: 10px;
  }

  .yjPropertiesDetailInfo {

    .yjPropertiesDetailList {
      background: @color-bg;
      border-radius: 5px;
      margin-bottom: 20px;
      padding: 20px;

      .yjPropertiesDetailListItem {
        border-bottom: 1px dotted;
        border-color: fade(@color-accent-border, 40%);
        padding: 5px 0;
        text-transform: @yj-transform;

        &:last-child {
          border-bottom: none;
          padding-bottom: 5px;
        }

        h4 {
          display: inline-flex;
          margin: 0;
          width: 70%;

          .yjPropertiesDetailTitle {
            color: @color-secondary;
            font-size: @font-size-base / 1.2;
            font-weight: 700;
            margin: 0;
            text-transform: @yj-transform;
          }
        }

        span {
          text-transform: none;
        }

        div {
          display: inline-flex;
          font-size: @font-size-base / 1.2;
          text-transform: capitalize;
          width: 100%;
          word-break: break-all;
        }
      }
    }
  }
}

//Assignments Tab

.yjPropertiesAssignmentsTab {

  .yjPropertiesAssignmentsNotifications {
    color: @color-primary;
    font-size: @font-size-base / 1.2;
    text-align: right;
    text-transform: @yj-transform;
  }

  .yjPropertiesAssignmentsList {
    background: @color-bg;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 20px;

    .yjPropertiesAssignmentsListItem {
      border-bottom: 1px dotted;
      border-color: fade(@color-accent-border, 40%);
      padding: 5px 0;
      text-transform: @yj-transform;
      word-break: break-all;

      &:last-child {
        border-bottom: none;
        padding-bottom: 5px;
      }

      h4 {
        display: inline-flex;
        margin: 0;
        width: 70%;

        .yjPropertiesAssignmentsTitle {
          color: @color-secondary;
          font-size: @font-size-base / 1.2;
          font-weight: 700;
          margin: 0;
          text-transform: @yj-transform;
        }
      }

      div {
        display: inline-flex;
        font-size: @font-size-base / 1.2;
        text-transform: capitalize;
        width: 100%;
      }
    }
  }
}

//Checkouts Tab

.yjPropertiesCheckoutsTab {

  .yjPropertiesCheckoutsNotifications {
    color: @color-primary;
    font-size: @font-size-base / 1.2;
    text-align: right;
    text-transform: @yj-transform;
  }

  .yjPropertiesCheckoutsList {
    background: @color-bg;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 20px;

    .yjPropertiesCheckoutsListItem {
      border-bottom: 1px dotted;
      border-color: fade(@color-accent-border, 30%);
      padding: 5px 0;
      text-transform: @yj-transform;
      word-break: break-all;

      &:last-child {
        border-bottom: none;
        padding-bottom: 5px;
      }

      h4 {
        display: inline-flex;
        margin: 0;
        width: 70%;

        .yjPropertiesCheckoutsTitle {
          color: @color-secondary;
          font-size: @font-size-base / 1.2;
          font-weight: 700;
          margin: 0;
          text-transform: @yj-transform;
        }
      }

      div {
        display: inline-flex;
        font-size: @font-size-base / 1.2;
        text-transform: capitalize;
        width: 100%;
      }
    }
  }
}

//Versions

.yjPropertiesVersionsTab {

  .yjPropertiesVersionsNotifications {
    color: @color-primary;
    font-size: @font-size-base / 1.2;
    text-align: right;
    text-transform: @yj-transform;
  }

  .yjPropertiesVersionsList {
    background: @color-bg;
    border-radius: 5px;
    margin-bottom: 20px;
    padding: 20px;

    .yjPropertiesVersionsListItem {
      border-bottom: 1px dotted;
      border-color: fade(@color-accent-border, 40%);
      padding: 5px 0;
      text-transform: @yj-transform;

      &:last-child {
        border-bottom: none;
        padding-bottom: 5px;
      }

      h4 {
        display: inline-flex;
        margin: 0;
        width: 70%;

        .yjPropertiesVersionsTitle {
          color: @color-secondary;
          font-size: @font-size-base / 1.2;
          font-weight: 700;
          margin: 0;
          text-transform: @yj-transform;
        }
      }

      div {
        display: inline-flex;
        font-size: @font-size-base / 1.2;
        text-transform: capitalize;
        width: 100%;
      }
    }
  }
}

.yjPropertiesVersionsDateTime {
  font-size: @font-size-base / 1.2;
  text-transform: capitalize;
}

.yjPropertiesVersionsButtonPanel {
  border-color: fade(@color-accent-border, 40%);
  border-top: 1px solid;
  margin-top: 10px;
  padding-top: 10px;
  text-align: right;
}

.yjPropertiesAssignmentsAdditionalNotes {
  font-size: @font-size-base / 1.2;
  padding-top: 10px;

  span {
    color: @color-secondary;
    font-weight: 700;
    text-transform: @yj-transform;
  }
}

.yjPropertiesRequiredText {
  color: @color-primary;
  font-size: @font-size-base / 1.2;
  font-style: italic;
  padding-top: 10px;
}
