import { shallow } from "enzyme";
import React from "react";
import SelectUploadOptionModal from "..";
import Modal from "@app/components/Modal";
import { Button } from "antd";

describe("<SelectUploadOptionModal />", () => {
  it("should render component", () => {
    const component = shallow(<SelectUploadOptionModal />);
    expect(component.html()).not.toBe(null);
  });

  it("should render with props", () => {
    const component = shallow(
      <SelectUploadOptionModal onOptionSelect={() => null} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should have title: Setting File Properties", () => {
    const component = shallow(<SelectUploadOptionModal />);
    expect(component.find(Modal).prop("title")).toEqual(
      "Setting File Properties"
    );
  });

  it("should have Button: Same Properties", () => {
    const component = shallow(<SelectUploadOptionModal />);
    expect(component.find(Modal).find(Button).at(0).prop("children")).toEqual(
      "Same Properties"
    );
  });

  it("should have Button: Same Properties", () => {
    const component = shallow(<SelectUploadOptionModal />);
    expect(component.find(Modal).find(Button).at(1).prop("children")).toEqual(
      "Different Properties"
    );
  });
});
