import React, { useCallback, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { Button, Checkbox, Dropdown, Form, Menu, Modal as AntModal, Select, Drawer, Modal } from 'antd';
import {
  ApartmentOutlined,
  AuditOutlined,
  ContainerOutlined,
  CopyOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DownOutlined,
  DragOutlined,
  EditOutlined,
  ExclamationCircleOutlined,
  FileDoneOutlined,
  FilterOutlined,
  MenuFoldOutlined,
  MenuUnfoldOutlined,
  SettingOutlined,
  LinkOutlined,
  DisconnectOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { useForm } from 'antd/lib/form/Form';
import { CheckboxChangeEvent } from 'antd/lib/checkbox';
import { Store } from 'antd/lib/form/interface';

import styles from './index.module.less';
import TagManagement from '@app/features/FileArea/TagManagement';
import AssignOption, { AssignFormEvents } from '@app/features/FileArea/Assign';
import ColumnFilter from '@app/components/ColumnFilter';
import ReCategorize from '@app/features/FileArea/ReCategorize';
import ClearFilter from '@app/components/ClearFilter';
import { RootState } from '@app/redux/reducers/state';
import DownloadModal, { downloadTypes } from '@app/components/DownloadModal';
import { IFile } from '@app/types/fileAreaTypes';

import {
  updateContextMenuAssignOption,
  updateContextMenuCheckoutOption,
  updateContextMenuDeleteOption,
  updateContextMenuDownloadOption,
  updateContextMenuPropetiesoption,
  updateContextMenuPublishFiles,
  updateContextMenuReCategorizeOption,
  updateContextMenuMoveFilesOption,
  updateContextMenuReNameFilesOption,
  updateContextMenuCopyFilesOption,
  updateContextMenuStatusOption,
  updateContextMenuUnpublishFiles,
  updateLoadGridOption,
  updateContextMenuLinkFilesOption,
  updateContextMenuUnlinkFilesOption,
  updateContextMenuToBeDeleted,
  updateContextMenuCopyLinkFiles,
} from '@app/redux/actions/fileAreaActions';
import ChangeStatus from '../ChangeStatus';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import CheckoutOption, { ICheckoutFile } from '../CheckoutOption';
import { deleteFile, recategorizeFiles, updateAssigneeNStatus, updateFileStatus, moveFilesService, copyFiles, reNameFiles, linkToSite, unlinkFilesFromBinders } from '@app/api/fileAreaService';
import logger from '@app/utils/logger';
import DocumentPropeties from '../DocumentPropeties';

import FilterTemplateManagementEdit from '@app/components/GenericDataTable/FilterTemplateManagement/edit';
import FilterTemplateManagementSave from '@app/components/GenericDataTable/FilterTemplateManagement/save';
import { GenericFilter } from '@app/components/GenericDataTable/types';
import { CreateFilterTemplateRequest, IFilterTemplate, SavedFilterTemplate } from '@app/types/filterTemplateTypes';
import {
  applyASavedFilter,
  clearGridFilters,
  getSavedFilters,
  saveFilterTemplate,
  updateFilterTemplateSaved,
  onSelectedSavedFilter,
  updateGridHasUpdates,
} from '@app/redux/actions/gridsActions';
import { getGridFilterFromIFilterTemplate } from '@app/components/GenericDataTable/FilterTemplateManagement/util';
import HTTPResponse from '@app/utils/http/interfaces/HttpResponse';

import { LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE } from '@app/components/Uploader';
import { checkoutFiles } from './FileAreaActionPanelFunctions/checkoutFiles';
import { FORBIDDEN_ERROR_CODE } from '@app/utils';
import { GridRefreshIcon } from '@app/components/GenericDataTable/GridRefresh';
import PublishFiles, { IPublishFiles } from '../PublishFiles/PublishFilesDrawerContent';
import UnpublishFiles, { IUnpublishFiles } from '../UnpublishFiles/UnpublishFilesDrawerContent';
import { MdOpenInNew } from 'react-icons/md';
import { publishFiles } from './FileAreaActionPanelFunctions/publishFiles';
import { unpublishFiles } from './FileAreaActionPanelFunctions/unpublishFiles';
import { copyToClipboard } from '@app/components/GenericDataTable/util';
import ReNameFiles from '../ReNameFiles';
import hasPermission from '@app/utils/permission';
import MoveFiles from '@app/features/FileAreaActionPanel/MoveFiles';
import LinkFilesDrawer from '@app/features/Link/LinkFilesDrawer';
import UnlinkFilesDrawer, { FileLink } from '@app/features/Unlink/UnlinkFilesDrawer';
import { useParams } from "react-router-dom";
import { UpdateFunctionalFlowDataLoading } from '@app/redux/actions/functionalFlowActions';
import SubmitButton from '@app/components/SubmitButton';
import UnpublishFilesDrawer from '@app/features/FileArea/UnpublishFiles/UnpublishFilesDrawer';
import PublishFilesDrawer from '@app/features/FileArea/PublishFiles/PublishFilesDrawer';
import { TagManagementDrawer } from '@app/features/FileArea/TagManagement/TagManagementDrawer';

const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';

export interface IfileAreaActionPanel {
  siteId: string;
  selectedFileList: IFile[];
  showDownload?: boolean;
  showStatus?: boolean;
  showAssign?: boolean;
  showCheckout?: boolean;
  fileDownloaded?: any;
  showPropeties?: boolean;
  showDelete?: boolean;
  showReCategorize?: boolean;
  showMoveFiles?: boolean;
  syncGrid?: any;
  showPublish?: boolean;
  showUnpublish?: boolean;
  showToBeDelete?: boolean;
  showCopyLink?: boolean;
  showReName?: boolean;
  showCopy?: boolean;
  showLinkFiles?: boolean;
  showUnlinkFiles?: boolean;
  originalFileAreaWithLinked?: boolean;
  showManageTags?: boolean;
  showManageCheckin?: boolean;
  toggleIconClicked: (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => void;
  onFolderTreeChange?: (event: number) => void;
  additionalActionPanel?: JSX.Element;
}

const { confirm } = AntModal;
const { Option } = Select;
const elementName = 'FilterChangeTrigger';
const TABLE_KEY = 'fileArea';
const TO_BE_DELETED_STATUS = 'To be Deleted';

export const FileAreaActionPanel: React.FC<IfileAreaActionPanel> = ({
  siteId,
  toggleIconClicked,
  selectedFileList,
  showDownload = false,
  showStatus = false,
  showAssign = false,
  showPropeties = false,
  showReCategorize = false,
  showMoveFiles = false,
  showCheckout = false,
  fileDownloaded,
  showDelete = false,
  showPublish = false,
  showUnpublish = false,
  showToBeDelete = false,
  showCopyLink = false,
  showLinkFiles = false,
  showUnlinkFiles = false,
  showReName = false,
  showCopy = false,
  syncGrid,
  originalFileAreaWithLinked = false,
  showManageTags = false,
  showManageCheckin = false,
  onFolderTreeChange = () => { },
  additionalActionPanel = null,
}) => {
  const CHECKOUT_FAILED_ERRORCODE = 400;
  const [form] = useForm();
  const [changeStatusForm] = useForm();
  const [emailForm] = useForm();
  const [showPanel, setShowPanel] = useState(true);
  const [showAddNewFilterModal, setShowAddNewFilterModal] = useState(false);
  const [showEditFiltersModal, setShowEditFiltersModal] = useState(false);
  const [showTagsManageModal, setShowTagsManageModal] = useState(false);
  const [showPublishModal, setShowPublishModal] = useState(false);
  const [showUnpublishModal, setShowUnpublishModal] = useState(false);
  const [assignOptionForm] = Form.useForm();
  const [selectedUnlinkFiles, setSelectedUnlinkFiles] = useState<FileLink[]>([]);
  const [assignOptionDetails, setAssignOptionDetails] = useState<{
    assignee: number;
    files: IFile[];
  }>({ assignee: 0, files: [] });
  const [showAssignModal, setShowAssignModal] = useState(false);
  const handleAddNewFilterCancel = () => {
    setNewFilterName('');
    setShowAddNewFilterModal(false);
    setNameValid(true);
  };
  const [newFilterName, setNewFilterName] = useState('');
  const [sortedSavedFilterList, setSortedFilterList] = useState([] as any[]);
  const { showFilter, showFilterSaveButton, gridFilters, savedFilters, columns, filter_template_saved, hasUpdates } = useSelector((state: RootState) => state.grid);
  const { download, status, assign, checkout, publish, unpublish, deleteFiles, propeties, reCategorize, moveFiles, renameFiles, copySelectedFiles, linkFiles, unLinkFiles, toBeDeleted, copyLink } = useSelector((state: RootState) => state.fileArea);
  const { userPermission } = useSelector((state: RootState) => state.userManagement);
  const { folderTree } = useSelector((state: RootState) => state.fileArea);
  const { isLoading } = useSelector((state: RootState) => state.functionalFlow);

  const [isNameValid, setNameValid] = useState(true);
  const [showFiltersButton, setShowFiltersButton] = useState(true);
  const [publishFileList, setPublishFileList] = useState<IPublishFiles[]>([]);
  const [unpublishFileList, setUnpublishFileList] = useState<IUnpublishFiles[]>([]);
  const [publishFileExpiration, setPublishFileExpiration] = useState<moment.Moment | undefined>(undefined);
  const dispatch = useDispatch();
  const { binderId } = useParams<any>();

  useEffect(() => {
    if (savedFilters.length === 0) {
      setShowEditFiltersModal(false);
    }
    const sortedList = (savedFilters as any[]).sort((filter1: any, filter2: any) => {
      return filter1.name.localeCompare(filter2.name);
    });
    setSortedFilterList(sortedList);
  }, [savedFilters]);

  // use to clear the grid filters on component mount.
  useEffect(() => {
    dispatch(clearGridFilters());
  }, []);


  const onClickSavedFilter = (value: any) => {
    const savedFilterIndex = savedFilters.findIndex((filter: SavedFilterTemplate) => filter.id === value);
    const savedFilter: SavedFilterTemplate = savedFilters[savedFilterIndex];
    if (savedFilter) {
      const gridFilterTemplates = getGridFilterFromIFilterTemplate(savedFilter.content, columns);
      const savedFilterColumns = Object.getOwnPropertyNames(savedFilter.content);
      const optionalFilterColumns = columns.filter((column: any) => !column.default && savedFilterColumns.includes(column.key)).map((column: any) => column.key);
      const filterColumns = columns.map((column: any) => {
        if (optionalFilterColumns.includes(column.key)) {
          return { ...column, selected: true };
        } else {
          return column;
        }
      });

      dispatch(
        applyASavedFilter({
          gridFilterTemplates,
          filterColumns,
          selected: optionalFilterColumns.length > 0,
          selectedElement: {
            name: elementName,
            checked: true,
            multiple: true,
          },
        })
      );
      dispatch(onSelectedSavedFilter(savedFilter));
      setShowFiltersButton(true);
    }
  };

  const {
    options,
    isOptionsFetched,
    successedFiles,
    pendingSave,
    permissions,
  } = useSelector((state: RootState) => {
    return {
      options: {
        ...state.fileDetails.options,
        folderTree,
      },


      isOptionsFetched: state.fileDetails.isOptionsFetched,
      successedFiles: state.fileDetails.successedFiles,
      pendingSave: state.fileDetails.pendingSave,
      permissions: state.userManagement.userPermission,
    };
  });

  const onChangeSaveNewFilterName = (name: string) => {
    if (
      savedFilters.findIndex((filter: SavedFilterTemplate) => {
        return filter.name.toLowerCase() === name.toLowerCase().trim();
      }) !== -1
    ) {
      setNameValid(false);
      setNewFilterName(name);
    } else {
      setNameValid(true);
      setNewFilterName(name);
    }
  };

  const onSearchSavedFilters = (input: any, option: any) => {
    if (typeof option?.children === 'string') {
      return option.children.toLowerCase().startsWith(input.toLowerCase());
    } else {
      return false;
    }
  };

  const handleShowTagManageModalCancel = () => {
    setShowTagsManageModal(false);
  };
  const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';
  const [checkingOutFilesCount, setCheckingOutFilesCount] = useState(0);
  const [checkedOuFilesDownload, setCheckedOuFilesDownload] = useState<IFile[] | undefined>(undefined);

  const resetAssignModal = () => {
    setAssignOptionDetails({ assignee: 0, files: [] });
    dispatch(updateContextMenuAssignOption(false));
    setShowAssignModal(false);
  };

  const cancelAssignModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        assignOptionForm.resetFields();
        resetAssignModal();
      },
    });
  };

  const cancelPublishModal = () => {
    setPublishFileExpiration(undefined);
    setPublishFileList([]);
    setShowPublishModal(false);
    dispatch(updateContextMenuPublishFiles(false));
  };
  const cancelUnpublishModal = () => {
    setUnpublishFileList([]);
    setShowUnpublishModal(false);
    dispatch(updateContextMenuUnpublishFiles(false));
  };

  const handlePublishFiles = async () => {
    dispatch(UpdateFunctionalFlowDataLoading(true));
    const fileIds = publishFileList.filter((e) => e.checked).map((e) => e.id);

    try {
      const { data } = await publishFiles(fileIds, siteId, publishFileExpiration?.toDate());
      if (data) {
        syncGrid(true);
        const files = publishFileList
          .filter((e) => !e.checked)
          .map((e) => {
            return { ...e, checked: true };
          });
        if (files.length === 0) {
          cancelPublishModal();
          return;
        }
        setPublishFileList(files);
        setPublishFileExpiration(undefined);
      }
    } catch (e) {
      errorNotification([''], 'Publishing Failed');
      logger.error('File Area Module', 'Publish files', e);
    } finally {
      dispatch(UpdateFunctionalFlowDataLoading(false));
    }
  };

  const handleFileUnlinkSubmit = () => {
    const unlinkedData = selectedUnlinkFiles
      .filter(file => file.selectedBinders && file.selectedBinders.length > 0)
      .map(file => ({
        fileId: file.fileId,
        binderIds: file.selectedBinders
      }));

    dispatch(UpdateFunctionalFlowDataLoading(true));
    unlinkFilesFromBinders(unlinkedData)
      .then((response) => {
        if (response.data) {
          successNotification([''], 'File(s) Unlinked Successfully');
          syncGrid(true);
          resetUnlinkFilesModal();
        } else {
          errorNotification([''], 'File Unlinking Failed');
        }
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'File Unlinking Failed');
        }
        logger.error('File Area Module', 'Unlink files', error);
      }).finally(() => {
        dispatch(UpdateFunctionalFlowDataLoading(false));
      });
  };

  const handleUnpublishFiles = async () => {
    dispatch(UpdateFunctionalFlowDataLoading(true));
    const fileIds = unpublishFileList.filter((e) => e.checked).map((e) => e.id);
    try {
      const { data } = await unpublishFiles(fileIds, siteId);
      if (data) {
        syncGrid(true);
        const files = publishFileList.filter((e) => !e.checked);
        if (files.length === 0) {
          cancelUnpublishModal();
          return;
        }
        setUnpublishFileList(files);
      }
    } catch (e) {
      errorNotification([''], 'Unpublishing Failed');
      logger.error('File Area Module', 'Unpublish files', e);
    } finally {
      dispatch(UpdateFunctionalFlowDataLoading(false));
    }
  };

  const handleShowAssignModalCancel = (isAllFilesRemoved = false) => {
    if (assignOptionDetails.assignee > 0 || assignOptionForm.getFieldValue('statusId') > 0 || assignOptionForm.getFieldValue('assignNotes')) {
      if (isAllFilesRemoved) {
        assignOptionForm.resetFields();
        resetAssignModal();
      } else {
        cancelAssignModal();
      }
    } else {
      resetAssignModal();
    }
  };
  const onAssigneeUpdate = (value: any) => {
    const currentAssignState = { ...assignOptionDetails };
    currentAssignState.assignee = value;
    setAssignOptionDetails(currentAssignState);
  };
  const onFilesChange = (fileList: IFile[]) => {
    const currentAssignState = { ...assignOptionDetails };
    currentAssignState.files = fileList;
    setAssignOptionDetails(currentAssignState);
  };
  const handleAssignFormEvents: AssignFormEvents = {
    onAssigneeUpdate,
    onFilesChange,
  };

  const onUpdatedAssignee = (response: HTTPResponse<any>) => {
    if (response.data) {
      const SET_TIMEOUT_ASSIGNEE = 500;
      successNotification([''], 'Assignment Successful');
      // successNotification([''], 'File Assignment Email sent successfully');
      syncGrid(true);
      dispatch(updateContextMenuAssignOption(false));
      setTimeout(() => {
        assignOptionForm.resetFields();
        setShowAssignModal(false);
        setAssignOptionDetails({ assignee: 0, files: [] });
      }, SET_TIMEOUT_ASSIGNEE);
    } else {
      errorNotification([''], 'Assignment Failed');
    }
  };

  const handleAssignOptionUpdate = () => {
    assignOptionForm.validateFields().then((values) => {
      const selectedFileIds = assignOptionDetails.files.length > 0 ? assignOptionDetails.files.map((file) => file.id) : selectedFileList.map((file) => file.id);
      updateAssigneeNStatus({
        assigneeId: values.assigneeId,
        statusId: values.statusId,
        fileIds: selectedFileIds,
        note: values.assignNotes,
      })
        .then((response) => {
          onUpdatedAssignee(response);
        })
        .catch((error) => {
          if (error.statusCode === FORBIDDEN_ERROR_CODE) {
            errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
          } else {
            errorNotification([''], 'Assignment Failed');
          }
          logger.error('AssignOption', 'Update Assignee and Status', error);
        });
    });
  };
  const onCancelRecategorizeModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        resetReCategorizeModal();
      },
    });
  };

  const onCancelMoveFilesModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        resetMoveFilesModal();
      },
    });
  };

  const onCancelLinkFilesModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        resetLinkFilesModal();
      },
    });
  };

  const onCancelReNameFilesModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        assignOptionForm.resetFields();
        resetReNameFilesModal();
      },
    });
  };

  const [showReCategorizeModal, setShowReCategorizeModal] = useState(false);
  const [showMoveFileModal, setShowMoveFileModal] = useState(false);
  const [showLinkFileModal, setShowLinkFilesModal] = useState(false);
  const [showReNameFilesModal, setShowReNameFilesModal] = useState(false);
  const [showUnlinkFilesModal, setShowUnlinkFilesModal] = useState(false);
  const [recategorizeDetails, setRecategorizeDetails] = useState<{
    fileList: IFile[];
    folderId: number;

  }>();
  const [moveFileDetails, setMoveFilesDetails] = useState<{
    fileList: IFile[];
    folderId: number;
    sourceClientId: string;
    destinationClientId: number;
  }>();

  const [linkFilesDetails, setLinkFilesDetails] = useState<{
    fileIds: string[];
    binderIds: string[];
  }>();

  const [reNameFilesDetails, setReNameFilesDetails] = useState<{
    fileList: IFile[];
  }>();

  const handleShowReCategorizeModalCancel = (hasSelectedFiles = true) => {
    if (hasSelectedFiles && recategorizeDetails?.folderId !== undefined) {
      onCancelRecategorizeModal();
    } else {
      resetReCategorizeModal();
    }
  };

  const handleCancel = () => setShowMoveFileModal(false);
  const handleRenameCancel = () => setShowReNameFilesModal(false);
  const handleReCategorizeFileCancel = () => setShowReCategorizeModal(false);
  const handleShowMoveFilesModalCancel = (hasSelectedFiles = true) => {
    if (hasSelectedFiles && moveFileDetails?.folderId !== undefined) {
      onCancelMoveFilesModal();
    } else {
      resetMoveFilesModal();
    }
  };

  const handleShowLinkFilesModal = (show: boolean) => {
    const areToBeDeleted = selectedFileList.filter((file) => file.status.name === TO_BE_DELETED_STATUS);
    if (areToBeDeleted.length > 0 && show) {
      errorNotification([''], 'Unable to link file(s). Please change the Status and retry.');
      resetLinkFilesModal();
    } else {
      setShowLinkFilesModal(show);
    }
  }

  const hasLinkedFiles = (files: IFile[]): boolean => {
    const areSelectedFilesLinked = files.some((file) => file.linked);
    if (areSelectedFilesLinked) {
      errorNotification([''], 'You cannot perform this action to linked files. Please unlink files first.');
    }
    return areSelectedFilesLinked;
  };

  const handleShowLinkFilesModalCancel = () => {
    resetLinkFilesModal();
  };

  const handleShowReNameFilesModalCancel = (hasSelectedFiles = true) => {
    if (hasSelectedFiles) {
      onCancelReNameFilesModal();
    } else {
      resetReNameFilesModal();
    }
  };

  const handleShowUnlinkFilesModalCancel = () => {
    resetUnlinkFilesModal();
  };

  const resetReCategorizeModal = () => {
    setRecategorizeDetails({
      folderId: undefined as any,
      fileList: undefined as any,
    });
    dispatch(updateContextMenuReCategorizeOption(false));
    setShowReCategorizeModal(false);
  };

  const resetMoveFilesModal = () => {
    setMoveFilesDetails({
      folderId: undefined as any,
      fileList: undefined as any,
      sourceClientId: undefined as any,
      destinationClientId: undefined as any,
    });
    dispatch(updateContextMenuMoveFilesOption(false));
    setShowMoveFileModal(false);
  };

  const resetLinkFilesModal = () => {
    setLinkFilesDetails({
      binderIds: undefined as any,
      fileIds: undefined as any,
    });
    dispatch(updateContextMenuLinkFilesOption(false));
    handleShowLinkFilesModal(false);
  };

  const resetReNameFilesModal = () => {
    setReNameFilesDetails({
      fileList: undefined as any
    });
    dispatch(updateContextMenuReNameFilesOption(false));
    setShowReNameFilesModal(false);
    assignOptionForm.resetFields();
    setRenameButtonEnable(false);
  };

  const resetUnlinkFilesModal = () => {
    setSelectedUnlinkFiles([]);
    dispatch(updateContextMenuUnlinkFilesOption(false));
    setShowUnlinkFilesModal(false);
  };

  const handleReCategorizeUpdateDetails = (folderId: number, fileList: IFile[]) => {
    setRecategorizeDetails({ folderId: folderId, fileList: fileList });
  };

  const handleLinkFilesUpdateDetails = (fileList: string[], selectedBinderIds: string[]) => {
    setLinkFilesDetails({ fileIds: fileList, binderIds: selectedBinderIds });
  };

  const handleValuesChange = (changedValues: any, allValues: any) => {
    // Check if any input field has a non-empty value
    const anyRenamedValues = Object.values(allValues).some(value => value);
    setRenameButtonEnable(anyRenamedValues);
  };

  const handleReNameFilesUpdateDetails = (fileList: any[]) => {

    const updatedFileList = fileList.map((file: IFile) => ({
      fileId: file.id,
      title: file.title
    }));

    reNameFiles(
      updatedFileList
    )
      .then((response) => {
        successNotification([''], 'ReNamed Successfully');
        syncGrid(true);
        resetReNameFilesModal();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'ReName Failed');
        }
        logger.error('File Area Module', 'ReName files', error);
      });

  };

  const handleRecategorizeUpdate = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    recategorizeFiles(
      recategorizeDetails?.fileList.map((file) => file.id),
      recategorizeDetails?.folderId ? recategorizeDetails.folderId : 0,
      binderId
    )
      .then((response) => {
        successNotification([''], 'File Re-Categorization Successful');
        syncGrid(true);
        resetReCategorizeModal();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'File Re-Categorization Failed');
        }
        logger.error('File ARea Module', 'File Re-Categorization', error);
      });
  };


  const handleLinkFileUpdate = () => {
    dispatch(UpdateFunctionalFlowDataLoading(true));

    linkToSite(
      linkFilesDetails
    )
      .then((response) => {
        successNotification([''], 'File(s) Linked Successfully');
        syncGrid(true);
        resetLinkFilesModal();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'File Linking Failed');
        }
        logger.error('File Area Module', 'Move files', error);
      }).finally(() => {
        dispatch(UpdateFunctionalFlowDataLoading(false));
      });
  };

  const handleFileReNameSubmit = () => {
    assignOptionForm.submit();
  };


  const cancelChangeStatusModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        setStatusFileRemoved(false);
        setSelectedNewStatusState(0);
        dispatch(updateContextMenuStatusOption(false));
        setShowStatusChangeModal(false);
      },
    });
  };

  const [selectedNewStatusState, setSelectedNewStatusState] = useState(0);
  const [fileStatusList, setFilesStatusList] = useState<IFile[]>();
  const [showStatusChangeModal, setShowStatusChangeModal] = useState(false);
  const [statusFileRemoved, setStatusFileRemoved] = useState(false);

  const handleCloseStatusModal = () => {
    dispatch(updateContextMenuStatusOption(false));
    setShowStatusChangeModal(false);
    setFilesStatusList([]);
    setStatusFileRemoved(false);
  };

  const handleShowStatusChangeModalCancel = () => {
    if (selectedNewStatusState > 0 || statusFileRemoved) {
      cancelChangeStatusModal();
    } else {
      handleCloseStatusModal();
    }
  };

  const resetFileStatusValues = () => {
    setFilesStatusList([]);
    dispatch(updateContextMenuStatusOption(false));
    setShowStatusChangeModal(false);
    setSelectedNewStatusState(0);
    syncGrid(true);
  };

  const handleNewStatusUpdate = () => {
    const isDeleteStatus = selectedNewStatusState === 6;

    if (isDeleteStatus) {
      if (hasLinkedFiles(selectedFileList)) {
        return;
      }
    }

    updateFileStatus(fileStatusList && fileStatusList.length > 0 ? fileStatusList?.map((file) => file.id) : selectedFileList?.map((file) => file.id), selectedNewStatusState)
      .then((response) => {
        successNotification([''], 'Status Update Successful');
        resetFileStatusValues();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'Status Update Failed');
        }
        logger.error('File ARea Module', 'Update Status', error);
      });

    dispatch(updateContextMenuStatusOption(false));
  };

  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [downloadType, setDownloadType] = useState<downloadTypes | undefined>(downloadTypes.individual);
  const [checkoutZip, setCheckoutZip] = useState(false);
  const [showCheckoutdModal, setshowCheckoutdModal] = useState(false);
  const [validatedCheckoutForm, setValidatedCheckoutForm] = useState(true);
  const [renameButtonEnable, setRenameButtonEnable] = useState(false);
  const renderToggleIcon = () => {
    return showPanel ? <MenuFoldOutlined /> : <MenuUnfoldOutlined />;
  };
  const handleOnToggleClicked = (event: React.MouseEvent<HTMLDivElement, MouseEvent>) => {
    toggleIconClicked(event);
    setShowPanel(!showPanel);
  };

  const handleOnDownloadModalCancel = () => {
    dispatch(updateContextMenuDownloadOption(false));
    setShowDownloadModal(false);
  };

  const displaydownloadModal = (downloadTypeInput: downloadTypes, display: boolean) => {
    setDownloadType(downloadTypeInput);
    setShowDownloadModal(display);
  };

  const [documentPropeties, displayDocumentPropeties] = useState(false);

  useEffect(() => {
    setCheckoutZip(false);
    if (checkout) {
      setCheckingOutFilesCount(selectedFileList.length);
    }
    setshowCheckoutdModal(checkout);
  }, [checkout, selectedFileList]);

  useEffect(() => {
    displaydownloadModal(downloadTypes.individual, download);
  }, [download]);

  useEffect(() => {
    setShowStatusChangeModal(status);
  }, [status]);

  useEffect(() => {
    setShowAssignModal(assign);
  }, [assign]);

  useEffect(() => {
    displayDocumentPropeties(propeties);
  }, [propeties]);

  useEffect(() => {
    if (reCategorize && !hasLinkedFiles(selectedFileList)) {
      setShowReCategorizeModal(true);
    }
    dispatch(updateContextMenuReCategorizeOption(false));
  }, [reCategorize]);

  useEffect(() => {
    if (moveFiles && !hasLinkedFiles(selectedFileList)) {
      setShowMoveFileModal(true);
    }
    dispatch(updateContextMenuMoveFilesOption(false));
  }, [moveFiles]);

  useEffect(() => {
    if (toBeDeleted && !hasLinkedFiles(selectedFileList)) {
      handleToBeDelete(selectedFileList);
    }
    dispatch(updateContextMenuToBeDeleted(false));
  }, [toBeDeleted]);

  useEffect(() => {
    if (copyLink && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {
      handleCopyLink(selectedFileList);
    }
    dispatch(updateContextMenuCopyLinkFiles(false));
  }, [copyLink]);

  useEffect(() => {
    setShowReNameFilesModal(renameFiles);
  }, [renameFiles]);

  useEffect(() => {
    handleShowLinkFilesModal(linkFiles);
  }, [linkFiles]);

  useEffect(() => {
    setShowUnlinkFilesModal(unLinkFiles);
  }, [unLinkFiles]);

  useEffect(() => {
    if (copySelectedFiles && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {
      showCopyFilesDialog(copySelectedFiles);
    }
    dispatch(updateContextMenuCopyFilesOption(false));
  }, [copySelectedFiles]);

  useEffect(() => {
    dispatch(getSavedFilters(TABLE_KEY, siteId));
  }, []);

  useEffect(() => {
    if (publish && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {
      const mapToCheckoutFiles = (selectedFiles: IFile[]): IPublishFiles[] => {
        const checkoutFileList = [] as IPublishFiles[];
        selectedFiles?.forEach((file) => {
          checkoutFileList.push({
            id: file.id,
            checked: true,
            title: file.title,
          });
        });
        return checkoutFileList;
      };
      setPublishFileList(mapToCheckoutFiles(selectedFileList));
      setShowPublishModal(true);
      dispatch(updateContextMenuPublishFiles(false));
    }
  }, [publish]);

  useEffect(() => {
    if (unpublish && (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList))) {
      const mapFiles = (selectedFiles: IFile[]): IUnpublishFiles[] => {
        const fileList = [] as IUnpublishFiles[];
        selectedFiles?.forEach((file) => {
          fileList.push({
            id: file.id,
            checked: true,
            title: file.title,
          });
        });
        return fileList;
      };
      setUnpublishFileList(mapFiles(selectedFileList));
      setShowUnpublishModal(true);
      dispatch(updateContextMenuUnpublishFiles(false));
    }
  }, [unpublish]);


  const showCopyFilesDialog = (value: boolean) => {
    if (value) {
      confirm({
        title: "File(s) will be copied. Do you wish to continue?",
        icon: <CopyOutlined />,
        okText: 'Yes',
        cancelText: 'No',
        onOk: () => {
          handleCopyFiles();
          dispatch(updateContextMenuCopyFilesOption(false));

        },
        onCancel: () => {
          dispatch(updateContextMenuCopyFilesOption(false));
        }

      });
    }
  }

  const handleOnCheckoutModalCancel = () => {
    confirm({
      title: 'Are you sure you want to discard the changes?',
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        dispatch(updateContextMenuCheckoutOption(false));
        setshowCheckoutdModal(false);
      },
    });
  };

  const handleFileDelete = (response: HTTPResponse<any>) => {
    if (response) {
      successNotification([''], 'File(s) Deleted Successfully');
      syncGrid(true);
      dispatch(updateContextMenuDeleteOption(false));
    } else {
      errorNotification([''], 'Deletion Failed');
      dispatch(updateContextMenuDeleteOption(false));
    }
  };

  const onClickOkDeleteFiles = useCallback((selectedFiles: IFile[]) => {
    const fileIdList: any[] = [];
    selectedFiles.forEach((file) => {
      fileIdList.push(file.id);
    });
    deleteFile(fileIdList)
      .then((response) => {
        handleFileDelete(response);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'Deletion Failed');
        }
        dispatch(updateContextMenuDeleteOption(false));
      });
  }, []);

  const handleToBeDelete = (selectedFiles: IFile[]) => {
    updateFileStatus(
      selectedFiles.map((e) => e.id),
      6
    ).then(() => syncGrid(true));
  };

  const handleCopyLink = (selectedFiles: IFile[]) => {
    let ids = selectedFiles.map((e) => e.id).join(',');
    copyToClipboard(ids);
    syncGrid(true);
  };

  const handleDeleteFiles = useCallback((selectedFiles: IFile[]) => {
    const totalFiles = selectedFiles.length;
    const publishedCount = selectedFiles.filter((file) => file.published).length;
    const fileLabel = `${totalFiles} ${totalFiles === 1 ? 'file' : 'files'}`;
    const message = `${fileLabel} will be deleted.${publishedCount > 0 ? ' Any published files would be unpublished when deleted.' : ''} Are you sure you want to proceed?`;
    confirm({
      title: message,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        onClickOkDeleteFiles(selectedFiles);
      },
      onCancel() {
        dispatch(updateContextMenuDeleteOption(false));
      },
    });
  }, []);

  useEffect(() => {
    if (deleteFiles && selectedFileList.length > 0) {
      if (deleteFiles && !hasLinkedFiles(selectedFileList)) {
        handleDeleteFiles(selectedFileList);
      }
      dispatch(updateContextMenuDeleteOption(false));
    }
  }, [deleteFiles, handleDeleteFiles, selectedFileList]);

  const setCheckoutCommonNote = (values: Store) => {
    values.files.forEach((value: ICheckoutFile) => {
      value.checkNote = values.commonNote;
    });
  };

  const formatReturnDates = (values: Store): ICheckoutFile[] => {
    const returnCheckoutValues = [] as ICheckoutFile[];
    values.files.forEach((value: ICheckoutFile) => {
      returnCheckoutValues.push({
        ...value,
        returnDate: value?.returnDate?.format('YYYY-MM-DD'),
      });
    });
    return returnCheckoutValues;
  };
  const handleSavedCheckoutFiles = () => {
    setshowCheckoutdModal(false);
    dispatch(updateContextMenuCheckoutOption(false));
    setDownloadType(checkoutZip ? downloadTypes.checkoutZip : downloadTypes.checkoutIndividual);
    setShowDownloadModal(true);
    fileDownloaded(true);
    dispatch(updateLoadGridOption(true));
    setTimeout(() => {
      dispatch(updateLoadGridOption(false));
    });
    if (emailForm.getFieldsValue().emailUser || emailForm.getFieldsValue().emailContact) {
      successNotification([''], 'Check-out Email Sent Successfully');
    }
  };

  const onCheckoutFiles = () => {
    form.validateFields().then(async (values) => {
      values.files = values.files.filter((file: ICheckoutFile) => file.checked);
      if (values.commonNote) {
        setCheckoutCommonNote(values);
      }
      setCheckedOuFilesDownload(values.files);
      const { hasError, errorCode } = await checkoutFiles(formatReturnDates(values));
      if (hasError) {
        errorCode && errorCode === FORBIDDEN_ERROR_CODE
          ? errorNotification([''], FORBIDDEN_ERROR_MESSAGE)
          : errorCode && errorCode === CHECKOUT_FAILED_ERRORCODE
            ? errorNotification([''], LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE)
            : errorNotification([''], 'Check-Out Failed');
      } else {
        handleSavedCheckoutFiles();
      }
    });
  };

  const downloadOptionsMenu = (
    <Menu className={styles.yjFilterMenuDropdownWrapper}>
      <Menu.Item
        hidden={selectedFileList && selectedFileList?.length <= 0}
        onClick={() => {
          displaydownloadModal(downloadTypes.individual, true);
        }}
        key="1"
      >
        Download Files
      </Menu.Item>
      <Menu.Item
        hidden={selectedFileList && selectedFileList?.length <= 1}
        onClick={() => {
          displaydownloadModal(downloadTypes.zip, true);
        }}
        key="2"
      >
        Download as a zip file
      </Menu.Item>
    </Menu>
  );

  const onSaveFilterTemplate = () => {
    dispatch(updateFilterTemplateSaved(true));

    const createFilterTemplateRequest: CreateFilterTemplateRequest = {
      name: newFilterName.trim(),
      content: {},
    };
    gridFilters.forEach((filter: GenericFilter) => {
      const field = filter['key'];
      if (filter?.isArray && (filter?.value || filter?.value === 0)) {
        if ((createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) === undefined) {
          (createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) = [];
        }
        (createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) = [
          ...(createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]),
          filter['value'],
        ];
      } else {
        createFilterTemplateRequest.content[field as keyof IFilterTemplate] = filter['value'];
      }
    });
    dispatch(saveFilterTemplate(createFilterTemplateRequest, TABLE_KEY, siteId));
    setShowAddNewFilterModal(false);
    setNewFilterName('');
  };

  const onCheckoutAction = () => {
    setCheckoutZip(false);
    setshowCheckoutdModal(true);
    setCheckingOutFilesCount(selectedFileList.length);
  };

  const onRefreshGrid = () => {
    syncGrid(true);
    dispatch(updateGridHasUpdates(false));
  };
  const onPublishAction = () => {
    dispatch(updateContextMenuPublishFiles(true));
  };
  const onUnpublishAction = () => {
    dispatch(updateContextMenuUnpublishFiles(true));
  };

  const handleMoveFilesActionBar = () => {
    if (!showMoveFiles) return;
    if (!hasLinkedFiles(selectedFileList)) {
      setShowMoveFileModal(true);
    }
  };

  const handleReCategorizeActionBr = () => {
    if (!showReCategorize) return;
    if (!hasLinkedFiles(selectedFileList)) {
      setShowReCategorizeModal(true);
    }
  };

  const handleCopyFilesActionBar = () => {
    if (!showCopy) return;
    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {
      showCopyFilesDialog(true);
    }
  };

  const handleDeleteActionBar = () => {
    if (!showDelete) return;
    if (!hasLinkedFiles(selectedFileList)) {
      handleDeleteFiles(selectedFileList);
    }
  };

  const handleToBeDeletedActionBar = () => {
    if (!showToBeDelete) return;
    if (!hasLinkedFiles(selectedFileList)) {
      handleToBeDelete(selectedFileList);
    }
  };

  const handleCopyLinkActionBar = () => {
    if (!showCopyLink) return;
    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {
      handleCopyLink(selectedFileList);
    }
  };

  const handleManageTags = () => {
    if (!hasLinkedFiles(selectedFileList)) {
      setShowTagsManageModal(true);
    }
  };

  const handlePublishActionBar = () => {
    if (!showPublish) return;
    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {
      onPublishAction();
    }
  };

  const handleUnpublishActionBar = () => {
    if (!showUnpublish) return;
    if (originalFileAreaWithLinked || !hasLinkedFiles(selectedFileList)) {
      onUnpublishAction();
    }
  };

  const SET_TIMEOUT_ASSIGNEE = 500;
  const handleCopyFiles = () => {
    const selectedFileUploadReferenceList = selectedFileList?.map((file: IFile) => file.uploadReference);

    copyFiles(
      selectedFileUploadReferenceList
    )
      .then((response) => {
        successNotification([''], 'Copied Successfully');
        setTimeout(() => {
          syncGrid(true);

        }, SET_TIMEOUT_ASSIGNEE);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'Copy Failed');
        }
        logger.error('File Area Module', 'Copy Files', error);
      });
  };

  return (
    <>
      <DocumentPropeties
        file={selectedFileList[0]}
        onCloseDrawer={() => {
          displayDocumentPropeties(false);
          dispatch(updateContextMenuPropetiesoption(false));
        }}
        displayDrawer={documentPropeties}
      />

      {/* Re-Categorize option Drawer */}

      <ReCategorize
        onSuccess={() => syncGrid(true)}
        selectedFiles={selectedFileList}
        onClosePopup={handleReCategorizeFileCancel}
        options={options}
        form={assignOptionForm}
        onFolderTreeChange={onFolderTreeChange}
        showReCategorizeModal={showReCategorizeModal}        // Provide the 'options' prop
        binderId={binderId}
      />


      {/* Move Files drawyer */}
      <MoveFiles
        siteId={siteId}
        binderId={binderId}
        onSuccess={() => syncGrid(true)}
        selectedFiles={selectedFileList}
        onClosePopup={handleCancel}
        options={options}
        form={assignOptionForm}
        onFolderTreeChange={onFolderTreeChange}
        showMoveFileModal={showMoveFileModal}
      />

      {/* File ReName Drawer */}
      <ReNameFiles
        onSuccess={() => syncGrid(true)}
        selectedFiles={selectedFileList}
        onClosePopup={handleRenameCancel}
        options={options}
        form={assignOptionForm}
        showReNameFilesModal={showReNameFilesModal}
      />


      {/* Add New Filter Drawer */}
      <Drawer
        visible={showAddNewFilterModal}
        title={'Save as a New Filter'}
        width={700}
        onClose={handleAddNewFilterCancel}
        className={"yjDrawerPanel"}
        footer={[
          <Button key="back" type="default" onClick={handleAddNewFilterCancel}>
            cancel
          </Button>,
          <Button disabled={newFilterName.trim().length < 1 || !isNameValid} key="submit" type="primary" onClick={() => onSaveFilterTemplate()}>
            Save
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <FilterTemplateManagementSave isNameValid={isNameValid} newFilterName={newFilterName} onFilterNameChangeHandler={(name: string) => onChangeSaveNewFilterName(name)} />
        </div>
      </Drawer>
      {/* Manage existing Filters Drawer */}
      <Drawer
        visible={showEditFiltersModal}
        title={'Manage Filter'}
        width={700}
        onClose={() => setShowEditFiltersModal(false)}
        className={"yjDrawerPanel"}
        footer={[
          <Button key="back" type="default" onClick={() => setShowEditFiltersModal(false)}>
            cancel
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <FilterTemplateManagementEdit tableKey={TABLE_KEY} groupedValue={siteId} />
        </div>
      </Drawer>

      {/* Assign option Drawer */}


      <AssignOption
        formRef={assignOptionForm}
        siteId={siteId}
        selectedFiles={selectedFileList}
        onClosePopup={() => handleShowAssignModalCancel(true)}
        showAssignModal={showAssignModal}
        onSuccess={() => syncGrid(true)}
        form={assignOptionForm}
      />



      {/*  Tag Management Drawer */}
      <TagManagementDrawer
        visible={showTagsManageModal}
        onClose={handleShowTagManageModalCancel}
        binderId={binderId}
        syncGrid={() => syncGrid(true)}
      />

      {/*  Status Change Drawer */}
      <Drawer
        width={700}
        visible={showStatusChangeModal}
        title={'Update Status'}
        onClose={handleShowStatusChangeModalCancel}
        className={"yjDrawerPanel"}
        footer={[
          <Button key="back" type="default" onClick={handleShowStatusChangeModalCancel}>
            cancel
          </Button>,
          <Button key="update" type="primary" onClick={handleNewStatusUpdate} disabled={selectedNewStatusState === 0}>
            update
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <ChangeStatus
            onFilesChange={(fileList, onMounted, onAllRemoved) => {
              if (onAllRemoved) {
                handleCloseStatusModal();
                return;
              }
              if (!onMounted && !onAllRemoved) {
                setStatusFileRemoved(true);
              }
              setFilesStatusList(fileList);
            }}
            onFinish={handleNewStatusUpdate}
            form={changeStatusForm}
            selectedFiles={selectedFileList}
            onNewStatusSelect={setSelectedNewStatusState}
          />
        </div>
      </Drawer>

      {/*  Download Option Menu Drawer */}
      <Modal
        visible={showDownloadModal}
        title={'Download Files'}
        maskClosable={false}
        destroyOnClose={true}
        className="yjCommonModalSmall"
        onCancel={handleOnDownloadModalCancel}
        footer={[
          <Button onClick={handleOnDownloadModalCancel} key="submit" type="primary">
            Done
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <DownloadModal
            hasDownloaded={(hasDownloaded: boolean) => {
              if (hasDownloaded) {
                setShowDownloadModal(false);
              }
            }}
            selectedFiles={downloadType === downloadTypes.checkoutIndividual || downloadType === downloadTypes.checkoutZip ? checkedOuFilesDownload : selectedFileList}
            downloadType={downloadType}
          />
        </div>
      </Modal>

      {/*  Checkout Option Menu Drawer */}
      <Drawer
        destroyOnClose={true}
        key={'checkoutModal'}
        visible={showCheckoutdModal}
        title={'Check-out Files'}
        onClose={handleOnCheckoutModalCancel}
        width={700}
        className={"yjDrawerPanel"}
        footer={[
          <>
            {selectedFileList.length > 1 && checkingOutFilesCount > 1 && (
              <Checkbox
                key={1}
                onChange={(e: CheckboxChangeEvent) => {
                  setCheckoutZip(e.target.checked);
                }}
              >
                As ZIP Files
              </Checkbox>
            )}
          </>,

          <Button key={'cancelCheckout'} onClick={handleOnCheckoutModalCancel} type="default">
            Cancel
          </Button>,
          <Button disabled={!validatedCheckoutForm} key={'openCheckout'} onClick={onCheckoutFiles} type="primary">
            check-out
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <CheckoutOption
            validatedCheckoutEmail={(validated) => {
              setValidatedCheckoutForm(validated);
            }}
            onCloseModal={(closeModal: boolean) => {
              if (closeModal) {
                setshowCheckoutdModal(false);
                dispatch(updateContextMenuCheckoutOption(false));
              }
            }}
            onFileRemoved={(fileCount: number) => {
              setCheckingOutFilesCount(fileCount);
            }}
            form={form}
            emailForm={emailForm}
            onFinish={onCheckoutFiles}
            fileList={selectedFileList ? selectedFileList : []}
          />
        </div>
      </Drawer>

      {/*  publish files Drawer */}
      <PublishFilesDrawer
        isLoading={isLoading}
        visible={showPublishModal}
        publishFileList={publishFileList}
        onClose={cancelPublishModal}
        onFileListChange={setPublishFileList}
        onPublish={handlePublishFiles}
        expirationDate={publishFileExpiration}
        setExpirationDate={setPublishFileExpiration}
      />

      {/*  unpublish files Drawer */}
      <UnpublishFilesDrawer
        isLoading={isLoading}
        visible={showUnpublishModal}
        unpublishFileList={unpublishFileList}
        onClose={cancelUnpublishModal}
        onFileListChange={setUnpublishFileList}
        onUnpublish={handleUnpublishFiles}
      />

      {/* File Link Drawer */}
      {showLinkFileModal && (
        <LinkFilesDrawer linkFilesLoading={isLoading} onClosePopup={handleShowLinkFilesModalCancel}
          onItemSelect={handleLinkFilesUpdateDetails} onSuccess={handleLinkFileUpdate}
          selectedFiles={selectedFileList} showDrawer={showLinkFileModal} />
      )}

      {/* File Unlink Drawer */}
      {showUnlinkFilesModal && (
        <UnlinkFilesDrawer unlinkFilesLoading={isLoading} onUnlinkChange={setSelectedUnlinkFiles} onClosePopup={handleShowUnlinkFilesModalCancel}
          onSuccess={handleFileUnlinkSubmit} selectedFiles={selectedFileList} showDrawer={showUnlinkFilesModal} />
      )}

      <div className={styles.yjFileAreaMainActionPanel}>
        <div className={styles.yjActionListContainer}>
          <div onClick={handleOnToggleClicked} className={styles.yjFileAreaCollapsibleTriggerWrapper}>
            {renderToggleIcon()}
          </div>
          <div hidden={!userPermission.privDMSCanViewFileArea || !hasPermission(folderTree, 'FILE_AREA_DOWNLOAD')} className={`${styles.yjActionListWrapper} ${!showDownload ? styles.disabled : ""}`} aria-disabled={!showDownload} tabIndex={showDownload ? 0 : -1}>
            <Dropdown getPopupContainer={() => document.getElementById('downloadOptionsMenu') as HTMLElement} overlay={downloadOptionsMenu} trigger={['click']}>
              <div>
                <Button>
                  <div id="downloadOptionsMenu">
                    <>
                      <DownloadOutlined /> <span>Download</span>
                    </>
                  </div>
                </Button>
              </div>
            </Dropdown>
          </div>
          {<>
            {/* <Tooltip placement="topLeft" title={'This feature is coming soon'} color="#78bf59">
            <div hidden={!userPermission.privDMSCanViewFileArea} className={styles.yjActionListWrapper}>
              <MailOutlined /> <span>Email</span>
            </div>
          </Tooltip> */}
            <div className={`${styles.yjActionListWrapper} ${!showAssign ? styles.disabled : ""}`} onClick={() => showAssign ? setShowAssignModal(true) : undefined} aria-disabled={!showAssign} tabIndex={showAssign ? 0 : -1} hidden={!userPermission.privDMSCanManageFileAssign || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} >
              <AuditOutlined /> <span>Assign</span>
            </div>
            <div className={`${styles.yjActionListWrapper} ${!showStatus ? styles.disabled : ""}`} onClick={() => showStatus ? setShowStatusChangeModal(true) : undefined} aria-disabled={!showStatus} tabIndex={showStatus ? 0 : -1} hidden={!userPermission.privDMSCanManageFileStatus || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} >
              <FileDoneOutlined /> <span>Status</span>
            </div>

            <div className={`${styles.yjActionListWrapper} ${!showMoveFiles ? styles.disabled : ""}`} onClick={handleMoveFilesActionBar} aria-disabled={!showMoveFiles} tabIndex={showMoveFiles ? 0 : -1}
              hidden={!userPermission.privDMSCanMoveFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} >
              <DragOutlined /> <span>Move</span>
            </div>

            <div className={`${styles.yjActionListWrapper} ${!showReCategorize ? styles.disabled : ""}`} hidden={!userPermission.privDMSCanRecategorizeFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}
              onClick={handleReCategorizeActionBr} aria-disabled={!showReCategorize} tabIndex={showReCategorize ? 0 : -1}
            >
              <ApartmentOutlined /> <span>Re-Categorize</span>
            </div>
            <div hidden={!userPermission.privDMSCanRenameFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showReName ? styles.disabled : ""}`}
              onClick={() => showReName ? setShowReNameFilesModal(true) : undefined} aria-disabled={!showReName} tabIndex={showReName ? 0 : -1}>
              <EditOutlined /> <span>ReName</span>

            </div>
            <div hidden={!userPermission.privDMSCanCopyFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showCopy ? styles.disabled : ""}`}
              onClick={handleCopyFilesActionBar} aria-disabled={!showCopy} tabIndex={showCopy ? 0 : -1}>
              <CopyOutlined /> <span>Copy</span>
            </div>
            <div hidden={!userPermission.privDMSCanCheckInCheckOutInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showCheckout ? styles.disabled : ""}`} aria-disabled={!showCheckout} tabIndex={showCheckout ? 0 : -1}>
              <Button onClick={() => showCheckout ? onCheckoutAction() : undefined}>
                <ContainerOutlined /> <span>Check-out</span>
              </Button>
            </div>
            <div hidden={!userPermission.privDMSCanPublishUnpublishInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showPublish ? styles.disabled : ""}`} aria-disabled={!showPublish} tabIndex={showPublish ? 0 : -1}>
              <Button onClick={handlePublishActionBar}>
                <MdOpenInNew />
                <span style={{ marginTop: '-3px' }}>Publish</span>
              </Button>
            </div>
            <div hidden={!userPermission.privDMSCanPublishUnpublishInternalFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')} className={`${styles.yjActionListWrapper} ${!showUnpublish ? styles.disabled : ""}`} aria-disabled={!showUnpublish} tabIndex={showUnpublish ? 0 : -1}>
              <Button onClick={handleUnpublishActionBar}>
                <MdOpenInNew style={{ transform: 'rotate(180deg)' }} />
                <span style={{ marginTop: '-3px' }}>Unpublish</span>
              </Button>
            </div>
            <div hidden={!userPermission.privDMSCanDeleteFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}
              className={`${styles.yjActionListWrapper} ${!showDelete ? styles.disabled : ""}`}
              onClick={handleDeleteActionBar} aria-disabled={!showDelete} tabIndex={showDelete ? 0 : -1}>
              <DeleteOutlined /> <span>Delete</span>
            </div>
            <div
              hidden={!hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') || !userPermission.privDMSCanViewFileArea}
              onClick={() => showPropeties && (displayDocumentPropeties(true), dispatch(updateContextMenuPropetiesoption(true)))} aria-disabled={!showPropeties} tabIndex={showPropeties ? 0 : -1}
              className={`${styles.yjActionListWrapper} ${(!showPropeties || !userPermission.privDMSCanViewFileArea) ? styles.disabled : ""}`}
            >
              <SettingOutlined className={`yJFileAreaRow`} />
              <span className={` yJFileAreaRow`}>Properties</span>
            </div>
            <div hidden={!userPermission.privDMSCanMarkFilesAsToBeDeleted || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}
              className={`${styles.yjActionListWrapper} ${(!userPermission.privDMSCanMarkFilesAsToBeDeleted || !showToBeDelete) ? styles.disabled : ""}`}
              onClick={handleToBeDeletedActionBar} aria-disabled={!showToBeDelete} tabIndex={showToBeDelete ? 0 : -1}>
              <DeleteOutlined /> <span>To be Deleted</span>
            </div>
            <div hidden={!userPermission.privDMSCanViewFileSourceLink}
              className={`${styles.yjActionListWrapper} ${(!userPermission.privDMSCanViewFileSourceLink || !showCopyLink) ? styles.disabled : ""}`} onClick={handleCopyLinkActionBar} aria-disabled={!showCopyLink} tabIndex={showCopyLink ? 0 : -1}>
              <ShareAltOutlined /> <span>Copy Link</span>
            </div>
            <div className={`${styles.yjActionListWrapper} ${!showLinkFiles ? styles.disabled : ""}`} onClick={() => showLinkFiles ? handleShowLinkFilesModal(true) : undefined} aria-disabled={!showLinkFiles} tabIndex={showLinkFiles ? 0 : -1}
              hidden={!hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') || !userPermission.privDMSCanLinkUnlinkFiles} >
              <LinkOutlined /> <span>Link Files</span>
            </div>
            <div hidden={!userPermission.privDMSCanLinkUnlinkFiles || !hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS')}
              className={`${styles.yjActionListWrapper} ${!showUnlinkFiles ? styles.disabled : ""}`} onClick={() => showUnlinkFiles ? setShowUnlinkFilesModal(true) : undefined} aria-disabled={!showUnlinkFiles} tabIndex={showUnlinkFiles ? 0 : -1}>
              <DisconnectOutlined /> <span>Unlink Files</span>
            </div>
          </>}
        </div>
        <div className={styles.yjActionButtonsContainer}>
          <div className={styles.yjActionButtonsLeftCorner}>
            <div className={styles.yjFileAreaFilterDropdownWrapper}>
              <GridRefreshIcon hasUpdates={hasUpdates} onRefreshGrid={onRefreshGrid} />
              {showFiltersButton ? (
                <Button icon={<FilterOutlined />} className={styles.yjFiltersButton} onClick={() => setShowFiltersButton(false)}>
                  Filters <DownOutlined />
                </Button>
              ) : (
                <Select
                  showSearch
                  style={{ width: 200 }}
                  placeholder="Select a filter"
                  optionFilterProp="children"
                  autoFocus={true}
                  defaultOpen={true}
                  onBlur={() => setShowFiltersButton(true)}
                  showArrow={false}
                  onSelect={onClickSavedFilter}
                  notFoundContent={`No Results Found`}
                  filterOption={onSearchSavedFilters}
                >
                  {sortedSavedFilterList.length > 0 && (
                    <Option disabled={true} key={-1} value={'Manage Filters'}>
                      <Button className={styles.yjDropdownManageFilters} type="primary" onClick={() => setShowEditFiltersModal(true)}>
                        Manage Filters
                      </Button>
                    </Option>
                  )}

                  {sortedSavedFilterList &&
                    sortedSavedFilterList.map((savedFilter: SavedFilterTemplate) => {
                      return (
                        <Option key={savedFilter.id} value={savedFilter.id}>
                          {savedFilter.name}
                        </Option>
                      );
                    })}
                </Select>
              )}
            </div>
            <div className={styles.yjFileAreaFilterActionButtonWrapper}>
              {showFilterSaveButton && (
                <Button disabled={filter_template_saved} type="primary" className={styles.yjSaveFilterButton} onClick={() => setShowAddNewFilterModal(true)}>
                  Save as a new filter
                </Button>
              )}
            </div>
          </div>
          <div className={styles.yjActionButtonsRightCorner}>
          {additionalActionPanel}
            <Button 
              hidden={!userPermission.privDMSCanManageFileTags} 
              disabled={!showManageTags} 
              aria-disabled={!showManageTags}
              tabIndex={showManageTags ? 0 : -1}
              onClick={handleManageTags}
              className={styles.yjManageTagsButton}>
              Manage Tags
            </Button>
            <ColumnFilter tableKey={TABLE_KEY} />
            {showFilter && <ClearFilter />}
          </div>
        </div>
      </div>
    </>
  );
};
