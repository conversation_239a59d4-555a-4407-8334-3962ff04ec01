import React from "react";
import { Tag , Tooltip } from "antd";
import useFilter from "../hooks/useFilter";

import styles from "./index.module.less";

export default ({
  filterKey,
  filterName,
  isArray,
  value,
  displayText,
}: any) => {
  const filters = useFilter(filterKey, filterName, isArray);

  const removeFilter = () => {
    filters.removeFilter(value, false);
  };

  return (
    <Tag
      className={styles.yjFilterTags}
      key={`${filterKey}-${JSON.stringify(value)}`}
      closable
      onClose={removeFilter}
    >
      <Tooltip title={displayText}>
        <span
        className={styles.yjFilterTagText}
      >{`${filterName} : ${displayText}`}
      </span>
      </Tooltip>
    </Tag>
  );
};
