@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjTableHeaderWrapper {
  padding: 1em;

  .flex-mixin(center, flex, flex-end);
}

.yjFilterCloudWrapper {
  flex-wrap: wrap;
  margin: 0 1em;
  //max-height: 8vh;
  overflow-x: hidden;
  overflow-y: auto;

  .yjFilterTags {
    background-color: @color-bg-grid-filterby-tag;
    border-color: @color-border-grid-filterby-tag;
    border-radius: 3em;
    color: @color-font-grid-filterby-tag;
    display: flex;
    float: left;
    margin-bottom: 8px;
    overflow: hidden;
    padding: .3em .8em;
    text-overflow: ellipsis;
    white-space: nowrap;

    span {
      color: @color-font-grid-filterby-tag-icon;
      margin-top: 4px;
    }

    .yjFilterTagText {
      margin-top: 0;
      max-width: 275px;
      overflow: hidden;
      padding-right: 10px;
      position: relative;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .yjFilterTextLabel {
    color: #ffffff;
    margin-right: 1em;
    font-size: small;
    position: relative;
    text-transform: uppercase;
    top: -3px;

    .font-mixin(@font-primary, @yjff-semibold);
  }

  .flex-mixin(center, flex, flex-start);
}

.yjFilterArea {
  background-color: #24303b;
  box-shadow: 0 6px 7px -6px @color-filter-area-shadow;
}

.yjClearFilterBtn {
  background: transparent;
  border: 1px solid @color-white;
  border-radius: .2em;
  color: @color-white;
  font-size: ceil(@font-size-base / 1.2);
  margin-left: 5px;
  text-transform: @yj-transform;

  &:hover {
    background-color: transparent;
    opacity: 1;
  }
}

.yjSelectDeSelect {
  font-weight: 700;
}

.yjFiltersButton {
  background: transparent;
  border: 1px solid #fff;
  color: #fff;
  font-size: @font-size-base / 1.2;
  margin-left: 0;
  text-transform: @yj-transform;

  &:hover {
    background: @color-bg-filearea-actionpanel;
    border: 1px solid @color-border-filter-dropdown-btn;
    color: @color-font-filearea-action-list;
  }
}

.yjFilterManagementModule {
  flex-basis: 100%;
  align-items: center;
  display: -ms-flex;
  display: flex;
  justify-content: flex-start;
}