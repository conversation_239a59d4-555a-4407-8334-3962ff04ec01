import React from "react";
import {Modal} from "antd";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { MemoryRouter } from 'react-router-dom';

import InterfaceManagement from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";

const CustomInterfaceManagement = (props) => {
    return (
        <MemoryRouter>
            <InterfaceManagement {...props} />
        </MemoryRouter>
    );
}
describe("InterfaceManagement Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const imComponent = mount(<CustomInterfaceManagement />);
        expect(imComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const imComponent = renderer.create(<CustomInterfaceManagement />).toJSON();
        expect(imComponent).toMatchSnapshot();
    });

    it("should have a Modal element",() => {
        const imComponent = mount(<CustomInterfaceManagement />);
        expect(imComponent.find(Modal)).toHaveLength(1);
    });

    it("should have a PageTitle component",() => {
        const imComponent = mount(<CustomInterfaceManagement />);
        expect(imComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const imComponent = mount(<CustomInterfaceManagement />);
        expect(imComponent.find(PageContent)).toHaveLength(1);
    });
});




