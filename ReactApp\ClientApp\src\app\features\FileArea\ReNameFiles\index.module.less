@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjCustomFormItem {
    margin-bottom: 5px;
    padding-top: 15px;
}

.yjMoverArrow {
    align-items: center;
    display: flex;
    justify-content: center;
}

.yjInfoIcon {
    color: @color-button-primary;
    font-size: 20px;
    position: relative;
    top: -4px;
}

.fileReNameLabel {
    background: #ffffff;
    color: #0e678e;
    font-size: 12.22px;
    height: 22px;
    text-transform: uppercase;
    font-family: 'Roboto Regular SemiBold';
    padding: 12px;
}

.reNameInputArea {
    max-height: 25px;
}


:global(.ant-table-cell) {
    vertical-align: top ;
    padding-top: 8px ;
}

:global(.yjDeteleFile) {
    margin-top: 5px ;
}