import React, { useState } from "react";
import { withRouter } from "react-router-dom";
import { Button, Select, Col, Skeleton, Tooltip } from "antd";

import DashboardV1 from "../V1/";
import DashboardV2 from "../V2/";
import DashboardV3 from "@app/features/DashboardV3";
import PageTitle from "@app/components/PageTitle";
import NonAuthorized from "@app/components/NonAuthorized";
import styles from "./index.module.less";
import { PageContent } from "@app/layouts/MasterLayout";
import config from "@app/utils/config";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from "@app/utils/logger";

const { Option } = Select;

const DASHBOARD_V1 = "DASHBOARD_V1";
const DASHBOARD_V2 = "DASHBOARD_V2";
const DASHBOARD_V3 = "DASHBOARD_V3";
const Page = (props: any) => {
  const [currentDashboard, setCurrentDashboard] = useState(DASHBOARD_V1);
  const [isChannelLoaded, setIsChannelLoaded] = useState<boolean>(false);
  const [channelId, setChannelId] = useState<string | undefined>();

  const dashboardMap: any = {
    DASHBOARD_V1: <DashboardV1 channelId={channelId} {...props} />,
    DASHBOARD_V2: <DashboardV2 />,
    DASHBOARD_V3: <DashboardV3 channelId={channelId} />,
  };


  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    /**
     * Will add the keyvalue if dropdown still visible
     */
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const options = {
      limit: 10,
      offset: page - 1,
      ...transformFilters,
    }
    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].channels, options)
      .then((res: any) => {
        logger.info('SideSelection', 'getPaginatedRecords', res.data);
        if (res.data) {
          return res.data;
        } else {
          logger.error('SideSelection', 'getPaginatedRecords', res.error);
          return []
        }
      })
      .catch((error: any) => {
        logger.error('SideSelection', 'getPaginatedRecords', error);

        return [];
      });
  };


  return (
    <>
      <PageTitle title={"Dashboard"}>
        {/* <Tooltip
          placement="topLeft"
          title={"This feature is coming soon"}
          color="#78bf59"
        >
          <Button type="primary">Edit</Button>
        </Tooltip>
        <Tooltip
          placement="topLeft"
          title={"This feature is coming soon"}
          color="#78bf59"
        >
          <Button type="primary">Manage</Button>
        </Tooltip>
        <Tooltip
          placement="topLeft"
          title={"This feature is coming soon"}
          color="#78bf59"
        >
          <div className={styles.yjDropdownButton}>
            <Select
              id='select-dashboard'
              data-testid='select-dashboard'
              onChange={(value) => setCurrentDashboard(value)}
              defaultValue={DASHBOARD_V1}
            >
              <Option value={DASHBOARD_V1}>Dashboard V1</Option>
              <Option value={DASHBOARD_V2}>Dashboard V2</Option>
              <Option value={DASHBOARD_V3}>Dashboard V3</Option>
            </Select>
          </div>
        </Tooltip> */}
        <Col span={8} className={styles.yjDashboardChannelSelector}>
          <InfinitySelect
            getPaginatedRecords={getPaginatedRecords}
            formatValue={(value) => {
              return `${value.name}`;
            }}
            isDefault={true}
            notFoundContent="No Offices Available"
            notLoadContent="Failed to load values in office dropdown"
            value={channelId}
            onChange={(e) => {
              setChannelId(e);
            }}
            placeholder="Office Name"
            onLoaded={(isLoaded: boolean) => {
              setIsChannelLoaded(isLoaded);
            }}
          />
        </Col>
      </PageTitle>

      {channelId ? (
        <PageContent>
          {dashboardMap[currentDashboard]}
        </PageContent>
      ) : isChannelLoaded ? (
        <NonAuthorized
          styleClassName={styles.yjNonAuthorizedDashboardWrapper}
          title={"You do not have the permission to access any offices"}
          subTitle={"Contact your organization's administrator for assistance"}
        />
      ) : (
        <Skeleton active={true} />
      )}
    </>
  );
};

export default withRouter(Page);
