import React from "react";
import {Collapse} from "antd";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import SubAccordion from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

const { Panel } = Collapse;
jest.mock("../index.module.less", () => ({
    yjSubAccordion: "yjSubAccordion",
}));

describe("SubAccordion Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const saComponent = shallow(<SubAccordion />);
        expect(saComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const saComponent = renderer.create(<SubAccordion />).toJSON();
        expect(saComponent).toMatchSnapshot();
    });

    it("should have  Collapse elements",() => {
        const saComponent = shallow(<SubAccordion />);
        expect(saComponent.find(Collapse)).toHaveLength(2);
    });

    it("should have Panel elements",() => {
        const saComponent = shallow(<SubAccordion />);
        expect(saComponent.find(Panel)).toHaveLength(6);
    });

    it("should have  p elements",() => {
        const saComponent = shallow(<SubAccordion />);
        expect(saComponent.find("p")).toHaveLength(5);
    });

    it("should have a div elements",() => {
        const saComponent = shallow(<SubAccordion />);
        expect(saComponent.find(".yjSubAccordion")).toHaveLength(1);
    });
});

