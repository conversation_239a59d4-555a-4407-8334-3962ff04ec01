import fileDetailsReducer from "../fileDetailsReducer";
import * as actions from "../../actionTypes/fileDetailsActionTypes";

describe("File details reducer test suite", () => {
  it("should handle SET_FILE_STATUSES action", () => {
    const action = {
      type: actions.SET_FILE_STATUSES,
      fileStatuses: [1],
    };
    expect(fileDetailsReducer({}, action)).toEqual({
      options: { fileStatuses: [1] },
    });
  });

  it("should handle SET_FILE_YEARS action", () => {
    const action = {
      type: actions.SET_FILE_YEARS,
      years: [1990],
    };
    expect(fileDetailsReducer({}, action)).toEqual({
      options: { years: [1990] },
    });
  });

  it("should handle SET_PROJECTS action", () => {
    const action = {
      type: actions.SET_PROJECTS,
      projects: ["A"],
    };
    expect(fileDetailsReducer({}, action)).toEqual({
      options: { projects: ["A"] },
    });
  });

  it("should handle SET_PROJECTS action", () => {
    const action = {
      type: actions.SET_USERS,
      users: [3, 4],
    };
    expect(fileDetailsReducer({}, action)).toEqual({
      options: { users: [3, 4] },
    });
  });

  it("should handle SET_FILE_TYPES action", () => {
    const action = {
      type: actions.SET_FILE_TYPES,
      fileTypes: ["EXE"],
    };
    expect(fileDetailsReducer({}, action)).toEqual({
      options: { fileTypes: ["EXE"] },
    });
  });

  it("should handle SET_IS_OPTIONS_FETCHED action", () => {
    const actionTrue = {
      type: actions.SET_IS_OPTIONS_FETCHED,
      isOptionsFetched: true,
    };
    expect(fileDetailsReducer({}, actionTrue)).toEqual({
      isOptionsFetched: true,
    });

    const actionFalse = {
      type: actions.SET_IS_OPTIONS_FETCHED,
      isOptionsFetched: false,
    };
    expect(fileDetailsReducer({}, actionFalse)).toEqual({
      isOptionsFetched: false,
    });
  });

  it("should handle SET_SUCCESSED_FILES action", () => {
    const actionTrue = {
      type: actions.SET_SUCCESSED_FILES,
      successedFiles: true,
    };
    expect(fileDetailsReducer({}, actionTrue)).toEqual({
      successedFiles: true,
    });

    const actionFalse = {
      type: actions.SET_SUCCESSED_FILES,
      successedFiles: false,
    };
    expect(fileDetailsReducer({}, actionFalse)).toEqual({
      successedFiles: false,
    });
  });

  it("should handle SET_PENDING_SAVE action", () => {
    const actionTrue = {
      type: actions.SET_PENDING_SAVE,
      pendingSave: true,
    };
    expect(fileDetailsReducer({}, actionTrue)).toEqual({
      pendingSave: true,
    });

    const actionFalse = {
      type: actions.SET_PENDING_SAVE,
      pendingSave: false,
    };
    expect(fileDetailsReducer({}, actionFalse)).toEqual({
      pendingSave: false,
    });
  });
});
