import { render } from '@testing-library/react';
import ContextMenu from '@app/components/ContextMenu';
import React from 'react';
import { FileAreaActionPanel } from '@app/features/FileArea/FileAreaActionPanel';

describe('File Area - Copy Link Test Suite', () => {
  it('should have the copy link option on context menu', async () => {
    const component = await render(
      <ContextMenu
        positon={{
          x: undefined,
          y: undefined,
        }}
        visible={true}
      />
    ).findAllByText('Copy Link');

    expect(component.length).toBe(1);
  });

  it('should have the copy link option on action panel', async () => {
    const component = await render(
      <FileAreaActionPanel
        siteId={''}
        selectedFileList={[]}
        toggleIconClicked={function (event: React.MouseEvent<HTMLDivElement, MouseEvent>): void {
          throw new Error('Function not implemented.');
        }}
      />
    ).findAllByText('Copy Link');

    expect(component.length).toBe(1);
  });
});
