import React from "react";
import { Tag } from "antd";
import { LockOutlined, CheckCircleOutlined, StopOutlined } from "@ant-design/icons";
import { stat } from "fs";

const statusMap: Record<string, { color: string;icon:any;label:string }> = {
    "Active": { color: "#0C6A00",icon:<CheckCircleOutlined />,label:"Active" },
    "Inactive": { color: "#C8102E",icon:<StopOutlined/> ,label:"Inactive"},
    "Active - LH": { color: "#BF6E00",icon:<LockOutlined />,label:"Active - Legal Hold" },
    "Inactive - LH": { color: "#666E76",icon:<LockOutlined /> ,label:"Inactive - Legal Hold" },
};

interface IProps {
    value: string
}

export default ({ value }: IProps) => {
    
    const status = statusMap[value];

    return (
        <div style={{ textAlign: 'left', color: status.color }}>
        
        {status.icon}  {status.label} 
            
        </div>
    );
}