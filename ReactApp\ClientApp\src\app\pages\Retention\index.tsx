import React, { useRef, useState } from 'react';
import { Button } from 'antd';
import PageTitle from '@app/components/PageTitle';
import { PlusOutlined } from '@ant-design/icons/lib';
import PageContent from '@app/components/PageContent';
import { useForm } from 'antd/lib/form/Form';
import { createRetention, updateRetention } from '@app/api/retentionService';
import logger from '@app/utils/logger';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import RetentionGrid from "@app/features/Retention/RetentionGrid";
import RetentionAddEditDrawer from "@app/features/Retention/RetentionAddEditDrawer";

const RetentionPage = (props: any) => {
    let [form] = useForm();
    // const { userPermission } = useSelector( (state: RootState) => state.userManagement );
    const ref = useRef<{ refresh: () => void }>(null);
    let [showDrawer, setShowDrawer] = useState(false);


    const refresh = () => {
        if (ref.current) {
            ref.current.refresh();
        }
    };
    const openCreateRetentionDrawer = (data: any) => {
        form?.setFieldsValue({
            id: data?.id,
            name: data?.name,
            description: data?.description,
            retentionStatus: !data?.id ? true : data?.status?.value === 1 ? true : false,
            hide: data?.hide,
            retentionFolders: data?.retentionFolders,
        })
    };

    const closeDrawer = () => {
        form.resetFields();
    };

    const formatFolderData = (data: any[]): any[] => {
        return data.map((e) => (e.children ? {
            id: e.id,
            name: e.folderName,
            retention: 0,
            subFolders: formatFolderData(e.children)
        } : { id: e.id, name: e.folderName, retention: 0 }));
    };

    const onSuccess = (values: any) => {
        values.retentionFolders = formatFolderData(values.retentionFolders);
        values.retentionStatus = values.retentionStatus ? 1 : 2;
        if (values.id) {
            updateRetention(values)
                .then(() => {
                    successNotification([''], 'retention updated successfully');
                    refresh();
                    closeDrawer();
                })
                .catch((e) => {
                    errorNotification([''], 'Error updating retention');
                    logger.error('Master Data Module', 'Create Site', e);
                });
        } else {
            createRetention(values)
                .then(() => {
                    successNotification([''], 'retention created successfully');
                    refresh();
                    closeDrawer();
                })
                .catch((e) => {
                    errorNotification([''], 'Error creating retention');
                    logger.error('Master Data Module', 'Create Site', e);
                });
        }

    };

    return (
        <>
            <PageTitle title={props.title}>
            </PageTitle>
            <PageContent>
                {<RetentionAddEditDrawer
                    data-testid="retention-grid"
                    formRef={form}
                    onSuccess={onSuccess}
                    onDrawerClose={closeDrawer}
                    showDrawer={showDrawer}
                />}
                <RetentionGrid ref={ref} />
                {/*<Alert 
                    message="You do not have permission to access any retention. Contact your organization's administrator for assistance"             
                    icon={<LockOutlined />}
                    type="error" 
                    showIcon 
                />*/}
            </PageContent>
        </>
    );
};

export default RetentionPage;
