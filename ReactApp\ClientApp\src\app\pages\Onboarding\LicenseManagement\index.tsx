import React, { Fragment, useState } from "react";
import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import { <PERSON><PERSON>, <PERSON> } from "antd";
import GenericDataTable from "../../../components/GenericDataTable";
import Tooltip from "antd/lib/tooltip";
import { MailOutlined, EyeOutlined, EditOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import Modal from "../../../components/Modal";
import { withRouter } from "react-router-dom";
import LicenseDetailsContainer from "../../../components/forms/LicenseManagement/LicenseDetailsContainer";
import config from "../../../utils/config";
import moment from "moment";
import { successNotification } from "../../../utils/antNotifications";
import { TERMINATED } from "@app/constants/licenseStatuses";

import { Sorter } from "../../../components/GenericDataTable/util";
import { renderTag } from "@app/components/Tag";

export const formatGridColumnValue = (value: any) => {
  return <p className={styles.yjGridTextFormat}>{value}</p>;
};

const SORTER: Sorter = {
  value: "expirationDate",
  order: "descend",
};

const Page = (props: any) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [license, setLicense] = useState({
    id: "",
    status: { value: 0, name: "" },
  });

  const handleCancel = (e: any) => {
    setShowEditModal(false);
  };

  const [dataTableModal, setDataTableModal] = useState({
    visible: false,
    title: "",
    modalData: [],
  });

  const showDatatableModal = (title: string, data: []) => {
    setDataTableModal({
      visible: true,
      title: title,
      modalData: data,
    });
  };

  const hideDatatableModal = () => {
    setDataTableModal({
      visible: false,
      title: dataTableModal.title,
      modalData: dataTableModal.modalData,
    });
  };

  const formatAndAlignDate = (value: any) => {
    return (
      <p className={styles.yjGridTextFormat}>
        {" "}
        {moment(value).format(config.dateFormat)}{" "}
      </p>
    );
  };

  const renderGridColumns = () => {
    return {
      companyName: (value: any) => {
        return (
          <div className={styles.yjCompanyName}>
            <Tooltip placement="leftTop" title={value}>
              {value}
            </Tooltip>
          </div>
        );
      },
      compliances: (value: any) => {
        return (
          <p className={styles.yjGridTextCenter}>
            <Button
              className={styles.yjViewButton}
              onClick={() => showDatatableModal("Compliances", value)}
            >
              {"View"}
            </Button>
          </p>
        );
      },
      integrations: (value: any) => {
        return (
          <p className={styles.yjGridTextCenter}>
            <Button
              className={styles.yjViewButton}
              onClick={() => showDatatableModal("Integrations", value)}
            >
              {"View"}
            </Button>
          </p>
        );
      },
      effectiveDate: (value: string) => {
        return formatAndAlignDate(value);
      },
      expirationDate: (value: string) => {
        return formatAndAlignDate(value);
      },
      vertical: (value: any) => {
        return formatGridColumnValue(value.name);
      },
      status: (record: any) => {
        return renderTag(record.name, record.value);
      },
      supportLevel: (record: any) => {
        return record ? record.name : "N/A";
      },
      action: (value: any, record: any) => (
        <div className={"yjActionIconWrapper"}>
          {/* <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Button
              disabled={record.status.value === TERMINATED}
              icon={<MailOutlined />}
              onClick={() =>
                successNotification([""], "Email sent Successfully")
              }
            />
          </Tooltip> */}
          <Tooltip title="View">
            <Button
              icon={<EyeOutlined />}
              onClick={() => {
                setLicense(record);
                setShowEditModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              disabled={record.status.value === TERMINATED}
              icon={<EditOutlined />}
              onClick={() => {
                props.history.push(
                  `/onboarding/license-management/edit/${record.id}`
                );
              }}
            />
          </Tooltip>
        </div>
      ),
    };
  };

  return (
    <Fragment>
      <Modal
        visible={showEditModal}
        title={"License Details"}
        onCancel={handleCancel}
        footer={[
          <Button key="back" type="default" onClick={handleCancel}>
            cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() =>
              props.history.push(
                `/onboarding/license-management/edit/${license.id}`
              )
            }
            disabled={license.status.value === TERMINATED}
          >
            Manage
          </Button>,
        ]}
        maskClosable={false}
      >
        <div className={styles.yjModalContentWrapper}>
          <LicenseDetailsContainer licenseId={license.id} disabled />
        </div>
      </Modal>

      <Modal
        key="gridButtonPopupModal"
        {...dataTableModal}
        size={"small"}
        onCancel={hideDatatableModal}
        footer={null}
      >
        <div className={styles.yjDataTableModal}>
          {dataTableModal.modalData && (
            <List>
              {dataTableModal.modalData.map((i: any) => (
                <List.Item key={i.value}>{i.name}</List.Item>
              ))}
            </List>
          )}
          {dataTableModal.modalData.length === 0 &&
            `No ${dataTableModal.title} Applied.`}
        </div>
      </Modal>

      <PageTitle title={props.title}>
        <Button
          type="primary"
          onClick={() =>
            props.history.push("/onboarding/license-management/create")
          }
        >
          Add New License
        </Button>
      </PageTitle>
      <PageContent>
        <GenericDataTable
          scrollColumnCounter={7}
          rowKey={"id"}
          tableKey={"licmngt"}
          endpoint={config.api.organizationAPI.licenses}
          customRender={renderGridColumns()}
          sorted={SORTER}
          fixedColumns={["id", "companyName"]}
          hasFilterManagement={false}
          isDraggable={true}
        />
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
