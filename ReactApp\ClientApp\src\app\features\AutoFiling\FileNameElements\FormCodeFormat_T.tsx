import { Form, Select } from 'antd';
import React from 'react';

const options = [
	{
		label: "I", // Form Code
		options: [
			{ label: "1040", value: "1040" }, // Option 1
			{ label: "Individual", value: "Individual" }, // Option 2
			{ label: "Form 1040", value: "Form 1040" }, // Option 3
		],
	},
	{
		label: "C", // Form Code
		options: [
			{ label: "1120", value: "1120" },
			{ label: "Corporation", value: "Corporation" },
			{ label: "Form 1120", value: "Form 1120" },
		],
	},
	{
		label: "S", // Form Code
		options: [
			{ label: "1120S", value: "1120S" },
			{ label: "S-Corporation", value: "S-Corporation" },
			{ label: "Form 1120S", value: "Form 1120S" },
		],
	},
	{
		label: "P", // Form Code
		options: [
			{ label: "1065", value: "1065" },
			{ label: "Partnership", value: "Partnership" },
			{ label: "Form 1065", value: "Form 1065" },
		],
	},
	{
		label: "F", // Form Code
		options: [
			{ label: "1041", value: "1041" },
			{ label: "Fiduciary", value: "Fiduciary" },
			{ label: "Form 1041", value: "Form 1041" },
		],
	},
	{
		label: "K", // Form Code
		options: [
			{ label: "5500", value: "5500" },
			{ label: "Deferred Comp", value: "Deferred Comp" },
			{ label: "Form 5500", value: "Form 5500" },
		],
	},
	{
		label: "X", // Form Code
		options: [
			{ label: "990", value: "990" },
			{ label: "Exempt", value: "Exempt" },
			{ label: "Form 990", value: "Form 990" },
		],
	},
	{
		label: "Y", // Form Code
		options: [
			{ label: "706-709", value: "706-709" },
			{ label: "Gift-Estate", value: "Gift-Estate" },
			{ label: "Form 706-709", value: "Form 706-709" },
		],
	},
];
const FormCodeFormat_T = ({index, value}:any) => (<Form.Item label={`Form Code Format`} name={`${index}-FormCode`} initialValue={value}>
	<Select placeholder="Select Form Code" options={options} defaultValue={value} />
</Form.Item>);
export default FormCodeFormat_T;