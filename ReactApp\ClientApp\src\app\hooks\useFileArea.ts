import { useEffect, useState } from "react";
import { getFileAreaIdBySite } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { IFileArea } from "@app/types/fileAreaTypes";

export const useFileArea = (siteId: string) => {
  const [fileArea, setFileArea] = useState<IFileArea>();

  useEffect(() => {
    getFileAreaIdBySite(siteId).then((res) => {
      if (res.status == 200 && res.data) {
        setFileArea(res.data);
      } else {
        logger.error('File Area Module', 'Get File Area Error', res);
      }
    }).catch((e) => {
      logger.error('File Area Module', 'Get File Area Error', e);
    });
  }, [siteId]);

  return fileArea;
};
