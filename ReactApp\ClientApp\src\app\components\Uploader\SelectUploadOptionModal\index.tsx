import React from 'react';
import { But<PERSON> } from 'antd';
import { ModalProps } from 'antd/lib/modal';

import styles from './index.module.less';
import { SAME_PROPERTY_UPLOAD, DIFFERENT_PROPERTY_UPLOAD } from '../constants/uploadTypes';
import Modal from '@app/components/Modal';

type PropTypes = {
  onOptionSelect: (option: number) => void
}

export default ({ onOptionSelect, ...props }: ModalProps & PropTypes) => {
  return (
    <>
      <Modal
        {...props}
        title="Setting File Properties"
        footer={null}
        size="auto"
      >
        <div className={styles.yjSettingFileProp}>

          <p>Do you want to add the same properties or choose different properties for the files?</p>

          <Button type="primary" onClick={() => onOptionSelect(SAME_PROPERTY_UPLOAD)}>
            Same Properties
              </Button>
          <Button type="primary" onClick={() => onOptionSelect(DIFFERENT_PROPERTY_UPLOAD)}>
            Different Properties
              </Button>
        </div>
      </Modal>
    </>
  );
};
