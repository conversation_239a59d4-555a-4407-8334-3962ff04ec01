// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Alerts Test Suite should create and match to snapshot 1`] = `
Array [
  <div
    className="yjAlerts"
  >
    <div
      className="ant-alert ant-alert-success"
      data-show={true}
      role="alert"
      style={Object {}}
    >
      <span
        aria-label="check-circle"
        className="anticon anticon-check-circle ant-alert-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="check-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm193.5 301.7l-210.6 292a31.8 31.8 0 01-51.7 0L318.5 484.9c-3.8-5.3 0-12.7 6.5-12.7h46.9c10.2 0 19.9 4.9 25.9 13.3l71.2 98.8 157.2-218c6-8.3 15.6-13.3 25.9-13.3H699c6.5 0 10.3 7.4 6.5 12.7z"
          />
        </svg>
      </span>
      <div
        className="ant-alert-content"
      >
        <div
          className="ant-alert-message"
        >
          Success Message
        </div>
        <div
          className="ant-alert-description"
        />
      </div>
      <button
        className="ant-alert-close-icon"
        onClick={[Function]}
        tabIndex={0}
        type="button"
      >
        <span
          aria-label="close"
          className="anticon anticon-close"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
            />
          </svg>
        </span>
      </button>
    </div>
    <div
      className="ant-alert ant-alert-info"
      data-show={true}
      role="alert"
      style={Object {}}
    >
      <span
        aria-label="info-circle"
        className="anticon anticon-info-circle ant-alert-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="info-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm32 664c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V456c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272zm-32-344a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
          />
        </svg>
      </span>
      <div
        className="ant-alert-content"
      >
        <div
          className="ant-alert-message"
        >
          Informational Notes
        </div>
        <div
          className="ant-alert-description"
        />
      </div>
      <button
        className="ant-alert-close-icon"
        onClick={[Function]}
        tabIndex={0}
        type="button"
      >
        <span
          aria-label="close"
          className="anticon anticon-close"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
            />
          </svg>
        </span>
      </button>
    </div>
    <div
      className="ant-alert ant-alert-warning"
      data-show={true}
      role="alert"
      style={Object {}}
    >
      <span
        aria-label="exclamation-circle"
        className="anticon anticon-exclamation-circle ant-alert-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="exclamation-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm-32 232c0-4.4 3.6-8 8-8h48c4.4 0 8 3.6 8 8v272c0 4.4-3.6 8-8 8h-48c-4.4 0-8-3.6-8-8V296zm32 440a48.01 48.01 0 010-96 48.01 48.01 0 010 96z"
          />
        </svg>
      </span>
      <div
        className="ant-alert-content"
      >
        <div
          className="ant-alert-message"
        >
          Warning Message
        </div>
        <div
          className="ant-alert-description"
        />
      </div>
      <button
        className="ant-alert-close-icon"
        onClick={[Function]}
        tabIndex={0}
        type="button"
      >
        <span
          aria-label="close"
          className="anticon anticon-close"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
            />
          </svg>
        </span>
      </button>
    </div>
    <div
      className="ant-alert ant-alert-error"
      data-show={true}
      role="alert"
      style={Object {}}
    >
      <span
        aria-label="close-circle"
        className="anticon anticon-close-circle ant-alert-icon"
        role="img"
      >
        <svg
          aria-hidden="true"
          data-icon="close-circle"
          fill="currentColor"
          focusable="false"
          height="1em"
          viewBox="64 64 896 896"
          width="1em"
        >
          <path
            d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm165.4 618.2l-66-.3L512 563.4l-99.3 118.4-66.1.3c-4.4 0-8-3.5-8-8 0-1.9.7-3.7 1.9-5.2l130.1-155L340.5 359a8.32 8.32 0 01-1.9-5.2c0-4.4 3.6-8 8-8l66.1.3L512 464.6l99.3-118.4 66-.3c4.4 0 8 3.5 8 8 0 1.9-.7 3.7-1.9 5.2L553.5 514l130 155c1.2 1.5 1.9 3.3 1.9 5.2 0 4.4-3.6 8-8 8z"
          />
        </svg>
      </span>
      <div
        className="ant-alert-content"
      >
        <div
          className="ant-alert-message"
        >
          Error Message
        </div>
        <div
          className="ant-alert-description"
        />
      </div>
      <button
        className="ant-alert-close-icon"
        onClick={[Function]}
        tabIndex={0}
        type="button"
      >
        <span
          aria-label="close"
          className="anticon anticon-close"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="close"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M563.8 512l262.5-312.9c4.4-5.2.7-13.1-6.1-13.1h-79.8c-4.7 0-9.2 2.1-12.3 5.7L511.6 449.8 295.1 191.7c-3-3.6-7.5-5.7-12.3-5.7H203c-6.8 0-10.5 7.9-6.1 13.1L459.4 512 196.9 824.9A7.95 7.95 0 00203 838h79.8c4.7 0 9.2-2.1 12.3-5.7l216.5-258.1 216.5 258.1c3 3.6 7.5 5.7 12.3 5.7h79.8c6.8 0 10.5-7.9 6.1-13.1L563.8 512z"
            />
          </svg>
        </span>
      </button>
    </div>
  </div>,
  <div
    className="yjMessages"
  >
    <div
      className="ant-space ant-space-horizontal ant-space-align-center"
      style={Object {}}
    >
      <div
        className="ant-space-item"
        style={
          Object {
            "marginRight": 8,
          }
        }
      >
        <button
          className="ant-btn ant-btn-default"
          onClick={[Function]}
          type="button"
        >
          <span>
            Info Message
          </span>
        </button>
      </div>
      <div
        className="ant-space-item"
        style={
          Object {
            "marginRight": 8,
          }
        }
      >
        <button
          className="ant-btn ant-btn-default"
          onClick={[Function]}
          type="button"
        >
          <span>
            Success Message
          </span>
        </button>
      </div>
      <div
        className="ant-space-item"
        style={
          Object {
            "marginRight": 8,
          }
        }
      >
        <button
          className="ant-btn ant-btn-default"
          onClick={[Function]}
          type="button"
        >
          <span>
            Error Message
          </span>
        </button>
      </div>
      <div
        className="ant-space-item"
        style={Object {}}
      >
        <button
          className="ant-btn ant-btn-default"
          onClick={[Function]}
          type="button"
        >
          <span>
            Warning Message
          </span>
        </button>
      </div>
    </div>
  </div>,
]
`;
