DMS Internal Application
============

This repo contains all the code related to DMS internal front-end application.

# Main tech-stack:

  * React js
  * TypeScript
  * Ant design UI library
  * Redux
  * Context API


# Build and Test

| Pipeline | Status |
| ------ | ------ |
| FrontEnd-Internal | [![Build Status](https://dev.azure.com/iris-global/Document%20Management/_apis/build/status/DMS-Frontend/FrontEnd-Internal?branchName=dev)](https://dev.azure.com/iris-global/Document%20Management/_build/latest?definitionId=23&branchName=dev) |


# Project Setup

## Node Version Requirement
This project requires Node.js version v16.20.2. There are two ways to install and manage Node.js:

### Option 1: Using NVM (Recommended)
Node Version Manager (NVM) is the recommended way to install and manage Node.js versions. You can find the installation instructions and documentation at [NVM GitHub Repository](https://github.com/nvm-sh/nvm).

To install and use Node.js v16.20.2 with NVM:
```bash
nvm install 16.20.2
nvm use 16.20.2
```

### Option 2: Direct Download
Alternatively, you can download Node.js v16.20.2 directly from the official Node.js website.

## Clone the repo 
```
git clone https://<EMAIL>/iris-global/Document Management/_git/FrontEnd-Internal
```

## Authenticate azure artifacts NPM feed for get the access to the discovery client library

```
npm install -g vsts-npm-auth --registry https://registry.npmjs.com --always-auth false

vsts-npm-auth -config .npmrc
```

## Install the packages 

```
npm i
```

### Environment Variables Setup

Before running the application, you need to update the environment variables in the `env-config.js` file. You can retrieve the required environment variables from:
Azure DevOps Variable Groups

## Run the app : 
```
npm start
```

# Development Guidelines

## Code Style
- Follow TypeScript best practices
- Use functional components with hooks
- Follow the existing project structure
- Use proper naming conventions

## Git Workflow
1. Create a new branch from `dev`
2. Make your changes
3. Submit a pull request
4. Ensure CI pipeline passes

## Branch Naming Convention
Follow these naming patterns when creating new branches:

- Feature branch: `feature/DMS-123-implement-search-functionality`
- Bug fix branch: `bugfix/DMS-456-fix-pagination-error`
- Hotfix branch: `hotfix/DMS-789-fix-critical-login-issue`