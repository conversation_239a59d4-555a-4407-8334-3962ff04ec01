import React, { useEffect, useState } from "react";
import { Row, Col, Skeleton, Form, Input,But<PERSON>,Drawer,Modal as AntModal } from "antd";
import {
ExclamationCircleOutlined
} from "@ant-design/icons/lib/icons";
import styles from "./index.module.less";
import SelectedFilesGrid, {
    ColumnConfig,
} from "@app/features/FileArea/SelectedFilesGrid";
import { IFile } from "@app/types/fileAreaTypes";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import logger from "@app/utils/logger";
import { FileDetailsOptions } from "@app/types/FileDetailsOptions";
import { FormInstance } from "antd/lib/form";
import { reNameFiles  } from '@app/api/fileAreaService';
import { errorNotification, successNotification } from '@app/utils/antNotifications';

export interface ReNameFilesProps {
    onSuccess: () => void;
    selectedFiles: IFile[];
    onClosePopup: (event: boolean) => void;
    options: FileDetailsOptions;
    form: FormInstance;
    showReNameFilesModal:boolean;
}

export default (props: ReNameFilesProps) => {
    const [selectedFileList, setSelectedFileList] = useState<IFile[]>([]);
    const reNameFilesColumnConfigs: ColumnConfig[] = [
      { title: '', dataIndex: 'remove', key: 'remove', width: 40 },
      {
        title: 'Title',
        dataIndex: 'title',
        key: 'title',
        ellipsis: true,
        render: (text: any, record: IFile, index: number) => (
          <Form.Item
            name={record.id}
            initialValue={record.title}
            className={styles.reNameInputAreaPanel}
            rules={[
              {
                required: true,
                message: 'Title is required!',
              },
            ]}
          >
            <Input placeholder={record.title} className={styles.reNameInputArea} />
          </Form.Item>
        ),
      },
    ];
    const [reNameFilesDetails, setReNameFilesDetails] = useState<IFile[]>([]);
    const [renameButtonEnable, setRenameButtonEnable] = useState(false);    
    const { confirm } = AntModal;
    const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';
    const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';

    useEffect(() => {
        setSelectedFileList([]);
        props.form.resetFields();
        const getFiles = async () => {
            const fileList: IFile[] = await Promise.all(
                props.selectedFiles.map(async (file) => ({
                    ...file,

                }))
            );
            setSelectedFileList(fileList);
        };
        getFiles();
        setRenameButtonEnable(false);
    }, [props.selectedFiles,props.showReNameFilesModal]);

    const handleValuesChange = (allValues: any) => {
      const isRenamed = selectedFileList.some((file) => {
        const newValue = allValues[file.id]?.trim();
        return newValue && newValue !== file.title.trim();
      });
      setRenameButtonEnable(isRenamed);
    };

    const handleFilesChange = (fileList: any[]) => {

        if (fileList.length > 0) {
            setSelectedFileList(fileList);
        } else {
            props.onClosePopup(false);
        }
    };


    const handleFinish = (values: any) => {

        const updatedFileList = selectedFileList.map((file) => ({
            ...file,
            title: values[file.id] || file.title, // Update title if there's a new value
        }));

           logger.debug('Rename file Component', 'updatedFileList', { updatedFileList });       
           handleReNameFilesUpdateDetails(updatedFileList);

    };

    const handleReNameFilesUpdateDetails = (fileList: any[]) => {
    
        const updatedFileList = fileList.map((file: IFile) => ({
          fileId: file.id,
          title: file.title
        }));
    
        reNameFiles(
          updatedFileList
        )
          .then((response) => {
            successNotification([''], 'Renamed Successfully');            
            props.onSuccess();
            resetReNameFilesModal();
          })
          .catch((error) => {
            if (error.statusCode === FORBIDDEN_ERROR_CODE) {
              errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
            } else {
              errorNotification([''], 'ReName Failed');
            }
            logger.error('File Area Module', 'ReName files', error);
          });
    
      };

      const resetReNameFilesModal = () => {
        reNameFilesDetails && setReNameFilesDetails([]);
      props.form.resetFields();
      props.onClosePopup(true);
      };

     const onCancelReNameFilesModal = () => {
        confirm({
          title: DISCARD_MESSAGE,
          icon: <ExclamationCircleOutlined />,
          okText: 'Yes',
          cancelText: 'No',
          onOk() {
            resetReNameFilesModal();
          },
        });
      };

    const handleShowReNameFilesModalCancel = (hasSelectedFiles = true) => {
        if (hasSelectedFiles) {
          onCancelReNameFilesModal();
        } else {
          resetReNameFilesModal();
        }
      };

      const handleFileReNameSubmit = () => {
        props.form.submit();
      };

    return (
      <Drawer
        visible={props.showReNameFilesModal}
        title={'ReName File(s)'}
        onClose={() => handleShowReNameFilesModalCancel()}
        className={'yjDrawerPanel'}
        width={700}
        placement="right"
        footer={[
          <div className={styles.yjReNameFilesInfoFooter}>
            <span className={styles.yjReNameFilesInfo}></span>

            <div className={styles.yjReNameFilesInfoButtons}>
              <Button key="cancel" type="default" onClick={() => handleShowReNameFilesModalCancel()}>
                cancel
              </Button>
              <Button key="rename" type="primary" onClick={handleFileReNameSubmit} disabled={!renameButtonEnable}>
                Rename
              </Button>
            </div>
          </div>,
        ]}
      >
        <Form form={props.form} key="moveFilesForm" onFinish={handleFinish} onValuesChange={handleValuesChange}>
          <Row gutter={24}>
            <Col span={12}>
              {selectedFileList.length > 0 ? (
                <SelectedFilesGrid onFilesChange={handleFilesChange} columnConfigs={reNameFilesColumnConfigs} dataList={selectedFileList} scroll={200} />
              ) : (
                <Skeleton />
              )}
            </Col>
          </Row>
        </Form>
      </Drawer>
    );

};