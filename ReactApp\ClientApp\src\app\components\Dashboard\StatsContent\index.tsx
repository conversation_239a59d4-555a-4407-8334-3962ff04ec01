import React from "react";
import { Card, Col, Row, Tooltip } from "antd";
const { Meta } = Card;
export default () => {
  return (
    <>
      <Row gutter={12}>
        {/* <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="License ID" description={"100586"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="Expiration Date" description={"2020/12/31"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="Remaining Days" description={"45"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper yjLblStatus">
              <Meta title="Status" description={"ACTIVE"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="Allocated Space" description={"50GB"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="Internal Users" description={"200"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="Support Level" description={"PREMIUM"} />
            </Card>
          </Tooltip>
        </Col>
        <Col span={3}>
          <Tooltip
            placement="topLeft"
            title={"This feature is coming soon"}
            color="#78bf59"
          >
            <Card className="yjCardWrapper">
              <Meta title="Integration" description={"SALES FORCE"} />
            </Card>
          </Tooltip>
        </Col> */}
      </Row>
    </>
  );
};
