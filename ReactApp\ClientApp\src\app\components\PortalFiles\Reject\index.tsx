import React, { useState } from "react";
import { Button } from "antd";

import SelectedFilesGrid, {
  ColumnConfig,
} from "@app/features/FileArea/SelectedFilesGrid";
import styles from "./index.module.less";
import { rejectPortalFiles } from "@app/api/portalServices";
import logger from "@app/utils/logger";
import {
  errorNotification,
  infoNotification,
} from "@app/utils/antNotifications";
import { IFile } from "@app/types/fileAreaTypes";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";

export interface RejectProps {
  siteId: string;
  selectedFiles: IFile[];
  onModalClose: (isSuccess?: boolean) => void;
}

const defaultRejectOptionColumnConfigs: ColumnConfig[] = [
  { title: "", dataIndex: "remove", key: "remove", width: 40 },
  // { title: "Request Id", dataIndex: "requestId", key: "requestId" },
  // { title: "Request Name", dataIndex: "name", key: "name" },
  { title: "File Title", dataIndex: "title", key: "title", ellipsis: true },
];
export default (props: RejectProps) => {
  const [selectedFiles, setSelectedFiles] = useState(props.selectedFiles);

  const handleFilesChange = (fileList: IFile[]) => {
    if (fileList.length > 0) {
      setSelectedFiles(fileList);
    } else {
      props.onModalClose();
    }
  };

  const handleRejectedFiles = (response: HTTPResponse<any>) => {
    if (response.data) {
      infoNotification([""], "File(s) are being rejected. Please wait.");
      props.onModalClose(true);
    } else {
      errorNotification([""], "Rejection Failed");
    }
  };

  const handleFilesReject = () => {
    const fileIds: any[] = selectedFiles.map((file) => file.id);
    rejectPortalFiles(props.siteId, fileIds)
      .then((response) => {
        handleRejectedFiles(response);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        } else {
          errorNotification([""], "Rejection Failed");
        }
        logger.error("Portal Files-Reject", "Reject Portal Files", error);
      });
  };

  return (
    <>
      <p>
        Do you wish to reject these file(s)? They will be deleted from Portal
        Files and you won’t be able to revert this action.
      </p>
      <SelectedFilesGrid
        onFilesChange={handleFilesChange}
        columnConfigs={defaultRejectOptionColumnConfigs}
        dataList={selectedFiles}
      />
      <div className={styles.yjCenterButtonWrapper}>
        <Button key="no" type="default" onClick={() => props.onModalClose()}>
          No
        </Button>
        <Button key="yes" type="primary" onClick={handleFilesReject}>
          Yes
        </Button>
      </div>
    </>
  );
};
