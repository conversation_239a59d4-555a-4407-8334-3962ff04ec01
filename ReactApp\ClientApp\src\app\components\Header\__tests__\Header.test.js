import React from "react";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import Header from "..";
import { shallow, mount } from "enzyme";
import { Menu, Dropdown } from "antd";
jest.mock("../index.module.less", () => ({
  yjAppLogo: "yjAppLogo",
}));

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const createHeaderComponent = (headerProps) => {
  const INITIAL_STATE = {
    grid:{
      selectedElement: {},
      columnQueryParameters: {}
    }
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <Header {...headerProps} />
    </ReduxProvider>
  );
};

describe("<Header/>", () => {
  it("should render Header component", () => {
    const component = shallow(createHeaderComponent());
    expect(component.html()).not.toBe(null);
  });

  it("should contain logo image", () => {
    const component = mount(createHeaderComponent());
    expect(component.find(".yjAppLogo").length).toBe(1);
  });

  it("should contain right side top menu", () => {
    const component = mount(createHeaderComponent());
    expect(component.find(Menu).length).toBe(1);
  });

  it("should redirect user after logout", () => {
    const mockedPush = jest.fn();
    const props = {
      history: {
        push: mockedPush,
      },
    };
    const component = mount(createHeaderComponent(props));
    const logoutBtn = component.find(Menu.Item).at(2);
    logoutBtn.simulate("click");

    expect(mockedPush).toBeCalledWith("");
  });

  it("Menu should contain language selector menu item", () => {
    const component = mount(createHeaderComponent());
    const languageButton = component.find(Menu.Item).at(2);
    expect(languageButton).toHaveLength(1);
  });
  it("Menu should contain language selector dropdown", () => {
    const component = mount(createHeaderComponent());
    expect(component.find(Dropdown).length).toBe(1);
  });
  it("language selector dropdown contains a menu", () => {
    const component = mount(createHeaderComponent());
    const dropdown = component.find(Dropdown);
    const submenuItems = dropdown.find(Menu.Item);
    submenuItems.forEach((item) => item.simulate("click"));
  });
  it("language selector has menu items", () => {
    const component = mount(createHeaderComponent());
    const dropdown = component.find(Dropdown);
    const submenuItems = dropdown.find(Menu);
    expect(submenuItems).not.toBe(null);
  });
});
