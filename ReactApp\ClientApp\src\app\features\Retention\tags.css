.droppable-tag {
    transition: background-color 0.3s, border 0.3s;
    position: relative;
}

.droppable-tag.dragged-over {
    background-color: #e0e0e0;
    border: 1px dashed #aaa;
}

.droppable-tag-placeholder {
    height: 50px;
    border: 1px dashed #ccc;
    margin: 4px;
    position: relative;
    z-index: 1;
}

/* Smooth transition for tag reordering */
.tag-transition {
    transition: transform 0.3s;
}
.droppable-area {
    transition: border-color 0.3s;
}

.droppable-area.hover {
    border-style: dashed !important;
    border-color: lightblue !important;
    background: #d6e4e6 !important;
}

.droppable-tag.hover {
    border-style: dashed !important;
    border-color: lightblue !important;
    background: #d5e0e6 !important;
    opacity: 0.8;
}