import { getModules } from '@app/api/verticalService';
import {
    SET_MODULES,
    SetModulesAction,
    IsModulesFetchedAction,
    MODULES_FETCH_SUCCESSFUL,
} from '../actionTypes/verticalTypes';
import { Module } from '@app/types/Modules';
import { AppThunk } from '@app/types/AppThunk';

export function setModules(verticalId: number, data: [Module]): SetModulesAction {
    return {
        type: SET_MODULES,
        verticalId: verticalId,
        payload: data,
    };
}

export function isModulesFetched(isFetched: boolean): IsModulesFetchedAction {
    return {
        type: MODULES_FETCH_SUCCESSFUL,
        isFetched: isFetched,
    };
}

export function fetchModulesByVerticalId(verticalId: number): AppThunk {
    return async (dispatch, getState) => {
        if(verticalId !== getState().vertical.id) {
            dispatch(isModulesFetched(false));
            const { data } = await getModules(verticalId);
            dispatch(setModules(verticalId, data));
        }
        dispatch(isModulesFetched(true));
    };
}

