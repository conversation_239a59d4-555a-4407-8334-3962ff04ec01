@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjLicenseManagementDetails {

  .yjSectionHeading {
    color: @text-color;
    font-size: ceil(@font-size-base * .89);
    margin: .2em 0;
    text-transform: @yj-transform;

    .font-mixin(@font-primary, @yjff-bold);
  }

  .yjSectionDivider {
    border-color: @border-color-base;
    height: 1.5px;
  }
}

@button-size: 25px;

.yjListWrapper {

  .yjListHeader {
    background-color: fade(@text-color, 5%);
    margin: 1em 0;
    padding: .5em;
  }

  .yjSubHeading {
    color: fade(@text-color, 55%);
    margin-right: 1em;
  }

  .yjRemoveButton {
    background: fade(@color-danger, 80%);
    border-color: fade(@color-danger, 60%);
    height: @button-size;
    min-width: @button-size;
    width: @button-size;
  }
}

//Modal popup sub heading

.yjModuleSubHeading {
  color: @color-secondary;
  font-size: @font-size-lg;
  margin-bottom: 1em;
  margin-top: 20px;
  text-transform: uppercase;

  .font-mixin(@font-primary, @yjff-semibold);
}

.yjInfoIcon {
  color: @color-button-primary;
  font-size: 20px;
  position: relative;
  top: -4px;
}
