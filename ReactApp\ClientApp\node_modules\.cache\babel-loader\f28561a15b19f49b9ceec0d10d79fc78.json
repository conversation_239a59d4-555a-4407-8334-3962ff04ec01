{"ast": null, "code": "import \"antd/es/drawer/style\";\nimport _Drawer from \"antd/es/drawer\";\nimport \"antd/es/row/style\";\nimport _Row from \"antd/es/row\";\nimport \"antd/es/col/style\";\nimport _Col from \"antd/es/col\";\nimport \"antd/es/skeleton/style\";\nimport _Skeleton from \"antd/es/skeleton\";\nimport \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/modal/style\";\nimport _Modal from \"antd/es/modal\";\nimport \"antd/es/form/style\";\nimport _Form from \"antd/es/form\";\nimport \"antd/es/input/style\";\nimport _Input from \"antd/es/input\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\FileArea\\\\ReNameFiles\\\\index.tsx\";\nimport React, { useEffect, useState } from \"react\";\nimport { ExclamationCircleOutlined } from \"@ant-design/icons/lib/icons\";\nimport styles from \"./index.module.less\";\nimport SelectedFilesGrid from \"@app/features/FileArea/SelectedFilesGrid\";\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\nimport logger from \"@app/utils/logger\";\nimport { reNameFiles } from '@app/api/fileAreaService';\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\nexport default (props => {\n  const [selectedFileList, setSelectedFileList] = useState([]);\n  const reNameFilesColumnConfigs = [{\n    title: '',\n    dataIndex: 'remove',\n    key: 'remove',\n    width: 40\n  }, {\n    title: 'Title',\n    dataIndex: 'title',\n    key: 'title',\n    ellipsis: true,\n    render: (text, record, index) => /*#__PURE__*/React.createElement(_Form.Item, {\n      name: record.id,\n      initialValue: record.title,\n      className: styles.reNameInputAreaPanel,\n      rules: [{\n        required: true,\n        message: 'Title is required!'\n      }],\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 37,\n        columnNumber: 11\n      }\n    }, /*#__PURE__*/React.createElement(_Input, {\n      placeholder: record.title,\n      className: styles.reNameInputArea,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 48,\n        columnNumber: 13\n      }\n    }))\n  }];\n  const [reNameFilesDetails, setReNameFilesDetails] = useState([]);\n  const [renameButtonEnable, setRenameButtonEnable] = useState(false);\n  const {\n    confirm\n  } = _Modal;\n  const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';\n  const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';\n  useEffect(() => {\n    setSelectedFileList([]);\n    props.form.resetFields();\n\n    const getFiles = async () => {\n      const fileList = await Promise.all(props.selectedFiles.map(async file => ({ ...file\n      })));\n      setSelectedFileList(fileList);\n    };\n\n    getFiles();\n    setRenameButtonEnable(false);\n  }, [props.selectedFiles, props.showReNameFilesModal]);\n\n  const handleValuesChange = allValues => {\n    const isRenamed = selectedFileList.some(file => {\n      var _allValues$file$id;\n\n      const newValue = (_allValues$file$id = allValues[file.id]) === null || _allValues$file$id === void 0 ? void 0 : _allValues$file$id.trim();\n      return newValue && newValue !== file.title.trim();\n    });\n    setRenameButtonEnable(isRenamed);\n  };\n\n  const handleFilesChange = fileList => {\n    if (fileList.length > 0) {\n      setSelectedFileList(fileList);\n    } else {\n      props.onClosePopup(false);\n    }\n  };\n\n  const handleFinish = values => {\n    const updatedFileList = selectedFileList.map(file => ({ ...file,\n      title: values[file.id] || file.title // Update title if there's a new value\n\n    }));\n    logger.debug('Rename file Component', 'updatedFileList', {\n      updatedFileList\n    });\n    handleReNameFilesUpdateDetails(updatedFileList);\n  };\n\n  const handleReNameFilesUpdateDetails = fileList => {\n    const updatedFileList = fileList.map(file => ({\n      fileId: file.id,\n      title: file.title\n    }));\n    reNameFiles(updatedFileList).then(response => {\n      successNotification([''], 'Renamed Successfully');\n      props.onSuccess();\n      resetReNameFilesModal();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], 'ReName Failed');\n      }\n\n      logger.error('File Area Module', 'ReName files', error);\n    });\n  };\n\n  const resetReNameFilesModal = () => {\n    reNameFilesDetails && setReNameFilesDetails([]);\n    props.form.resetFields();\n    props.onClosePopup(true);\n  };\n\n  const onCancelReNameFilesModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 140,\n          columnNumber: 17\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        resetReNameFilesModal();\n      }\n\n    });\n  };\n\n  const handleShowReNameFilesModalCancel = (hasSelectedFiles = true) => {\n    if (hasSelectedFiles) {\n      onCancelReNameFilesModal();\n    } else {\n      resetReNameFilesModal();\n    }\n  };\n\n  const handleFileReNameSubmit = () => {\n    props.form.submit();\n  };\n\n  return /*#__PURE__*/React.createElement(_Drawer, {\n    visible: props.showReNameFilesModal,\n    title: 'ReName File(s)',\n    onClose: () => handleShowReNameFilesModalCancel(),\n    className: 'yjDrawerPanel',\n    width: 700,\n    placement: \"right\",\n    footer: [/*#__PURE__*/React.createElement(\"div\", {\n      className: styles.yjReNameFilesInfoFooter,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 170,\n        columnNumber: 11\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: styles.yjReNameFilesInfo,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 171,\n        columnNumber: 13\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.yjReNameFilesInfoButtons,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 173,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(_Button, {\n      key: \"cancel\",\n      type: \"default\",\n      onClick: () => handleShowReNameFilesModalCancel(),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 174,\n        columnNumber: 15\n      }\n    }, \"cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      key: \"rename\",\n      type: \"primary\",\n      onClick: handleFileReNameSubmit,\n      disabled: !renameButtonEnable,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 177,\n        columnNumber: 15\n      }\n    }, \"Rename\")))],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 162,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(_Form, {\n    form: props.form,\n    key: \"moveFilesForm\",\n    onFinish: handleFinish,\n    onValuesChange: handleValuesChange,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 184,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 185,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Col, {\n    span: 12,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 186,\n      columnNumber: 13\n    }\n  }, selectedFileList.length > 0 ? /*#__PURE__*/React.createElement(SelectedFilesGrid, {\n    onFilesChange: handleFilesChange,\n    columnConfigs: reNameFilesColumnConfigs,\n    dataList: selectedFileList,\n    scroll: 200,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 188,\n      columnNumber: 17\n    }\n  }) : /*#__PURE__*/React.createElement(_Skeleton, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 190,\n      columnNumber: 17\n    }\n  })))));\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/FileArea/ReNameFiles/index.tsx"], "names": ["React", "useEffect", "useState", "ExclamationCircleOutlined", "styles", "SelectedFilesGrid", "FORBIDDEN_ERROR_CODE", "logger", "reNameFiles", "errorNotification", "successNotification", "props", "selectedFileList", "setSelectedFileList", "reNameFilesColumnConfigs", "title", "dataIndex", "key", "width", "ellipsis", "render", "text", "record", "index", "id", "reNameInputAreaPanel", "required", "message", "reNameInputArea", "reNameFilesDetails", "setReNameFilesDetails", "renameButtonEnable", "setRenameButtonEnable", "confirm", "DISCARD_MESSAGE", "FORBIDDEN_ERROR_MESSAGE", "form", "resetFields", "getFiles", "fileList", "Promise", "all", "selectedFiles", "map", "file", "showReNameFilesModal", "handleValuesChange", "allValues", "isRenamed", "some", "newValue", "trim", "handleFilesChange", "length", "onClosePopup", "handleFinish", "values", "updatedFileList", "debug", "handleReNameFilesUpdateDetails", "fileId", "then", "response", "onSuccess", "resetReNameFilesModal", "catch", "error", "statusCode", "onCancelReNameFilesModal", "icon", "okText", "cancelText", "onOk", "handleShowReNameFilesModalCancel", "hasSelectedFiles", "handleFileReNameSubmit", "submit", "yjReNameFilesInfoFooter", "yjReNameFilesInfo", "yjReNameFilesInfoButtons"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,SAAhB,EAA2BC,QAA3B,QAA2C,OAA3C;AAEA,SACAC,yBADA,QAEO,6BAFP;AAGA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,iBAAP,MAEO,0CAFP;AAIA,SAASC,oBAAT,QAAqC,YAArC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAGA,SAASC,WAAT,QAA6B,0BAA7B;AACA,SAASC,iBAAT,EAA4BC,mBAA5B,QAAuD,6BAAvD;AAWA,gBAAgBC,KAAD,IAA6B;AACxC,QAAM,CAACC,gBAAD,EAAmBC,mBAAnB,IAA0CX,QAAQ,CAAU,EAAV,CAAxD;AACA,QAAMY,wBAAwC,GAAG,CAC/C;AAAEC,IAAAA,KAAK,EAAE,EAAT;AAAaC,IAAAA,SAAS,EAAE,QAAxB;AAAkCC,IAAAA,GAAG,EAAE,QAAvC;AAAiDC,IAAAA,KAAK,EAAE;AAAxD,GAD+C,EAE/C;AACEH,IAAAA,KAAK,EAAE,OADT;AAEEC,IAAAA,SAAS,EAAE,OAFb;AAGEC,IAAAA,GAAG,EAAE,OAHP;AAIEE,IAAAA,QAAQ,EAAE,IAJZ;AAKEC,IAAAA,MAAM,EAAE,CAACC,IAAD,EAAYC,MAAZ,EAA2BC,KAA3B,kBACN,0BAAM,IAAN;AACE,MAAA,IAAI,EAAED,MAAM,CAACE,EADf;AAEE,MAAA,YAAY,EAAEF,MAAM,CAACP,KAFvB;AAGE,MAAA,SAAS,EAAEX,MAAM,CAACqB,oBAHpB;AAIE,MAAA,KAAK,EAAE,CACL;AACEC,QAAAA,QAAQ,EAAE,IADZ;AAEEC,QAAAA,OAAO,EAAE;AAFX,OADK,CAJT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAWE;AAAO,MAAA,WAAW,EAAEL,MAAM,CAACP,KAA3B;AAAkC,MAAA,SAAS,EAAEX,MAAM,CAACwB,eAApD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAXF;AANJ,GAF+C,CAAjD;AAwBA,QAAM,CAACC,kBAAD,EAAqBC,qBAArB,IAA8C5B,QAAQ,CAAU,EAAV,CAA5D;AACA,QAAM,CAAC6B,kBAAD,EAAqBC,qBAArB,IAA8C9B,QAAQ,CAAC,KAAD,CAA5D;AACA,QAAM;AAAE+B,IAAAA;AAAF,YAAN;AACA,QAAMC,eAAe,GAAG,+CAAxB;AACA,QAAMC,uBAAuB,GAAG,qFAAhC;AAEAlC,EAAAA,SAAS,CAAC,MAAM;AACZY,IAAAA,mBAAmB,CAAC,EAAD,CAAnB;AACAF,IAAAA,KAAK,CAACyB,IAAN,CAAWC,WAAX;;AACA,UAAMC,QAAQ,GAAG,YAAY;AACzB,YAAMC,QAAiB,GAAG,MAAMC,OAAO,CAACC,GAAR,CAC5B9B,KAAK,CAAC+B,aAAN,CAAoBC,GAApB,CAAwB,MAAOC,IAAP,KAAiB,EACrC,GAAGA;AADkC,OAAjB,CAAxB,CAD4B,CAAhC;AAMA/B,MAAAA,mBAAmB,CAAC0B,QAAD,CAAnB;AACH,KARD;;AASAD,IAAAA,QAAQ;AACRN,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACH,GAdQ,EAcN,CAACrB,KAAK,CAAC+B,aAAP,EAAqB/B,KAAK,CAACkC,oBAA3B,CAdM,CAAT;;AAgBA,QAAMC,kBAAkB,GAAIC,SAAD,IAAoB;AAC7C,UAAMC,SAAS,GAAGpC,gBAAgB,CAACqC,IAAjB,CAAuBL,IAAD,IAAU;AAAA;;AAChD,YAAMM,QAAQ,yBAAGH,SAAS,CAACH,IAAI,CAACpB,EAAN,CAAZ,uDAAG,mBAAoB2B,IAApB,EAAjB;AACA,aAAOD,QAAQ,IAAIA,QAAQ,KAAKN,IAAI,CAAC7B,KAAL,CAAWoC,IAAX,EAAhC;AACD,KAHiB,CAAlB;AAIAnB,IAAAA,qBAAqB,CAACgB,SAAD,CAArB;AACD,GAND;;AAQA,QAAMI,iBAAiB,GAAIb,QAAD,IAAqB;AAE3C,QAAIA,QAAQ,CAACc,MAAT,GAAkB,CAAtB,EAAyB;AACrBxC,MAAAA,mBAAmB,CAAC0B,QAAD,CAAnB;AACH,KAFD,MAEO;AACH5B,MAAAA,KAAK,CAAC2C,YAAN,CAAmB,KAAnB;AACH;AACJ,GAPD;;AAUA,QAAMC,YAAY,GAAIC,MAAD,IAAiB;AAElC,UAAMC,eAAe,GAAG7C,gBAAgB,CAAC+B,GAAjB,CAAsBC,IAAD,KAAW,EACpD,GAAGA,IADiD;AAEpD7B,MAAAA,KAAK,EAAEyC,MAAM,CAACZ,IAAI,CAACpB,EAAN,CAAN,IAAmBoB,IAAI,CAAC7B,KAFqB,CAEd;;AAFc,KAAX,CAArB,CAAxB;AAKGR,IAAAA,MAAM,CAACmD,KAAP,CAAa,uBAAb,EAAsC,iBAAtC,EAAyD;AAAED,MAAAA;AAAF,KAAzD;AACAE,IAAAA,8BAA8B,CAACF,eAAD,CAA9B;AAEN,GAVD;;AAYA,QAAME,8BAA8B,GAAIpB,QAAD,IAAqB;AAExD,UAAMkB,eAAe,GAAGlB,QAAQ,CAACI,GAAT,CAAcC,IAAD,KAAkB;AACrDgB,MAAAA,MAAM,EAAEhB,IAAI,CAACpB,EADwC;AAErDT,MAAAA,KAAK,EAAE6B,IAAI,CAAC7B;AAFyC,KAAlB,CAAb,CAAxB;AAKAP,IAAAA,WAAW,CACTiD,eADS,CAAX,CAGGI,IAHH,CAGSC,QAAD,IAAc;AAClBpD,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,sBAAP,CAAnB;AACAC,MAAAA,KAAK,CAACoD,SAAN;AACAC,MAAAA,qBAAqB;AACtB,KAPH,EAQGC,KARH,CAQUC,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACC,UAAN,KAAqB7D,oBAAzB,EAA+C;AAC7CG,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO0B,uBAAP,CAAjB;AACD,OAFD,MAEO;AACL1B,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,eAAP,CAAjB;AACD;;AACDF,MAAAA,MAAM,CAAC2D,KAAP,CAAa,kBAAb,EAAiC,cAAjC,EAAiDA,KAAjD;AACD,KAfH;AAiBD,GAxBH;;AA0BE,QAAMF,qBAAqB,GAAG,MAAM;AAClCnC,IAAAA,kBAAkB,IAAIC,qBAAqB,CAAC,EAAD,CAA3C;AACFnB,IAAAA,KAAK,CAACyB,IAAN,CAAWC,WAAX;AACA1B,IAAAA,KAAK,CAAC2C,YAAN,CAAmB,IAAnB;AACC,GAJD;;AAMD,QAAMc,wBAAwB,GAAG,MAAM;AACpCnC,IAAAA,OAAO,CAAC;AACNlB,MAAAA,KAAK,EAAEmB,eADD;AAENmC,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLR,QAAAA,qBAAqB;AACtB;;AAPK,KAAD,CAAP;AASD,GAVF;;AAYD,QAAMS,gCAAgC,GAAG,CAACC,gBAAgB,GAAG,IAApB,KAA6B;AAClE,QAAIA,gBAAJ,EAAsB;AACpBN,MAAAA,wBAAwB;AACzB,KAFD,MAEO;AACLJ,MAAAA,qBAAqB;AACtB;AACF,GANH;;AAQE,QAAMW,sBAAsB,GAAG,MAAM;AACnChE,IAAAA,KAAK,CAACyB,IAAN,CAAWwC,MAAX;AACD,GAFD;;AAIF,sBACE;AACE,IAAA,OAAO,EAAEjE,KAAK,CAACkC,oBADjB;AAEE,IAAA,KAAK,EAAE,gBAFT;AAGE,IAAA,OAAO,EAAE,MAAM4B,gCAAgC,EAHjD;AAIE,IAAA,SAAS,EAAE,eAJb;AAKE,IAAA,KAAK,EAAE,GALT;AAME,IAAA,SAAS,EAAC,OANZ;AAOE,IAAA,MAAM,EAAE,cACN;AAAK,MAAA,SAAS,EAAErE,MAAM,CAACyE,uBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AAAM,MAAA,SAAS,EAAEzE,MAAM,CAAC0E,iBAAxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MADF,eAGE;AAAK,MAAA,SAAS,EAAE1E,MAAM,CAAC2E,wBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AAAQ,MAAA,GAAG,EAAC,QAAZ;AAAqB,MAAA,IAAI,EAAC,SAA1B;AAAoC,MAAA,OAAO,EAAE,MAAMN,gCAAgC,EAAnF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAIE;AAAQ,MAAA,GAAG,EAAC,QAAZ;AAAqB,MAAA,IAAI,EAAC,SAA1B;AAAoC,MAAA,OAAO,EAAEE,sBAA7C;AAAqE,MAAA,QAAQ,EAAE,CAAC5C,kBAAhF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJF,CAHF,CADM,CAPV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAsBE;AAAM,IAAA,IAAI,EAAEpB,KAAK,CAACyB,IAAlB;AAAwB,IAAA,GAAG,EAAC,eAA5B;AAA4C,IAAA,QAAQ,EAAEmB,YAAtD;AAAoE,IAAA,cAAc,EAAET,kBAApF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACGlC,gBAAgB,CAACyC,MAAjB,GAA0B,CAA1B,gBACC,oBAAC,iBAAD;AAAmB,IAAA,aAAa,EAAED,iBAAlC;AAAqD,IAAA,aAAa,EAAEtC,wBAApE;AAA8F,IAAA,QAAQ,EAAEF,gBAAxG;AAA0H,IAAA,MAAM,EAAE,GAAlI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADD,gBAGC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAJJ,CADF,CADF,CAtBF,CADF;AAqCH,CA3KD", "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Row, Col, Skeleton, Form, Input,But<PERSON>,Drawer,Modal as AntModal } from \"antd\";\r\nimport {\r\nExclamationCircleOutlined\r\n} from \"@ant-design/icons/lib/icons\";\r\nimport styles from \"./index.module.less\";\r\nimport SelectedFilesGrid, {\r\n    ColumnConfig,\r\n} from \"@app/features/FileArea/SelectedFilesGrid\";\r\nimport { IFile } from \"@app/types/fileAreaTypes\";\r\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\r\nimport logger from \"@app/utils/logger\";\r\nimport { FileDetailsOptions } from \"@app/types/FileDetailsOptions\";\r\nimport { FormInstance } from \"antd/lib/form\";\r\nimport { reNameFiles  } from '@app/api/fileAreaService';\r\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\r\n\r\nexport interface ReNameFilesProps {\r\n    onSuccess: () => void;\r\n    selectedFiles: IFile[];\r\n    onClosePopup: (event: boolean) => void;\r\n    options: FileDetailsOptions;\r\n    form: FormInstance;\r\n    showReNameFilesModal:boolean;\r\n}\r\n\r\nexport default (props: ReNameFilesProps) => {\r\n    const [selectedFileList, setSelectedFileList] = useState<IFile[]>([]);\r\n    const reNameFilesColumnConfigs: ColumnConfig[] = [\r\n      { title: '', dataIndex: 'remove', key: 'remove', width: 40 },\r\n      {\r\n        title: 'Title',\r\n        dataIndex: 'title',\r\n        key: 'title',\r\n        ellipsis: true,\r\n        render: (text: any, record: IFile, index: number) => (\r\n          <Form.Item\r\n            name={record.id}\r\n            initialValue={record.title}\r\n            className={styles.reNameInputAreaPanel}\r\n            rules={[\r\n              {\r\n                required: true,\r\n                message: 'Title is required!',\r\n              },\r\n            ]}\r\n          >\r\n            <Input placeholder={record.title} className={styles.reNameInputArea} />\r\n          </Form.Item>\r\n        ),\r\n      },\r\n    ];\r\n    const [reNameFilesDetails, setReNameFilesDetails] = useState<IFile[]>([]);\r\n    const [renameButtonEnable, setRenameButtonEnable] = useState(false);    \r\n    const { confirm } = AntModal;\r\n    const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';\r\n    const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';\r\n\r\n    useEffect(() => {\r\n        setSelectedFileList([]);\r\n        props.form.resetFields();\r\n        const getFiles = async () => {\r\n            const fileList: IFile[] = await Promise.all(\r\n                props.selectedFiles.map(async (file) => ({\r\n                    ...file,\r\n\r\n                }))\r\n            );\r\n            setSelectedFileList(fileList);\r\n        };\r\n        getFiles();\r\n        setRenameButtonEnable(false);\r\n    }, [props.selectedFiles,props.showReNameFilesModal]);\r\n\r\n    const handleValuesChange = (allValues: any) => {\r\n      const isRenamed = selectedFileList.some((file) => {\r\n        const newValue = allValues[file.id]?.trim();\r\n        return newValue && newValue !== file.title.trim();\r\n      });\r\n      setRenameButtonEnable(isRenamed);\r\n    };\r\n\r\n    const handleFilesChange = (fileList: any[]) => {\r\n\r\n        if (fileList.length > 0) {\r\n            setSelectedFileList(fileList);\r\n        } else {\r\n            props.onClosePopup(false);\r\n        }\r\n    };\r\n\r\n\r\n    const handleFinish = (values: any) => {\r\n\r\n        const updatedFileList = selectedFileList.map((file) => ({\r\n            ...file,\r\n            title: values[file.id] || file.title, // Update title if there's a new value\r\n        }));\r\n\r\n           logger.debug('Rename file Component', 'updatedFileList', { updatedFileList });       \r\n           handleReNameFilesUpdateDetails(updatedFileList);\r\n\r\n    };\r\n\r\n    const handleReNameFilesUpdateDetails = (fileList: any[]) => {\r\n    \r\n        const updatedFileList = fileList.map((file: IFile) => ({\r\n          fileId: file.id,\r\n          title: file.title\r\n        }));\r\n    \r\n        reNameFiles(\r\n          updatedFileList\r\n        )\r\n          .then((response) => {\r\n            successNotification([''], 'Renamed Successfully');            \r\n            props.onSuccess();\r\n            resetReNameFilesModal();\r\n          })\r\n          .catch((error) => {\r\n            if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n              errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n            } else {\r\n              errorNotification([''], 'ReName Failed');\r\n            }\r\n            logger.error('File Area Module', 'ReName files', error);\r\n          });\r\n    \r\n      };\r\n\r\n      const resetReNameFilesModal = () => {\r\n        reNameFilesDetails && setReNameFilesDetails([]);\r\n      props.form.resetFields();\r\n      props.onClosePopup(true);\r\n      };\r\n\r\n     const onCancelReNameFilesModal = () => {\r\n        confirm({\r\n          title: DISCARD_MESSAGE,\r\n          icon: <ExclamationCircleOutlined />,\r\n          okText: 'Yes',\r\n          cancelText: 'No',\r\n          onOk() {\r\n            resetReNameFilesModal();\r\n          },\r\n        });\r\n      };\r\n\r\n    const handleShowReNameFilesModalCancel = (hasSelectedFiles = true) => {\r\n        if (hasSelectedFiles) {\r\n          onCancelReNameFilesModal();\r\n        } else {\r\n          resetReNameFilesModal();\r\n        }\r\n      };\r\n\r\n      const handleFileReNameSubmit = () => {\r\n        props.form.submit();\r\n      };\r\n\r\n    return (\r\n      <Drawer\r\n        visible={props.showReNameFilesModal}\r\n        title={'ReName File(s)'}\r\n        onClose={() => handleShowReNameFilesModalCancel()}\r\n        className={'yjDrawerPanel'}\r\n        width={700}\r\n        placement=\"right\"\r\n        footer={[\r\n          <div className={styles.yjReNameFilesInfoFooter}>\r\n            <span className={styles.yjReNameFilesInfo}></span>\r\n\r\n            <div className={styles.yjReNameFilesInfoButtons}>\r\n              <Button key=\"cancel\" type=\"default\" onClick={() => handleShowReNameFilesModalCancel()}>\r\n                cancel\r\n              </Button>\r\n              <Button key=\"rename\" type=\"primary\" onClick={handleFileReNameSubmit} disabled={!renameButtonEnable}>\r\n                Rename\r\n              </Button>\r\n            </div>\r\n          </div>,\r\n        ]}\r\n      >\r\n        <Form form={props.form} key=\"moveFilesForm\" onFinish={handleFinish} onValuesChange={handleValuesChange}>\r\n          <Row gutter={24}>\r\n            <Col span={12}>\r\n              {selectedFileList.length > 0 ? (\r\n                <SelectedFilesGrid onFilesChange={handleFilesChange} columnConfigs={reNameFilesColumnConfigs} dataList={selectedFileList} scroll={200} />\r\n              ) : (\r\n                <Skeleton />\r\n              )}\r\n            </Col>\r\n          </Row>\r\n        </Form>\r\n      </Drawer>\r\n    );\r\n\r\n};"]}, "metadata": {}, "sourceType": "module"}