import React, { Fragment, useCallback, useEffect, useState } from "react";
import { FileTextOutlined } from "@ant-design/icons/lib";
import { List, Button } from "antd";
import download from "downloadjs";
import { useHistory } from "react-router-dom";

import styles from "./index.module.less";
import { IFile } from "@app/types/fileAreaTypes";
import { downloadFile, downloadZipFile } from "@app/api/fileAreaService";
import { errorNotification } from "@app/utils/antNotifications";
import logger from "@app/utils/logger";

const FORBIDDEN_ERROR_CODE = 403;

export enum downloadTypes {
  individual,
  zip,
  checkoutIndividual,
  checkoutZip,
}

export interface IDownloadModal {
  downloadType?: downloadTypes;
  selectedFiles?: IFile[];
  hasDownloaded?: any;
}

const getFileName = (responseFile: any): string => {
  const fileDepositon = responseFile.headers["content-disposition"] as string;
  return fileDepositon.split(";")[1].split("=")[1].trim().replace(/"/g, "");
};

const downloadFiles = (response: any) => {
  const blob = new Blob([response.data]);
  download(blob, getFileName(response));
};

const downloadAndSaveFile = (file: IFile, history: any) => {
  downloadFile(file.id)
    .then((response) => {
      downloadFiles(response);
    })
    .catch((error) => {
      const errorObject = JSON.parse(
        String.fromCharCode.apply(null, new Uint8Array(error) as any)
      );

      if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
        history.push("/forbidden");
      } else {
        errorNotification([""], "Download Failed");
      }

      logger.error("File Management Module", "Download Component", errorObject);
    });
};

const bulkDownload = (fileList: IFile[] | undefined, history: any) => {
  fileList?.forEach((file) => {
    downloadAndSaveFile(file, history);
  });
};

const zipDownload = (fileList: IFile[] | undefined, history: any) => {
  const fileIdList = [] as any;
  fileList?.forEach((file) => {
    fileIdList.push(file.id);
  });

  if (fileIdList.length > 0) {
    downloadZipFile(fileIdList)
      .then((response) => {
        downloadFiles(response);
      })
      .catch((error) => {
        const errorObject = JSON.parse(
          String.fromCharCode.apply(null, new Uint8Array(error) as any)
        );

        if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        } else {
          errorNotification([""], "Download Failed");
        }

        logger.error(
          "File Management Module",
          "Download Component",
          errorObject
        );
      });
  }
};

export default ({
  downloadType = downloadTypes.individual,
  selectedFiles,
  hasDownloaded,
}: IDownloadModal) => {
  const DOWNLOAD_COUNTER_TIME = 10;
  const DOWNLOAD_TIMEOUT = 1000;
  const [timeLeft, setTimeLeft] = useState(DOWNLOAD_COUNTER_TIME);
  const [retry, setRetry] = useState(false);
  useEffect(() => {
    if (!timeLeft) {
      return () => { };
    }
    const intervalId = setInterval(() => {
      setTimeLeft(timeLeft - 1);
    }, DOWNLOAD_TIMEOUT);
    return () => clearInterval(intervalId);
  }, [timeLeft]);
  const [downloadFilesList, setDownloadFilesList] = useState<
    IFile[] | undefined
  >(undefined);

  const history = useHistory();

  const triggerFileDownload = useCallback(
    (timeOutdownload: number) => {
      if (selectedFiles && selectedFiles.length > 0) {
        setDownloadFilesList(selectedFiles);
        downloadType === downloadTypes.individual ||
          downloadType === downloadTypes.checkoutIndividual
          ? setTimeout(() => {
            bulkDownload(downloadFilesList, history);
          }, timeOutdownload)
          : setTimeout(() => {
            zipDownload(downloadFilesList, history);
            setRetry(false);
          }, timeOutdownload);
        setRetry(false);
      }
    },
    [downloadType, selectedFiles, downloadFilesList]
  );

  useEffect(() => {
    const timeOutdownload =
      downloadType === downloadTypes.checkoutIndividual ||
        downloadType === downloadTypes.checkoutZip
        ? DOWNLOAD_TIMEOUT
        : 0;
    if (!retry) {
      triggerFileDownload(timeOutdownload);
    }
  }, [downloadType, triggerFileDownload, retry, downloadFilesList]);
  return (
    <div className={styles.yjModalContentWrapper}>
      <Fragment>
        {downloadType === downloadTypes.individual ||
          downloadType === downloadTypes.checkoutIndividual ? (
          <>
            Your download will start in a few seconds.If not please click below.
            <List
              itemLayout="horizontal"
              dataSource={downloadFilesList}
              renderItem={(item) =>
                item && (
                  <List.Item className={styles.yjDownloadItem}>
                    <List.Item.Meta
                      title={
                        <p className={styles.yjDownloadFileName}>
                          <span className={styles.yjDownloadFileIcon}>
                            <FileTextOutlined />
                          </span>
                          {`${item.title}-${item.id}.${item.type ? item.type : ""
                            }`}
                        </p>
                      }
                    />
                    <div className={styles.yjDownloadBtn}>
                      <Button
                        onClick={() => {
                          setRetry(true);
                          downloadAndSaveFile(item, history);
                        }}
                        type="default"
                      >
                        Download
                      </Button>
                    </div>
                  </List.Item>
                )
              }
            />
          </>
        ) : (
          <>
            If your download didn't start in{" "}
            <span className={styles.yjDigit}>{timeLeft}</span> seconds click
            below
            <List.Item className={styles.yjDownloadItem}>
              <List.Item.Meta
                // title={
                //   <p className={styles.yjDownloadFileName}>
                //     <span className={styles.yjDownloadFileIcon}>
                //       <FileTextOutlined />
                //     </span>
                //     {downloadFilesList
                //       ? `${downloadFilesList[downloadFilesList.length - 1]?.title
                //       }-${downloadFilesList[downloadFilesList.length - 1]?.id
                //       }.zip`
                //       : ""}
                //   </p>
                // }
              />
              <div className={styles.yjDownloadBtn}>
                <Button
                  onClick={() => {
                    setRetry(true);
                    zipDownload(downloadFilesList, history);
                  }}
                  type="default"
                >
                  Download
                </Button>
              </div>
            </List.Item>
          </>
        )}
      </Fragment>
    </div>
  );
};
