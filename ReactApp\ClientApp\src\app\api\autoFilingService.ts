import config from '@app/utils/config';
import http, { httpVerbs } from '@app/utils/http';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import { getParameterizedUrlWith } from "@app/utils";

export const createAutoFilingRule = (data: any) => {
  return http({
    method: httpVerbs.POST,
    url: config.api[OperationalServiceTypes.FileManagementService].autoFilingRules,
    data,
  });
};

export const updateAutoFilingRule = (data: any) => {
  return http({
    method: httpVerbs.PUT,
    url: getParameterizedUrlWith(`${config.api[OperationalServiceTypes.FileManagementService].autoFilingRules}/{ruleId}`, [{ name: "ruleId", value: data.id }]),
    data,
  });
};

export const deleteAutoFilingRule = (data: any) => {
  return http({
    method: httpVerbs.DELETE,
    url: getParameterizedUrlWith(`${config.api[OperationalServiceTypes.FileManagementService].autoFilingRules}/{ruleId}`, [{ name: "ruleId", value: data.id }]),
  });
};

export const deleteAutoFilingRuleLogs = (months: any) => {
  return http({
    method: httpVerbs.DELETE,
    url: getParameterizedUrlWith(`${config.api[OperationalServiceTypes.BffService].autoFilingLogs}/{months}`, [{ name: "months", value: months }]),
  });
};