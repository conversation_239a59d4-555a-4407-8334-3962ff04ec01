import React from "react";
import {Collapse} from "antd";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import Accordion from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

const { Panel } = Collapse;
jest.mock("../index.module.less", () => ({
    yjAccordian: "yjAccordian",
}));

describe("Accordion Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const acComponent = shallow(<Accordion />);
        expect(acComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const acComponent = renderer.create(<Accordion />).toJSON();
        expect(acComponent).toMatchSnapshot();
    });

    it("should have a Collapse element",() => {
        const acComponent = shallow(<Accordion />);
        expect(acComponent.find(Collapse)).toHaveLength(1);
    });

    it("should have Panel elements",() => {
        const acComponent = shallow(<Accordion />);
        expect(acComponent.find(Panel)).toHaveLength(3);
    });

    it("should have a p elements",() => {
        const acComponent = shallow(<Accordion />);
        expect(acComponent.find("p")).toHaveLength(3);
    });

    it("should have div elements",() => {
        const acComponent = shallow(<Accordion />);
        expect(acComponent.find(".yjAccordian")).toHaveLength(1);
    });

});
