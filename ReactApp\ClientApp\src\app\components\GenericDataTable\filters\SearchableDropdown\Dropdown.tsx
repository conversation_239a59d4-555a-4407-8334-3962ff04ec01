import React from 'react';
import { List } from 'antd';

type DropdownProps = {
    onClick: Function,
    data: any[],
    loading: boolean
}
export default (props: DropdownProps) => {

    const populatedList = props.data.map(i => <List.Item key={i} onClick={() => props.onClick(i)}>{i}</List.Item>);

    return (
        <>
            <List size={'small'} loading={props.loading}>
                {populatedList}
            </List>
        </>
    )

}
