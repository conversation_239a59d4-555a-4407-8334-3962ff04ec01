import ClearFilter from "..";
import React from "react";
import renderer from "react-test-renderer";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import { mount } from "enzyme";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);

const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);
const initialState = {
  tableKey: "",
  columns: [Object],
  selectedElement: { name: "", checked: true },
  filters: [],
  showFilter: false,
  selected: false,
  columnQueryParameters: [],
  searchQueryParameters: [],
};

const store = mockStore(initialState);
const wrapper = mount(
  <ReduxProvider store={store}>
    <ClearFilter />
  </ReduxProvider>
);

describe("Cleat Filter Test Suit", () => {
  it("<ClearFilter/> should render", () => {
    expect(wrapper.html()).not.toBeNull();
  });
  it("<ClearFilter/> should render and create the snapshot properly", () => {
    const component = renderer.create(wrapper).toJSON();
    expect(component).toMatchSnapshot();
  });
});
