import React, { useContext, useState, useEffect } from "react";
import { But<PERSON>, Checkbox, Dropdown, Menu, Tooltip } from "antd";
import {
  SettingOutlined,
  DownOutlined,
  ClearOutlined,
} from "@ant-design/icons";

import FilterCloud from "./FilterCloud";
import FilterManagement from "./FilterManagement";
import { DataTableContext } from "../DataTableContext";
import {
  updateSingleColumn,
  clearFilters as clearFiltersAction,
  updateColumns,
  removeFilter,
} from "../DataTableContext/actions";
import styles from "./index.module.less";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import {
  updateGridSingleColumn,
  updateGridFilters,
  filterCloudUpdated,
  hideSaveNewFilterBtn,
  removeSelectedSavedFilter,
  updateGridHasUpdates,
} from "@app/redux/actions/gridsActions";
import useComponentVisible from "@app/hooks/useComponentVisible";
import { GridRefreshIcon } from "../GridRefresh";

type TableHeaderProps = {
  tableKey?: string;
  hideHeaderPanel?: boolean;
  filterCloudPadding?: any;
  displayBulkDelete?: boolean;
  headerActionPanel?: JSX.Element;
  groupedValue?: string;
  hasFilterManagement?: boolean;
  hasRefreshButton?: boolean;
  updateGrid?: () => void;
};

const SELECT_ALL_KEY = "selectAll";
const SELECT_ALL = "Select All";

export default (props: TableHeaderProps) => {
  const { state, dispatch } = useContext(DataTableContext);
  const dispatchGrid = useDispatch();
  const [displayClearFilter, setDisplayClearFilter] = useState(false);
  const [optionalColumns, setOptionalColumns] = useState<Array<any>>([]);
  const [selectAllOption, setSelectAllOption] = useState<any>({
    data_type: "text",
    default: false,
    filter: false,
    filter_data: undefined,
    filter_type: "search",
    key: SELECT_ALL_KEY,
    selected: false,
    sorter: false,
    title: SELECT_ALL,
  });

  const {
    selectedElement,
    tableKey,
    filters,
    selected,
    columns,
    hasUpdates,
  } = useSelector((store: RootState) => store.grid);

  const {
    ref,
    isComponentVisible,
    setIsComponentVisible,
  } = useComponentVisible(false);

  useEffect(() => {
    const _optionalColumns = state.columns.filter((column) => !column.default);
    const allSelected = _optionalColumns.filter(
      (x: any) => x.key !== SELECT_ALL_KEY && !x.selected
    );
    setSelectAllOption({
      ...selectAllOption,
      selected: allSelected.length === 0,
      title: <span className={styles.yjSelectDeSelect}>{SELECT_ALL}</span>,
    });
    _optionalColumns.unshift(selectAllOption);
    setOptionalColumns(_optionalColumns);
  }, [state]);

  useEffect(() => {
    if (Object.keys(state.filters).length > 0) {
      dispatchGrid(updateGridFilters(true));
      setDisplayClearFilter(true);
      filterCloudUpdated(state.filters);
    } else {
      dispatchGrid(updateGridFilters(false));
      setDisplayClearFilter(false);
    }
  }, [state.filters, dispatchGrid]);

  useEffect(() => {
    dispatchGrid(
      updateGridSingleColumn({
        selectedElement: { name: "", checked: false },
        tableKey: props.tableKey,
        selected: false,
      })
    );
  }, [dispatchGrid, props.tableKey]);

  useEffect(() => {
    if (selected && tableKey === props.tableKey) {
      if (selectedElement.multiple) {
        const optionalGridColumns = columns.filter((x: any) => !x.default);
        dispatch(
          updateColumns(
            state.columns.map((column) => {
              if (column.default) {
                return column;
              } else {
                const optionalCol = optionalGridColumns.find(
                  (optionalColumn: any) => optionalColumn.key === column.key
                );
                const isEnable = optionalCol ? optionalCol.selected : false;
                return { ...column, selected: isEnable };
              }
            }),
            props.tableKey
          )
        );
        onRemoveAllFilter();
      } else {
        onRemoveFilter(selectedElement.name);
        dispatch(
          updateSingleColumn(
            selectedElement.name,
            selectedElement.checked,
            tableKey
          )
        );
      }
    }
  }, [selectedElement, dispatch, tableKey, props.tableKey, selected]);

  useEffect(() => {
    dispatch(clearFiltersAction());
  }, [filters, dispatch]);

  useEffect(() => {
    return () => {
      clearFilters();
    };
  }, []);

  const onClickCheckbox = (e: any) => {
    const elem = e.target;

    if (elem.name === SELECT_ALL_KEY) {
      if (!elem.checked) {
        onRemoveAllFilter();
      }
      dispatch(
        updateColumns(
          [
            ...state.columns.map((column) => {
              if (!column.default) {
                return { ...column, selected: elem.checked };
              }
              return column;
            }),
          ],
          props.tableKey
        )
      );
    } else {
      dispatch(updateSingleColumn(elem.name, elem.checked, props.tableKey));
      if (!elem.checked) {
        onRemoveFilter(elem.name);
      }
    }
  };

  const onRemoveFilter = (name: string) => {
    const filteeredtTags = state.filters.filter(
      (filter: any) => filter.key !== name
    );
    dispatch(removeFilter(filteeredtTags));
  };

  const onRemoveAllFilter = () => {
    const optionalColumnKeys = optionalColumns.map(
      (optionalColumn: any) => optionalColumn.key
    );
    dispatch(
      removeFilter(
        state.filters.filter(
          (filter) => !optionalColumnKeys.includes(filter.key)
        )
      )
    );
  };

  const clearFilters = () => {
    dispatch(clearFiltersAction());
    dispatchGrid(hideSaveNewFilterBtn());
    dispatchGrid(removeSelectedSavedFilter());
  };

  const onRefreshGrid = () => {
    props.updateGrid?.();
    dispatch(updateGridHasUpdates(false));
  };

  const generateMenu = () => {
    return (
      <Menu>
        {optionalColumns.map((element: any) => {
          return (
            <Menu.Item key={element.key}>
              <Checkbox
                key={element.key}
                disabled={element.default}
                name={element.key}
                onClick={onClickCheckbox}
                checked={element.selected}
              >
                {element.title}
              </Checkbox>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  };

  const shouldDisplayColumnSelector = (): boolean => {
    return !!(state.columns && state.columns.filter((i) => !i.default).length);
  };

  return (
    <div className={styles.yjFilterArea}>
      <div
        hidden={props.hideHeaderPanel}
        // style={{
        //   display:
        //     shouldDisplayColumnSelector() || displayClearFilter ? "" : "none",
        // }}
        className={styles.yjTableHeaderWrapper}
      >
        {props.hasRefreshButton && (
          <GridRefreshIcon
            hasUpdates={hasUpdates}
            onRefreshGrid={onRefreshGrid}
          />
        )}
        {props.hasFilterManagement && (
          <FilterManagement
            groupedValue={props.groupedValue}
            tableKey={props.tableKey}
          />
        )}

        {props.headerActionPanel}
        <Button
          icon={<ClearOutlined />}
          className={styles.yjClearFilterBtn}
          onClick={clearFilters}
          style={{ display: displayClearFilter ? "" : "none" }}
        >
          Clear Filters
        </Button>
        <div
          ref={ref}
          id="columnFilter"
          style={{
            display: shouldDisplayColumnSelector() ? "" : "none",
            position: "relative",
          }}
        >
          <Dropdown
            visible={isComponentVisible}
            key="columnFilter"
            trigger={["click"]}
            overlay={generateMenu}
            getPopupContainer={() =>
              document.getElementById("columnFilter") as HTMLElement
            }
          >
            <Button
              onClick={() => setIsComponentVisible(!isComponentVisible)}
              icon={<SettingOutlined />}
            >
              Columns <DownOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>

      {/* Column Filter Section */}
      <FilterCloud filterCloudPadding={props.filterCloudPadding} />
      {/* Column Filter Section Over */}
    </div>
  );
};
