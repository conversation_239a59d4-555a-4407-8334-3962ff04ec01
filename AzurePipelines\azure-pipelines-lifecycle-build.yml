name: $(Year:yy).$(DayOfYear)$(Rev:.r)
trigger:
  batch: true
  branches:
    include:
      - "*"
    exclude:
      - master
      - dev
      - release/*

resources:
  repositories:
    - repository: pipelineTemplates
      type: git
      name: MCP/AzurePipelineTemplates
      ref: refs/heads/master

variables:
  - group: GlobalVariablesLibrary
  - template: "VariableTemplates/vars-global.yml@pipelineTemplates"
  - name: isRelease
    value: $[contains(variables['System.PullRequest.TargetBranch'],'release/')]
  - name: Work_Directory
    value: "$(System.DefaultWorkingDirectory)"
  - name: Branch
    value: "$(Build.SourceBranchName)"

parameters:
  - name: publishContainers
    type: object
    default:
      - projectName: "dmsfeinternal"
        dockerBuildFile: "ReactApp/Dockerfile"
        supportedPlatform: "Linux"
        deploymentPlatform: "Linux"
        bindingHttpPort: 80
        bindingHttpsPort: 0
        containerServiceFilter: "Dms:FEInternal"
        ingressHostName: "dms"
        deploymentTemplate: "Deployment.template"
        npmWorkingFile: ReactApp/ClientApp/.npmrc
        versionConfigFile: ReactApp/ClientApp/public/env-config-version.js

  - name: deploymentEnvironments
    type: object
    default:
      - environmentName: "Sandbox"
        deploymentEnvironment: ["Sandbox.sandbox"]
        virtualMachineEnvironment: []
        libraryEnvironment: "Sandbox"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild"]
        deploymentConditions: "and(succeeded(), eq(variables.isGitPush, 'true'))"

      - environmentName: "DEV"
        deploymentEnvironment: ["Dev.dev"]
        virtualMachineEnvironment: []
        libraryEnvironment: "DEV"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild"]
        deploymentConditions: "and(succeeded(), eq(variables.isPullRequest, 'true'), eq(variables.isRelease, 'false'))"

      - environmentName: "QA"
        deploymentEnvironment: ["QA.qa"]
        virtualMachineEnvironment: []
        libraryEnvironment: "QA"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: "STG"
        deploymentEnvironment: ["Staging.stg"]
        virtualMachineEnvironment: []
        libraryEnvironment: "STG"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "QA"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: 'DEV-EUS-PRE'
        deploymentEnvironment: ['DEV-EUS-PRE.pre']
        virtualMachineEnvironment: []
        libraryEnvironment: 'DEV-EUS-PRE'
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "STG"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"
        
      - environmentName: "ProductionEUS"
        deploymentEnvironment: ["ProductionEUS.production"]
        virtualMachineEnvironment: []
        libraryEnvironment: "ProductionEUS"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "DEV-EUS-PRE"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

      - environmentName: "ProductionUKS"
        deploymentEnvironment: ["ProductionUKS.production"]
        virtualMachineEnvironment: []
        libraryEnvironment: "ProductionUKS"
        dependencyMatrix: ["ArtifactsBuild", "ContainerBuild", "DEV-EUS-PRE"]
        deploymentConditions: "and(succeeded(), eq(variables.isRelease, 'true'))"

stages:
  - stage: ArtifactsBuild
    jobs:
      - job: ArtifactsBuild
        variables:
          - name: CurrentBuildPlatform
            value: "linux"
          - name: solutionPath
            value: "FrontEnd-Internal.sln"
          - name: buildType
            value: "Debug"

        pool:
          name: Azure Pipelines
          vmImage: "windows-2022"
        steps:
          - checkout: self
            clean: true
            persistCredentials: true

          - task: npmAuthenticate@0
            inputs:
              workingFile: ReactApp/ClientApp/.npmrc

          - task: DotNetCoreCLI@2
            displayName: Build Solution
            inputs:
              command: 'build'
              projects: ${{ variables.solutionPath }}
              arguments: '--configuration $(buildType)'

          - task: PublishBuildArtifacts@1
            inputs:
              pathToPublish: "$(Build.SourcesDirectory)/AzurePipelines/ConfigurationTemplates"
              artifactName: configtemplates

  - stage: ContainerBuild
    displayName: "Build Containers"
    dependsOn: ArtifactsBuild
    jobs:
      - job: LinuxContainerBuild
        variables:
          - name: currentBuildPlatform
            value: "linux"
          - name: projectName
            value: "dmsfeinternal"

        pool:
          name: Azure Pipelines
          vmImage: "ubuntu-latest"
        steps:
          - checkout: self
            clean: true
            persistCredentials: true

          - ${{ each container in parameters.publishContainers }}:
              - task: npmAuthenticate@0
                inputs:
                  workingFile: ${{ container.npmWorkingFile }}

              - task: qetza.replacetokens.replacetokens-task.replacetokens@5
                displayName: "Replace tokens in ${{ container.versionConfigFile }}"
                inputs:
                  rootDirectory: ""
                  targetFiles: ${{ container.versionConfigFile }}
                  verbosity: detailed
                  tokenPattern: "doublebraces"
                  actionOnMissing: fail
                  actionOnNoFiles: fail
                  inlineVariables: |
                    version: $(Build.BuildNumber)

              - task: Docker@2
                displayName: "Compose and Push ${{ parameters.containerName }}"
                inputs:
                  containerRegistry: $(AzureContainerRegistry)
                  repository: "${{lower(container.projectName)}}-$(currentBuildPlatform)"
                  command: "buildAndPush"
                  Dockerfile: "${{ container.dockerBuildFile }}"
                  buildContext: "$(Build.Repository.LocalPath)"
                  tags: "$(Build.BuildNumber)"

  - ${{ each env in parameters.deploymentEnvironments }}:
      - stage: ${{replace(env.environmentName,'-','_')}}
        displayName: "${{ env.environmentName }} CD Pipeline"
        variables:
          - group: GlobalVariablesLibrary
          - group: ${{env.libraryEnvironment}}
          - template: VariableTemplates/vars-deploy-${{ env.environmentName }}.yml
          - name: CurrentEnvironment
            value: ${{env.environmentName}}

        dependsOn:
          - ${{ each dep in env.dependencyMatrix }}:
              - ${{replace(dep,'-','_')}}

        ${{ if ne(env.deploymentConditions, '') }}:
          condition: ${{ env.deploymentConditions }}

        jobs:
          - ${{ each deploy in env.deploymentEnvironment }}:
              - deployment: ${{replace(replace(deploy,'.','_'),'-','_')}}
                displayName: "Deploy Containers To ${{ deploy }}"
                pool:
                  name: Azure Pipelines
                  vmImage: "ubuntu-latest"
                environment: ${{ deploy }}

                workspace:
                  clean: all
                strategy:
                  runOnce:
                    deploy:
                      steps:
                        - checkout: none
                        - download: current
                          artifact: configtemplates
                        - ${{ each container in parameters.publishContainers }}:
                            - task: Kubernetes@1
                              displayName: "Deploy $(AksNamespace)-${{ container.projectName }} Cluster Config Maps"
                              inputs:
                                command: apply
                                useConfigurationFile: true
                                configurationType: inline
                                inline: |
                                  apiVersion: v1
                                  kind: ConfigMap
                                  metadata:
                                    name: $(AksNamespace)-${{ container.projectName }}-config
                                    namespace: $(AksNamespace)
                                  data:
                                    env-config.js: |
                                      window._env_ = {
                                        REACT_APP_DISCOVERY_URL: "https://$(DiscoveryAdvertisedAddress)",
                                        REACT_APP_LOG_LEVEL: "$(LogLevel)",
                                        REACT_APP_BACKEND_ENV: "$(AksNamespace)",
                                        REACT_APP_GAINSIGHT_ENVIRONMENT: "$(GainsightEnvironment)",
                                        REACT_APP_GAINSIGHT_CONTAINER: "$(GainsightContainer)",
                                        REACT_APP_OKTA_CLIENT_ID: "$(OKTAClientId)",
                                        REACT_APP_DEFAULT_CULTURE_CODE: "$(DefaultCultureCode)",
                                        REACT_APP_VERSION: "$(Version)",
                                        REACT_APP_VERSION_SHOW: "$(VersionShow)",
                                      };

                            - template: DeploymentTemplates/Deployment.yml
                              parameters:
                                targetContainer: "${{lower(container.projectName)}}-${{lower(container.deploymentPlatform)}}"
                                targetContainerTag: "$(Build.BuildNumber)"
                                serviceName: ${{lower(container.projectName)}}
                                numberOfContainerInstances: ${{variables.NumberofContainerInstances}}
                                advertisedAddress: "${{lower(container.ingressHostName)}}.$(DnsDomain)"
                                azureConfigurationLabel: $(AzureConfigurationLabel)
                                azureConfigurationFilter: ${{container.containerServiceFilter}}
                                aksDeploymentOs: ${{lower(container.deploymentPlatform)}}
                                aksDeploymentNamespace: "$(AksNamespace)"
                                aksIngressClass: "$(AzureIngressClass)"
                                aksIngressTlsIssuer: "$(GlobalTlsIssuer)"
                                containerPullAddress: "$(AzureContainerRegistryEndpoint)"
                                aksTemplateLocation: "$(Pipeline.Workspace)/configtemplates"
                                aksTemplateName: ${{container.deploymentTemplate}}
                                configMapName: "$(AksNamespace)-${{ container.projectName }}-config"
                                cpuRequests: ${{variables.ContainerCpuRequests}}
                                cpuLimit: ${{variables.ContainerCpuLimit}}
                                memoryRequests: ${{variables.ContainerMemoryRequests}}
                                memoryLimit: ${{variables.ContainerMemoryLimit}}
                                livenessProbeInitialDelaySeconds: ${{variables.LivenessProbeInitialDelaySeconds}}
                                readinessProbeInitialDelaySeconds: ${{variables.ReadinessProbeInitialDelaySeconds}}
                                configMapMountPath: "/app/wwwroot/env-config.js"
                                configMapMountSubPath: "env-config.js"
                                hpaMinReplicas: ${{ variables.ContainerHpaMinReplicas }}
                                hpaMaxReplicas: ${{ variables.ContainerHpaMaxReplicas }}
                                hpaMemoryAverageUtilization: ${{ variables.ContainerHpaMemoryAverageUtilization }}
                                hpaCpuAverageUtilization: ${{ variables.ContainerHpaCpuAverageUtilization }}
