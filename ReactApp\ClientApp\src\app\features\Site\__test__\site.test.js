import { shallow, mount } from "enzyme";
import React from "react";
import { Form, Input, Select, Button } from "antd";
import TextArea from "antd/lib/input/TextArea";

import Site from "../index";
import initTestSuite from "@app/utils/config/TestSuite";
import InfinitySelect from "@app/components/InfinitySelect";
import Modal from "@app/components/Modal";

describe("<Site/> test suite", () => {
  beforeAll(() => {
    initTestSuite();
  });
  it("Should render the <Site/>", () => {
    const component = shallow(<Site />);
    expect(component.html()).not.toBe(null);
  });
  it("Should render the <Site/> poperly when the formref is null", () => {
    const component = shallow(<Site formRef={null} />);
    expect(component.html()).not.toBe(null);
  });
  it("Should render the <Site/> poperly when the siteDetails is null", () => {
    const component = shallow(<Site siteDetails={[]} />);
    expect(component.html()).not.toBe(null);
  });
  it("Should render the <Site/> poperly when the action is save", () => {
    const component = shallow(<Site action="save" />);
    expect(component.html()).not.toBe(null);
  });
  it("Should render the <Site/> poperly when the action is edit", () => {
    const component = shallow(<Site action="edit" />);
    expect(component.html()).not.toBe(null);
  });
  it("Should render the <Site/> poperly when the action is view", () => {
    const component = shallow(<Site action="view" />);
    expect(component.html()).not.toBe(null);
  });

  it("should have a form", () => {
    const component = shallow(<Site />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 8 form input fields", () => {
    const component = shallow(<Site action="save" />);
    expect(component.find(Form.Item)).toHaveLength(8);
  });

  it("should have 3 form input fields", () => {
    const component = mount(<Site action="save" />);
    expect(component.find(Input).length).toEqual(3);
  });
  it("should have 3 form Select fields", () => {
    const component = mount(<Site action="save" />);
    expect(component.find(Select).length).toEqual(3);
  });
  it("should have 1 TextArea", () => {
    const component = mount(<Site action="save" />);
    expect(component.find(TextArea).length).toEqual(1);
  });
  it("should have 2 Buttons", () => {
    const component = mount(<Site action="save" />);
    expect(component.find(Button).length).toEqual(2);
  });

  it("should have 2 InfinitySelect input Field", () => {
    const component = mount(<Site action="save" />);
    expect(component.find(InfinitySelect).length).toEqual(2);
  });
  it("Panel should contain 2 Modals", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).length).toBe(2);
  });
  it("First Modal should have a title called Create Contact", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).at(0).props().title).toBe("Create Contact");
  });
  it("First Modal visible property should be false by default", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).at(0).props().visible).toBe(false);
  });
  it("First Modal destroyOnClose property should be true by default", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).at(0).props().destroyOnClose).toBe(true);
  });

  it("First Modal should have a button as the first element in the  footer ", () => {
    const component = mount(<Site />);
    const contactModalfotter = component.find(Modal).at(0).props().footer[0];
    expect(contactModalfotter.type.displayName).toBe("Button");
  });

  it("First Modal should have a button labled Close the first element in the footer ", () => {
    const component = mount(<Site />);
    const contactModalfotter = component.find(Modal).at(0).props().footer[0];
    expect(contactModalfotter.props.children).toBe("CANCEL");
  });

  it("First Modal should have a button type of primary the first element in the footer ", () => {
    const component = mount(<Site />);
    const contactModalfotter = component.find(Modal).at(0).props().footer[0];
    expect(contactModalfotter.props.type).toBe("default");
  });

  it("First Modal should have a button as the first element in the  footer ", () => {
    const component = mount(<Site />);
    const contactModalfotter = component.find(Modal).at(0).props().footer[1];
    expect(contactModalfotter.type.displayName).toBe("Button");
  });

  it("First Modal should have a button labled Close the first element in the footer ", () => {
    const component = mount(<Site />);
    const contactModalfotter = component.find(Modal).at(0).props().footer[1];
    expect(contactModalfotter.props.children).toBe("CREATE");
  });

  it("First Modal should have a button type of primary the first element in the footer ", () => {
    const component = mount(<Site />);
    const contactModalfotter = component.find(Modal).at(0).props().footer[1];
    expect(contactModalfotter.props.type).toBe("primary");
  });

  it("Second Modal should have a title called Create Contact", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).at(1).props().title).toBe("Selected Contacts");
  });
  it("Second Modal visible property should be false by default", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).at(1).props().visible).toBe(false);
  });
  it("Second Modal destroyOnClose property should be false by default", () => {
    const component = mount(<Site />);
    expect(component.find(Modal).at(1).props().destroyOnClose).toBe(false);
  });
});
