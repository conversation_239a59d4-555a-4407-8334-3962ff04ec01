@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../../styles/';

.yjAddUserManagementWrapper {
  background-color: @color-bg-content-wrapper;
  padding: 1.5em;

  button {
    margin-top: 30px;
    padding: 0;
  }
}

.yjModuleSubHeading {
  color: @color-secondary;
  font-size: @font-size-lg;
  margin-bottom: 1em;
  margin-top: 20px;
  text-transform: @yj-transform;

  .font-mixin(@font-primary, @yjff-bold);
}

// previously selected infinity list

.yjManageUsersInfinityListComponent {
  display: flex;
  width: 100%;

  .yjManageUsersListItemValue {
    width: 92%;

    p {
      margin-bottom: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;

      svg {
        color: @color-secondary;
        margin-right: 5px;
      }
    }
  }

  .yjManageUsersListItemAction {

    button {
      background: @color-danger;
      border: none;
      box-shadow: none;
      color: @color-font-white;
      height: 25px;
      width: 25px;
    }
    .flex-mixin(center, flex, flex-end);
  }
}

.yjBadge {
  background: @color-primary;
  border-radius: 10px;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  height: 30px;
  line-height: 20px;
  margin-left: 5px;
  min-width: 30px;
  padding: 6px;
  text-align: center;
  white-space: nowrap;
  z-index: auto;
}

// newly selected ant list

.yjManageUsersNewlySelectedUsersList {
  display: flex;
  width: 100%;

  .yjManageUsersNewlySelectedUsersListLeft {
    width: 95%;

    p {
      margin-bottom: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;

      svg {
        color: @color-secondary;
        margin-right: 5px;
      }
    }

    .yjManageUsersNewlySelectedUsersListName {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;
    }
  }

  .yjManageUsersNewlySelectedUsersListRight {

    button {
      background: @color-danger;
      border: none;
      box-shadow: none;
      color: @color-font-white;
      height: 25px;
      width: 25px;
    }
    .flex-mixin(center, flex, flex-end);
  }
}

.yjModalContentWrapper {
  margin: -24px;
  max-height: 65vh;
  overflow: hidden auto;
  padding: 1.5em;
}
