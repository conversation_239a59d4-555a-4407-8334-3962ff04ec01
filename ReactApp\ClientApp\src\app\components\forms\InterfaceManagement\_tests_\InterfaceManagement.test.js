import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import InterfaceManagement from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

jest.mock("../index.module.less", () => ({
    sampleStyle: "sampleStyle",
}));

describe("InterfaceManagement Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const imComponent = shallow(<InterfaceManagement />);
        expect(imComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const imComponent = renderer.create(<InterfaceManagement />).toJSON();
        expect(imComponent).toMatchSnapshot();
    });

    it("should have a div element",() => {
        const imComponent = shallow(<InterfaceManagement />);
        expect(imComponent.find(".sampleStyle")).toHaveLength(1);
    });
});
