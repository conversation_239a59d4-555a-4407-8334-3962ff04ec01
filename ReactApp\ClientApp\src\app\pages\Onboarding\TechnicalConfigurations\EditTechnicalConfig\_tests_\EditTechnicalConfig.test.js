import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import EditTechnicalConfig from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../../components/PageTitle";
import PageContent from "../../../../../components/PageContent";
import TechnicalConfigurations from "../../../../../components/forms/TechnicalConfigurations";

describe("EditTechnicalConfig Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const etcComponent = shallow(<EditTechnicalConfig />);
        expect(etcComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const etcComponent = renderer.create(<EditTechnicalConfig />).toJSON();
        expect(etcComponent).toMatchSnapshot();
    });

    it("should have a PageTitle component",() => {
        const etcComponent = shallow(<EditTechnicalConfig />);
        expect(etcComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const etcComponent = shallow(<EditTechnicalConfig />);
        expect(etcComponent.find(PageContent)).toHaveLength(1);
    });

    it("should have a IntegrationManagement component",() => {
        const etcComponent = shallow(<EditTechnicalConfig />);
        expect(etcComponent.find(TechnicalConfigurations)).toHaveLength(1);
    });
});





