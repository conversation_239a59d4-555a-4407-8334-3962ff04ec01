import React, { useEffect, useState } from "react";
import { ColumnsType } from "antd/lib/table";
import { Button, Skeleton } from "antd";
import {
  CheckCircleOutlined,
  CloseCircleOutlined,
  DownloadOutlined,
} from "@ant-design/icons";
import download from "downloadjs";

import styles from "./index.module.less";
import DashboardGenericTable from "../DashboardGenericTable";
import config from "@app/utils/config";
import {
  downloadFile,
  getFullfiledFilesDetails,
  checkSiteAvailable,
} from "@app/api/fileAreaService";
import NonAuthorized from "@app/components/NonAuthorized";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

const ACCEPTED_FILE_STATUS = 2;

export default (props: any) => {
  const [loading, setLoading] = useState<boolean>(false);
  const [hasSite, setHasSite] = useState<boolean>(true);
  const [response, setResponse] = useState<any>();

  useEffect(() => {
    setLoading(true);
    checkSiteAvailable(props.channelId)
      .then(() => {
        setHasSite(true);
        getFullfiledFilesDetails(props.channelId)
          .then((responseValue) => {
            setLoading(false);
            setResponse(responseValue.data);
          })
          .catch(() => {
            setLoading(false);
            setResponse(undefined);
          });
      })
      .catch(() => {
        setHasSite(false);
        setLoading(false);
        setResponse(undefined);
      });
  }, [props.channelId]);

  const getFileName = (responseFile: any): string => {
    const fileDepositon = responseFile.headers["content-disposition"] as string;
    return fileDepositon.split(";")[1].split("=")[1].trim().replace(/"/g, "");
  };

  const downloadFiles = (downnloadedData: any) => {
    const blob = new Blob([downnloadedData.data]);
    download(blob, getFileName(downnloadedData));
  };

  const onClickDownload = (record: any) => {
    downloadFile(record.fileId)
      .then((responseValue) => {
        downloadFiles(responseValue);
      })
      .catch((error) => {
        const errorObject = JSON.parse(
          String.fromCharCode.apply(null, new Uint8Array(error) as any)
        );
        if (errorObject.statusCode === FORBIDDEN_ERROR_CODE) {
          props.history.push("/forbidden");
        }
      });
  };

  const columns: ColumnsType = [
    {
      title: "",
      dataIndex: "",
      key: "download",
      render: (value, record: any) =>
        record.status.value === ACCEPTED_FILE_STATUS && (
          <Button
            className={"yjNoWrapperButton"}
            onClick={() => onClickDownload(record)}
            icon={<DownloadOutlined />}
          ></Button>
        ),
      width: "10%",
    },

    {
      dataIndex: "fileName",
      className: "yjDashboradTextWrap",
      title: "File Name",
      width: "30%",
      render: (value: any) => {
        return `${value}`;
      },
    },

    {
      dataIndex: "requestName",
      className: "yjDashboradTextWrap",
      title: "Request",
      width: "25%",
      render: (value: any) => {
        return `${value}`;
      },
    },

    {
      dataIndex: "dateTimeline",
      title: "Date",
      width: "25%",
      render: (value: any) => {
        return `${value}`;
      },
    },
    {
      dataIndex: "user",
      className: "yjDashboradTextWrap",
      title: "Actioned By",
      width: "30%",
      render: (value: any) => {
        return `${value.name}`;
      },
    },

    {
      dataIndex: "status",
      className: "yjDashboradTextCenter",
      title: "Status",
      width: "25%",
      render: (value: any) => {
        return value.value === ACCEPTED_FILE_STATUS ? (
          <div className="yjGridTextCenterDashboard">
            <CheckCircleOutlined
              style={{ color: "green", fontSize: "1.5em" }}
            />
          </div>
        ) : (
          <div className="yjGridTextCenterDashboard">
            <CloseCircleOutlined style={{ color: "red", fontSize: "1.5em" }} />
          </div>
        );
      },
    },
  ];
  return (
    <>
      {loading ? (
        <Skeleton />
      ) : response && !!response.totalRecordCount ? (
        <>
          {" "}
          <DashboardGenericTable
            dataKey="fileId"
            endpoint={
              config.api[OperationalServiceTypes.PortalService]
                .getFullfilledFiles
            }
            columns={columns}
            {...props}
            options={{ queryParameters: { channelId: props.channelId } }}
            onErrorLoading={(error) => {
              if (error.statusCode === FORBIDDEN_ERROR_CODE) {
                props.history.push("/forbidden");
              }
            }}
          />
          <p>Last 7 days</p>
        </>
      ) : (
        <NonAuthorized
          styleClassName={styles.yjNonAuthorizedDashboardWrapper}
          title={!hasSite ? "No Sites Available" : "All Good Here!"}
          subTitle={
            !hasSite
              ? "Create a Site or Request your Organisation’s Administrator to Create a Site for this office"
              : "No files have been accepted or rejected in the past 7 days"
          }
        />
      )}
    </>
  );
};
