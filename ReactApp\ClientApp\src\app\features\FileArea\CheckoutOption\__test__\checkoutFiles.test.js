import React from "react";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { Form } from "antd";
import initTestSuite from "@app/utils/config/TestSuite";
import {Provider} from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import Page from "../index";

const fileList = [
  { id: "AA-00172", title: "File 1" },
  { id: "AA-00173", title: "File 2" },
];

const ReduxProvider = ({ children, store }) => (
    <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const CustomPage = (props) => {
  const INITIAL_STATE = {
    fileArea: {
      fileAreaSettings: {

      }
    }
  }
  const store = mockStore(INITIAL_STATE);

  return (
      <ReduxProvider store={store}>
        <Page {...props}/>
      </ReduxProvider>
  );
};

describe("Checkout Files Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("Checkout files component should render", () => {
    const component = mount(<CustomPage />);
    expect(component.html()).not.toBe(null);
  });

  it("Checkout Files component should render and create the snapshot properly", () => {
    const component = renderer.create(<CustomPage />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it(" Checkout Files component should have a form", () => {
    const component = mount(<CustomPage />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it(" Checkout Files component should have 6 form items initially without any props", () => {
    const component = mount(<CustomPage />);
    expect(component.find(Form.Item)).toHaveLength(6);
  });

  it(" Checkout Files component should have a form list", () => {
    const component = mount(<CustomPage />);
    expect(component.find(Form.List)).toHaveLength(1);
  });

  it(" Checkout Files component shouldrender with fileList prop", () => {
    const component = mount(<CustomPage fileList={fileList} />);
    expect(component.html()).not.toBe(null);
  });

  it("Checkout Files component should render and create the snapshot properly with fileList prop", () => {
    const component = renderer.create(<CustomPage fileList={fileList} />).toJSON();
    expect(component).toMatchSnapshot();
  });
});
