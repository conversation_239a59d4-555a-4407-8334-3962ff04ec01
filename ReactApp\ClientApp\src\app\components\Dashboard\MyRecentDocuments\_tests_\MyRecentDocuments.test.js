import React from "react";
import renderer from "react-test-renderer";
import { Table } from "antd";
import { shallow } from "enzyme";

import initTestSuite from "@app/utils/config/TestSuite";
import MyRecentDocuments from "../index";

describe("My Recent Documents Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const mrdComponent = shallow(<MyRecentDocuments />);
    expect(mrdComponent.html()).not.toBe(null);
  });

  it("should create and match to snapshot", () => {
    const mrdComponent = renderer.create(<MyRecentDocuments />).toJSON();
    expect(mrdComponent).toMatchSnapshot();
  });

  it("should have a Table component", () => {
    const mrdComponent = shallow(<MyRecentDocuments />);
    expect(mrdComponent.find(Table)).toHaveLength(1);
  });
});
