import React from 'react';
import { Drawer, Button } from 'antd';
import SubmitButton from '@app/components/SubmitButton';
import styles from './PublishFilesDrawer.module.less';
import PublishFilesDrawerContent from './PublishFilesDrawerContent';

interface PublishFilesDrawerProps {
  isLoading: boolean;
  visible: boolean;
  publishFileList: any[];
  onClose: () => void;
  onFileListChange: (files: any[]) => void;
  onPublish: () => void;
  expirationDate: moment.Moment | undefined;
  setExpirationDate: (date: moment.Moment | undefined) => void;
}

const PublishFilesDrawer: React.FC<PublishFilesDrawerProps> = ({
  isLoading,
  visible,
  publishFileList,
  onClose,
  onFileListChange,
  onPublish,
  expirationDate,
  setExpirationDate,
}) => {
  return (
    <Drawer
      destroyOnClose={true}
      width={700}
      key={'publishfilesmodal'}
      visible={visible}
      title={'Publish File(s)'}
      onClose={onClose}
      className={"yjDrawerPanel"}
      footer={[
        <Button key={'cancelPublish'} onClick={onClose} disabled={isLoading} type="default">
          Cancel
        </Button>,
        <SubmitButton
          key={'publishFiles'}
          onClick={onPublish}
          type="primary"
          disabled={!publishFileList.some((file: any) => file.checked)}
        >
          Publish
        </SubmitButton>,
      ]}
    >
      <div className={styles.yjModalContentWrapper}>
        <PublishFilesDrawerContent
          fileList={publishFileList}
          onFileListChange={onFileListChange}
          closeModal={onClose}
          expirationDate={expirationDate}
          setExpirationDate={setExpirationDate}
        />
      </div>
    </Drawer>
  );
};

export default PublishFilesDrawer;
