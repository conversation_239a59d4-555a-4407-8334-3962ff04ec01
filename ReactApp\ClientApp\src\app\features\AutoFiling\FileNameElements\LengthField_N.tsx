import React, { useState } from 'react';
import { Radio, Input } from 'antd';

interface CustomFormFieldProps {
	value?: number;
	onChange?: (value: any) => void;
}
const LengthField_N: React.FC<CustomFormFieldProps> = ({ value = 0, onChange }) => {
	const [ClientRefLength, setClientRefLength] = useState<number>(value);

	const handleRadioChange = (e: any) => {
		const value = Number(e.target.value);
		setClientRefLength(value);
		if (onChange) {
			onChange(value);
		}
	};

	const handleInputChange = (e: any) => {
		const value = Number(e.target.value);
		setClientRefLength(value);
		if (onChange) {
			onChange(value);
		}
	};

	return (
		<>
			<Radio.Group onChange={handleRadioChange} value={ClientRefLength}>
				<Radio value={0}>Variable Length</Radio>
				<Radio value={ClientRefLength === 0 ? 1 : ClientRefLength}>Fixed Length</Radio>
			</Radio.Group>
			{ClientRefLength !== 0 && (
				<Input
					type="number"
					value={ClientRefLength}
					onChange={handleInputChange}
					placeholder="Enter fixed length"
					style={{ width: '64px' }}
				/>
			)}
		</>
	);
};
export default LengthField_N;