import { checkoutFile } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { ICheckoutFile } from "../../CheckoutOption";

export const checkoutFiles = async (
  fileList: ICheckoutFile[]
): Promise<{
  data: boolean;
  hasError: boolean;
  errorCode: number | null;
}> => {
  try {
    let errorOccured = false;
    for (const file of fileList) {
      const response = await checkoutFile(file);
      if (!response) {
        errorOccured = true;
        break;
      }
    }
    return { data: true, errorCode: null, hasError: errorOccured };
  } catch (error) {
    logger.error("File Area", "File Area Action Panel", error);
    return {
      data: false,
      errorCode: error.statusCode ? error.statusCode : null,
      hasError: true,
    };
  }
};
