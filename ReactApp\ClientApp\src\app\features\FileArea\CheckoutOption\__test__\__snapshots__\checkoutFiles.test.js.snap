// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Checkout Files Suite Checkout Files component should render and create the snapshot properly 1`] = `
<div>
  <div>
    <div>
      <form
        autoComplete="off"
        className="ant-form ant-form-horizontal"
        onReset={[Function]}
        onSubmit={[Function]}
      >
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-18"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div>
              <div
                className="ant-row"
                style={
                  Object {
                    "marginLeft": -12,
                    "marginRight": -12,
                  }
                }
              >
                <div
                  className="ant-col ant-col-2"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                />
                <div
                  className="ant-col ant-col-4"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="ID"
                      >
                        ID
                      </label>
                    </div>
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-6"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="File Title"
                      >
                        File Title
                      </label>
                    </div>
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="Return Date"
                      >
                        Return Date
                      </label>
                    </div>
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        >
                          <label
                            className="ant-radio-wrapper ant-radio-wrapper-checked"
                          >
                            <span
                              className="ant-radio ant-radio-checked"
                              style={Object {}}
                            >
                              <input
                                checked={true}
                                className="ant-radio-input"
                                onBlur={[Function]}
                                onChange={[Function]}
                                onFocus={[Function]}
                                onKeyDown={[Function]}
                                onKeyPress={[Function]}
                                onKeyUp={[Function]}
                                type="radio"
                              />
                              <span
                                className="ant-radio-inner"
                              />
                            </span>
                            <span>
                              Individual Notes
                            </span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div />
            </div>
          </div>
          <div
            className="ant-col ant-col-6"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row"
              style={
                Object {
                  "marginLeft": -12,
                  "marginRight": -12,
                }
              }
            >
              <div
                className="ant-col ant-col-24"
                style={
                  Object {
                    "paddingLeft": 12,
                    "paddingRight": 12,
                  }
                }
              >
                <div
                  className="ant-row ant-form-item"
                  style={Object {}}
                >
                  <div
                    className="ant-col ant-col-24 ant-form-item-control"
                    style={Object {}}
                  >
                    <div
                      className="ant-form-item-control-input"
                    >
                      <div
                        className="ant-form-item-control-input-content"
                      >
                        <label
                          className="ant-radio-wrapper"
                        >
                          <span
                            className="ant-radio"
                            style={Object {}}
                          >
                            <input
                              checked={false}
                              className="ant-radio-input"
                              onBlur={[Function]}
                              onChange={[Function]}
                              onFocus={[Function]}
                              onKeyDown={[Function]}
                              onKeyPress={[Function]}
                              onKeyUp={[Function]}
                              type="radio"
                            />
                            <span
                              className="ant-radio-inner"
                            />
                          </span>
                          <span>
                            Common Notes
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="ant-row"
              style={
                Object {
                  "marginLeft": -12,
                  "marginRight": -12,
                }
              }
            >
              <div
                className="ant-col ant-col-24"
                style={
                  Object {
                    "paddingLeft": 12,
                    "paddingRight": 12,
                  }
                }
              >
                <div
                  className="ant-row ant-form-item"
                  style={Object {}}
                >
                  <div
                    className="ant-col ant-col-24 ant-form-item-control"
                    style={Object {}}
                  >
                    <div
                      className="ant-form-item-control-input"
                    >
                      <div
                        className="ant-form-item-control-input-content"
                      >
                        <div
                          id="commonNote"
                          onChange={[Function]}
                        >
                          <textarea
                            className="ant-input ant-input-disabled"
                            disabled={true}
                            onChange={[Function]}
                            onCompositionEnd={[Function]}
                            onCompositionStart={[Function]}
                            onKeyDown={[Function]}
                            style={Object {}}
                            value=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
`;

exports[`Checkout Files Suite Checkout Files component should render and create the snapshot properly with fileList prop 1`] = `
<div>
  <div>
    <div>
      <form
        autoComplete="off"
        className="ant-form ant-form-horizontal"
        onReset={[Function]}
        onSubmit={[Function]}
      >
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-18"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div>
              <div
                className="ant-row"
                style={
                  Object {
                    "marginLeft": -12,
                    "marginRight": -12,
                  }
                }
              >
                <div
                  className="ant-col ant-col-2"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                />
                <div
                  className="ant-col ant-col-4"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="ID"
                      >
                        ID
                      </label>
                    </div>
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-6"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="File Title"
                      >
                        File Title
                      </label>
                    </div>
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="Return Date"
                      >
                        Return Date
                      </label>
                    </div>
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-col-24 ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        >
                          <label
                            className="ant-radio-wrapper ant-radio-wrapper-checked"
                          >
                            <span
                              className="ant-radio ant-radio-checked"
                              style={Object {}}
                            >
                              <input
                                checked={true}
                                className="ant-radio-input"
                                onBlur={[Function]}
                                onChange={[Function]}
                                onFocus={[Function]}
                                onKeyDown={[Function]}
                                onKeyPress={[Function]}
                                onKeyUp={[Function]}
                                type="radio"
                              />
                              <span
                                className="ant-radio-inner"
                              />
                            </span>
                            <span>
                              Individual Notes
                            </span>
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div />
            </div>
          </div>
          <div
            className="ant-col ant-col-6"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row"
              style={
                Object {
                  "marginLeft": -12,
                  "marginRight": -12,
                }
              }
            >
              <div
                className="ant-col ant-col-24"
                style={
                  Object {
                    "paddingLeft": 12,
                    "paddingRight": 12,
                  }
                }
              >
                <div
                  className="ant-row ant-form-item"
                  style={Object {}}
                >
                  <div
                    className="ant-col ant-col-24 ant-form-item-control"
                    style={Object {}}
                  >
                    <div
                      className="ant-form-item-control-input"
                    >
                      <div
                        className="ant-form-item-control-input-content"
                      >
                        <label
                          className="ant-radio-wrapper"
                        >
                          <span
                            className="ant-radio"
                            style={Object {}}
                          >
                            <input
                              checked={false}
                              className="ant-radio-input"
                              onBlur={[Function]}
                              onChange={[Function]}
                              onFocus={[Function]}
                              onKeyDown={[Function]}
                              onKeyPress={[Function]}
                              onKeyUp={[Function]}
                              type="radio"
                            />
                            <span
                              className="ant-radio-inner"
                            />
                          </span>
                          <span>
                            Common Notes
                          </span>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div
              className="ant-row"
              style={
                Object {
                  "marginLeft": -12,
                  "marginRight": -12,
                }
              }
            >
              <div
                className="ant-col ant-col-24"
                style={
                  Object {
                    "paddingLeft": 12,
                    "paddingRight": 12,
                  }
                }
              >
                <div
                  className="ant-row ant-form-item"
                  style={Object {}}
                >
                  <div
                    className="ant-col ant-col-24 ant-form-item-control"
                    style={Object {}}
                  >
                    <div
                      className="ant-form-item-control-input"
                    >
                      <div
                        className="ant-form-item-control-input-content"
                      >
                        <div
                          id="commonNote"
                          onChange={[Function]}
                        >
                          <textarea
                            className="ant-input ant-input-disabled"
                            disabled={true}
                            onChange={[Function]}
                            onCompositionEnd={[Function]}
                            onCompositionStart={[Function]}
                            onKeyDown={[Function]}
                            style={Object {}}
                            value=""
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
`;
