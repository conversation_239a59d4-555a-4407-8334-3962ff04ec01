import { SET_TEMPLATES } from "@app/redux/actionTypes/templateActionType";

interface ValueName {
  value: number;
  name: string;
}

interface Node {
  id: number;
  name: string;
  childNodes?: Node[];
  retention?: number;
}

interface TemplateType {
  name: string;
  status: ValueName;
  created: string;
  description: string;
  id: string;
  hide: boolean;
  binderTemplateJobType: ValueName;
  createdBy: ValueName;
  modifiedBy: ValueName;
  modified: string;
  parentNodes: Node[];
  value: string;
}

export interface ITemplates {
  activeTemplates: TemplateType[];
}

export const initialState: ITemplates = {
  activeTemplates: [],
};

export const templateReducer = (state = initialState, action: any) => {
  if (action.type === SET_TEMPLATES) {
    return { ...state, activeTemplates: action.payload };
  } else {
    return state;
  }
};
