import React from "react";
import renderer from "react-test-renderer";
import { shallow } from "enzyme";
import {Form, Tag} from 'antd';
import TagManagement from "../index";
import initTestSuite from "@app/utils/config/TestSuite";

describe("Tag Management Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

   it("should render", () => {
       const tmComponent = shallow(<TagManagement />);
       expect(tmComponent.html()).not.toBe(null);
   });

    it("should render and create snapshot properly", () => {
        const tmComponent = renderer.create(<TagManagement />).toJSON();
        expect(tmComponent).toMatchSnapshot();
    });

   it("should render with props", () => {
       const tmComponent = shallow(<TagManagement siteId={'xxx'}/>);
       expect(tmComponent.html()).not.toBe(null);
    });

    it("should render when props are null", () => {
        const tmComponent = shallow(<TagManagement siteId={null}/>);
        expect(tmComponent.html()).not.toBe(null);
    });

    it("should render when props are undefined", () => {
        const tmComponent = shallow(<TagManagement siteId={undefined}/>);
        expect(tmComponent.html()).not.toBe(null);
    });

    it("should have a form", () => {
        const tmComponent = shallow(<TagManagement siteId={'xxx'}/>);
        expect(tmComponent.find(Form)).toHaveLength(1);
    });

    it("should have two form inputs in the form", () => {
        const tmComponent = shallow(<TagManagement siteId={'xxx'}/>);
        expect(tmComponent.find(Form.Item)).toHaveLength(2);
    });

    it("should match the props with first form item in the form", () => {
        const tmComponent = shallow(<TagManagement siteId={'xxx'}/>);
        expect(tmComponent.find(Form.Item).at(0).props().children.props.type).toEqual('text');
        expect(tmComponent.find(Form.Item).at(0).props().name).toEqual('newTag');
    });

    it("should match the button name with form item in the form", () => {
        const tmComponent = shallow(<TagManagement siteId={'xxx'}/>);
        expect(tmComponent.find(Form.Item).at(1).props().children.props.htmlType).toEqual('submit');
        expect(tmComponent.find(Form.Item).at(1).props().children.props.children).toEqual('Add New Tag');
    });

    it("should not have Tag elements if data list is 0", () => {
        const tmComponent = shallow(<TagManagement siteId={'xxx'}/>);
        expect(tmComponent.find(Tag).exists()).toBeFalsy();
    });
});
