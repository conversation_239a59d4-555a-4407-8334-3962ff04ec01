import React from "react";
import renderer from "react-test-renderer";
import { shallow } from "enzyme";
import { Form, Select } from "antd";

import EmailDocuments from "../index";

describe("Cleat Filter Test Suit", () => {
  it("<EmailDocuments/> should render", () => {
    const component = shallow(<EmailDocuments />);
    expect(component.html()).not.toBeNull();
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer.create(<EmailDocuments />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("should have a form", () => {
    const component = shallow(<EmailDocuments />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have a select", () => {
    const component = shallow(<EmailDocuments />);
    expect(component.find(Select)).toHaveLength(2);
  });

  it("should have 5 form input fields", () => {
    const component = shallow(<EmailDocuments />);
    expect(component.find(Form.Item)).toHaveLength(5);
  });
});
