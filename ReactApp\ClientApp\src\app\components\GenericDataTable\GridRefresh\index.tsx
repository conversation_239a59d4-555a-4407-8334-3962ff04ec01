import { SyncOutlined } from "@ant-design/icons";
import { Tooltip } from "antd";
import React from "react";

interface RefreshIconInf {
  onRefreshGrid: () => void;
  hasUpdates: boolean | undefined;
}

export const GridRefreshIcon: React.FC<RefreshIconInf> = (props) => {
  return (
    <Tooltip title={"Refresh records"}>
      <div
        style={{
          display: "inline",
          position: "relative",
          paddingRight: "30px",
          color: "white",
          width: "30px",
          height: "30px",
          cursor: "pointer",
        }}
        onClick={props.onRefreshGrid}
      >
        <SyncOutlined
          style={{
            position: "absolute",
            fontSize: "20px",
            paddingTop: "5px",
          }}
        />
        {props.hasUpdates && (
          <div
            style={{
              position: "absolute",
            }}
          >
            <svg height="30px" width="30px">
              <circle cx="15" cy="6" r="6" fill="#63AD62" />
            </svg>
          </div>
        )}
      </div>
    </Tooltip>
  );
};
