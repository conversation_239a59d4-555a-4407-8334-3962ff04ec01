import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import http from "@app/utils/http";
import httpVerbs from "@app/utils/http/httpVerbs";
import config from "@app/utils/config";
import { getParameterizedUrl } from "@app/utils";
import { Tags } from "@app/features/FileArea/TagManagement";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export const getExistingTags = (binderId: string): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.FileManagementService].tags,
      binderId
    ),
  });
};

export const addNewTag = (
  binderId: string,
  tags: string[]
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.POST,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.FileManagementService].tags,
      binderId
    ),
    data: { tags: tags },
  });
};

export const updateTag = (
  binderId: string,
  tags: Tags[]
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.PUT,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.FileManagementService].tags,
      binderId
    ),
    data: { tags: tags },
  });
};

export const deleteTag = (
  binderId: string,
  ids: number[]
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.DELETE,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.FileManagementService].tags,
      binderId
    ),
    data: { tags: ids },
  });
};
