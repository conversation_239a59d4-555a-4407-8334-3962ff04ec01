import { FileAreaButtonPanel } from "../fileAreaButtonPanel";
import { shallow, mount } from "enzyme";
import React from "react";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import renderer from "react-test-renderer";
import { Button, Select } from "antd";
import { MemoryRouter } from "react-router-dom";

import { formActions } from "@app/types";
import Modal from "@app/components/Modal";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const createFileAreaButtonPanelComponent = (props) => {
  const INITIAL_STATE = {
    fileArea: {
      fileAreaSettings: {},
      loadGrid: true,
      portalFilesSelectedRequest: { requestId: "", action: formActions.ADD },
    },
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <MemoryRouter>
      <ReduxProvider store={store}>
        <FileAreaButtonPanel siteId="S-001" siteName="SITE001" />
      </ReduxProvider>
    </MemoryRouter>
  );
};

describe("File Area Button Panel Test Suite", () => {
  it("File Area Button Panel should render", () => {
    const component = shallow(createFileAreaButtonPanelComponent());
    expect(component.html()).not.toBe(null);
  });
  it("File Area Button Panel should render and create the snapshot", () => {
    const component = renderer
      .create(createFileAreaButtonPanelComponent())
      .toJSON();
    expect(component).toMatchSnapshot();
  });

  it("Panel should contain 4 buttons", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Button).length).toBe(4);
  });
  it("Panel should contain 1 select dropdown", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Select).length).toBe(1);
  });
  it("Panel should contain 2 Modals", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).length).toBe(2);
  });
});

describe("FileAreaButton Panel -> Request Modal Unit Tests", () => {
  it("First Modal should have a title called Request", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).at(0).props().title).toBe("Request");
  });
  it("First Modal visible property should be false by default", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).at(0).props().visible).toBe(false);
  });
  it("First Modal destroyOnClose property should be true by default", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).at(0).props().destroyOnClose).toBe(true);
  });
  it("Request Modal should have a span as the first elemnt", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(0).props().footer[0]
      .props.children;
    expect(requestModalfotter[0].type).toBe("span");
  });
  it("Request Modal should have a span named Copy Link", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(0).props().footer[0]
      .props.children;
    expect(requestModalfotter[0].props.children[1].trim()).toBe("Copy Link");
  });
  it("Request Modal should have a button as the second element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(0).props().footer[0]
      .props.children;

    expect(requestModalfotter[1].type.displayName).toBe("Button");
  });
  it("Request Modal should have a button named cancel the second element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(0).props().footer[0]
      .props.children;
    expect(requestModalfotter[1].props.children).toBe("cancel");
  });
  it("Request Modal should have a button as the third element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(0).props().footer[0]
      .props.children;
    expect(requestModalfotter[2].type.displayName).toBe("Button");
  });
  it("Request Modal should have a button named send the third element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(0).props().footer[0]
      .props.children;
    expect(requestModalfotter[2].props.children).toBe("Send");
  });
});

describe("FileAreaButton Panel -> Checkin Modal Unit Tests", () => {
  it("Second Modal should have a title called Check-In File(s)", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).at(1).props().title).toBe("Check-In File(s)");
  });
  it("Second Modal visible property should be false by default", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).at(1).props().visible).toBe(false);
  });
  it("Second Modal destroyOnClose property should be true by default", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    expect(component.find(Modal).at(1).props().destroyOnClose).toBe(true);
  });

  it("Checkin Modal should have a button as the first element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(1).props().footer[0];
    expect(requestModalfotter.type.displayName).toBe("Button");
  });
  it("Checkin Modal should have a button named cancel the first element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(1).props().footer[0];
    expect(requestModalfotter.props.children).toBe("cancel");
  });
  it("Checkin Modal should remote a button as the second element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(1).props().footer[1];
    expect(requestModalfotter.type.displayName).toBe("Button");
  });
  it("Checkin Modal should have a button named send the second element ", () => {
    const component = mount(createFileAreaButtonPanelComponent());
    const requestModalfotter = component.find(Modal).at(1).props().footer[1];
    expect(requestModalfotter.props.children).toBe("check-in");
  });
});
