@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjUploadRequestWrapper {
  background-color: @color-bg;
  height: 100vh;

  .yjUploadRequestHeader {
    background-color: @color-bg;
    padding: 0;

    .yjUploadRequestLogo {
      background: url(../../../../styles/assets/images/Logo.png) no-repeat center center;
      height: 70px;
      margin: 0 .75em;
      width: 230px;
    }

    .yjUploadRequestHeading {
      background: @color-white;

      h1 {
        color: @color-secondary;
        padding: .5em 1em;
      }
    }
  }

  .yjUploadRequestContent {
    background: @color-white;
    margin: 2em;
    max-height: 70vh;
    overflow: scroll;
    padding: 1em;
    text-align: center;

    .yjExternalUrlTitle {
      font-size: @font-size-lg;
      text-transform: @yj-transform;
    }

    .yjUploadRequestInfo {
      font-size: @font-size-base;
      text-align: left;
    }

    .yjUploadRequestForm {

      .yjUploadRequestFormRowItem {
        margin-bottom: .5em;

        > div {
          text-align: left;
          width: 10%;
        }
      }
    }
  }
}

.yjUploadRequestDescription {
  padding: 0;
  word-break: break-all;
}

.yjRequestUploadBtn {
  margin-top: 1em;
}
