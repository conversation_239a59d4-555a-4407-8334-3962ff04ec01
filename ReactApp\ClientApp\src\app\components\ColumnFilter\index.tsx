import React, { useEffect, useState } from "react";
import { Menu, Checkbox, Dropdown, Button } from "antd";
import { SettingOutlined, DownOutlined } from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";

import styles from "./index.module.less";
import { RootState } from "@app/redux/reducers/state";
import {
  updateGridColumns,
  updateGridSingleColumn,
  removeGridFilters,
} from "@app/redux/actions/gridsActions";
import useComponentVisible from "@app/hooks/useComponentVisible";

export interface IColumnFilter {
  columns?: any;
  filters?: any;
  tableKey?: string;
}

const SELECT_ALL_KEY = "selectAll";
const SELECT_ALL = "Select All";

export default (props: IColumnFilter) => {
  const { columns, gridFilters } = useSelector(
    (state: RootState) => state.grid
  );
  const dispatch = useDispatch();
  const [optionalColumns, setOptionalColumns] = useState<Array<any>>([]);
  const [selectAllOption, setSelectAllOption] = useState<any>({
    data_type: "text",
    default: false,
    filter: false,
    filter_data: undefined,
    filter_type: "search",
    key: SELECT_ALL_KEY,
    selected: false,
    sorter: false,
    title: SELECT_ALL,
  });

  const {
    ref,
    isComponentVisible,
    setIsComponentVisible,
  } = useComponentVisible(false);

  useEffect(() => {
    const _optionalColumns = columns.filter((column: any) => !column.default);
    const allSelected = _optionalColumns.filter(
      (x: any) => x.key !== SELECT_ALL_KEY && !x.selected
    );

    setSelectAllOption({
      ...selectAllOption,
      selected: allSelected.length === 0,
    });

    _optionalColumns.unshift({
      ...selectAllOption,
      selected: allSelected.length === 0,
      title: <span className={styles.yjSelectDeSelect}>{SELECT_ALL}</span>,
    });

    setOptionalColumns(_optionalColumns);
  }, [columns]);

  const onClickCheckbox = (e: any) => {
    const element = e.target;

    if (element.name === SELECT_ALL_KEY) {
      dispatch(
        updateGridColumns({
          columns: [
            ...columns.filter((column: any) => column.default),
            ...optionalColumns
              .filter((x: any) => x.key !== SELECT_ALL_KEY)
              .map((column: any) => {
                return { ...column, selected: element.checked };
              }),
          ],
          tableKey: props.tableKey,
          selectedElement: {
            name: element.name,
            checked: element.checked,
            multiple: true,
          },
          selected: true,
        })
      );

      if (!element.checked && gridFilters.length > 0) {
        const filtersNeedtoRemoved = columns
          .filter((column: any) => !column.default)
          .map((column: any) => column.key);
        if (filtersNeedtoRemoved.length > 0) {
          dispatch(removeGridFilters(filtersNeedtoRemoved));
        }
      }
    } else {
      dispatch(
        updateGridSingleColumn({
          selectedElement: { name: element.name, checked: element.checked },
          tableKey: props.tableKey,
          selected: true,
        })
      );

      if (!element.checked && gridFilters.length > 0) {
        const gridFilter = gridFilters
          .filter((filter: any) => filter.key === element.name)
          .map((filter: any) => filter.key);

        if (gridFilter.length > 0) {
          dispatch(removeGridFilters(gridFilter));
        }
      }
    }
  };

  const generateMenu = () => {
    return (
      <Menu>
        {optionalColumns.map((element: any) => {
          return (
            <Menu.Item key={element.key}>
              <Checkbox
                key={element.key}
                disabled={element.default}
                name={element.key}
                onClick={onClickCheckbox}
                checked={element.selected}
              >
                {element.title}
              </Checkbox>
            </Menu.Item>
          );
        })}
      </Menu>
    );
  };

  return (
    <div className={styles.yjFilterArea}>
      <div className={styles.yjTableHeaderWrapper}>
        <div
          ref={ref}
          id="columnFilterFileArea"
          style={{ position: "relative" }}
        >
          <Dropdown
            visible={isComponentVisible}
            trigger={["click"]}
            overlay={generateMenu}
            getPopupContainer={() =>
              document.getElementById("columnFilterFileArea") as HTMLElement
            }
          >
            <Button
              onClick={() => setIsComponentVisible(!isComponentVisible)}
              icon={<SettingOutlined />}
            >
              Columns <DownOutlined />
            </Button>
          </Dropdown>
        </div>
      </div>
    </div>
  );
};
