import React, { useState, useEffect, useRef, useContext } from "react";
import { Select } from "antd";

import useFilter from "../../hooks/useFilter";
import {
  getTableHeaderElement,
  handleScrollPlacement,
} from "../../util/getTableHeaderElement";
import { GenericGridFilterTypes } from "../../types";
import { DataTableContext } from "../../DataTableContext";
const { Option } = Select;

export default ({ data, values }: any) => {
  const { filter, addFilter, removeFilter } = useFilter(
    data.key,
    data.title,
    true
  );
  const { state } = useContext(DataTableContext);
  const OPTIONS_LIMIT = 5;
  const SPACE_BAR = "Spacebar";
  data.filter_data = state.filterDropDownOptions[data.key] ?? data.filter_data;
  const [options, setOptions] = useState(data.filter_data);
  const [inputValue, setInputValue] = useState(null);
  const selectRef = useRef<any>(null);
  const defaultValues: any = filter.containsData
    ? filter.data.map((i) => i.value)
    : [];

  const onSelect = (e: any) => {
    const getName = options.filter(
      (i: any) => i.value.toString() === e.toString()
    )[0].name;
    addFilter(e, e, getName, GenericGridFilterTypes.MULTI);
  };

  const onDeselect = (e: any) => {
    removeFilter(e);
  };

  const onChange = (e: any) => {
    if (!e.length) {
      removeFilter(null, true);
    }
  };

  const onSearch = (e: any) => {
    setInputValue(e.toLowerCase());
    const filtered = data.filter_data.filter((i: any) =>
      i.name.toLowerCase().includes(e.toLowerCase())
    );
    setOptions(filtered);
  };

  const resetOptions = () => {
    setOptions(data.filter_data.slice(0, OPTIONS_LIMIT));
  };

  useEffect(() => {
    handleScrollPlacement(data.key, selectRef);
  }, [data.key]);

  const onClickMultiDropdownSelect = (e: any) => {
    const dropdownOption = e.target["className"] as string;
    if (
      dropdownOption &&
      !dropdownOption.includes("ant-select-item-option-content")
    ) {
      resetOptions();
    }
    e.stopPropagation();
    e.preventDefault();
  };

  return (
    <Select
      ref={selectRef}
      id={data.key}
      filterOption={false}
      onKeyDown={(event) => {
        if ((event.key === " " || event.key === SPACE_BAR) && !inputValue) {
          event.preventDefault();
        }
      }}
      mode={"multiple"}
      className="yjMultiSelectOptionSelect"
      value={defaultValues}
      maxTagTextLength={5}
      maxTagPlaceholder={
        defaultValues.length > 0 && defaultValues.length === 1
          ? `${defaultValues.length} Option Selected`
          : `${defaultValues.length} Options Selected`
      }
      dropdownMatchSelectWidth={false}
      onClick={onClickMultiDropdownSelect}
      onSearch={onSearch}
      onSelect={onSelect}
      onDeselect={onDeselect}
      onChange={onChange}
      notFoundContent={`No Results Found`}
      maxTagCount={0}
      showSearch
      showArrow
      allowClear={filter.containsData}
      style={{ width: "100%" }}
      getPopupContainer={() => getTableHeaderElement(data.key)}
    >
      {options && options.slice(0, OPTIONS_LIMIT).map(
        (i: any) =>
          i.value !== "" && (
            <Option key={i.value} value={i.value}>
              {i.name}
            </Option>
          )
      )}
    </Select>
  );
};
