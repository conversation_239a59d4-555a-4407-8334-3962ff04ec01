import React, { Fragment, useEffect, useState } from "react";
import { with<PERSON>outer } from "react-router-dom";
import { <PERSON><PERSON>, Tooltip } from "antd";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";
import { useForm } from "antd/lib/form/Form";

import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import Modal from "@app/components/Modal";
import UserDetailsContainer from "@app/components/forms/UserManagement/Users/<USER>";
import { getUserCount, requestResetPassword } from "@app/api/userService";
import {
  errorNotification,
  successNotification,
} from "@app/utils/antNotifications";
import GenericDataTable from "@app/components/GenericDataTable";
import config from "@app/utils/config";
import userStatusColorSwitch from "@app/utils/css/userStatusColorSwitch";
import styles from "@pages/UserManagement/User/index.module.less";
import logger from "@app/utils/logger";
import { Sorter } from "@app/components/GenericDataTable/util";
import InfinityList from "@app/components/InfinityList";
import { getParameterizedUrl } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import {renderTag} from "@app/components/Tag";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const PRIMARY_ADMIN_CANNOT_BE_RESET_ERROR_CODE = 0;

const SORTER: Sorter = {
  value: "created",
  order: "descend",
};

const Page = (props: any) => {
  const navigateToCreateUser = () => {
    if (!hasUserCount) {
      return -1;
    }
    if (userCount > 0) {
      return props.history.push("/user-management/users/create");
    }
    return errorNotification(
      [""],

      "The user limit has been reached.Upgrade your licence or delete any inactive user accounts"
    );
  };

  const [form] = useForm();
  const [showEditModal, setShowEditModel] = useState(false);
  const [showResetPasswordModal, setShowResetPasswordModal] = useState<boolean>(
    false
  );
  const [showUserGroupModal, setShowUserGroupModal] = useState<boolean>(false);

  const [userId, setUserId] = useState<any | undefined>();
  const [name, setName] = useState<any | undefined>();

  const [userCount, setUserCount] = useState(0);
  const [hasUserCount, setHasUserCount] = useState<boolean>(false);

  const handleCancel = () => {
    setShowEditModel(false);
  };
  const [selectedRequestRowKeys, setSelectedRequestRowKeys] = useState([]);
  const [gridUpdated, setGridUpdated] = useState<boolean>(false);
  useEffect(() => {
    getUserCount()
      .then((response: any) => {
        setUserCount(response.headers["x-remaining-count"]);
        setHasUserCount(true);
      })
      .catch(() => setHasUserCount(true));
  }, []);

  const redirectToEditPage = (record: any) => {
    props.history.push(`/user-management/users/edit/${record.userId}`);
  };

  const showUserGroup = (record: any) => {
    setUserId(record.userId);
    setName(`${record.firstName} ${record.lastName}`);
    setShowUserGroupModal(true);
  };

  const renderHeaderActionPanel = () => {
    return (
      <>
        <Button
          hidden={selectedRequestRowKeys.length === 0}
          type="primary"
          onClick={() => setShowResetPasswordModal(true)}
        >
          Reset Password
        </Button>
      </>
    );
  };

  const onRequestResetPassword = () => {
    setGridUpdated(true);
    requestResetPassword(selectedRequestRowKeys)
      .then((response: any) => {
        setShowResetPasswordModal(false);
        loadGrid();
        if (response.data) {
          successNotification([""], "Password reset email sent sucessfully");
        } else {
          errorNotification([""], "Failed to send password reset email");
        }
      })
      .catch((error) => {
        if (
          error.errorCode &&
          error.errorCode === PRIMARY_ADMIN_CANNOT_BE_RESET_ERROR_CODE
        ) {
          errorNotification(
            [""],
            "Password cannot be reset for primary admins"
          );
        } else {
          errorNotification([""], "Failed to send password reset email");
          logger.error("User management module", "Reset Password", error);
        }
        setShowResetPasswordModal(false);
        loadGrid();
      });
  };

  const loadGrid = () => {
    setSelectedRequestRowKeys([]);
    setGridUpdated(false);
  };

  const renderGroupColumn = (value: any, record: any) =>
    record.role.value === 1 ? (
      "Not Applicable"
    ) : value.length === 0 ? (
      "No User Groups"
    ) : (
      <Button
        type="link"
        onClick={() => showUserGroup(record)}
        className={styles.yjGridTextCenter}
      >
        {value.length} User {value.length === 1 ? "Group" : "Groups"}
      </Button>
    );

  const rowSelectionRequest = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRequestRowKeys(selectedRowKeys);
    },
    selectedRowKeys: selectedRequestRowKeys,
    getCheckboxProps: (record: any) => ({
      disabled: record?.protected,
      name: record?.name,
    }),
  };

  const renderGridColumns = () => {
    return {
      status: (record: any) => {
        return renderTag(
          record.name,
          record.value,
          userStatusColorSwitch(record.value)
        );
      },
      groups: (value: any, record: any) => {
        return renderGroupColumn(value, record);
      },
      created: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      modified: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      action: (text: any, record: any) => {
        return (
          <div className={`${styles.yjActionIconWrapper} yJFileAreaRow`}>
            <Tooltip className="yJFileAreaRow" title="Edit">
              <Button
                className="yJFileAreaRow"
                onClick={() => redirectToEditPage(record)}
                icon={<EditOutlined />}
              />
            </Tooltip>
          </div>
        );
      },
    };
  };

  const renderResetPasswordModal = () => (
    <Modal
      visible={showResetPasswordModal}
      title={"Reset Password"}
      onCancel={() => setShowResetPasswordModal(false)}
      footer={[
        <Button
          key="back"
          type="default"
          onClick={() => setShowResetPasswordModal(false)}
        >
          cancel
        </Button>,
        <Button type="default" onClick={() => onRequestResetPassword()}>
          Reset Password
        </Button>,
      ]}
      size="small"
    >
      <div>
        <p>
          Are you sure you want to reset the password of the selected user(s)
        </p>
      </div>
    </Modal>
  );

  const renderUserGroupModal = () => (
    <Modal
      visible={showUserGroupModal}
      title={`User Groups of ${name}`}
      onCancel={() => {
        setShowUserGroupModal(false);
      }}
      footer={[
        <Button
          key="back"
          type="default"
          onClick={() => setShowUserGroupModal(false)}
        >
          Close
        </Button>,
      ]}
      size="small"
    >
      <InfinityList
        paginatedLimit={20}
        idKeyValue="groupId"
        endpoint={getParameterizedUrl(
          config.api[OperationalServiceTypes.UserService].userGroupByuser,
          userId
        )}
        notFoundContent="No User Group(s) Found"
        listClassName={`yjInfinityListClass ${styles.yjManageUsersInfinityListComponent}`}
        formatValue={(value: any) => {
          return (
            <Tooltip title={value.name} placement={"topLeft"}>
              <p>{value.name}</p>
            </Tooltip>
          );
        }}
      />
    </Modal>
  );

  return (
    <Fragment>
      <Modal
        visible={showEditModal}
        title={"View User"}
        onCancel={handleCancel}
        footer={[
          <Button key="back" type="default" onClick={handleCancel}>
            cancel
          </Button>,
        ]}
      >
        <div>
          <h6>User Details</h6>
          <UserDetailsContainer
            formRef={form}
            userId={"user01"}
            action={"view"}
          />
        </div>
      </Modal>
      {renderResetPasswordModal()}
      {renderUserGroupModal()}
      <PageTitle title={props.title}>
        <Button
          onClick={navigateToCreateUser}
          type="primary"
          icon={<PlusOutlined />}
        >
          Create User
        </Button>
      </PageTitle>
      <PageContent>
        <div className="yjCustomTblHeader">
          <GenericDataTable
            tableKey={"usermngt"}
            updatedGrid={gridUpdated}
            rowSelection={rowSelectionRequest}
            endpoint={
              config.api[OperationalServiceTypes.UserService].createUser
            }
            rowKey={"userId"}
            scrollColumnCounter={6}
            selectedRecordCount={selectedRequestRowKeys.length}
            customRender={renderGridColumns()}
            headerActionPanel={renderHeaderActionPanel()}
            sorted={SORTER}
            fixedColumns={["userId", "firstName"]}
            isDraggable={true}
          />
        </div>
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
