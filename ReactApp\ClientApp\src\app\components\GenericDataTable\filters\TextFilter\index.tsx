import React, { useState, useEffect, useRef } from "react";
import { Select } from "antd";
import debounce from "lodash/debounce";
import config from "@app/utils/config";
import useFilter from "../../hooks/useFilter";
import { getAutocompleteOptions } from "@app/api/genericDataTable";
import { SearchOutlined } from "@ant-design/icons";
import {
  getTableHeaderElement,
  handleScrollPlacement,
} from "../../util/getTableHeaderElement";
import { GenericGridFilterTypes } from "../../types";

const { Option } = Select;

export default (props: any) => {
  const { filter, addFilter, removeFilter } = useFilter(
      props.data.key,
      props.data.title,
    false,
      props.data.filter_type
  );
  const ENTER_KEY = "Enter";
  const MINIMUM_CHARACTER_LENGTH = 2;
  const SPACE_BAR = "Space";

  const selectRef = useRef<any>(null);
  const [options, setOptions] = useState([]);
  const [inputText, setInputText] = useState("");
  const [loadingData, setLoadingData] = useState(false);

  useEffect(() => {
    if (filter.containsData) {
      setInputText(filter.data[0].value);
    } else {
      setInputText("");
    }
  }, [filter.containsData, filter.data]);

  useEffect(() => {
    const callbackFn = (dataInput: any) => {
      setOptions(dataInput);
      setLoadingData(false);
    };
    if (inputText.length > MINIMUM_CHARACTER_LENGTH) {
      setLoadingData(true);
      props.searchPromise(props, inputText, callbackFn);
    } else {
      callbackFn([]);
    }
  }, [props.data.key, props.searchPromise, inputText, props.searchFieldParameters]);

  const onSearch = (e: any) => {
    if (e) {
      setInputText(e);
    } else if (!e && filter.containsData) {
      setInputText(inputText);
    } else {
      setInputText("");
    }
  };

  const onSelect = (e: any) => {
    setInputText(e);
    addFilter(e, e, e, GenericGridFilterTypes.SEARCH);
  };

  const onPressEnter = (e: any) => {
    e.stopPropagation();
    const value = String(e.target.value).trim();
    if (
      value &&
      value.length > MINIMUM_CHARACTER_LENGTH &&
      e.key === ENTER_KEY
    ) {
      setInputText(value);
      addFilter(value, value, value, GenericGridFilterTypes.SEARCH);
    }
  };

  const onChange = (e: any) => {
    if (!e && filter.containsData) {
      removeFilter(null);
    }
  };

  useEffect(() => {
    handleScrollPlacement(props.data.key, selectRef);
  }, [props.data.key]);

  return (
    <div className="yjGridTextFilterSelect">
      <Select
        ref={selectRef}
        id={props.data.key}
        loading={loadingData}
        dropdownMatchSelectWidth={false}
        onClick={(e) => {
          e.stopPropagation();
          e.preventDefault();
        }}
        onKeyDown={(event) => {
          if (
            (event.key === " " || event.key === SPACE_BAR) &&
            inputText === ""
          ) {
            event.preventDefault();
          }
        }}
        onSelect={onSelect}
        value={inputText}
        showSearch
        suffixIcon={<SearchOutlined />}
        onChange={onChange}
        allowClear={filter.containsData}
        onSearch={onSearch}
        notFoundContent={
          inputText.length <= MINIMUM_CHARACTER_LENGTH || loadingData
            ? null
            : `No Results Found`
        }
        style={{ width: "100%" }}
        onInputKeyDown={onPressEnter}
        getPopupContainer={() => getTableHeaderElement(props.data.key)}
      >
        {options.map((i) => (
          <Option key={i} value={i}>
            {i}
          </Option>
        ))}
      </Select>
    </div>
  );
};
