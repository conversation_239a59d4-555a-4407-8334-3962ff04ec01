export default function flattenModules(modules: any[] | undefined) {
  const licenseModules = [] as any;
  modules
    ?.filter((module) => module.checked || module.isMandatory)
    .forEach((module) => {
      if (module.checked || module.isMandatory) {
        licenseModules.push({ moduleId: module.id, functions: [] });
        if (module.subModules && module.subModules.length > 0) {
          module.subModules?.forEach((subModule: any) => {
            if (subModule.checked || subModule.isMandatory) {
              licenseModules.push({
                moduleId: subModule.id,
                functions: getSelectedFunctions(subModule.functions),
              });
            }
          });
        }
      }
    });

  return licenseModules;
}

const getSelectedFunctions = (functions: any[] | undefined) => {
  if (functions && functions.length > 0) {
    return functions
      .filter((func) => func.isMandatory || func.checked)
      .map((func) => func.id);
  } else {
    return [];
  }
};
