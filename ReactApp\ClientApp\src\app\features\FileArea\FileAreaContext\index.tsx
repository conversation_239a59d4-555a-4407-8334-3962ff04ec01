import React, { createContext, useState } from "react";

const defaultContext = {
  download: false,
  SetDownload: () => {},
};

export const FileAreaContext = createContext(defaultContext);

export default (props: any) => {
  const [download, SetDownload]: any = useState<{
    download: boolean;
    SetDownload: any;
  }>(defaultContext);

  return (
    <FileAreaContext.Provider
      value={{
        download,
        SetDownload,
      }}
    >
      {props.children}
    </FileAreaContext.Provider>
  );
};
