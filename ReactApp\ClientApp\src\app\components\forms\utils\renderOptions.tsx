import React from "react";
import { Select } from "antd";
import { ValueType } from "@app/types/ValueType";

const { Option } = Select;

export default (options: (ValueType | any)[], isNameAsKey = false) => {
  return (
    options.length > 0 &&
    options.map((option) => (
      <Option
        key={option.value || option.value === 0 ? option.value : option}
        value={getValue(option, isNameAsKey)}
      >
        {option?.name?  option?.name : (option?.value ? `value: ${option?.value}`: false) || option }
      </Option>
    ))
  );
};

export const getValue = (value: any, isNameAsKey: boolean): string => {
  if (isNameAsKey) {
    return value.name || value;
  } else {
    return value.value || value.value === 0 ? value.value : value;
  }
};
