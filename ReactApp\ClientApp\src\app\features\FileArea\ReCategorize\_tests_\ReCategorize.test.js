import React from "react";
import {Select, Row, Col} from "antd";
import {mount} from "enzyme";
import renderer from "react-test-renderer";
import {MemoryRouter} from "react-router-dom";

import ReCategorize from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

jest.mock("../index.module.less", () => ({
    yjMoverArrow: "yjMoverArrow",
}));

const CustomReCategorize = (props) => {
    return (
        <MemoryRouter>
            <ReCategorize {...props} />
        </MemoryRouter>
    );
}

describe("Re-Categorize Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const csComponent = mount(<CustomReCategorize />);
        expect(csComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const tmComponent = renderer.create(<CustomReCategorize />).toJSON();
        expect(tmComponent).toMatchSnapshot();
    });

    it("should render with props",() => {
        const csComponent = mount(<CustomReCategorize selectedFiles={[]} onClosePopup={() => {}} onNewSiteSelect={() => {}} />);
        expect(csComponent.html()).not.toBe(null);
    });

    it("should render with props are null",() => {
        const csComponent = mount(<CustomReCategorize selectedFiles={null} onClosePopup={null} onNewSiteSelect={null} />);
        expect(csComponent.html()).not.toBe(null);
    });

    it("should render with props are undefined",() => {
        const csComponent = mount(<CustomReCategorize selectedFiles={undefined} onClosePopup={undefined} onNewSiteSelect={undefined} />);
        expect(csComponent.html()).not.toBe(null);
    });

    it("should have a Select element",() => {
        const csComponent = mount(<CustomReCategorize selectedFiles={[]} onClosePopup={() => {}} onNewSiteSelect={() => {}} />);
        expect(csComponent.find(Select)).toHaveLength(1);
    });

    it("should have Col elements", () => {
        const component = mount(<CustomReCategorize selectedFiles={[]} onClosePopup={() => {}} onNewSiteSelect={() => {}} />);
        expect(component.find(Col)).toHaveLength(5);
    });

    it("should have Row elements", () => {
        const component = mount(<CustomReCategorize selectedFiles={[]} onClosePopup={() => {}} onNewSiteSelect={() => {}} />);
        expect(component.find(Row)).toHaveLength(2);
    });

    it("should have div elements",() => {
        const component = mount(<CustomReCategorize selectedFiles={[]} onClosePopup={() => {}} onNewSiteSelect={() => {}} />);
        expect(component.find(".yjMoverArrow")).toHaveLength(2);
    });
});

