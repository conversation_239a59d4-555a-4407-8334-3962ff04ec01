import { FormInstance, Skeleton } from "antd";
import React, { useEffect, useState } from "react";

import { loadSiteDropdownData } from "@app/api/sitesServices";
import { formActionType } from "@app/utils";
import Site from "./index";

export interface SiteContainer {
  action: formActionType;
  formRef?: FormInstance;
  onFinish?: (values: any) => void;
  onChange?: () => void;
  siteData?: any;
  hasError?: any;
}

export const mapSiteData = (data: any) => {
  return {
    ...data,
  };
};

const mapUpdatedFormData = (formValues: any) => {
  console.info("formValues.status", formValues.status.value);
  return {
    siteId: formValues.siteId,
    name: formValues.name,
    description: formValues.description,
    active: formValues.status.value === 1 || formValues.status.value === 3,
    channelId: formValues.channel.value,
    createdBy: formValues.createdBy ? formValues.createdBy.name : null,
    created: formValues.created ? formValues.created.split("T")[0] : null,
    modified: formValues.modified ? formValues.modified.split("T")[0] : null,
    field1: formValues.field1,
    field2: formValues.field2,
    contacts: [],
    deletedContacts: [],
    crmSiteId: formValues.crmSiteId,
    legalHold: formValues.status.value === 3 || formValues.status.value === 4,
    updateContacts: [],
    country: formValues.country,
    address: formValues.address,
    city: formValues.city,
    state: formValues.state,
    zipCode: formValues.zipCode,
  };
};

export default ({
  formRef,
  onChange,
  onFinish,
  action,
  siteData,
  hasError,
}: SiteContainer) => {
  const [siteDetails, setSiteDetails] = useState<{} | undefined>();
  const [formLoaded, setFormLoaded] = useState<boolean>(false);

  useEffect(() => {
    if (siteData && action === "edit") {
      populateUserDropdownData();
      formRef?.setFieldsValue(mapUpdatedFormData(siteData));
    } else {
      if (action === "save") {
        populateUserDropdownData();
        formRef?.setFieldsValue({ contacts: [] });
      }
    }
  }, [formRef, action, siteData]);

  const populateUserDropdownData = () => {
    loadSiteDropdownData().then((values) => {
      setSiteDetails({
        status: values[0]?.data,
      });
      setFormLoaded(true);
    });
  };
  return formLoaded ? (
    <Site
      formRef={formRef}
      onChange={onChange}
      onFinish={onFinish}
      siteDetails={siteDetails}
      action={action}
      siteData={siteData}
      hasError={hasError}
    />
  ) : (
    <Skeleton active />
  );
};
