import React from "react";
import { shallow } from "enzyme";
import FunctionalFlowManagement from "..";
import renderer from "react-test-renderer";
import {
  mapModuleResponseToModule,
  AssignFunctionsToModule,
} from "../utils/mapFunctionsToModules";
import initTestSuite from "@app/utils/config/TestSuite";

const emptyModules = [];

describe("Fuctional Flow Component Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("Functional Flow Component should render", () => {
    const component = shallow(<FunctionalFlowManagement />);
    expect(component.html()).not.toBe(null);
  });
  it("Functional Flow Component should render , and create the snapshot properly", () => {
    const component = renderer.create(<FunctionalFlowManagement />).toJSON();
    expect(component).toMatchSnapshot();
  });
  it("Functional Flow Component should render when actionType is edit", () => {
    const component = shallow(<FunctionalFlowManagement actionType="edit" />);
    expect(component.html()).not.toBe(null);
  });
  it("Functional Flow Component should render when actionType prop is view", () => {
    const component = shallow(<FunctionalFlowManagement actionType="view" />);
    expect(component.html()).not.toBe(null);
  });
  it("Functional Flow Component should render when actionType prop is add", () => {
    const component = shallow(<FunctionalFlowManagement actionType="add" />);
    expect(component.html()).not.toBe(null);
  });

  it("Functional Flow Component should render when details prop is null", () => {
    const component = shallow(<FunctionalFlowManagement details={null} />);
    expect(component.html()).not.toBe(null);
  });
  it("Functional Flow Component should render when details prop is empty Array", () => {
    const component = shallow(
      <FunctionalFlowManagement details={emptyModules} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("mapModuleResponseToModule  function should be exsist", () => {
    expect(typeof mapModuleResponseToModule).toBe("function");
  });

  it("mapModuleResponseToModule should return an empty array when an empty array is passed as the first argument", () => {
    expect(
      mapModuleResponseToModule([
        { id: "", name: "", isMandatory: [], flows: [], subModules: [] },
      ])
    ).toEqual([
      { flows: [], id: "", isMandatory: [], name: "", subModules: [] },
    ]);
  });
  it("mapModuleResponseToModule should return an empty array when null is passed as the first argument", () => {
    expect(mapModuleResponseToModule(null)).toEqual([]);
  });

  it("AssignFunctionsToModule  function should be exsist", () => {
    expect(typeof mapModuleResponseToModule).toBe("function");
  });

  it("AssignFunctionsToModule should return an empty array when an empty array is passed as the first and second argument", () => {
    expect(
      AssignFunctionsToModule(
        [{ id: "", name: "", isMandatory: [], flows: [], subModules: [] }],
        [{ id: "", flows: [], subModules: [] }]
      )
    ).toEqual([
      { flows: [], id: "", isMandatory: [], name: "", subModules: [] },
    ]);
  });
  it("AssignFunctionsToModule should return null when modules and functional modules arguments are null", () => {
    expect(AssignFunctionsToModule(null, null)).toEqual(null);
  });
});
