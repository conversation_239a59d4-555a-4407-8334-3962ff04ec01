import { Button } from "antd";
import { useForm } from "antd/lib/form/Form";
import React, { Fragment, useState, useEffect } from "react";
import { Beforeunload } from "react-beforeunload";
import { useParams } from "react-router";

import { createSite, getSite, updateSite } from "@app/api/sitesServices";
import SiteContainer from "@app/features/Site/SiteContainer";
import PageContent from "@app/components/PageContent";
import PageTitle from "@app/components/PageTitle";
import {
  errorNotification,
  successNotification,
} from "@app/utils/antNotifications";
import CancelChangesModal from "@app/utils/confirm/CancelChangesModal";
import { PromptOnload } from "@app/utils/confirm/onloadPrompt";
import logger from "@app/utils/logger";
import styles from "./index.module.less";
import { IsFormDataChanged } from "@app/utils/forms/formDataEquality";

const CONTACT_CREATED_EVENT = "ContactCreated";
const CONTACT_UPDATED_EVENT = "ContactUpdated";
const SITE_CREATED_EVENT = "SiteCreated";
const SITE_UPDATED_EVENT = "SiteUpdated";

const mapUpdatedFormData = (formValues: any) => {
  return {
    channelId: formValues.channelId,
    siteId: formValues.siteId,
    description: formValues.description,
    name: formValues.name,
    statusId: mapStatusId(formValues.active, formValues.legalHold),
    newContacts: formValues.contacts,
    removedContacts: formValues.deletedContacts,
    field1: formValues.field1 ? formValues.field1 : null,
    field2: formValues.field2 ? formValues.field2 : null,
    crmSiteId: formValues.crmSiteId,
    country: formValues.country,
    address: formValues.address,
    city: formValues.city,
    active: formValues.active,
    legalHold: formValues.legalHold,
    state: formValues.state,
    zipCode: formValues.zipCode,
    updateContacts: formValues.updateContacts,
  };
};

const mapStatusId = (active: boolean, legalHold: boolean) => {
  if (active && !legalHold) return 1; // Active
  if (!active && !legalHold) return 2; // Inactive
  if (active && legalHold) return 3; // Active LH
  if (!active && legalHold) return 4; // Inactive LH
};

const mapInitialFormData = (site: any) => {
  return {
    channelId: site.channel.value,
    siteId: site.siteId,
    description: site.description,
    name: site.name,
    newContacts: [],
    removedContacts: [],
    field1: site.field1 ? site.field1 : null,
    field2: site.field2 ? site.field2 : null,
    crmSiteId: site.crmSiteId,
    updateContacts: [],
  };
};

export default (props: any) => {
  const MANAGE_SITES_LANING_PAGE = "/master-data/manage-sites";
  const { id } = useParams<any>();
  const [siteForm] = useForm();
  const [formChanaged, setFormChanged] = useState<boolean>(false);
  const [formCancel, setFormCancel] = useState(false);
  const [initialFormData, setInitialFormData] = useState<any | undefined>();
  const [hasError, setHasError] = useState<undefined | any>();

  const onChangeSite = () => {
    setHasError(undefined);
    setFormChanged(true);
  };

  const [siteData, setSiteData] = useState<any | undefined>();
  useEffect(() => {
    if (id) {
      getSite(id).then((response: any) => {
        setSiteData(response.data);
        setInitialFormData(mapInitialFormData(response.data));
      });
    }
  }, [id]);

  const PageTitleName = () => {
    if (!id) {
      return props.title;
    } else {
      if (siteData) {
        return `${props.title} - ${siteData.name}`;
      } else {
        return props.title;
      }
    }
  };

  const onSaveSite = () => {
    siteForm.validateFields().then((values) => {
      const updatedFormData = mapUpdatedFormData(values);
      if (id) {
        if (IsFormDataChanged(initialFormData, updatedFormData)) {
          onUpdateSite(updatedFormData);
        } else {
          successNotification([""], "Nothing to Update");
          setFormCancel(true);
          props.history.push(MANAGE_SITES_LANING_PAGE);
        }
      } else {
        onCreateSite(updatedFormData);
      }
    });
  };

  const onUpdateSite = (values: any) => {
    updateSite(values)
      .then((response) => {
        showToastMessages(response);
        setFormCancel(true);
        props.history.push(MANAGE_SITES_LANING_PAGE);
      })
      .catch((error) => {
        setHasError(error);
        logger.error("Master Data Module", "Create Site", error);
      });
  };

  const onCreateSite = (values: any) => {
    createSite(values)
      .then((response) => {
        showToastMessages(response);
        setFormCancel(true);
        props.history.push(MANAGE_SITES_LANING_PAGE);
      })
      .catch((error) => {
        setHasError(error);
        logger.error("Master Data Module", "Create Site", error);
      });
  };

  const showToastMessages = (response: any) => {
    const result = response?.data?.result;
    if (result)
      result.forEach((e: any) => {
        switch (e.eventType) {
          case CONTACT_CREATED_EVENT:
            if (e.success)
              successNotification([""], "Contact(s) created successfully");
            else errorNotification([""], "Contact(s) creation failed");
            break;
          case CONTACT_UPDATED_EVENT:
            if (e.success)
              successNotification([""], "Contact(s) updated successfully");
            else errorNotification([""], "Contact(s) update failed");
            break;
          case SITE_CREATED_EVENT:
            if (e.success)
              successNotification([""], "Site created successfully");
            else errorNotification([""], "Site creation failed");
            break;
          case SITE_UPDATED_EVENT:
            if (e.success)
              successNotification([""], "Site updated successfully");
            else errorNotification([""], "Site update failed");
            break;
        }
      });
  };

  const onCancelSite = () => {
    if (
      siteForm.isFieldsTouched([
        "status",
        "name",
        "field2",
        "field1",
        "description",
        "channelId",
      ]) ||
      formChanaged
    ) {
      setFormCancel(true);
    } else {
      props.history.push(MANAGE_SITES_LANING_PAGE);
    }
  };

  return (
    <Fragment>
      <PageTitle
        title={PageTitleName()}
        pageTitleClassName={styles.yjManageSiteTitle}
      ></PageTitle>
      <PageContent>
        {
          <CancelChangesModal
            visible={formCancel}
            title="Discard Site"
            content={
              id
                ? "The changes made to the site will be discarded. Are you sure you want to proceed?"
                : "Your details will not be saved. Are you sure you want to discard the site?"
            }
            okText={id ? "Proceed" : "Discard"}
            cancelText="Cancel"
            onCancel={() => {
              setFormCancel(false);
            }}
            onOk={() => {
              props.history.push(MANAGE_SITES_LANING_PAGE);
            }}
          />
        }
        {!formCancel && formChanaged && (
          <PromptOnload isBlocking={true} isSaving={false} />
        )}
        <Beforeunload onBeforeunload={(event) => event.preventDefault()} />
        <SiteContainer
          onFinish={onSaveSite}
          onChange={onChangeSite}
          action={id ? "edit" : "save"}
          siteData={siteData ? siteData : undefined}
          formRef={siteForm}
          hasError={hasError ? hasError : undefined}
        />
        <div className={styles.yjManageSitesButtonWrapper}>
          <div>
            <Button onClick={onCancelSite} type="default">
              Cancel
            </Button>
            <Button onClick={onSaveSite} type="primary">
              {id ? "Update" : "Create"}
            </Button>
          </div>
        </div>
      </PageContent>
    </Fragment>
  );
};
