import React from "react";
import { Skeleton } from "antd";
import { useSelector } from "react-redux";

import Routes from "@app/routes";
import menuConfig from "@app/menus/menuConfig";
import MasterLayout from "@app/layouts/MasterLayout";
import DashboardContainer from "@app/pages/Dashboard/Container";
import { RootState } from "@app/redux/reducers/state";

const Dashboard = (props: any) => {
  const { userLoaded } = useSelector((state: RootState) => state.auth);

  return userLoaded ? (
    <MasterLayout {...props} routes={Routes} menus={menuConfig}>
      <DashboardContainer {...props} />
    </MasterLayout>
  ) : (
    <Skeleton active={!userLoaded} />
  );
};

export default Dashboard;
