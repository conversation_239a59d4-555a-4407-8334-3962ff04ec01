@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@file-path: '../../../styles/';

.yjFolderStructureSection {
  border-top: 1px solid @border-color-base;
  padding: 20px 0;
}

.yjAddParentFolderButtonWrapper {
  position: absolute;
  right: 10px;
  top: -8px;

  button {
    flex-basis: 100%;
  }
  .flex-mixin(center, flex, flex-end);
}

.yjFolderStructureTree {
  padding-left: 30px;
}

.yjFolderStructureEditAction {

  button {
    color: #007599;
  }
}

.YjDeleteChildFolderIcon {
  border: none;
  margin: 0;
  padding: 0;

  svg {
    color: @color-primary;
    font-size: 23px;
  }
}

.YJTreeNodeChildIcon {
  display: inline-block;
  vertical-align: bottom;
  margin-bottom: 34px;
}

.YJTreeNodeFolderName {
  width: 400px;
  overflow: hidden;
  text-overflow: ellipsis;
}
