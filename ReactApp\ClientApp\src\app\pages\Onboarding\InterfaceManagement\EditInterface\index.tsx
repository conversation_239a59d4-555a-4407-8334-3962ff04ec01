import React, { Fragment } from "react";
import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";
import InterfaceManagement from "../../../../components/forms/InterfaceManagement";

const page = (props: any) => {
  return (
    <Fragment>
      <PageTitle title={props.title} />
      <PageContent>
        <InterfaceManagement />
      </PageContent>
    </Fragment>
  );
};

export default page;
