import React from "react";
import {Popover, Input} from "antd";
import {mount} from "enzyme";
import renderer from "react-test-renderer";

import SearchableDropdown from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

const _initialValue = {
    filters: [],
    columns: [],
    records: [],
    pagination: {
        total: 0,
        current: 1,
        pageSize: 20
    },
    loading: false
};
const TableContext = React.createContext({
    state: _initialValue,
    dispatch: () => null
});

const CustomSearchableDropdown = (props) => {
    return (
        <TableContext.Provider value={null}>
            <SearchableDropdown {...props} />
        </TableContext.Provider>
    );
}
describe("SearchableDropdown Test Suite", () => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const sdComponent = mount(<CustomSearchableDropdown data={{key : 1}} endpoint={null} searchFieldParameters={null} />);
        expect(sdComponent.html()).not.toBe(null);
    });

    it("should create and match to snapshot", () => {
        const sdComponent = renderer.create(<CustomSearchableDropdown data={{key : 1}} endpoint={null} searchFieldParameters={null} />).toJSON();
        expect(sdComponent).toMatchSnapshot();
    });

    it("should have a Popover element", () => {
        const sdComponent = mount(<CustomSearchableDropdown data={{key : 1}} endpoint={null} searchFieldParameters={null} />);
        expect(sdComponent.find(Popover)).toHaveLength(1);
    });

    it("should have a Input element", () => {
        const sdComponent = mount(<CustomSearchableDropdown data={{key : 1}} endpoint={null} searchFieldParameters={null} />);
        expect(sdComponent.find(Input)).toHaveLength(1);
    });

    it("should have a div element", () => {
        const sdComponent = mount(<CustomSearchableDropdown data={{key : 1}} endpoint={null} searchFieldParameters={null} />);
        expect(sdComponent.find("div")).toHaveLength(1);
    });
});

