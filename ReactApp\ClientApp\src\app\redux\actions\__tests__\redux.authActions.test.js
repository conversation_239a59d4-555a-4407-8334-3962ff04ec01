import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import Mock<PERSON>dapter from "axios-mock-adapter";
import axios from "axios";

import {LOGIN_FAIL, LOGIN_SUCCESS, LOGOUT_SUCCESS} from "@app/redux/actionTypes/authActionTypes";
import * as actions from "@app/redux/actions/authActions";

const middleware = [thunk];
const mockStore = configureMockStore(middleware);
const mock = new MockAdapter(axios);
const store = mockStore({});


describe('auth actions unit test suite', () => {
    it("should create an action to login success", () => {
        const expectedAction = {
            type: LOGIN_SUCCESS,
            payload: {
                access_token: 'xxxx',
                menuItems: 'xxxx',
            },
        };
        expect({
            type: LOGIN_SUCCESS, payload: {
                access_token: 'xxxx',
                menuItems: 'xxxx',
            }
        }).toEqual(expectedAction);
    });
    it("should create an action for failed login", () => {
        const expectedAction = {
            type: LOGIN_FAIL,
        };
        expect({type: LOGIN_FAIL}).toEqual(expectedAction);
    });
    it("should create an action to logout success", () => {
        const expectedAction = {
            type: LOGOUT_SUCCESS,
        };
        expect({type: LOGOUT_SUCCESS}).toEqual(expectedAction);
    });

    it('dispatches LOGIN_SUCCESS after a successfull API requets to login', () => {
        mock.onPost('api/login').reply(200, {});
        store.dispatch(actions.login({username: 'xxxx', password: 'xxxx'})).then(() => {
            let expectedActions = {
                type: LOGIN_SUCCESS,
                payload: {
                    access_token: 'xxxxx',
                    menuItems: 'xxxxx',
                },
            };
            expect(store.getActions()).toEqual(expectedActions)
        });
    });


});