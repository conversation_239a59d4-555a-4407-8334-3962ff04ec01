import React from 'react';
import { act, cleanup, fireEvent, render, screen } from '@testing-library/react';

import '@testing-library/jest-dom';
import TemplatePage from '../index';

describe('<TemplatePage> page component', () => {
  afterEach(cleanup);

  it('should render the title correctly', () => {
    const title = 'Title Name';
    const { getByText } = render(<TemplatePage title={title} />);
    expect(getByText(title)).toHaveTextContent('Title Name');
  });

  it('should call openCreateTemplateDrawer when the "Create Template" button is clicked', () => {
    const openCreateTemplateDrawer = jest.fn();

    const { getByTestId } = render(<TemplatePage title={`Test name`} openCreateTemplateDrawer={openCreateTemplateDrawer} />);

    const button = getByTestId('create-template-button');

    expect(button).toBeTruthy();
    expect(openCreateTemplateDrawer).toHaveBeenCalledTimes(0);

    fireEvent.click(button);
    act(() => {
      setTimeout(() => {
        expect(openCreateTemplateDrawer).toHaveBeenCalledTimes(1);
      }, 2000);
    });
  });

  it('`closeDrawer` should call when the `onClose` callback is fired with Cancel button', () => {
    const closeDrawer = jest.fn();
    const { getByTestId } = render(<TemplatePage title={`Test name`} closeDrawer={closeDrawer} />);

    fireEvent.click(getByTestId('create-template-button'));

    const button = screen.getByTestId('template-drawer-close');

    expect(button).toBeTruthy();
    expect(closeDrawer).toHaveBeenCalledTimes(0);
    fireEvent.click(button);
    act(() => {
      setTimeout(() => {
        expect(closeDrawer).toHaveBeenCalledTimes(1);
      }, 1000);
    });
  });

  it('`closeDrawer` should call when the `onClose` callback is fired with close icon(X)', async () => {
    const closeDrawer = jest.fn();
    const { getByTestId } = render(<TemplatePage title={`Test name`} closeDrawer={closeDrawer} />);

    const createTemplateButton = screen.getByTestId('create-template-button');

    fireEvent.click(createTemplateButton);
    const templateDrawer = screen.getAllByText('Create Template');
    expect(templateDrawer.length).toBeGreaterThan(1);

    const button = screen.getByTestId('template-drawer-close-icon').parentNode;
    expect(button).toBeTruthy();
    expect(closeDrawer).toHaveBeenCalledTimes(0);

    fireEvent.click(button);

    act(() => {
      setTimeout(() => {
        expect(closeDrawer).toHaveBeenCalledTimes(1);
        expect(templateDrawer.length).toEqual(1);
      }, 1000);
    });
  });

  it('Form fields should reset when the `onClose` callback is fired with  Cancel button', async () => {
    const closeDrawer = jest.fn();
    render(<TemplatePage closeDrawer={closeDrawer} />);

    const createTemplateButton = screen.getByTestId('create-template-button');

    fireEvent.click(createTemplateButton);

    const nameInput = screen.getByLabelText('Name');
    const descriptionInput = screen.getByLabelText('Description');

    // Change form values
    fireEvent.change(nameInput, { target: { value: 'John' } });
    fireEvent.change(descriptionInput, { target: { value: 'Sample description' } });

    // Check if form values have changed
    expect(nameInput.value).toBe('John');
    expect(descriptionInput.value).toBe('Sample description');

    // Click the cancel button
    const cancelButton = screen.getByTestId('template-drawer-close');

    expect(cancelButton).toBeTruthy();
    expect(closeDrawer).toHaveBeenCalledTimes(0);
    fireEvent.click(cancelButton);

    act(() => {
      setTimeout(() => {
        expect(closeDrawer).toHaveBeenCalledTimes(1);
        const createTemplateButton = screen.getByTestId('create-template-button');
        expect(screen.getAllByText('Create Template').length).toBeGreaterThan(0);

        fireEvent.click(createTemplateButton);
        expect(screen.getAllByText('Create Template').length).toBeGreaterThan(2);
      }, 300);
    });
  });

  it('drawer should not close if there are no data entered when create button pressed', async () => {
    const closeDrawer = jest.fn();
    const { getByTestId } = render(<TemplatePage title={`Test name`} closeDrawer={closeDrawer} />);

    const openCreateTemplateBtn = getByTestId('create-template-button');
    fireEvent.click(openCreateTemplateBtn);

    const createTemplateBtn = getByTestId('template-drawer-create');
    fireEvent.click(createTemplateBtn);

    expect(closeDrawer).toHaveBeenCalledTimes(0);
  });

  it('Errors should display when there are no data entered after create button pressed', async () => {
    const folderData = [
      {
        id: 1,
        name: 'Primary folder',
        presist: false,
        retention: false,
        subFolders: [
          {
            id: 2,
            name: 'Secondary folder (1)',
            subFolders: [],
            retention: false,
            presist: false,
          },
        ],
      },
    ];
    render(<TemplatePage title={`Test name`} initialFolderData={folderData} />);

    const openCreateTemplateBtn = screen.getByTestId('create-template-button');
    fireEvent.click(openCreateTemplateBtn);

    const folder = screen.getByText('Primary folder');
    fireEvent.mouseEnter(folder);

    const deleteFolder = screen.getByTestId('deleteFolder');
    fireEvent.click(deleteFolder);

    const createTemplateBtn = screen.getByTestId('template-drawer-create');
    fireEvent.click(createTemplateBtn);

    expect(screen.getAllByText('Required Field').length).toBe(2);
    expect(screen.getAllByText('Unable to create template. No folder structure defined.').length).toBe(1);
  });

  it('Errors should display when the template name exists after create button pressed', async () => {
    render(<TemplatePage title={`Test name`} />);

    const openCreateTemplateBtn = screen.getByTestId('create-template-button');
    fireEvent.click(openCreateTemplateBtn);

    const nameInput = screen.getByLabelText('Name');
    const descriptionInput = screen.getByLabelText('Description');

    // Change form values
    fireEvent.change(nameInput, { target: { value: 'John' } });
    fireEvent.change(descriptionInput, { target: { value: 'Sample description' } });

    // Check if form values have changed
    expect(nameInput.value).toBe('John');
    expect(descriptionInput.value).toBe('Sample description');

    const createTemplateBtn = screen.getByTestId('template-drawer-create');
    fireEvent.click(createTemplateBtn);

    expect(screen.getAllByText('Required Field').length).toBe(2);
    expect(screen.getAllByText('Unable to create template. No folder structure defined.').length).toBe(1);
  });

  it('Template should create when create button is pressed after a valid data entry', async () => {
    render(<TemplatePage title={`Test name`} />);
    const openCreateTemplateBtn = screen.getByTestId('create-template-button');
    fireEvent.click(openCreateTemplateBtn);

    const nameInput = screen.getByLabelText('Name');
    const descriptionInput = screen.getByLabelText('Description');

    // Change form values
    fireEvent.change(nameInput, { target: { value: 'John' } });
    fireEvent.change(descriptionInput, { target: { value: 'Sample description' } });

    // Check if form values have changed
    expect(nameInput.value).toBe('John');
    expect(descriptionInput.value).toBe('Sample description');

    const createTemplateBtn = screen.getByTestId('template-drawer-create');
    fireEvent.click(createTemplateBtn);
  });
});
