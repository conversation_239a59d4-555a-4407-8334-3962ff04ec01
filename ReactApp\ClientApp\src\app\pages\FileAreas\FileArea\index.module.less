@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjFileAreaUploadWrapper {
  margin-bottom: 13px;
}

.yjFileAreaCollapsible {
  width: 100%;
  .flex-mixin(flex-start, flex, space-between);
}

.yjFileAreaFileFinderExpanded {
  &:extend(.yjFileAreaCollapsible);

  .yjFolderTreeWrapper {
    padding-right: 15px;
    width: 25vw;
  }

  .yjFileAreaDetailsGrid {
    width: calc(100% - 21vw);
  }

  .yjFileFinderPanel {
    width: calc(20vw);
  }
}

.yjFileAreaFileFinderCollapsed {
  &:extend(.yjFileAreaCollapsible);

  transition: all, 1s, ease-in-out;

  .yjFileAreaDetailsGrid {
    width: 100%;
  }

  .yjFileFinderPanel {
    display: none;
  }
}

.yjFilterMenuDropdownWrapper {

  li {
    color: @color-primary;
    font-size: @font-size-base / 1.1;
  }
}

.yjDropdownManageFilters {
  background: @color-accent-secondary;
  border-color: @color-accent-secondary;
  color: @color-white;
  font-size: @font-size-base / 1.1;
  height: 26px;

  &:hover {
    background: @color-accent-secondary;
    border-color: @color-accent-secondary;
    opacity: .8;
  }
}

.yjFileAreaGridTextWrap {
  margin: 0;
  overflow: hidden;
  padding: 0;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 98%;
}

.yjGridTextFormat {
  padding-top: 0;
  margin: 0;
}

// Limit to a single line with ellipsis
.singleLine {
  max-height: 23px; // Adjust based on your row height
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
  display: flex;
  flex-wrap: wrap;

  span {
    margin: 1px;
  }
}

// Allow multiple lines
.multiLine {
  max-height: none;
  overflow: visible;
  white-space: normal;

  span {
    margin: 1px;
  }
}