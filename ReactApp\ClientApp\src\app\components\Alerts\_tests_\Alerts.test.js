import React from "react";
import {<PERSON><PERSON>, <PERSON><PERSON>, Space} from "antd";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import Alerts from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

jest.mock("../index.module.less", () => ({
    yjAlerts: "yjAlerts",
    yjMessages: "yjMessages",
}));

describe("Alerts Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const aComponent = shallow(<Alerts />);
        expect(aComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const aComponent = renderer.create(<Alerts />).toJSON();
        expect(aComponent).toMatchSnapshot();
    });

    it("should have Alert elements",() => {
        const aComponent = shallow(<Alerts />);
        expect(aComponent.find(Alert)).toHaveLength(4);
    });

    it("should have a Space elements",() => {
        const aComponent = shallow(<Alerts />);
        expect(aComponent.find(Space)).toHaveLength(1);
    });

    it("should have Button elements",() => {
        const tComponent = shallow(<Alerts />);
        expect(tComponent.find(Button)).toHaveLength(4);
    });

    it("should have div elements",() => {
        const tComponent = shallow(<Alerts />);
        expect(tComponent.find(".yjAlerts")).toHaveLength(1);
        expect(tComponent.find(".yjMessages")).toHaveLength(1);
    });
});


