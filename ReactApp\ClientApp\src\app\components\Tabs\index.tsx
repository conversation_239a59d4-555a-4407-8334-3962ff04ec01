import React from "react";
import { Tabs } from 'antd';

import styles from './index.module.less';

const { TabPane } = Tabs;
const text = 'a sample component implemented by UI team as a reference';
export default () => {
  return (
    <div className={styles.yjTabs}>
    <Tabs type="card">
      <TabPane tab="Activity" key="1">
      <div className={styles.yjPropertiesDetailTab}>
        <div className={styles.yjPropertiesDetailPreview}>test</div>
        <div className={styles.yjPropertiesDetailInfo}>test</div>
      </div>
      </TabPane>
      <TabPane tab="Discussions" key="2">
      <p>{text}</p>
      </TabPane>
      <TabPane tab="Notifications" key="3">
      <p>{text}</p>
      </TabPane>
    </Tabs>
  </div>
  );
};
