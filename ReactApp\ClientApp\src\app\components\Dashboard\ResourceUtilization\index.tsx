import React, { useState } from "react";
import { Liquid } from "@ant-design/charts";
import { LiquidConfig } from "@ant-design/charts/es/liquid";
import { Select } from "antd";
import styles from "./index.module.less";

const { Option } = Select;

const ALLOCATED_USERS = "A";
const INTERNAL_USERS = "I";
export default () => {
  const [selectedUsers, setSelectedUsers] = useState(ALLOCATED_USERS);
  const ALLOCATED_USER_PERCENTAGE = 0.54;
  const USER_PERCENTAGE = 0.4;
  const FULL_PERCENTAGE = 100;
  const config: LiquidConfig = {
    percent:
      selectedUsers === ALLOCATED_USERS
        ? ALLOCATED_USER_PERCENTAGE
        : USER_PERCENTAGE,
    statistic: {
      content: {
        formatter: ({ percent }: any) => {
          const PERCENTAGE = percent * FULL_PERCENTAGE;
          const MESSAGE_FOR_USER_TYPE =
            selectedUsers === ALLOCATED_USERS ? "%" : "users remaining";
          return `${PERCENTAGE}${MESSAGE_FOR_USER_TYPE}`;
        },
        style: { fontSize: "22px" },
      },
    },
    liquidStyle: () => {
      return {
        fill: selectedUsers === ALLOCATED_USERS ? "#0DBF50" : "#BFBF0D",
      };
    },
    color: () => "#acc9ff",
    animation: {
      appear: { duration: 1000 },
    },
    style: { height: 250 },
  };

  return (
    <>
      <div className={styles.yjChartSelectWrapper}>
        <Select
          style={{ width: "50%" }}
          getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}
          defaultValue={ALLOCATED_USERS}
          onChange={setSelectedUsers}
        >
          <Option value={ALLOCATED_USERS}>Allocated Users</Option>
          <Option value={INTERNAL_USERS}>Internal Users</Option>
        </Select>
      </div>
      <Liquid {...config} />
    </>
  );
};
