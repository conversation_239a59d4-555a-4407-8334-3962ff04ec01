import {Form, Row, Select} from 'antd';
import React, { useEffect, useState } from 'react';
import styles from "./index.module.less";

const { Option } = Select;

const DateProcessed_T = ({index, value= 'YYYYMMDD'}:any) => (<Form.Item label={`Date Processed`} name={`${index}-Date`} initialValue={value}>
	<Select placeholder="Select Date Format">
		<Option value="YYYYMMDD">YYYYMMDD</Option>
		<Option value="MMDDYY">MMDDYY</Option>
		<Option value="YYMMDD">YYMMDD</Option>
		<Option value="DDMMYY">DDMMYY</Option>
	</Select>
</Form.Item>);
export default DateProcessed_T;