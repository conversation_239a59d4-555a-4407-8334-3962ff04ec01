import React from "react";
import styles from "./index.module.less";

type RecordCountProps = {
  total: number;
  recordsCount: number;
  current: number;
  pageSize: number;
  selectedRecordCount?: number;
};
export default (props: RecordCountProps) => {
  const from = props.current * props.pageSize - props.pageSize + 1;

  const to = props.current * props.pageSize;
  const fixedTo = to > props.total ? props.total : to;

  const recordDisplayText =
    props.selectedRecordCount && props.selectedRecordCount > 1
      ? "Records Selected"
      : "Record Selected";

  const selectedRecords =
    props.selectedRecordCount && props.recordsCount > 0
      ? ` - ${props.selectedRecordCount}  ${recordDisplayText}  `
      : "";

  return (
    <div className={styles.yjGridRecordCount}>
      {!!props.total &&
        ` ${from} - ${fixedTo} of ${props.total} Result${
          props.total === 1 ? "" : "(s)"
        } ${selectedRecords} `}
    </div>
  );
};
