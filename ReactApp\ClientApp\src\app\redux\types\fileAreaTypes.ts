import { formActions } from "@app/types";
import { IFolderTreeResponse } from "@app/types/FileAreaFolderTreeTypes";

export interface IFileArea {
  fileAreaSettings: IFileAreaSettings;
  folderTree: IFolderTreeResponse;
  hasCommonData: boolean;
  download: boolean;
  checkout: boolean;
  publish: boolean;
  unpublish: boolean;
  loadGrid: boolean;
  status: boolean;
  assign: boolean;
  deleteFiles: boolean;
  propeties: boolean;
  reCategorize: boolean;
  moveFiles: boolean;
  renameFiles:boolean;
  copySelectedFiles:boolean;
  linkFiles: boolean;
  unLinkFiles: boolean;
  filesUploaded: boolean;
  toBeDeleted: boolean;
  copyLink: boolean;
  allowedToCloseProperties: boolean;
  portalFilesUpload: IPortalFile;
  portalFileRequestSent: boolean;
  portalFilesSelectedRequest: {
    requestId: string | null;
    action: formActions | null;
  };
}

export interface IFileAreaSettings {
  urlFileUpload: boolean;
  internalFileStatusUpdate: boolean;
  internalFilesEmail: boolean;
  internalFileSetAssignee: boolean;
  internalFileSetProject: boolean;
  internalFilesDownload: boolean;
  internalFilesCheckinCheckout: boolean;
  internalFilesRecategorize: boolean;
  fileAreaFolderStructure: boolean;
  fileAreaManageEmailSubscription: boolean;
  fileAreaManageTags: boolean;
  internalFilesAssign: boolean;
  internalFilesCopy: boolean;
  internalFilesDelete: boolean;
  internalFilesHistory: boolean;
  internalFilesMove: boolean;
  internalFilesRename: boolean;
  internalFilesUpdate: boolean;
  internalFilesUpload: boolean;
  internalFilesView: boolean;
  internalFilesViewAssignmentHistory: boolean;
  internalFilesViewCheckoutHistory: boolean;
  internalFilesViewProperties: boolean;
  internalFilesViewVersionHistory: boolean;
  portalFilesView: boolean;
  internalFilesPublishUnPublish: boolean;
}

export interface IPortalFile {
  requestId: string | null;
  securityKey: string | null;
}
