import ContextMenu from "..";
import { shallow, mount } from "enzyme";
import React from "react";
import renderer from "react-test-renderer";

describe("<ContextMenu/>", () => {
  it("context menu should render", () => {
    const component = shallow(<ContextMenu />);
    expect(component.html()).not.toBe(null);
  });
  it("context menu should render , and create the snapshot properly", () => {
    const component = renderer.create(<ContextMenu />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("context menu should return a menu when prop visible is true", () => {
    const component = mount(<ContextMenu visible={true} />);
    expect(component).not.toBe(null);
  });

  it("context menu should return a menu when prop visible is true and prop positon is provided", () => {
    const component = mount(
      <ContextMenu visible={true} positon={{ x: 10, y: 20 }} />
    );
    expect(component).not.toBe(null);
  });

  it("context menu should have a ul of options", () => {
    const component = mount(
      <ContextMenu visible={true} positon={{ x: 10, y: 20 }} />
    );
    expect(component.find("ul").length).toBe(1);
  });

  it("context menu should have a 3  options", () => {
    const component = mount(
      <ContextMenu visible={true} positon={{ x: 10, y: 20 }} />
    );
    expect(component.find("li").length).toBe(12);
  });
});
