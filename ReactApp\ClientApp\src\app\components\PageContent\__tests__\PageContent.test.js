import React from "react";
import PageContent from "..";
import { shallow } from "enzyme";
import { Layout } from "antd";
const { Content } = Layout;

describe("<PageContent/>", () => {
  it("should render PageContent component", () => {
    const component = shallow(<PageContent />);
    expect(component.html()).not.toBe(null);
  });

  it("should contain Content component", () => {
    const component = shallow(<PageContent />);
    expect(component.find(Content).length).toBe(1);
  });
});
