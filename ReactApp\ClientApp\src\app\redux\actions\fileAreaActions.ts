import {
  SET_FOLDER_TREE,
  SET_FILE_AREA_SETTINGS,
  //SET_HAS_COMMON_DATA,
  UPDATE_CONTEXT_MENU_DOWNLOAD,
  UPDATE_CONTEXT_MENU_STATUS,
  UPDATE_CONTEXT_MENU_ASSIGN,
  UPDATE_CONTEXT_MENU_CHECKOUT,
  UPDATE_CONTEXT_MENU_DELETE,
  UPDATE_CONTEXT_MENU_PROPETIES,
  UPDATE_CONTEXT_MENU_RE_CATEGORIZE,
  UPDATE_ALLOWED_TO_CLOSE_PROPERTIES,
  UPDATE_PORTAL_FILE_UPLOAD_DETAILS,
  PORTAL_FILE_REQUEST_SENT,
  UPDATE_LOAD_GRID,
  PORTAL_FILE_UPDATE_SELECTED_REQUEST,
  UPDATE_CONTEXT_MENU_PUBLISH,
  UPDATE_CONTEXT_MENU_UNPUBLISH,
  UPDATE_CONTEXT_MENU_MOVE_FILES,
  UPDATE_CONTEXT_MENU_RENAME_FILES,
  UPDATE_CONTEXT_MENU_COPY_FILES,
  UPDATE_CONTEXT_MENU_LINK_FILES,
  UPDATE_CONTEXT_MENU_UNLINK_FILES,
  UPDATE_CONTEXT_MENU_TO_BE_DELETED,
  UPDATE_CONTEXT_MENU_COPY_LINK
} from "../actionTypes/fileAreaActionType";
import { IFileAreaSettings, IPortalFile } from "../types/fileAreaTypes";
import { AppThunk } from "@app/types/AppThunk";
import {
  getFolderStructureBySite,
} from "@app/api/fileAreaService";
import { formActions } from "@app/types";

const FORBIDDEN_ERROR_CODE = 403;

export const setFolderTree = (data: any) => {
  return {
    type: SET_FOLDER_TREE,
    payload: data,
  };
};

export const setFileAreaSettings = (data: IFileAreaSettings) => {
  return {
    type: SET_FILE_AREA_SETTINGS,
    payload: data,
  };
};

// export const setHasCommonData = (hasFetched: boolean) => {
//   return {
//     type: SET_HAS_COMMON_DATA,
//     payload: hasFetched,
//   };
// };

export const updateContextMenuDownloadOption = (download: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_DOWNLOAD,
    payload: download,
  };
};

export const updateContextMenuStatusOption = (download: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_STATUS,
    payload: download,
  };
};

export const updateContextMenuAssignOption = (download: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_ASSIGN,
    payload: download,
  };
};

export const updateContextMenuCheckoutOption = (checkout: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_CHECKOUT,
    payload: checkout,
  };
};

export const updateContextMenuPublishFiles = (publish: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_PUBLISH,
    payload: publish,
  };
};

export const updateContextMenuUnpublishFiles = (unpublish: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_UNPUBLISH,
    payload: unpublish,
  };
};

export const updateLoadGridOption = (loadGrid: boolean) => {
  return {
    type: UPDATE_LOAD_GRID,
    payload: loadGrid,
  };
};

export const updateContextMenuPropetiesoption = (propeties: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_PROPETIES,
    payload: propeties,
  };
};

export const updateContextMenuCopyLinkFiles = (copyLink: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_COPY_LINK,
    payload: copyLink,
  };
};

export const updateContextMenuToBeDeleted = (toBeDeleted: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_TO_BE_DELETED,
    payload: toBeDeleted,
  };
};

export const getFolderTree = (siteId: string): AppThunk => {
  return async (dispatch) => {
    const { data } = await getFolderStructureBySite(siteId);
    dispatch(setFolderTree(data));
  };
};
//
// export const getFileAreaSettings = (siteId: string): AppThunk => {
//   return async (dispatch) => {
//     const { data } = await getFileAreaSettingsBySite(siteId);
//     dispatch(setFileAreaSettings(data));
//   };
// };

export const updateContextMenuDeleteOption = (deleteFiles: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_DELETE,
    payload: deleteFiles,
  };
};

export const updateContextMenuReCategorizeOption = (reCategorize: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_RE_CATEGORIZE,
    payload: reCategorize,
  };
};

export const updateContextMenuMoveFilesOption = (move: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_MOVE_FILES,
    payload: move,
  };
};

export const updateContextMenuReNameFilesOption = (reName: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_RENAME_FILES,
    payload: reName,
  };
};

export const updateContextMenuCopyFilesOption = (copy: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_COPY_FILES,
    payload: copy,
  };
};

export const updateAllowedToCloseProperties = (allowedToClose: boolean) => {
  return {
    type: UPDATE_ALLOWED_TO_CLOSE_PROPERTIES,
    payload: allowedToClose,
  };
};

export const updatePortalFIlesUploadDetails = (
  portalFilesUpload: IPortalFile
) => {
  return {
    type: UPDATE_PORTAL_FILE_UPLOAD_DETAILS,
    payload: portalFilesUpload,
  };
};

export const updateFileRequestSent = (sentRequest: boolean) => {
  return {
    type: PORTAL_FILE_REQUEST_SENT,
    payload: sentRequest,
  };
};

export const updatePortalFilesSelectedRequest = (sentRequest: {
  requestId: string | null;
  action: formActions | null;
}) => {
  return {
    type: PORTAL_FILE_UPDATE_SELECTED_REQUEST,
    payload: sentRequest,
  };
};

export const updateContextMenuLinkFilesOption = (link: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_LINK_FILES,
    payload: link,
  };
};

export const updateContextMenuUnlinkFilesOption = (unLink: boolean) => {
  return {
    type: UPDATE_CONTEXT_MENU_UNLINK_FILES,
    payload: unLink,
  };
};