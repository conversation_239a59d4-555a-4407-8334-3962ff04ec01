import types from "./action-types";
import { GenericFilter } from "@app/components/GenericDataTable/types";
import { DISPATCH_ACTION_FILTERS } from "../hooks/useFilter";
import logger from "@app/utils/logger";

export const updateColumns = (columns: any, tableKey: any) => {
  return {
    type: types.UPDATE_COLUMNS,
    payload: { columns: columns, tableKey: tableKey },
  };
};

export const setTableLoading = (isLoading: boolean) => {
  return {
    type: types.TABLE_LOADING,
    payload: isLoading,
  };
};

export const updateSingleColumn = (
  name: string,
  checked: boolean,
  tableKey?: string
) => {
  logger.info("GenericDataTable", "Update Single Column", { tableKey, key: name, data: { selected: checked } });
  return {
    type: types.UPDATE_SINGLE_COLUMN,
    payload: {
      tableKey,
      key: name,
      data: {
        selected: checked,
      },
    },
  };
};

export const clearFilters = () => {
  return {
    type: types.CLEAR_ALL_FILTERS,
    payload: [],
  };
};

export const updateFilterValue = (key: string, value: any) => {
  return {
    type: types.UPDATE_FILTER_VALUE,
    payload: {
      key: key,
      value: value,
    },
  };
};

export const removeFilterValue = (key: string) => {
  return {
    type: types.REMOVE_FILTER_VALUE,
    payload: key,
  };
};

export const removeFilter = (filter: GenericFilter[]) => {
  return {
    type: DISPATCH_ACTION_FILTERS,
    payload: filter,
  };
};

export const updateDataRecords = (data: any) => {
  return {
    type: types.UPDATE_DATA_RECORDS,
    payload: {
      pagination: {
        total: data.totalRecordCount,
        current: data.pageNumber,
        pageSize: data.pageSize,
      },
      records: data.records,
    },
  };
};

export const updateChange = (pagination: any, filters: any, sorter: any) => {
  return {
    type: types.UPDATE_CHANGE,
    payload: { pagination, filters, sorter },
  };
};

export const setSelectedSavedFilter = (filters: GenericFilter[]) => {
  return {
    type: types.SET_SELECTED_SAVED_FILTER,
    payload: filters,
  };
};

export const updateFilterDropDownValues = (key: string, value: any[]) => {
  return {
    type: types.UPDATE_FILTER_DROPDOWN_VALUES,
    payload: {
      key: key,
      value: value,
    },
  };
};
