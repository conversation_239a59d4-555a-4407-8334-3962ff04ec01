import { But<PERSON>, Col, Drawer, Form, Input, Row, Switch } from 'antd';
import React, { useEffect, useState } from 'react';
import { FormInstance } from 'antd/lib/form/Form';
import { required } from '@app/components/forms/validators';
import { CloseOutlined } from '@ant-design/icons/lib';
import styles from '@app/features/Channel/index.module.less';
import FolderTree from '@app/components/FolderTreeEditor';
import { TreeDataNode } from 'rc-tree-select/lib/interface';
import { Rule } from 'antd/lib/form';
import CancelChangesModal from '@app/utils/confirm/CancelChangesModal';
import { checkRetentionNameExists } from '@app/api/retentionService';
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import logger from '@app/utils/logger';

type Props = {
  showDrawer: boolean;
  formRef: FormInstance;
  onDrawerClose: () => void;
  onSuccess: (values: any) => void;
};

const MAX_LENGTH_HUNDRED = 100;
const MAX_LENGTH_TWENTY_FIVE = 25;

const RetentionAddEditDrawer = ({ onDrawerClose, showDrawer, formRef, onSuccess }: Props) => {
  const [show, setShow] = useState(false);
  const [folderData, setFolderData] = useState<Array<TreeDataNode>>([]);
  const [changed, setChanged] = useState(false);
  const [formCancel, setFormCancel] = useState(false);
  const action = formRef.getFieldValue('id')? 'edit': 'add';
  const RetentionName = formRef.getFieldValue('name');
  const { userPermission } = useSelector( (state: RootState) => state.userManagement );

  useEffect(() => {
    setShow(showDrawer);
    if (showDrawer) {
      setFolderData([]);
      setChanged(false);
      setFormCancel(false);
    }
  }, [showDrawer]);

  useEffect(() => {
    formRef.setFields([{ name: 'RetentionFolders', value: folderData }]);

  }, [folderData]);

  const onFinish = () => {
    formRef.setFieldsValue(['RetentionFolders',folderData])
    formRef.validateFields().then((values) => {
      onSuccess(values)
    });
  };

  const onCancelButton = () => {
    if (changed) {
      return setFormCancel(true);
    }
    onDrawerClose();
  };

  const validateForlder = (): Rule => ({
    validator(_rule, value) {
      if (folderData.length == 0) {
        return Promise.reject('At least one folder is required');
      }
      for (let folder of folderData) {
        if (folder.children && folder.children.length > 0) {
          return Promise.resolve();
        }
      }
      return Promise.reject('At least one secondary folder is required');
    },
  });

  const tempalteNameAvailability = (): Rule => ({
    validator(_rule, value) {
      return value ? checkRetentionName(value) : Promise.resolve();
    },
  });

  const checkRetentionName = (name: any): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (RetentionName == name) return resolve(true);
      checkRetentionNameExists(name)
        .then((_e) => reject('Retention Name already exists'))
        .catch((_e) => resolve(true));
    });
  };

  return (
    <>
      <CancelChangesModal
        visible={formCancel}
        title="DISCARD Retention"
        content="The changes will be discarded. Are you sure you want to proceed?"
        okText="Proceed"
        cancelText="Cancel"
        onCancel={() => {
          setFormCancel(false);
        }}
        onOk={() => {
          setFormCancel(false);
          onDrawerClose();
        }}
      />
      <Drawer
        title={action==='add'? 'Create Retention': 'Update Retention'}
        placement="right"
        onClose={onCancelButton}
        visible={show}
        className={'yjDrawerPanel'}
        width={700}
        closeIcon={<CloseOutlined data-testid="Retention-drawer-close-icon" />}
        footer={[
          <div key={`footer-${formRef.getFieldValue('id')||0}`} style={{ textAlign: 'right' }}>
            <Button key="back" data-testid="Retention-drawer-close" type="default" onClick={onCancelButton}>
              Cancel
            </Button>
            <Button   key="update" data-testid="Retention-drawer-create" type="primary" onClick={onFinish}>
              {action==='add'? 'Create' : 'Update'}
            </Button>
          </div>,
        ]}
      >
        <Form form={formRef} key="createSiteForm" layout="vertical" onChange={() => setChanged(true)}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label={'Name'} name="name" rules={[required, tempalteNameAvailability()]}>
                <Input maxLength={MAX_LENGTH_TWENTY_FIVE} autoComplete="off"   />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label={'Description'} name="description" rules={[required]}>
                <Input maxLength={MAX_LENGTH_HUNDRED} autoComplete="off"   />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item  valuePropName="checked" label={'Status'} name="RetentionStatus" initialValue={action === 'add' ? true : formRef.getFieldValue('RetentionStatus')} >
                <Switch style={{ margin: 0 }}   />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item valuePropName="checked" label={'Hide'} name="hide" initialValue={action === 'add' ? false : formRef.getFieldValue('hide')}>
                <Switch style={{ margin: 0 }}   />
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <h2>Folder Structure</h2>
              <div className={'edit' === 'edit' ? `${styles.yjFolderStructureTreeWrapper} ${styles.yjFolderStructureTreeUpdate}` : `${styles.yjFolderStructureTreeWrapper}`}>
                {showDrawer && <FolderTree onFolderTreeUnmount={(data) => {logger.debug('RetentionAddEditDrawer','FolderTree Unmounted', { data });setFolderData(data)}} onChange={() => { logger.debug('RetentionAddEditDrawer','FolderTree Changed','');setChanged(true)}}  />}
              </div>
            </Col>
            {action === 'edit' && (
                <Form.Item name="id" initialValue={formRef.getFieldValue('id')} hidden>
                  <Input type="hidden" />
                </Form.Item>
            )}
          </Row>
        </Form>
      </Drawer>
    </>
  );
};

export default RetentionAddEditDrawer;
