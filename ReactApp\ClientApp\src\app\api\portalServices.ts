import { CancelToken } from "axios";

import http from "../utils/http";
import config from "@app/utils/config";
import httpVerbs from "../utils/http/httpVerbs";
import { ICreateRequest } from "@app/types/portalTypes";
import { getMultipleParameterizedUrl, getParameterizedUrl } from "@app/utils";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export const saveRequest = (data: ICreateRequest) => {
  return http({
    method: httpVerbs.POST,
    url: config.api[OperationalServiceTypes.PortalService].portalFileRequests,
    data: data,
  });
};

export const updateRequest = (data: ICreateRequest, requestId: string) => {
  return http({
    method: httpVerbs.PUT,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.FileManagementService]
        .updateFileRequest,
      requestId
    ),
    data: data,
  });
};

export const getPortlFileRequest = (requestId: string) => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.PortalService].portalFileRequestById,
      requestId
    ),
  });
};

export const validatePortalFileRequest = (
  requestId: string,
  secretKey: string
) => {
  return http({
    method: httpVerbs.GET,
    url: getMultipleParameterizedUrl(
      config.api[OperationalServiceTypes.PortalService]
        .validatePortalFileRequest,
      [{ key: "requestId", value: requestId }]
    ),
    params: { securityKey: secretKey ? secretKey : "" },
  });
};

export const uploadPortalFile = (
  fileName: string,
  file: File,
  requestId: string,
  email: string,
  referenceNo: string | null,
  chunkId: number,
  totalChunkCount: number,
  onUploadProgress?: (progressEvent: any) => void,
  cancelToken?: CancelToken
): Promise<HTTPResponse<any>> => {
  const formData = new FormData();
  formData.append("file", file, fileName);
  formData.append("email", email);
  referenceNo && formData.append("ReferenceNumber", referenceNo.toString());
  formData.append("chunkId", chunkId.toString());
  formData.append("totalChunkCount", totalChunkCount.toString());
  return http(
    {
      method: httpVerbs.POST,
      url: config.api[OperationalServiceTypes.FileStorageService].uploadFiles,
      headers: {
        "content-type": "multipart/form-data",
      },
      data: formData,
      onUploadProgress,
      cancelToken,
    },
    {
      authorization: "portal-upload-token",
    }
  );
};

export const submitPortalFile = (
  requestId: string,
  referenceNumber: string | null,
  securityKey: any,
  email: string
) => {
  return http(
    {
      method: httpVerbs.POST,
      url: getParameterizedUrl(
        config.api[OperationalServiceTypes.PortalService].submitPortalFiles,
        requestId
      ),
      data: {
        requestId: requestId,
        referenceNumber: referenceNumber,
        securityKey: securityKey,
        email: email,
      },
    },
    {
      authorization: "portal-upload-token",
    }
  );
};

export const deleteFileRequests = (requestIds: any) => {
  return http({
    method: httpVerbs.DELETE,
    url: config.api[OperationalServiceTypes.PortalService].portalFileRequests,
    data: { requestIds: requestIds },
  });
};

export const getPortalFileRequestById = (
  fileId: string
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api[OperationalServiceTypes.FileManagementService]
        .portalFileRequestWithKey,
      fileId
    ),
  });
};
export const rejectPortalFiles = (siteId: any, fileIds: any) => {
  return http({
    method: httpVerbs.PATCH,
    url:
      config.api[OperationalServiceTypes.PortalService].portalFileActionsReject,
    data: { siteId: siteId, PortalFileIds: fileIds },
  });
};
