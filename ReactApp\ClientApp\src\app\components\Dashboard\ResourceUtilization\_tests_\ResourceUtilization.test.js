import React from "react";
import renderer from "react-test-renderer";
import {Select} from "antd";
import { Liquid } from '@ant-design/charts';
import {shallow} from "enzyme";

import initTestSuite from "@app/utils/config/TestSuite";
import ResourceUtilization from "../index";

const {Option} = Select;
describe("Resource Utilization Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const ruComponent = shallow(<ResourceUtilization/>);
        expect(ruComponent.html()).not.toBe(null);
    });

    it("should create and match to snapshot", () => {
        const ruComponent = renderer.create(<ResourceUtilization/>).toJSON();
        expect(ruComponent).toMatchSnapshot();
    });

    it("should have a Select component", () => {
        const ruComponent = shallow(<ResourceUtilization/>);
        expect(ruComponent.find(Select)).toHaveLength(1);
    });

    it("should have 2 Option components", () => {
        const ruComponent = shallow(<ResourceUtilization/>);
        expect(ruComponent.find(Option)).toHaveLength(2);
    });

    it("should have a Liquid component", () => {
        const ruComponent = shallow(<ResourceUtilization/>);
        expect(ruComponent.find(Liquid)).toHaveLength(1);
    });
});

