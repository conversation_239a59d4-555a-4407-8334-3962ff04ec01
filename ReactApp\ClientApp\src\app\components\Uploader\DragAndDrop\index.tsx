import React, { useEffect, useState } from "react";
import { Upload, Button } from "antd";
import { DraggerProps } from "antd/lib/upload";

import styles from "./index.module.less";
import { URL_UPLOAD } from "../constants/uploadTypes";
import { PlusSquareFilled } from "@ant-design/icons";

const { Dragger } = Upload;

type PropTypes = {
  onURLOptionSelect: (option: number) => void;
  hasUrlUploadPermission: boolean;
};

export default ({
  onURLOptionSelect,
  hasUrlUploadPermission,
  ...props
}: DraggerProps & PropTypes) => {
  const [browserVisibility, setBrowserVisibility] = useState(false);
  const [enableBrowse, setEnableBrowse] = useState(false);
  const [uploaderShrinkedView, setUploaderShrinkedView] = useState(true);

  const enableBrowserVisibility = () => {
    setBrowserVisibility(true);
    setEnableBrowse(true);
  };

  useEffect(() => {
    if (browserVisibility) {
      setEnableBrowse(true);
    } else {
      setEnableBrowse(false);
    }
  }, [browserVisibility]);
  return (
    <>
      <div
        className={styles.yjDragDropWrapper}
        onMouseOver={() => {
          setBrowserVisibility(false);
        }}
      >
        <Dragger
          openFileDialogOnClick={enableBrowse}
          {...props}
          className={styles.yjDragDrop}
          multiple
          showUploadList={false}
        >
          {uploaderShrinkedView ? (
            <div data-testid="shrinkedView">
              <Button
                data-testid="expandViewButton"
                icon={<PlusSquareFilled />}
                className={styles.yjBtnMiniFileUploader}
                onClick={() => {
                  setUploaderShrinkedView(false);
                }}
              >
                FILE Uploader
              </Button>
            </div>
          ) : (
            <div data-testid="expandedView">
              <Button
                data-testid="expandCloseButton"
                className={styles.yjClose}
                onClick={() => {
                  setUploaderShrinkedView(true);
                }}
              >
                X
              </Button>

              <p className={styles.yjDragDropText}>Drag & Drop Here</p>
              <p className={styles.yjDragDropText}>or</p>
              <Button
                data-testid="fileUploadBrowseButton"
                onMouseLeave={() => {
                  setEnableBrowse(false);
                }}
                onMouseOver={() => {
                  enableBrowserVisibility();
                }}
                onClick={() => {
                  enableBrowserVisibility();
                }}
                type="primary"
                className={styles.yjUploadBrowseButton}
              >
                Browse
              </Button>
              {/* <p
                data-testid="fileUrlUploader"
                className={styles.yjUploadLinkButton}
                onClick={(e) => {
                  e.stopPropagation();
                  onURLOptionSelect(URL_UPLOAD);
                }}
              >
                URL UPLOADER
              </p> */}
            </div>
          )}
        </Dragger>
      </div>
    </>
  );
};
