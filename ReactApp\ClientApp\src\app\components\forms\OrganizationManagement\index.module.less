@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjOrganizationMgtManage {

  .yjSectionHeading {
    color: @text-color;
    font-size: ceil(@font-size-base * .89);
    margin: .2em 0;
    text-transform: @yj-transform;

    .font-mixin(@font-primary, @yjff-bold);
  }

  .yjSectionDivider {
    border-color: @border-color-base;
    height: 1.5px;
  }
}

@button-size: 25px;

.yjListWrapper {

  .yjListHeader {
    background: @color-modal-bg-sub-header;
    margin: 1em 0;
    padding: .5em 1em;

    .flex-mixin(center, flex, space-between);
  }

  .yjSubHeading {
    color: @color-secondary;
    margin-right: 1em;

    .font-mixin(@font-primary, @yjff-semibold);
  }

  .yjRemoveButton {
    background: transparent;
    border-color: transparent;
    box-shadow: none;
    height: @button-size;
    min-width: @button-size;
    width: @button-size;

    &:hover {
      opacity: .8;
    }
  }
}

.yjAddNewFieldWrapper {

  .yjAddNewAddressButton {
    background-color: fade(@color-bg-add-address-btn, 10%);
    border: 1px dashed @color-border-add-address-btn;
    color: @color-font-add-address-btn;
    font-size: ceil(@font-size-base/1.2);
    margin: 0;
    width: 100%;
  }
}
