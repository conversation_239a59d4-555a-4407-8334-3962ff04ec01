import React, { useEffect } from "react";
import {
  Row,
  Col,
  Form,
  Select,
  Checkbox,
  Input,
  Collapse,
  Tooltip,
} from "antd";
import { FormInstance } from "antd/lib/form";

import styles from "./index.module.less";
import { max } from "@app/components/forms/validators";

const COMING_SOON = "This feature is coming soon";

export interface IEmailDocument {
  onChangeEmailForm?: (event: React.FormEvent<HTMLFormElement>) => void;
  key?: string;
  form?: FormInstance;
  hideAttchments?: boolean;
}

const filterOption = (input: any, option: any) => {
  return option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

const { Panel } = Collapse;
export default ({
  key,
  onChangeEmailForm,
  form,
  hideAttchments = false,
}: IEmailDocument) => {
  const EMAIL_TEXT_MAX_LENGTH = 1000;
  useEffect(() => {
    form && form.resetFields();
  }, [form]);

  return (
    <div className={styles.yjEmailOptionWrapper}>
      {/* <Tooltip placement="topLeft" title={COMING_SOON} color="#78bf59">
        <div className={"yjSecondaryAccordian"}>
          <Collapse accordion expandIconPosition={"right"}>
            <Panel header="Email Options" key="1">
              <Form
                form={form}
                onChange={onChangeEmailForm}
                style={{ opacity: "0.5" }}
              >
                <Row gutter={24}>
                  <Col span={24}>
                    <p className={styles.yjEmailOptionTitle}>
                      ADDITIONAL INFORMATION FOR THIS EMAIL
                    </p>
                    <Form.Item
                      name="email"
                      rules={[max(EMAIL_TEXT_MAX_LENGTH)]}
                    >
                      <Input.TextArea
                        placeholder={COMING_SOON}
                        disabled={true}
                      />
                    </Form.Item>
                    {!hideAttchments && (
                      <Form.Item>
                        <Checkbox
                          className={styles.yjAttachDocuments}
                        ></Checkbox>{" "}
                        Attach documents to the email
                      </Form.Item>
                    )}
                  </Col>
                  <Col span={4}>
                    <Form.Item
                      children={null}
                      label="Notify Users"
                      colon={false}
                      name="username"
                    ></Form.Item>
                  </Col>
                  <Col span={5}>
                    <Form.Item name="emailUser">
                      <Select
                        notFoundContent={COMING_SOON}
                        className="yjMultiSelectOptionSelect"
                        getPopupContainer={(trigger) =>
                          trigger.parentNode as HTMLElement
                        }
                        showSearch
                        showArrow
                        style={{ width: 200 }}
                        mode="multiple"
                        placeholder="Select a User"
                        optionFilterProp="children"
                        filterOption={filterOption}
                      > */}
      {/* <Option value="harry">Harry Potter</Option>
                      <Option value="ron">Ron Weasley</Option>
                      <Option value="hermione">Hermione Granger</Option>
                      <Option value="luna">Luna Lovegood</Option>
                      <Option value="neville">Neville Longbottom</Option> */}
      {/* </Select>
                    </Form.Item>
                  </Col>
                  <Col span={5}>
                    <Form.Item name="emailContact">
                      <Select
                        className="yjMultiSelectOptionSelect"
                        notFoundContent={COMING_SOON}
                        getPopupContainer={(trigger) =>
                          trigger.parentNode as HTMLElement
                        }
                        showSearch
                        showArrow
                        style={{ width: 200 }}
                        mode="multiple"
                        placeholder="Select a Contact"
                        optionFilterProp="children"
                        filterOption={filterOption}
                      > */}
      {/* <Option value="draco">Draco Malfoy</Option>
                      <Option value="lucius">Lucius Malfoy</Option>
                      <Option value="Bellatrix">Bellatrix Lestrange</Option>
                      <Option value="Igor">Igor Karkaroff</Option> */}
      {/* </Select>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </Panel>
          </Collapse>
        </div>
      </Tooltip> */}
    </div>
  );
};
