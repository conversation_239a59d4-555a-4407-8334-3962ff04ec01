export interface IFunctionalFlowState {
  modules: Module[] | null;
  functionModules: FunctionModule[] | null;
  isLoading: boolean;
  errors: string[];
  hasErrors: boolean;
  savedSuccessfully: boolean;
  isEdited: boolean;
  editModule: FunctionSubModule[] | null;
}

export interface Module {
  id: number;
  name: string;
  subModules: SubModule[] | null;
  isMandatory: boolean;
  flows: Flows[];
}

export interface FunctionModule {
  id: number;
  flows: number[];
  subModules: FunctionSubModule[];
}

export interface FunctionSubModule {
  id: number;
  flows: number[];
}

export interface SubModule {
  id: number;
  name: string;
  flows: Flows[] | null;
}

export interface Flows {
  id: number;
  name: string;
  isChecked?: boolean;
  isMandatory?: boolean;
  dependencies?: number[];
}

/////////////////////////////////////////////////////////////

export interface PreModule {
  id: number;
  name: string;
  subModules: PreSubModule[] | null;
  isMandatory: boolean;
  flows: PreFlows[];
}

export interface PreSubModule {
  id: number;
  name: string;
  flows: PreFlows[] | null;
}

export interface PreFlows {
  id: number;
  name: string;
  isMandatory?: boolean;
  isChecked: boolean;
  dependencies?: number[];
}
