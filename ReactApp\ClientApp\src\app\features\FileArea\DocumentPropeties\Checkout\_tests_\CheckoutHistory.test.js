import React from "react";
import {List} from "antd";
import { shallow, mount } from "enzyme";
import renderer from "react-test-renderer";
import axios from "axios";

import CheckHistory, {getFileCountStatus, createNote, NOTE_FOR_CHECKED_IN, NOTE_FOR_UNDO_CHECKED_OUT} from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import MockAdapter from "axios-mock-adapter";

const MOCK_ID = "xxx";
const mockData = [
    {checkOutDate: "12/12/12", checkInDate: "11/11/11", returnDate: "10/10/10", checkOutNotes: "xxx1", checkOutBy: {name: "xxxn1", value: "xxxv1"}},
    {checkOutDate: "09/09/09", checkInDate: "08/08/08", returnDate: "07/07/07", checkOutNotes: "xxx2", checkOutBy: {name: "xxxn2", value: "xxxv2"}},
    {checkOutDate: "06/06/06", checkInDate: "05/05/05", returnDate: "04/04/04", checkOutNotes: "xxx3", checkOutBy: {name: "xxxn3", value: "xxxv3"}},
];

const mock = new MockAdapter(axios);
jest.mock("../../index.module.less", () => ({
    yjPropertiesCheckoutsTab: "yjPropertiesCheckoutsTab",
    yjPropertiesCheckoutsNotifications: "yjPropertiesCheckoutsNotifications",
    yjPropertiesCheckoutsList: "yjPropertiesCheckoutsList",
    yjPropertiesRequiredText: "yjPropertiesRequiredText",
}));

describe("Checkout History Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
        mock.onGet("YJAPI/files/xxx/checkouts").reply(200, mockData);
    });

    it("should render",() => {
        const chComponent = shallow(<CheckHistory />);
        expect(chComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const chComponent = renderer.create(<CheckHistory />).toJSON();
        expect(chComponent).toMatchSnapshot();
    });

    it("should render with props",() => {
        const chComponent = shallow(<CheckHistory fileId={""} />);
        expect(chComponent.html()).not.toBe(null);
    });

    it("should render with props are null",() => {
        const chComponent = shallow(<CheckHistory fileId={null} />);
        expect(chComponent.html()).not.toBe(null);
    });

    it("should render with props are undefined",() => {
        const chComponent = shallow(<CheckHistory fileId={undefined} />);
        expect(chComponent.html()).not.toBe(null);
    });

    it("should have a List.Item elements",async() => {
        const chComponent = mount(<CheckHistory fileId={MOCK_ID} />);
        expect(chComponent.find(List.Item)).toHaveLength(15);
    });

    it("should have a List.Meta elements",() => {
        const chComponent = mount(<CheckHistory fileId={MOCK_ID} />);
        expect(chComponent.find(List.Item.Meta)).toHaveLength(15);
    });

    it("should getFileCountStatus function return a value",() => {
        const zeroItemResponse = getFileCountStatus(0);
        expect(zeroItemResponse).toEqual("No Records Available");
        const fiveItemResponse = getFileCountStatus(5);
        expect(fiveItemResponse).toEqual("05 Checkouts");
        const twelveItemResponse = getFileCountStatus(12);
        expect(twelveItemResponse).toEqual("12 Checkouts");
        const twelveTagsResponse = getFileCountStatus(12,"Tags");
        expect(twelveTagsResponse).toEqual("12 Tags");
    });

    it("should createNote function return a value",() => {
        const record = {
            checkInDate: "2021-03-13",
            checkOutBy: {value:1, name:"Conarc Admin"},
            checkOutDate: "2021-04-13",
            checkOutNotes: "xxxxxxxxx",
            returnDate: "2021-05-13",
        }
        expect(createNote(record)).toEqual(NOTE_FOR_CHECKED_IN);
        record.checkInDate = "N/A";
        expect(createNote(record)).toEqual(NOTE_FOR_UNDO_CHECKED_OUT);
        record.checkInDate = "Pending";
        expect(createNote(record)).toEqual(undefined);
    });

    it("should have div elements",() => {
        const chComponent = mount(<CheckHistory fileId={MOCK_ID} />);
        expect(chComponent.find(".yjPropertiesCheckoutsTab")).toHaveLength(1);
        expect(chComponent.find(".yjPropertiesCheckoutsNotifications")).toHaveLength(1);
        expect(chComponent.find(".yjPropertiesCheckoutsList")).toHaveLength(3);
        expect(chComponent.find(".yjPropertiesRequiredText")).toHaveLength(3);
    });

});
