import React from "react";
import { mount, shallow } from "enzyme";
import FolderTree from "../index";
import initTestSuite from "@app/utils/config/TestSuite";
import Tree from "rc-tree";
import { Button } from "antd";

jest.mock("../index.module.less", () => ({
  yjFolderStructureEditAction: "yjFolderStructureEditAction",
  yjFolderStructureSection: "yjFolderStructureSection",
  yjAddParentFolderButtonWrapper: "yjAddParentFolderButtonWrapper",
  yjFolderStructureTree: "yjFolderStructureTree",
}));

describe("folder tree test suite", () => {
  beforeAll(() => {
    initTestSuite();
  });
  it("should render <FolderTree />", () => {
    const folderTreeComponent = mount(<FolderTree />);
    expect(folderTreeComponent.html()).not.toBe(null);
  });
  it("should create <FolderTree /> and match to snapshot", () => {
    const folderTreeComponent = mount(<FolderTree />);
    expect(folderTreeComponent).toMatchSnapshot();
  });

  it("should render <FolderTree/> component if prop formRef is null ", () => {
    const component = shallow(<FolderTree formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <FolderTree/> component if prop action is create ", () => {
    const component = shallow(<FolderTree action="create" />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <FolderTree/> component if prop action is edit ", () => {
    const component = shallow(<FolderTree action="edit" />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <FolderTree/> component if prop action is view ", () => {
    const component = shallow(<FolderTree action="view" />);
    expect(component.html()).not.toBe(null);
  });

  it("should have a Tree", () => {
    const component = shallow(<FolderTree />);
    expect(component.find(Tree)).toHaveLength(1);
  });

  it("should have 2 Buttons", () => {
    const component = shallow(<FolderTree />);
    expect(component.find(Button)).toHaveLength(2);
  });

  it("The first button label should be Delete", () => {
    const component = shallow(<FolderTree />);
    const firstButton = component.find(Button).at(0).props();
    expect(firstButton.children).toBe("Delete");
  });

  it("The first button label should be Hidden", () => {
    const component = shallow(<FolderTree />);
    const firstButton = component.find(Button).at(0).props();
    expect(firstButton.hidden).toBe(true);
  });

  it("The first button type should be default", () => {
    const component = shallow(<FolderTree />);
    const firstButton = component.find(Button).at(0).props();
    expect(firstButton.type).toBe("default");
  });

  it("The second button label should be Create Folder", () => {
    const component = shallow(<FolderTree />);
    const secondButton = component.find(Button).at(1).props();
    expect(secondButton.children).toBe("Create Folder");
  });

  it("The second button type should be primary", () => {
    const component = shallow(<FolderTree />);
    const secondButton = component.find(Button).at(1).props();
    expect(secondButton.type).toBe("primary");
  });

  it("The expanded keys of the FolderTree Component should be [ '0', '0_0', '0_1' ]", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.expandedKeys).toStrictEqual(["0", "0_0", "0_1"]);
  });

  it("The FolderTree Component should have one parent folder", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData.length).toBe(1);
  });

  it("The FolderTree Parent folder key should be 0", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].key).toBe("0");
  });

  it("The FolderTree Parent folder name should be New Folder", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].title).toBe("New Folder");
  });

  it("The FolderTree Component should have two child folders", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children.length).toBe(2);
  });

  it("The FolderTree Component's first parent's , first child's key should be 0_0", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children[0].key).toBe("0_0");
  });

  it("The FolderTree Component's first parent's , first child's title should be New Folder", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children[0].title).toBe("New Folder");
  });

  it("The FolderTree Component's first parent's , first child should be a leaf folder", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children[0].isLeaf).toBe(true);
  });

  it("The FolderTree Component's first parent's , first child's key should be 0_0", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children[1].key).toBe("0_1");
  });

  it("The FolderTree Component's first parent's , first child's title should be New Folder(2)", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children[1].title).toBe("New Folder(2)");
  });

  it("The FolderTree Component's first parent's , first child should be a leaf folder", () => {
    const component = shallow(<FolderTree />);
    const tree = component.find(Tree).at(0).props();
    expect(tree.treeData[0].children[1].isLeaf).toBe(true);
  });

  it("should have the follwing css classes", () => {
    const component = mount(<FolderTree />);
    expect(component.find(".yjFolderStructureEditAction")).toBeDefined();
    expect(component.find(".yjFolderStructureSection")).toBeDefined();
    expect(component.find(".yjAddParentFolderButtonWrapper")).toBeDefined();
    expect(component.find(".yjFolderStructureTree")).toBeDefined();
  });
});
