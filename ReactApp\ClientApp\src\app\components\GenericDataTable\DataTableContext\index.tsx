import React, { createContext, useReducer } from "react";
import reducer from "./reducer";
import { GenericFilter } from "../types";

export type FilterDropdownOptions = {
  key: string;
  values: any[];
};

type GenericDataTableContext = {
  filters: GenericFilter[];
  columns: any[];
  records: any[];
  pagination: {
    total: number;
    current: number;
    pageSize: number;
  };
  sorter?: any;
  loading: boolean;
  filterDropDownOptions: any;
};

const _initialState: GenericDataTableContext = {
  filters: [],
  columns: [],
  records: [],
  pagination: {
    total: 0,
    current: 1,
    pageSize: 20,
  },
  loading: false,
  filterDropDownOptions: [],
};

export const DataTableContext = createContext<{
  state: GenericDataTableContext;
  dispatch: React.Dispatch<any>;
}>({
  state: _initialState,
  dispatch: () => null,
});

export const DataTableContextProvider = (props: any) => {
  const [state, dispatch]: any = useReducer(reducer, _initialState);

  return (
    <DataTableContext.Provider value={{ state, dispatch }}>
      {props.children}
    </DataTableContext.Provider>
  );
};
