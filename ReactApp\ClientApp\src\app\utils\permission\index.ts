import { PERMISSIONS  } from "@app/constants";

const hasPermission =(folderTree: any, p: string)=> {

  let isPermission = false;
  switch (p) {
    case PERMISSIONS.FILE_AREA_UPLOADER:
      isPermission = folderTree.siteStatusId === 1 || folderTree.siteStatusId === 3 ;
      break;
    case PERMISSIONS.FILE_AREA_DOWNLOAD:
      isPermission = folderTree.siteStatusId === 1 || folderTree.siteStatusId === 3 ;
      break;
    case PERMISSIONS.FILE_AREA_FILE_EDIT:
      isPermission = folderTree.siteStatusId === 1;
      break;
    case PERMISSIONS.FILE_AREA_OTHER_OPTIONS:
      isPermission = folderTree.siteStatusId === 1;
    case PERMISSIONS.FILE_AREA_CLIENT_PORTAL_FILE_EDIT:
      isPermission = folderTree.siteStatusId === 1;
      break;
  }
  // TODO : IFM side doesn't have siteStatusId for the moment. Until new attribute introduce isPermission  set to true Alway  ;
  isPermission = true;
  return isPermission;
}
export default hasPermission;
