// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Cleat Filter Test Suit should render and create the snapshot properly 1`] = `
<div>
  <div
    className="yjSecondaryAccordian"
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
  >
    <div
      className="ant-collapse ant-collapse-icon-position-right"
      role="tablist"
    >
      <div
        className="ant-collapse-item"
      >
        <div
          aria-expanded={false}
          className="ant-collapse-header"
          onClick={[Function]}
          onKeyPress={[Function]}
          role="tab"
          tabIndex={0}
        >
          <span
            aria-label="right"
            className="anticon anticon-right ant-collapse-arrow"
            role="img"
          >
            <svg
              aria-hidden="true"
              data-icon="right"
              fill="currentColor"
              focusable="false"
              height="1em"
              viewBox="64 64 896 896"
              width="1em"
            >
              <path
                d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
              />
            </svg>
          </span>
          Email Options
        </div>
      </div>
    </div>
  </div>
</div>
`;
