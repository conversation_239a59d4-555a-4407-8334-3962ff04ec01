import React from "react";
import { Button } from "antd";
import { ClearOutlined } from "@ant-design/icons";
import { useDispatch } from "react-redux";

import styles from "./index.module.less";
import { clearGridFilters } from "@app/redux/actions/gridsActions";

export interface IClearFilter {
  filters?: any;
}

export default (props: IClearFilter) => {
  const dispatch = useDispatch();
  return (
    <Button
      onClick={() => {
        dispatch(clearGridFilters());
      }}
      icon={<ClearOutlined />}
      className={styles.yjClearFilterBtn}
    >
      Clear Filters
    </Button>
  );
};
