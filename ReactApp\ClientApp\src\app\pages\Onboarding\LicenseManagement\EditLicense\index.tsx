import React, { Fragment, useState } from "react";
import { <PERSON><PERSON>, Form } from "antd";
import { Beforeunload } from "react-beforeunload";

import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";
import LicenseDetailsContainer from "../../../../components/forms/LicenseManagement/LicenseDetailsContainer";
import styles from "./index.module.less";
import { confirm } from "../../../../utils/confirm";
import { PromptOnload } from "../../../../utils/confirm/onloadPrompt";
import logger from "../../../../utils/logger";

const REDIRECT_URL = "/onboarding/license-management";
const REDIRECT_TIMEOUT = 200;

export default (props: any) => {
  const {
    match: {
      params: { id },
    },
  } = props;
  const [form] = Form.useForm();
  const [isModsTouched, isModulesTouched] = useState(false);
  const [formAction, setFormAction] = useState(false);
  const [isFormsTouched, setIsFormsTouched] = useState(false);

  const handleSave = (event: React.MouseEvent) => {
    setFormAction(true);
    form
      .validateFields()
      .then((values) => {
        form.submit();
      })
      .catch((error) => {
        form.scrollToField(error.errorFields[0].name);
        logger.error("Licence Management", "Licence Management Edit", error);
      });
  };

  const handleSuccess = () => {
    setFormAction(true);
    setTimeout(() => {
      props.history.push(REDIRECT_URL);
    }, REDIRECT_TIMEOUT);
  };

  const handleCancel = () => {
    if (form.isFieldsTouched() || isModsTouched) {
      confirm(() => {
        handleSuccess();
      });
    } else {
      handleSuccess();
    }
  };

  return (
    <Fragment>
      <PageTitle title={props.title} />
      <PageContent>
        <div className={styles.yjEditLicenseMgtModuleWrapper}>
          <div className={styles.yjEditLicenseModules}>
            {!formAction && (isModsTouched || isFormsTouched) && (
              <PromptOnload isBlocking={true} isSaving={false} />
            )}
            <Beforeunload onBeforeunload={(event) => event.preventDefault()} />
            <LicenseDetailsContainer
              form={form}
              licenseId={id}
              isModulesTouched={isModulesTouched}
              onSaveSuccessed={() => {
                handleSuccess();
              }}
              isEdit={true}
              onChangeLicenceForm={() => {
                setIsFormsTouched(true);
              }}
            />
          </div>

          <div className={styles.yjEditButtonWrapper}>
            <Button type="default" onClick={handleCancel}>
              Cancel
            </Button>
            <Button type="primary" htmlType="submit" onClick={handleSave}>
              Save
            </Button>
          </div>
        </div>
      </PageContent>
    </Fragment>
  );
};
