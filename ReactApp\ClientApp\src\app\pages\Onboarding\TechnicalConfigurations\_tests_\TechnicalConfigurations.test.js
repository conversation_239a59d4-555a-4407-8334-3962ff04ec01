import React from "react";
import {Modal} from "antd";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { MemoryRouter } from 'react-router-dom';

import TechnicalConfiguration from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";

const CustomTechnicalConfiguration = (props) => {
    return (
        <MemoryRouter>
            <TechnicalConfiguration {...props} />
        </MemoryRouter>
    );
}
describe("TechnicalConfiguration Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const tcComponent = mount(<CustomTechnicalConfiguration />);
        expect(tcComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const tcComponent = renderer.create(<CustomTechnicalConfiguration />).toJSON();
        expect(tcComponent).toMatchSnapshot();
    });

    it("should have a Modal element",() => {
        const tcComponent = mount(<CustomTechnicalConfiguration />);
        expect(tcComponent.find(Modal)).toHaveLength(1);
    });

    it("should have a PageTitle component",() => {
        const tcComponent = mount(<CustomTechnicalConfiguration />);
        expect(tcComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const tcComponent = mount(<CustomTechnicalConfiguration />);
        expect(tcComponent.find(PageContent)).toHaveLength(1);
    });
});





