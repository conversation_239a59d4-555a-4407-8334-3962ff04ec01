@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@file-path: '../../../styles/';

.yjHeader {
  background-color: @color-bg-header;
  border-bottom: 1px solid @color-border-header;
  padding: 0;

  .yjAppLogo {
    background: url(@app-logo) no-repeat center center;
    cursor: pointer;
    height: 100%;
    margin: 0 .75em;
    width: 130px;
  }
  .yjFirmLogo {
    cursor: pointer;
    height: 50px;
    margin-left: 0.75em;
    width: auto;
  }
  .yjFirmName {
    line-height: 100%;
    font-size: 30px;
    margin-left: 0.75em;
  }
  .yjHeaderMenu {
    background-color: @color-bg-header-menu;

    li {
      color: @color-font-header-menu;

      svg {
        color: @color-font-menu-icon;
      }
    }

    .yjHeaderUserName span{
      font-size: 10px;
      margin-left: 10px;
    }
  }

  .flex-mixin(center, flex, space-between);
}
