export type UserPermission = {
  privDMSCanViewFileArea: boolean | undefined;
  privDMSCanUploadFiles: boolean;
  privDMSCanManageFileTags: boolean;
  privDMSCanDeleteFileTags: boolean;
  privDMSCanManageFileAssign: boolean;
  privDMSCanRecategorizeFiles: boolean; // Updated key name
  privDMSCanManageFileStatus: boolean;
  privDMSCanCheckInCheckOutInternalFiles: boolean;
  privDMSCanPublishUnpublishInternalFiles: boolean;
  privDMSCanViewFileHistory: boolean;
  privDMSCanDeleteFiles: boolean;
  privDMSCanManageFileProperties: boolean;
  privDMSCanMarkFilesAsToBeDeleted: boolean;
  privDMSCanViewFileSourceLink: boolean;
  privDMSCanCreateFileArea: boolean; // New attribute
  privDMSCanViewTemplates: boolean | undefined;
  privDMSCanCreateTemplates: boolean;
  privDMSCanManageTemplates: boolean;
  privDMSCanManageAutoFiling: boolean | undefined; // New attribute
  privDMSCanManageRetention: boolean;  // New attribute
  privDMSCanMoveFiles: boolean;        // New attribute
  privDMSCanRenameFiles: boolean;      // New attribute
  privDMSCanCopyFiles: boolean;        // New attribute
  privDMSCanLinkUnlinkFiles: boolean;  // New attribute
}
