import React from "react";
import PageTitle from "..";
import { shallow } from "enzyme";

const sampleTitle = "Sample Title";

describe("<PageTitle/>", () => {
  it("should render PageTitle component", () => {
    const component = shallow(<PageTitle />);
    expect(component.html()).not.toBe(null);
  });

  it("should contain Title", () => {
    const component = shallow(<PageTitle title={sampleTitle} />);
    expect(component.html().indexOf(sampleTitle)).not.toBe(-1);
  });

  it("should render children", () => {
    const component = shallow(
      <PageTitle title={sampleTitle}>
        <div>Hello</div>
      </PageTitle>
    );
    expect(component.html().indexOf(sampleTitle)).not.toBe(-1);
  });
});
