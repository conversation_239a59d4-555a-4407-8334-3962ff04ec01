import { gridReducer, initialState } from "../gridReducers";
import * as actions from "../../actionTypes/gridActionTypes";

describe("grid reducer unit test suite", () => {
  it("should return the initial state", () => {
    expect(gridReducer(undefined, {})).toEqual(initialState);
  });

  it("should handle UPDATE_FILTERS", () => {
    const startAction = {
      type: actions.UPDATE_FILTERS,
      payload: true,
    };
    // it's empty on purpose because it's just starting to fetch posts
    expect(gridReducer({}, startAction)).toEqual({ showFilter: true });
  });

  it("should handle CLEAR_ALL_FILTERS", () => {
    const successAction = {
      type: actions.CLEAR_ALL_FILTERS,
    };
    expect(gridReducer({}, successAction)).toEqual({
      filters: [],
      gridFilters: [],
      showFilterSaveButton: false,
    });
  });

  it("should handle ADD_GRID_FILTER", () => {
    const updateAction = {
      type: actions.ADD_GRID_FILTER,
    };
    expect(gridReducer({ gridFilters: [] }, updateAction)).toEqual({
      gridFilters: [undefined],
      showFilterSaveButton: true,
    });
  });

  it("should handle APPLY_SAVED_FILTER", () => {
    const failAction = {
      type: actions.APPLY_SAVED_FILTER,
      payload: { gridFilterTemplates: [] },
    };
    expect(gridReducer({}, failAction)).toEqual({
      gridFilters: [],
      showFilter: true,
      showFilterSaveButton: true,
    });
  });

  it("should handle FILTER_CLOUD_UPDATED", () => {
    const failAction = {
      type: actions.FILTER_CLOUD_UPDATED,
      payload: [],
    };
    expect(gridReducer({}, failAction)).toEqual({
      gridFilters: [],
    });
  });

  it("should handle UPDATE_SAVED_FILTER_TEMPLATES", () => {
    const failAction = {
      type: actions.UPDATE_SAVED_FILTER_TEMPLATES,
      payload: [],
    };
    expect(gridReducer({}, failAction)).toEqual({
      savedFilters: [],
    });
  });

  it("should handle REMOVE_GRID_FILTER", () => {
    const failAction = {
      type: actions.REMOVE_GRID_FILTER,
      payload: [],
    };
    const filtersAfterRemoveGivenFilter = [];
    expect(gridReducer({ gridFilters: [] }, failAction)).toEqual({
      gridFilters: filtersAfterRemoveGivenFilter,
      showFilterSaveButton: filtersAfterRemoveGivenFilter.length !== 0,
    });
  });

  it("should handle UPDATE_SINGLE_COLUMN", () => {
    const failAction = {
      type: actions.UPDATE_SINGLE_COLUMN,
      payload: {
        selectedElement: "xxxx",
        tableKey: "xxxx",
        selected: "xxxx",
      },
    };
    expect(gridReducer({}, failAction)).toEqual({
      selectedElement: "xxxx",
      tableKey: "xxxx",
      selected: "xxxx",
    });
  });

  it("should handle UPDATE_COLUMNS", () => {
    const failAction = {
      type: actions.UPDATE_COLUMNS,
      payload: {
        columns: [],
      },
    };
    expect(gridReducer({}, failAction)).toEqual({
      columns: [],
    });
  });

  it("should handle UPDATE_COLUMN_PARAMETERS", () => {
    const failAction = {
      type: actions.UPDATE_COLUMN_PARAMETERS,
      payload: "xxxx",
    };
    expect(gridReducer({}, failAction)).toEqual({
      columnQueryParameters: "xxxx",
    });
  });

  it("should handle UPDATE_SEARCH_PARAMETERS", () => {
    const failAction = {
      type: actions.UPDATE_SEARCH_PARAMETERS,
      payload: "xxxx",
    };
    expect(gridReducer({}, failAction)).toEqual({
      searchQueryParameters: "xxxx",
    });
  });
});
