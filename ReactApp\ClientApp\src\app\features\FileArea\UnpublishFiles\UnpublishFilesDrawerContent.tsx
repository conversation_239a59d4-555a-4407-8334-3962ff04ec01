import React from "react";
import { <PERSON>, <PERSON>, <PERSON>, But<PERSON>, Modal as <PERSON>t<PERSON><PERSON><PERSON>, Toolt<PERSON> } from "antd";
import { CloseOutlined, ExclamationCircleOutlined } from "@ant-design/icons";
import styles from "./index.module.less";

const { confirm } = AntModal;

export interface IUnpublishFilesOptions {
  fileList: IUnpublishFiles[];
  onFileListChange: (fileList: IUnpublishFiles[]) => void;
  closeModal: () => void;
}

export interface IUnpublishFiles {
  id: string;
  checked: boolean;
  title: string | null;
}

export default ({
  fileList,
  onFileListChange,
  closeModal,
}: IUnpublishFilesOptions) => {
  return (
    <>
      <div className={styles.yjModalContentWrapper}>
        <div className={styles.yjPublishFilesWrapper}>
          <div className={styles.yjPublishFilesUpperSection}>
            <Row gutter={24}>
              <Col span={24}>
                <div className={styles.yjPublishFilesFormHeader}>
                  <Row gutter={24}>
                    <Col span={2} className={styles.yjPublishFilesColumn}></Col>
                    <Col span={4} className={styles.yjPublishFilesColumn}>
                      <Form.Item
                        children={null}
                        label="ID"
                        colon={false}
                      ></Form.Item>
                    </Col>
                    <Col span={18} className={styles.yjPublishFilesColumn}>
                      <Form.Item
                        children={null}
                        label="File Title"
                        colon={false}
                      ></Form.Item>
                    </Col>
                  </Row>
                </div>
                <div className={styles.yjPublishFilesFormGrid}>
                  {fileList.map((file: any) => (
                    <Row
                      key={file.id}
                      gutter={24}
                      className={styles.yjPublishFilesFormRow}
                    >
                      <Col span={2}>
                        <Tooltip title="Remove File">
                          <Button
                            type="primary"
                            icon={<CloseOutlined />}
                            className={styles.yjDeteleFile}
                            onClick={() => {
                              confirm({
                                title: `File will be removed from unpublishing. Do you wish to continue?`,
                                icon: <ExclamationCircleOutlined />,
                                okText: "Yes",
                                cancelText: "No",
                                onOk() {
                                  const newFiles = fileList.filter(
                                    (e: any) => e.id !== file.id
                                  );
                                  onFileListChange(newFiles);
                                  if (newFiles.length === 0) closeModal();
                                },
                              });
                            }}
                          />
                        </Tooltip>
                      </Col>
                      <Col span={4}>
                        <div className={styles.yjCheckoutId}>{file.id}</div>
                      </Col>
                      <Col span={18}>
                        <div className={styles.yjCheckoutFileName}>
                          {file.title}
                        </div>
                      </Col>
                    </Row>
                  ))}
                </div>
              </Col>
            </Row>
          </div>
        </div>
      </div>
    </>
  );
};
