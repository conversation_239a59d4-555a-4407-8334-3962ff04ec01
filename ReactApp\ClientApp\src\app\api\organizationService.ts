import http from "../utils/http";
import config, { getApiUrl } from "../utils/config";
import HTTPResponse from "../utils/http/interfaces/HttpResponse";
import httpVerbs from "../utils/http/httpVerbs";

export const getIntegrations = (): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: config.api.organizationAPI.integrations,
  });
};

export const getOrganizationDetailsByLicenseId = (
  licenseId: string
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getApiUrl(
      `${config.api.organizationAPI.licenses}/${licenseId}/organizations`
    ),
  });
};

export const modifyOrganization = (
  organizationObj: any,
  licenseId: string
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.PUT,
    url: getApiUrl(
      `${config.api.organizationAPI.licenses}/${licenseId}/organizations`
    ),
    data: organizationObj,
  });
};
