import {
  getFunctionalFlowData,
  UpdateFunctionalFlow,
} from "../../api/functionalFlowService";
import {
  DATA_LOAD_SUCCESS,
  DATA_LOAD_ERROR,
  DATA_LOADING,
  EDIT_FUNCTIONAL_FLOW,
  SAVE_FUNCTIONAL_FLOW,
  SAVE_SUCCESS_FUNCTIONAL_FLOW,
  SAVE_FAIL_FUNCTIONAL_FLOW,
  REDIRECT_FUNCTIONAL_FLOW,
  VERICAL_LOAD_SUCCESS,
} from "../actionTypes/functionalFlowActionTypes";
import {
  Module,
  FunctionModule,
  PreModule,
  FunctionSubModule,
} from "../types/functionalFlow/functionalFlow";
import { getFlows } from "../../api/verticalService";
import {
  mapModuleResponseToModule,
  AssignFunctionsToModule,
  generateFunctionalFlowRequestBody,
} from "../../components/forms/FunctionalFlowManagement/utils/mapFunctionsToModules";

const dispatchAction = (dispatch: Function, type: any, payloadInput?: any) => {
  dispatch({
    type: type,
    payload: payloadInput,
  });
};

export const loadFunctionalFlowData = (licenceId: string) => (
  dispatch: Function
) => {
  getFunctionalFlowData(licenceId)
    .then((response) => {
      dispatchAction(dispatch, DATA_LOAD_SUCCESS, response.data);
      dispatchAction(dispatch, DATA_LOADING, false);
    })
    .catch((error) => {
      dispatch({
        type: DATA_LOAD_ERROR,
        payload: ["Data Load Error"],
      });
    });
};

export const retriveFunctionalFlowData = (licenceId: string) => (
  dispatch: Function
) => {
  Promise.all([getFunctionalFlowData(licenceId), getFlows(licenceId)])
    .then(async (values) => {
      const originalObject = values[1].data.modules as PreModule[];
      const functionModulesx = values[0].data.modules as FunctionModule[];
      const mappedModules = mapModuleResponseToModule(originalObject);
      const functionalModules = AssignFunctionsToModule(
        mappedModules,
        functionModulesx
      );

      dispatchAction(dispatch, DATA_LOAD_SUCCESS, {
        modules: functionalModules,
      });
      dispatchAction(dispatch, VERICAL_LOAD_SUCCESS, {
        functionModules: functionModulesx,
      });
      dispatchAction(dispatch, DATA_LOADING, false);
    })
    .catch((error) => {
      dispatchAction(dispatch, DATA_LOAD_ERROR, ["Data Load Error"]);
    });
};

export const EditFunctionalFlow = (
  details: Module[] | null | undefined,
  editModule: FunctionSubModule[] | null
) => (dispatch: Function) => {
  dispatchAction(dispatch, EDIT_FUNCTIONAL_FLOW, {
    modules: details,
    editModule: editModule,
  });
};

export const SaveFunctionalFlow = (
  details: Module[] | null | undefined,
  licenceId: string
) => (dispatch: Function) => {
  dispatchAction(dispatch, SAVE_FUNCTIONAL_FLOW);

  try {
    const requestBody = generateFunctionalFlowRequestBody(details);
    UpdateFunctionalFlow(requestBody, licenceId)
      .then(() => {
        dispatchAction(dispatch, SAVE_SUCCESS_FUNCTIONAL_FLOW);
      })
      .catch((error) => {
        dispatchAction(dispatch, SAVE_FAIL_FUNCTIONAL_FLOW, ["Failed to Save"]);
      });
  } catch (e) {
    dispatchAction(dispatch, SAVE_FAIL_FUNCTIONAL_FLOW, ["Failed to Save"]);
  }

  dispatch({
    type: EDIT_FUNCTIONAL_FLOW,
  });
};

export const redirectToFunctionalFlow = () => (dispatch: Function) => {
  dispatch({
    type: REDIRECT_FUNCTIONAL_FLOW,
  });
};

export const UpdateFunctionalFlowDataLoading = (isLoading: boolean) => {
  return {
    type: DATA_LOADING,
    payload: isLoading,
  };
};
