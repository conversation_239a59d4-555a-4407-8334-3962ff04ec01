import { CancelTokenSource, Canceler } from "axios";

export type ProgressingFileInfo = {
  name: string;
  size: number;
  uploadOptions: any;
  chunkCount: number;
  chunkCounter: number;
  chunkStartSize: number;
  chunkEndSize: number;
  toBeProceed: boolean;
  retryCount: number;
  percent?: number;
  currentChunkPercent?: number;
  cancelTokenSource?: CancelTokenSource;
  error?: Error;
  cancel?: Canceler;
  referenceNumber: string | null;
  completed: boolean;
};

export type ProgressingSimpleFileInfo = {
  id: any;
  referenceNumber: string | null;
  uploadOptions: any;
  chunkCount: number;
  chunkCounter: number;
  chunkStartSize: number;
  chunkEndSize: number;
  toBeProceed: boolean;
  uploadResult?: (uid: string) => void;
  percent?: number;
  currentChunkPercent?: number;
  cancelTokenSource?: CancelTokenSource;
  error?: Error;
  cancel?: Canceler;
  progress?: (percent: number) => void;
};

export interface FileList {
  [uid: string]: ProgressingFileInfo;
}

export interface SimpleFileList {
  [uid: string]: ProgressingSimpleFileInfo;
}
