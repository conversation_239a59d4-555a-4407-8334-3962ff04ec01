import React, { Fragment } from "react";
import { Select } from "antd";

import { ChannelType } from "@app/types/fileAreaTypes";

const { Option } = Select;
export interface IChannelSelector {
  data?: ChannelType[] | null;
  onChannelChange?: (value: any, option: any) => void;
  defaultChannel?: string;
}

const filterOption = (input: any, option: any) => {
  return option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
};

export default (props: IChannelSelector) => {
  return (
    <Fragment>
      <Select
        notFoundContent="No Results Found"
        showSearch
        onChange={props.onChannelChange}
        className={"yjChannelSelector"}
        defaultValue={props.defaultChannel}
        filterOption={filterOption}
      >
        {props.data?.map((value: any) => {
          return (
            <Option key={value.id} value={value.id}>
              {`${value.name}`}
            </Option>
          );
        })}
      </Select>
    </Fragment>
  );
};
