import { encrypt, decrypt } from "../cryptoText";

describe("Crypto test Suite", () => {
  it("should exsist encrypt function", () => {
    expect(typeof encrypt).toBe("function");
  });
  it("should exsist decrypt function", () => {
    expect(typeof decrypt).toBe("function");
  });
  it("encrypt should return a value input is provided", () => {
    expect(encrypt("testValue")).not.toBe(null);
  });

  it("encrypt should return a  string value input is not provided", () => {
    expect(encrypt()).not.toBe(null);
  });

  it("decrypt should return a value input is provided", () => {
    expect(decrypt("testValue")).not.toBe(null);
  });

  it("Encrypt and Decrpt should happen properly", () => {
    const encrptedValue = encrypt("abc");
    const decrptValue = decrypt(encrptedValue);
    expect(decrptValue).toBe("abc");
  });
});
