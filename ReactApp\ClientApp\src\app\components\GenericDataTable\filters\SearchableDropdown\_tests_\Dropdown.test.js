import React from "react";
import {List} from "antd";
import {shallow} from "enzyme";
import renderer from "react-test-renderer";

import initTestSuite from "@app/utils/config/TestSuite";
import Dropdown from "../Dropdown";

describe("Dropdown Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const dComponent = shallow(<Dropdown data={[]} loading={false} onClick={null} />);
        expect(dComponent.html()).not.toBe(null);
    });

    it("should create and match to snapshot", () => {
        const dComponent = renderer.create(<Dropdown data={[]} loading={false} onClick={null} />).toJSON();
        expect(dComponent).toMatchSnapshot();
    });

    it("should have a List component", () => {
        const dComponent = shallow(<Dropdown data={[]} loading={true} onClick={() => {}} />);
        expect(dComponent.find(List)).toHaveLength(1);
    });

    it("should have List.Item elements", () => {
        const dComponent = shallow(<Dropdown data={['A','B','C','D']} loading={true} onClick={() => {}} />);
        expect(dComponent.find(List.Item)).toHaveLength(4);
    });
});