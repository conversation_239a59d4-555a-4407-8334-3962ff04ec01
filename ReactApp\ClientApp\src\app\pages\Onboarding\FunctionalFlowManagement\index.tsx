import React, { Fragment, useState, useEffect } from "react";
import { But<PERSON> } from "antd";
import Tooltip from "antd/lib/tooltip";
import { withRouter } from "react-router-dom";
import { useDispatch } from "react-redux";
import { EyeOutlined, EditOutlined } from "@ant-design/icons";

import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import GenericDataTable from "../../../components/GenericDataTable";
import styles from "./index.module.less";
import Modal from "../../../components/Modal";
import config from "../../../utils/config";
import FunctionalFlowDetailsContainer from "../../../components/forms/FunctionalFlowManagement/FunctionalFlowDetailsContainer";
import { redirectToFunctionalFlow } from "../../../redux/actions/functionalFlowActions";
import { TERMINATED } from "@app/constants/licenseStatuses";

import { Sorter } from "../../../components/GenericDataTable/util";

const SORTER: Sorter = {
  value: "companyName",
  order: "ascend",
};

const Page = (props: any) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const [record, setRecord] = useState({ licenseId: "" });
  const dispatch = useDispatch();

  const handleCancel = (e: any) => {
    setShowEditModal(false);
  };

  const redirectToEditPage = (recordInput: any) => {
    props.history.push(
      `/onboarding/flow-management/edit/${recordInput.licenseId}`
    );
  };

  useEffect(() => {
    dispatch(redirectToFunctionalFlow());
  });

  const renderGridColumns = () => {
    return {
      companyName: (value: any) => {
        return (
          <div className={styles.yjCompanyName}>
            <Tooltip placement="leftTop" title={value}>
              {value}
            </Tooltip>
          </div>
        );
      },

      action: (text: any, flowRecordValue: any) => (
        <div className={"yjActionIconWrapper"}>
          <Tooltip title="View">
            <Button
              icon={<EyeOutlined />}
              onClick={() => {
                setRecord(flowRecordValue);
                setShowEditModal(true);
              }}
            />
          </Tooltip>
          <Tooltip title="Edit">
            <Button
              disabled={flowRecordValue.status.value === TERMINATED}
              icon={<EditOutlined />}
              onClick={() => {
                redirectToEditPage(flowRecordValue);
              }}
            />
          </Tooltip>
        </div>
      ),
    };
  };

  return (
    <Fragment>
      {/* <Modal
        visible={showEditModal}
        title={"Flow Management Details"}
        onCancel={handleCancel}
        footer={[
          <span
            style={{ float: "left", color: "#0e678e", fontStyle: "italic" }}
          >
            *This feature is coming soon
          </span>,
          <Button key="back" type="default" onClick={handleCancel}>
            cancel
          </Button>,
          <Button
            key="submit"
            type="primary"
            onClick={() => {
              redirectToEditPage(record);
            }}
          >
            Manage
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <FunctionalFlowDetailsContainer
            key={record.licenseId}
            actionType="view"
            licenceId={record.licenseId}
          />
        </div>
      </Modal> */}

      <PageTitle title={props.title} />
      <PageContent>
        <GenericDataTable
          endpoint={config.api.organizationAPI.organizations}
          rowKey={"licenseId"}
          tableKey={"functionalflows"}
          customRender={renderGridColumns()}
          sorted={SORTER}
        />
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
