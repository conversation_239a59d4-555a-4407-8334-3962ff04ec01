import React from "react";
import {List} from "antd";
import <PERSON>ckA<PERSON>pter from "axios-mock-adapter";
import axios from "axios";
import { shallow, mount } from "enzyme";
import renderer from "react-test-renderer";

import DetailHistory from "../index";
import initTestSuite from "@app/utils/config/TestSuite";

const MOCK_ID = "xxx";
const mockData = [
    {type: "pdf", size: "12 KB", year: "Peter1",
        status: {id: 1, name: "status1"}, assignee: {id: 1, name: "assignee1"},
        folder: {}, created: "12/12/12", createdBy: "user1", modified: "12/12/12",
        projects:[{id:1,name:"project1"},{id:2,name:"project2"}], tags:[{id:1,name:"tag1"},{id:2,name:"tag2"}], expirationDate: "12/12/12", expirationStatus: "Arrived"},
    {type: "pdf", size: "12 KB", year: "Peter2",
        status: {id: 1, name: "status1"}, assignee: {id: 1, name: "assignee1"},
        folder: {}, created: "12/12/12", createdBy: "user1", modified: "12/12/12",
        projects:[{id:1,name:"project1"},{id:2,name:"project2"}], tags:[{id:1,name:"tag1"},{id:2,name:"tag2"}], expirationDate: "12/12/12", expirationStatus: "Arrived"},
    {type: "pdf", size: "12 KB", year: "Peter3",
        status: {id: 1, name: "status1"}, assignee: {id: 1, name: "assignee1"},
        folder: {}, created: "12/12/12", createdBy: "user1", modified: "12/12/12",
        projects:[{id:1,name:"project1"},{id:2,name:"project2"}], tags:[{id:1,name:"tag1"},{id:2,name:"tag2"}], expirationDate: "12/12/12", expirationStatus: "Arrived"},
    {type: "pdf", size: "12 KB", year: "Peter4",
        status: {id: 1, name: "status1"}, assignee: {id: 1, name: "assignee1"},
        folder: {}, created: "12/12/12", createdBy: "user1", modified: "12/12/12",
        projects:[{id:1,name:"project1"},{id:2,name:"project2"}], tags:[{id:1,name:"tag1"},{id:2,name:"tag2"}], expirationDate: "12/12/12", expirationStatus: "Arrived"},
];

const mock = new MockAdapter(axios);
jest.mock("../../index.module.less", () => ({
    yjPropertiesDetailTab: "yjPropertiesDetailTab",
    yjPropertiesDetailPreview: "yjPropertiesDetailPreview",
    yjPropertiesDetailInfo: "yjPropertiesDetailInfo",
    yjPropertiesDetailList: "yjPropertiesDetailList",
    yjPropertiesDetailListItem: "yjPropertiesDetailListItem",
    yjPropertiesDetailTitle: "yjPropertiesDetailTitle",
    yjPropertiesDetailDescription: "yjPropertiesDetailDescription",
}));

describe("Detail History Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
        mock.onGet("YJAPI/files/xxx/details").reply(200, mockData);
    });

    it("should render",() => {
        const dhComponent = shallow(<DetailHistory />);
        expect(dhComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const dhComponent = renderer.create(<DetailHistory />).toJSON();
        expect(dhComponent).toMatchSnapshot();
    });

    it("should render with props",() => {
        const dhComponent = shallow(<DetailHistory fileId={""} />);
        expect(dhComponent.html()).not.toBe(null);
    });

    it("should render with props are null",() => {
        const dhComponent = shallow(<DetailHistory fileId={null} />);
        expect(dhComponent.html()).not.toBe(null);
    });

    it("should render with props are undefined",() => {
        const dhComponent = shallow(<DetailHistory fileId={undefined} />);
        expect(dhComponent.html()).not.toBe(null);
    });

    it("should have a List.Item elements",() => {
        const dhComponent = mount(<DetailHistory fileId={MOCK_ID} />);
        expect(dhComponent.find(List.Item)).toHaveLength(16);
    });

    it("should have a List.Meta elements",() => {
        const dhComponent = mount(<DetailHistory fileId={MOCK_ID} />);
        expect(dhComponent.find(List.Item.Meta)).toHaveLength(16);
    });

    it("should have div elements",() => {
        const dhComponent = mount(<DetailHistory fileId={MOCK_ID} />);
        expect(dhComponent.find(".yjPropertiesDetailTab")).toHaveLength(1);
        expect(dhComponent.find(".yjPropertiesDetailPreview")).toHaveLength(1);
        expect(dhComponent.find(".yjPropertiesDetailInfo")).toHaveLength(4);
        expect(dhComponent.find(".yjPropertiesDetailList")).toHaveLength(32);
        expect(dhComponent.find(".yjPropertiesDetailListItem")).toHaveLength(16);
        expect(dhComponent.find(".yjPropertiesDetailTitle")).toHaveLength(16);
        expect(dhComponent.find(".yjPropertiesDetailDescription")).toHaveLength(16);
    });

});

