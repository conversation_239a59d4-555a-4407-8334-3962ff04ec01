import { useContext, useState, useEffect } from "react";
import isEqual from "lodash/isEqual";
import { useDispatch, useSelector } from "react-redux";

import { DataTableContext } from "../DataTableContext";
import { GenericFilter, GenericGridFilterTypes } from "../types";
import {
  addGridFilter,
  removeGridFilter,
} from "@app/redux/actions/gridsActions";
import { RootState } from "@app/redux/reducers/state";

type useFilterStateType = {
  containsData: boolean;
  data: GenericFilter[];
};

export const DISPATCH_ACTION_FILTERS = "UPDATE_GD_FILTERS";

export default (
  key: string,
  filterName: string,
  isArray: boolean,
  filterType?: string
) => {
  const { state, dispatch } = useContext(DataTableContext);
  const [filterDataState, setFilterDataState] = useState<useFilterStateType>({
    containsData: false,
    data: [],
  });

  const gridFilters = useSelector(
    (store: RootState) => store.grid.gridFilters
  ) as GenericFilter[];

  const gridDispatch = useDispatch();

  useEffect(() => {
    const filterData = state.filters.filter((i) => i.key === key);
    setFilterDataState({
      containsData: !!filterData.length,
      data: filterData,
    });
  }, [key, state.filters]);

  const removeFilterProtected = (value: any, removeAll = false) => {
    let getFilters: GenericFilter[];
    if (!isArray || removeAll) {
      getFilters = [
        ...state.filters.filter((i) => {
          return i.key !== key;
        }),
      ];
    } else {
      getFilters = [
        ...state.filters.filter(
          (i) => !(i.key === key && isEqual(i.value, value))
        ),
      ];
    }
    return getFilters;
  };

  const getPreviousFilter = (newFilter: GenericFilter) => {
    return gridFilters.find(
      (filter) =>
        filter.key.toLocaleLowerCase() === newFilter.key.toLocaleLowerCase()
    );
  };

  const updateFilterTemplatedValues = (newFilter: GenericFilter) => {
    if (
      newFilter.filterType &&
      (newFilter.filterType === GenericGridFilterTypes.RANGE ||
        newFilter.filterType === GenericGridFilterTypes.SEARCH) &&
      getPreviousFilter(newFilter)
    ) {
      const previousFilter = getPreviousFilter(newFilter);
      gridDispatch(
        removeGridFilter(previousFilter?.key, previousFilter?.value)
      );
    }
  };

  const addFilter = (
    name: string,
    value: any,
    displayText: string,
    searchFilterType: string
  ) => {
    const finalObject: GenericFilter = {
      key,
      filterName,
      name,
      value,
      displayText,
      isArray,
      filterType: searchFilterType,
    };
    const getFilters = removeFilterProtected(value);
    updateFilterTemplatedValues(finalObject);
    getFilters.push(finalObject);
    dispatch({
      type: DISPATCH_ACTION_FILTERS, //add action for this
      payload: getFilters,
    });
    gridDispatch(addGridFilter(finalObject));
  };

  const removeFilter = (value: any, removeAll = false) => {
    const removeFilterP = removeFilterProtected(value, removeAll);
    if (gridFilters.length > 0) {
      const difference = gridFilters.filter((x) => !removeFilterP.includes(x));
      difference.forEach((filter) => {
        gridDispatch(removeGridFilter(filter?.key, filter?.value));
      });
    }
    dispatch({
      type: DISPATCH_ACTION_FILTERS, //add action for this
      payload: removeFilterP,
    });
  };

  const removeAllFilters = () => {
    dispatch({
      type: DISPATCH_ACTION_FILTERS, //add action for this
      payload: [],
    });
  };

  return {
    addFilter,
    removeFilter,
    removeAllFilters,
    filter: filterDataState,
  };
};
