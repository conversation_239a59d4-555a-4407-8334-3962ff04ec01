import React from "react";
import { Select } from "antd";
import { mount } from "enzyme";
import renderer from "react-test-renderer";

import { getValue } from "../renderOptions";
import initTestSuite from "@app/utils/config/TestSuite";
import renderOptions from "../renderOptions";

const { Option } = Select;
const RenderOptionsComponent = () => {
  return React.createElement(() => renderOptions([{ value: 1, name: "x" }]));
};
describe("RenderOptions test suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const imComponent = mount(<RenderOptionsComponent />);
    expect(imComponent.html()).not.toBe(null);
  });

  it("should create and match to snapshot", () => {
    const imComponent = renderer
      .create(renderOptions([{ value: 1, name: "x" }]))
      .toJSON();
    expect(imComponent).toMatchSnapshot();
  });

  it("should have a Option element", () => {
    const imComponent = mount(<RenderOptionsComponent />);
    expect(imComponent.find(Option)).toHaveLength(1);
  });

  it("get value should be a function", () => {
    expect(typeof getValue).toBe("function");
  });

  it("get value should return a string value", () => {
    expect(typeof getValue({ name: "xxx", value: 22 }, true)).toBe("string");
  });

  it("should return a valid responses on getValue function", () => {
    expect(getValue({ name: "xxx", value: 22 }, true)).toBe("xxx");
    expect(getValue({ name: "xxx", value: 33 }, false)).toBe(33);
    expect(getValue({ name: "xxx", value: 0 }, false)).toBe(0);
  });
});
