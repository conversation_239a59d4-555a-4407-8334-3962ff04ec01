import React, { useEffect, useState } from "react";
import { Skeleton } from "antd";
import { FormInstance } from "antd/lib/form";
import { useDispatch } from "react-redux";
import moment from "moment";
import { useHistory } from "react-router-dom";

import { IFile } from "@app/types/fileAreaTypes";
import { fetchOptions } from "@app/redux/actions/fileDetailsActions";
import {
  FileRecord,
  FileEvents,
} from "@app/components/forms/UploaderSubmit/types";
import UploaderSubmitContainer from "@app/components/forms/UploaderSubmit/UploaderSubmitContainer";
import { getFileDetailsByFileId } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { SAME_PROPERTY_UPLOAD } from "@app/components/Uploader/constants/uploadTypes";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";

export interface ManageFilesProps {
  siteId: string;
  binderId: string;
  form: FormInstance;
  dataList: IFile[];
  onFilesChange: (event: any) => void;
  onFormChange: (isFormChange: boolean, isAssigneeChange: boolean) => void;
  onFolderChange?: (id: number) => void;
  onTitleChange?: (title: string) => void;
  disabledForm?: boolean;
}

const FIELD_ASSIGNEE = "assigneeId";
const PERMANENT_EXPIRE_STATUS = "Permanent";
const NOT_APPLICABLE_EXPIRE_STATUS = "N/A";
export default (props: ManageFilesProps) => {
  const [selectedFiles, setSelectedFiles] = useState<FileRecord[]>([]);
  const [dataMappingTrigger, setDataMappingTrigger] = useState(false);
  const dispatch = useDispatch();
  const history = useHistory();

  const setManageFormFields = (data: any) => {
    props.form.setFieldsValue({
      statusId: data.status?.value,
      folder: data.folder?.id,
      year: data.year,
      assigneeId: data.assignee?.value,
      projectIds: data.projects?.map((project: any) => project.value),
      tags: data.tags?.map((tag: any) => tag.name),
      expirationDate:
        data.expirationStatus === PERMANENT_EXPIRE_STATUS ||
        data.expirationStatus === NOT_APPLICABLE_EXPIRE_STATUS
          ? undefined
          : data.expirationDate === NOT_APPLICABLE_EXPIRE_STATUS
          ? undefined
          : moment(data.expirationDate),
      permanent: data.expirationStatus === PERMANENT_EXPIRE_STATUS,
      description:
        data.description && data.description !== "N/A" ? data.description : "",
    });
    setDataMappingTrigger(true);
  };

  useEffect(() => {
    getFileDetailsByFileId(props.dataList[0]?.id)
      .then((response) => {
        setManageFormFields(response.data);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        }

        logger.error("Internal Files", "Manage Files Fetch data", error);
      });
    dispatch(fetchOptions(props.siteId, history));
  }, [dispatch, props.siteId, props.dataList]);

  useEffect(() => {
    convertFiles();
  }, [props.dataList]);

  const convertFiles = () => {
    const file: FileRecord = {
      title: props.dataList[0]?.title,
      referenceNumber: props.dataList[0]?.id,
      created: props.dataList[0]?.created,
      checked: true,
    };
    setSelectedFiles([file]);
  };

  const validateFormChangedValues = (formChanged: boolean) => {
    if (
      !props.form.getFieldValue("permanent") &&
      !props.form.getFieldValue("expirationDate")
    ) {
      props.onFormChange(
        false,
        props.dataList[0]?.assignee?.value !==
          props.form.getFieldValue(FIELD_ASSIGNEE)
      );
    } else {
      props.onFormChange(
        formChanged,
        props.dataList[0]?.assignee?.value !==
          props.form.getFieldValue(FIELD_ASSIGNEE)
      );
    }
  };

  const onTitleUpdate = (value: string) => {
    props.onTitleChange?.(value);
  };

  const fileEvents: FileEvents = {
    onTitleUpdate,
    onFileSelect: () => {},
    onFileRemove: () => {},
    onSaveSuccess: () => {},
    onFileSelectAll: () => {},
  };
  return (
    <>
      {selectedFiles.length > 0 && dataMappingTrigger ? (
        <UploaderSubmitContainer
          forManageFiles={true}
          uploadType={SAME_PROPERTY_UPLOAD}
          siteId={props.siteId}
          binderId={props.binderId}
          fileList={selectedFiles}
          disabledForm={props.disabledForm}
          form={props.form}
          onFormChange={(formChanged) => validateFormChangedValues(formChanged)}
          onFolderTreeChange={props.onFolderChange}
          fileEvents={fileEvents}
        />
      ) : (
        <Skeleton active />
      )}
    </>
  );
};
