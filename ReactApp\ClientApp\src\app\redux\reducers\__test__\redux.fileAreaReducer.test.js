import { fileAreaReducer, initialState } from "../fileAreaReducer";
import * as actions from "../../actionTypes/fileAreaActionType";

describe("File Area reducer test suite", () => {
  it("Should return the initial state", () => {
    expect(fileAreaReducer(undefined, {})).toBe(initialState);
  });
  it("should handle SET_FILE_AREA_SETTINGS", () => {
    const payload = {
      urlFileUpload: false,
      internalFileStatusUpdate: false,
      internalFileSetExpiration: false,
      internalFileEmailNotifications: false,
      internalFileSetAssignee: false,
      internalFileSetProject: false,
      internalFileDownload: false,
      internalFileCheckInCheckout: false,
      internalFileRecategorize: false,
    };

    const action = {
      type: actions.SET_FILE_AREA_SETTINGS,
      payload: payload,
    };
    expect(fileAreaReducer({}, action)).toEqual({ fileAreaSettings: payload });
  });

  it("should handle action SET_FOLDER_TREE", () => {
    const action = {
      type: actions.SET_FOLDER_TREE,
      payload: { siteId: "S-001", siteName: "site1", folders: [] },
    };
    expect(fileAreaReducer({}, action)).toEqual({
      folderTree: { siteId: "S-001", siteName: "site1", folders: [] },
    });
  });

  // it("should handle action SET_HAS_COMMON_DATA", () => {
  //   const actionTrue = {
  //     type: actions.SET_HAS_COMMON_DATA,
  //     payload: true,
  //   };
  //   const actionFalse = {
  //     type: actions.SET_HAS_COMMON_DATA,
  //     payload: false,
  //   };
  //   expect(fileAreaReducer({}, actionTrue)).toEqual({
  //     hasCommonData: true,
  //   });
  //   expect(fileAreaReducer({}, actionFalse)).toEqual({
  //     hasCommonData: false,
  //   });
  // });

  it("should handle action UPDATE_CONTEXT_MENU_DOWNLOAD", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_DOWNLOAD,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_DOWNLOAD,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      download: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      download: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_STATUS", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_STATUS,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_STATUS,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      status: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      status: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_ASSIGN", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_ASSIGN,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_ASSIGN,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      assign: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      assign: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_CHECKOUT", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_CHECKOUT,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_CHECKOUT,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      checkout: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      checkout: false,
    });
  });

  it("should handle action UPDATE_LOAD_GRID", () => {
    const actionTrue = {
      type: actions.UPDATE_LOAD_GRID,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_LOAD_GRID,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      loadGrid: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      loadGrid: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_PROPETIES", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_PROPETIES,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_PROPETIES,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      propeties: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      propeties: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_DELETE", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_DELETE,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_DELETE,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      deleteFiles: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      deleteFiles: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_RE_CATEGORIZE", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_RE_CATEGORIZE,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_RE_CATEGORIZE,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      reCategorize: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      reCategorize: false,
    });
  });
  
  it("should handle action UPDATE_CONTEXT_MENU_MOVE_FILES", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_MOVE_FILES,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_MOVE_FILES,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      reMoveFiles: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      reMoveFiles: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_RENAME_FILES", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_RENAME_FILES,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_RENAME_FILES,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      renameFiles: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      renameFiles: false,
    });
  });

  it("should handle action UPDATE_CONTEXT_MENU_COPY_FILES", () => {
    const actionTrue = {
      type: actions.UPDATE_CONTEXT_MENU_COPY_FILES,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_CONTEXT_MENU_COPY_FILES,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      copySelectedFiles: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      copySelectedFiles: false,
    });
  });

  it("should handle action UPDATE_ALLOWED_TO_CLOSE_PROPERTIES", () => {
    const actionTrue = {
      type: actions.UPDATE_ALLOWED_TO_CLOSE_PROPERTIES,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_ALLOWED_TO_CLOSE_PROPERTIES,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      allowedToCloseProperties: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      allowedToCloseProperties: false,
    });
  });

  it("should handle action UPDATE_PORTAL_FILE_UPLOAD_DETAILS", () => {
    const action = {
      type: actions.UPDATE_PORTAL_FILE_UPLOAD_DETAILS,
      payload: { requestId: "100", securityKey: "101" },
    };

    expect(fileAreaReducer({}, action)).toEqual({
      portalFilesUpload: { requestId: "100", securityKey: "101" },
    });
  });

  it("should handle action UPDATE_ALLOWED_TO_CLOSE_PROPERTIES", () => {
    const actionTrue = {
      type: actions.UPDATE_ALLOWED_TO_CLOSE_PROPERTIES,
      payload: true,
    };
    const actionFalse = {
      type: actions.UPDATE_ALLOWED_TO_CLOSE_PROPERTIES,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      allowedToCloseProperties: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      allowedToCloseProperties: false,
    });
  });

  it("should handle action PORTAL_FILE_REQUEST_SENT", () => {
    const actionTrue = {
      type: actions.PORTAL_FILE_REQUEST_SENT,
      payload: true,
    };
    const actionFalse = {
      type: actions.PORTAL_FILE_REQUEST_SENT,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      portalFileRequestSent: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      portalFileRequestSent: false,
    });
  });

  it("should handle action PORTAL_FILE_UPDATE_SELECTED_REQUEST", () => {
    const action = {
      type: actions.PORTAL_FILE_UPDATE_SELECTED_REQUEST,
      payload: { action: "save", requestId: "101" },
    };

    expect(fileAreaReducer({}, action)).toEqual({
      portalFilesSelectedRequest: { action: "save", requestId: "101" },
    });
  });

  it("should handle action FILES_UPLOADED", () => {
    const actionTrue = {
      type: actions.FILES_UPLOADED,
      payload: true,
    };
    const actionFalse = {
      type: actions.FILES_UPLOADED,
      payload: false,
    };
    expect(fileAreaReducer({}, actionTrue)).toEqual({
      filesUploaded: true,
    });
    expect(fileAreaReducer({}, actionFalse)).toEqual({
      filesUploaded: false,
    });
  });
});
