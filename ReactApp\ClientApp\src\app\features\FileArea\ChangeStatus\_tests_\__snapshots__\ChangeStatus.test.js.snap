// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Change Status Test Suite should create and match to snapshot 1`] = `
<div
  className="yjModalContentWrapper"
>
  <div
    className="yjChangeStatusGrid"
  >
    <div
      className="ant-table-wrapper"
    >
      <div
        className="ant-spin-nested-loading"
      >
        <div
          className="ant-spin-container"
        >
          <div
            className="ant-table ant-table-empty ant-table-fixed-header"
          >
            <div
              className="ant-table-container"
            >
              <div
                className="ant-table-header"
                style={
                  Object {
                    "overflow": "hidden",
                  }
                }
              >
                <table
                  style={
                    Object {
                      "tableLayout": "fixed",
                      "visibility": null,
                    }
                  }
                >
                  <colgroup />
                  <thead
                    className="ant-table-thead"
                  >
                    <tr>
                      <th
                        className="ant-table-cell"
                        colSpan={null}
                        rowSpan={null}
                        style={Object {}}
                      />
                      <th
                        className="ant-table-cell"
                        colSpan={null}
                        rowSpan={null}
                        style={Object {}}
                      >
                        Id
                      </th>
                      <th
                        className="ant-table-cell ant-table-cell-ellipsis"
                        colSpan={null}
                        rowSpan={null}
                        style={Object {}}
                        title="Title"
                      >
                        Title
                      </th>
                      <th
                        className="ant-table-cell"
                        colSpan={null}
                        rowSpan={null}
                        style={Object {}}
                      >
                        Current Status
                      </th>
                    </tr>
                  </thead>
                </table>
              </div>
              <div
                className="ant-table-body"
                onScroll={[Function]}
                style={
                  Object {
                    "maxHeight": "31vh",
                    "overflowY": "scroll",
                  }
                }
              >
                <table
                  style={
                    Object {
                      "tableLayout": "fixed",
                    }
                  }
                >
                  <colgroup>
                    <col
                      style={
                        Object {
                          "minWidth": 40,
                          "width": 40,
                        }
                      }
                    />
                  </colgroup>
                  <tbody
                    className="ant-table-tbody"
                  >
                    <tr
                      aria-hidden="true"
                      className="ant-table-measure-row"
                      style={
                        Object {
                          "fontSize": 0,
                          "height": 0,
                        }
                      }
                    >
                      <td
                        style={
                          Object {
                            "border": 0,
                            "height": 0,
                            "padding": 0,
                          }
                        }
                      >
                        <div
                          style={
                            Object {
                              "height": 0,
                              "overflow": "hidden",
                            }
                          }
                        >
                           
                        </div>
                      </td>
                      <td
                        style={
                          Object {
                            "border": 0,
                            "height": 0,
                            "padding": 0,
                          }
                        }
                      >
                        <div
                          style={
                            Object {
                              "height": 0,
                              "overflow": "hidden",
                            }
                          }
                        >
                           
                        </div>
                      </td>
                      <td
                        style={
                          Object {
                            "border": 0,
                            "height": 0,
                            "padding": 0,
                          }
                        }
                      >
                        <div
                          style={
                            Object {
                              "height": 0,
                              "overflow": "hidden",
                            }
                          }
                        >
                           
                        </div>
                      </td>
                      <td
                        style={
                          Object {
                            "border": 0,
                            "height": 0,
                            "padding": 0,
                          }
                        }
                      >
                        <div
                          style={
                            Object {
                              "height": 0,
                              "overflow": "hidden",
                            }
                          }
                        >
                           
                        </div>
                      </td>
                    </tr>
                    <tr
                      className="ant-table-placeholder"
                      style={
                        Object {
                          "display": null,
                        }
                      }
                    >
                      <td
                        className="ant-table-cell"
                        colSpan={4}
                        rowSpan={null}
                        style={Object {}}
                      >
                        No files Available
                      </td>
                    </tr>
                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="yjChangeStatusNew"
  >
    <form
      className="ant-form ant-form-horizontal ant-form-middle"
      id="basic"
      onReset={[Function]}
      onSubmit={[Function]}
    >
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -12,
            "marginRight": -12,
          }
        }
      >
        <div
          className="ant-col ant-col-8"
          style={
            Object {
              "paddingLeft": 12,
              "paddingRight": 12,
            }
          }
        />
        <div
          className="ant-col ant-col-16"
          style={
            Object {
              "paddingLeft": 12,
              "paddingRight": 12,
            }
          }
        >
          <div
            className="ant-row ant-form-item"
            style={Object {}}
          >
            <div
              className="ant-col ant-form-item-label"
              style={Object {}}
            >
              <label
                className="ant-form-item-no-colon"
                htmlFor="basic_newStatus"
                title="New Status"
              >
                New Status
              </label>
            </div>
            <div
              className="ant-col ant-form-item-control"
              style={Object {}}
            >
              <div
                className="ant-form-item-control-input"
              >
                <div
                  className="ant-form-item-control-input-content"
                >
                   
                  <div
                    className="ant-select yjChangeStatusSelectNew ant-select-single ant-select-show-arrow ant-select-show-search"
                    onBlur={[Function]}
                    onFocus={[Function]}
                    onKeyDown={[Function]}
                    onKeyUp={[Function]}
                    onMouseDown={[Function]}
                  >
                    <div
                      className="ant-select-selector"
                      onClick={[Function]}
                      onMouseDown={[Function]}
                    >
                      <span
                        className="ant-select-selection-search"
                      >
                        <input
                          aria-activedescendant="undefined_list_0"
                          aria-autocomplete="list"
                          aria-controls="undefined_list"
                          aria-haspopup="listbox"
                          aria-owns="undefined_list"
                          autoComplete="off"
                          className="ant-select-selection-search-input"
                          onChange={[Function]}
                          onCompositionEnd={[Function]}
                          onCompositionStart={[Function]}
                          onKeyDown={[Function]}
                          onMouseDown={[Function]}
                          onPaste={[Function]}
                          readOnly={false}
                          role="combobox"
                          style={
                            Object {
                              "opacity": null,
                            }
                          }
                          type="search"
                          unselectable={null}
                          value=""
                        />
                      </span>
                      <span
                        className="ant-select-selection-placeholder"
                      />
                    </div>
                    <span
                      aria-hidden={true}
                      className="ant-select-arrow"
                      onMouseDown={[Function]}
                      style={
                        Object {
                          "WebkitUserSelect": "none",
                          "userSelect": "none",
                        }
                      }
                      unselectable="on"
                    >
                      <span
                        aria-label="down"
                        className="anticon anticon-down ant-select-suffix"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="down"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                          />
                        </svg>
                      </span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
`;
