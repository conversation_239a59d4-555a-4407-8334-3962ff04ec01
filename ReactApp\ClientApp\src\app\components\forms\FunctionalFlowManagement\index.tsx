import React, { useEffect, useState } from "react";
import { Collapse, Checkbox, Row, Col } from "antd";
import { MinusOutlined, PlusOutlined } from "@ant-design/icons";
import { CheckboxChangeEvent } from "antd/lib/checkbox";

import {
  Module,
  Flows,
} from "../../../redux/types/functionalFlow/functionalFlow";
import { splitArrayToColumns } from "../../../utils/array";
import logger from "../../../utils/logger";
import styles from "./index.module.less";
const { Panel } = Collapse;

const GUTTER_SPLIT_VALUE = 3;
export interface IFunctionalFlowManagement {
  actionType: "add" | "edit" | "view";
  details?: Module[] | null;
  onEditFunctionCheckBox: (editedFuction: any | null) => void;
}

export default ({
  details,
  actionType,
  onEditFunctionCheckBox,
}: IFunctionalFlowManagement) => {
  const [disabled, setDisabled] = useState(false);

  useEffect(() => {
    if (actionType === "view") {
      setDisabled(true);
    }
  }, [actionType]);

  const onChangeFunctionCheckBox = ({ target }: CheckboxChangeEvent) => {
    const MODULE_SPLITTER_VALUE = 2;
    const value = target.value as string;
    const moduleId = Number(value.split("_")[0]);
    const subModuleId = Number(value.split("_")[1]);
    const functionId = Number(value.split("_")[MODULE_SPLITTER_VALUE]);

    try {
      const functionFlow = getFunctionalModule(
        moduleId,
        subModuleId,
        functionId
      );
      const modules = setDependentModule(functionFlow, moduleId);
      onEditFunctionCheckBox(modules);
    } catch (error) {
      logger.error(
        "Functional FLow Managemenet",
        "Functional Flow Edit",
        error
      );
    }
  };

  const getFunctionalModule = (
    moduleId: number,
    subModuleId: number,
    functionId: number
  ): Flows => {
    if (subModuleId === 0) {
      const functionFlow = details
        ?.find((x) => x.id === moduleId)
        ?.flows?.find((x) => x.id === functionId);
      if (functionFlow !== undefined) {
        return functionFlow;
      } else {
        throw new Error("functional flow not found");
      }
    } else {
      const functionFlow = details
        ?.find((x) => x.id === moduleId)
        ?.subModules?.find((x) => x.id === subModuleId)
        ?.flows?.find((x) => x.id === functionId);
      if (functionFlow !== undefined) {
        return functionFlow;
      } else {
        throw new Error("functional flow not found");
      }
    }
  };

  const setDependentModule = (
    functionalFlow: Flows,
    moduleId: number
  ): Module[] | null | undefined => {
    functionalFlow.isChecked = !functionalFlow.isChecked;
    if (
      functionalFlow.dependencies &&
      functionalFlow.dependencies?.length > 0
    ) {
      functionalFlow.dependencies.forEach((funcId) => {
        const dependentFunction = details
          ?.find((x) => x.id === moduleId)
          ?.flows?.find((x) => x.id === funcId);

        if (dependentFunction) {
          dependentFunction.isChecked = functionalFlow?.isChecked;
          dependentFunction.isMandatory = functionalFlow?.isChecked;
        }
      });
    }
    return details;
  };

  return (
    <Row gutter={16}>
      {details &&
        splitArrayToColumns(details as any[], GUTTER_SPLIT_VALUE)?.map(
          (colModules: any, index: any) => {
            return (
              <Col key={index} span={8} className={styles.yjAccordianWrapper}>
                {colModules.map((module: Module) => {
                  return (
                    <div
                      key={module.id}
                      className="m-b-15 ant-col-xs-24 ant-col-md-24"
                    >
                      <div className="commonSpace">
                        <div className={"yjCommonAccordian"}>
                          <Collapse
                            key="1"
                            style={{ margin: "0 8px" }}
                            expandIconPosition={"right"}
                          >
                            <Panel
                              showArrow={
                                (module.subModules?.length !== undefined &&
                                  module?.subModules?.length > 0) ||
                                module.flows.length > 0
                              }
                              header={module.name}
                              key={module.id}
                            >
                              <Collapse
                                key={module?.id}
                                expandIcon={({ isActive }) =>
                                  isActive ? (
                                    <MinusOutlined />
                                  ) : (
                                    <PlusOutlined />
                                  )
                                }
                                className="yjSecondaryCollapsePanel"
                              >
                                <div
                                  className={
                                    styles.yjSecondaryCollapsePanelContent
                                  }
                                >
                                  <ul>
                                    {module.flows?.map((func) => {
                                      return (
                                        <li key={func.id}>
                                          <Checkbox
                                            key={func.id}
                                            value={`${module.id}_0_${func.id}`}
                                            onChange={onChangeFunctionCheckBox}
                                            checked={func.isChecked}
                                            disabled={
                                              disabled || func.isMandatory
                                            }
                                          >
                                            {" "}
                                            {func.name}
                                          </Checkbox>
                                        </li>
                                      );
                                    })}
                                  </ul>
                                </div>
                                {module.subModules?.map((subModule) => {
                                  return (
                                    <Panel
                                      showArrow={
                                        subModule?.flows?.length !==
                                          undefined &&
                                        subModule?.flows?.length > 0
                                      }
                                      disabled={
                                        subModule?.flows?.length !==
                                          undefined &&
                                        subModule?.flows?.length <= 0
                                      }
                                      header={subModule?.name}
                                      key={subModule?.id}
                                    >
                                      <div
                                        className={
                                          styles.yjSecondaryCollapsePanelContent
                                        }
                                      >
                                        <ul
                                          className={
                                            styles.yjSecondaryCollapsePanelCheckboxList
                                          }
                                        >
                                          {subModule?.flows?.map((flow) => {
                                            return (
                                              <li key={flow.id}>
                                                <Checkbox
                                                  className={
                                                    styles.yjSecondaryCollapsePanelCheckbox
                                                  }
                                                  key={flow.id}
                                                  value={`${module.id}_${subModule.id}_${flow.id}`}
                                                  onChange={
                                                    onChangeFunctionCheckBox
                                                  }
                                                  checked={flow.isChecked}
                                                  disabled={
                                                    disabled || flow.isMandatory
                                                  }
                                                >
                                                  {" "}
                                                  {flow.name}
                                                </Checkbox>
                                              </li>
                                            );
                                          })}
                                        </ul>
                                      </div>
                                    </Panel>
                                  );
                                })}
                              </Collapse>
                            </Panel>
                          </Collapse>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </Col>
            );
          }
        )}
    </Row>
  );
};
