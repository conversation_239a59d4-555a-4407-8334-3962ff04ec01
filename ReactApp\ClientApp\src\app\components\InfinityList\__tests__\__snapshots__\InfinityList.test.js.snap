// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<InfinityList/> should render and create the snapshot properly 1`] = `
<div
  onScroll={[Function]}
  style={
    Object {
      "height": 400,
      "overflow": "auto",
    }
  }
>
  <div
    className="ant-list ant-list-split"
  >
    <div
      className="ant-spin-nested-loading"
    >
      <div
        className="ant-spin-container"
      >
        <div>
          <div
            className="ant-spin"
          >
            <span
              className="ant-spin-dot ant-spin-dot-spin"
            >
              <i
                className="ant-spin-dot-item"
              />
              <i
                className="ant-spin-dot-item"
              />
              <i
                className="ant-spin-dot-item"
              />
              <i
                className="ant-spin-dot-item"
              />
            </span>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
