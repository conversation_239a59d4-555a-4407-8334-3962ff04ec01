@import "@{file-path}/assets/images/_imagepath";
@import "@{file-path}/_mixins";
@import "@{file-path}/_variables";
@import "@{file-path}/_yjcommon";

@file-path: '../../../styles/';

    .yjUnlinkFilesFormHeader {
      color: @color-secondary;
      font-size: @font-size-base / 1.145;
      text-transform: @yj-transform;
      margin-bottom: 16px;

      .font-mixin(@font-primary, @yjff-semibold);
    }

    .yjUnlinkFilesFormGrid {
      margin-bottom: 10px;
      max-height: 350px;
      overflow-x: hidden;
      overflow-y: auto;

      .yjUnlinkFilesLinks {
        margin-bottom: 20px;
      }

      .yjUnlinkFileRow {
        border-bottom: 1px solid fade(@color-accent-border, 10%);
        margin-bottom: 8px;
        align-items: center;

        .font-mixin(@font-primary, @yjff-semibold);
      }

      .yjUnlinkSitesList {
        padding-left: 5rem;

        .yjUnlinkSiteRow {
          margin-bottom: 4px;
          padding-left: 8px;

          &:last-child {
            margin-bottom: 0;
          }

          :global(.ant-checkbox-wrapper) {
            font-size: @font-size-base;
          }
        }
      }
    }
