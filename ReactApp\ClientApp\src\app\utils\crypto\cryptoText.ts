import CryptoJS from "crypto-js";

const SECRET_KEY = "yjSecretKey";

export const encrypt = (ciperText: string) => {
  const b64 = CryptoJS.AES.encrypt(ciperText, SECRET_KEY).toString();
  const e64 = CryptoJS.enc.Base64.parse(b64);
  return e64.toString(CryptoJS.enc.Hex);
};

export const decrypt = (ciperText: string) => {
  const reb64 = CryptoJS.enc.Hex.parse(ciperText);
  const bytes = reb64.toString(CryptoJS.enc.Base64);
  const decryptedValue = CryptoJS.AES.decrypt(bytes, SECRET_KEY);
  return decryptedValue.toString(CryptoJS.enc.Utf8);
};
