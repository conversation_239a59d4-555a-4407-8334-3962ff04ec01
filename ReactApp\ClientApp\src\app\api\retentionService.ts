import config from '@app/utils/config';
import http, { httpVerbs } from '@app/utils/http';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import { getParameterizedUrlWith } from "@app/utils";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import logger from '@app/utils/logger';

export const checkRetentionNameExists = (name: string) => {
  return http({
    method: httpVerbs.HEAD,
    url: config.api[OperationalServiceTypes.FileManagementService].retentions,
    params: {
      name,
    },
  });
};

export const createRetention = (data: any) => {
  return http({
    method: httpVerbs.POST,
    url: config.api[OperationalServiceTypes.FileManagementService].retentions,
    data,
  });
};

export const updateRetention = (data: any) => {
  return http({
    method: httpVerbs.PUT,
    url: getParameterizedUrlWith( config.api[OperationalServiceTypes.FileManagementService].retentions, [ { name: "RetentionId", value: data.id } ] ),
    data,
  });
};

export const deleteRetentions = (data: any) => {
  logger.debug('Retention Service', 'deleteRetentions', data);
  return http({
    method: httpVerbs.PUT,
    url: getParameterizedUrlWith( config.api[OperationalServiceTypes.FileManagementService].retentionsSoftDelete, [ { name: "RetentionId", value: data.id } ] ),
    data,
  });
};

export const exportRetentions = (data: any): Promise<HTTPResponse<any>> => {
  logger.debug('Retention Service', 'exportRetentions', data);
  const queryString = new URLSearchParams(data).toString();
  return http({
    method: httpVerbs.GET,
    url: `${config.api[OperationalServiceTypes.FileManagementService].retentionsCSV}?${queryString}`,
    responseType: "blob",
  });
};