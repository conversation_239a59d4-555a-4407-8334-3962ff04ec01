import { GenericFilter } from "@app/components/GenericDataTable/types";
import { SavedFilterTemplate } from "@app/types/filterTemplateTypes";

export interface IGridView {
  tableKey: string;
  columns: any;
  selectedElement: SelectedElement;
  filters: any;
  selected: boolean;
  showFilter: boolean;
  columnQueryParameters?: any;
  searchQueryParameters?: any;
  gridFilters?: any;
  showFilterSaveButton?: boolean;
  savedFilters: [];
  filter_template_saved: boolean;
  preAppliedFilters: GenericFilter[];
  selectSavedFilter?: SavedFilterTemplate;
  hasUpdates?: boolean;
}

export interface SelectedElement {
  name: string;
  checked: boolean;
  multiple?: boolean;
}
