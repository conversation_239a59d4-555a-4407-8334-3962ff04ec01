import actionTypes from "./action-types";
import { setTableColumns } from "./persistColumns";
import { DISPATCH_ACTION_FILTERS } from "../hooks/useFilter";

export default (state: any, action: any) => {
  switch (action.type) {
    case actionTypes.UPDATE_COLUMNS:
      setTableColumns(action.payload.tableKey, [...action.payload.columns]);
      return {
        ...state,
        columns: action.payload.columns,
      };

    case actionTypes.UPDATE_SINGLE_COLUMN: {
      let _index = 0;
      const selectedColumn = state.columns.filter((i: any, index: number) => {
        if (i.key === action.payload.key) {
          _index = index;
          return true;
        }
        return false;
      })[0];
      state.columns[_index] = { ...selectedColumn, ...action.payload.data };
      setTableColumns(action.payload.tableKey, [...state.columns]);
      return { ...state, columns: [...state.columns] };
    }

    case actionTypes.UPDATE_FILTER_VALUE:
      return {
        ...state,
        filters: {
          ...state.filters,
          [action.payload.key]: action.payload.value,
        },
      };

    case actionTypes.REMOVE_FILTER_VALUE:
      const { [action.payload]: deletedKey, ...rest } = state.filters;
      return { ...state, filters: rest };

    case actionTypes.UPDATE_DATA_RECORDS: {
      state.pagination.total = action.payload.pagination.total;
      return { ...state, records: action.payload.records };
    }

    case actionTypes.UPDATE_CHANGE:
      return {
        ...state,
        pagination: action.payload.pagination,
        sorter: action.payload.sorter,
      };

    case actionTypes.TABLE_LOADING:
      return { ...state, loading: action.payload };

    case actionTypes.CLEAR_ALL_FILTERS:
      return { ...state, filters: [] };

    case DISPATCH_ACTION_FILTERS: {
      const { pagination, ...restOftheValues } = state;
      return {
        ...restOftheValues,
        pagination: {
          total: 0,
          current: 1,
          pageSize: pagination.pageSize,
        },
        filters: action.payload,
      };
    }

    case actionTypes.UPDATE_FILTER_DROPDOWN_VALUES: {
      const { filterDropDownOptions } = state;
      filterDropDownOptions[action.payload.key] = action.payload.value;
      return { ...state, filterDropDownOptions };
    }

    case actionTypes.SET_SELECTED_SAVED_FILTER: {
      return { ...state, filters: action.payload };
    }

    default:
      return state;
  }
};
