import { Form, Input } from "antd";
import React from "react";

export default ({
  isNameValid,
  newFilterName,
  onFilterNameChangeHandler,
}: any) => {
  return (
    <>
      <Form>
        <Form.Item
          validateStatus={isNameValid ? "success" : "error"}
          help={isNameValid ? "" : "Name Already Exists"}
        >
          <Input
            autoComplete="off"
            value={newFilterName}
            onChange={(e) => onFilterNameChangeHandler(e.target.value)}
          />
        </Form.Item>
      </Form>
    </>
  );
};
