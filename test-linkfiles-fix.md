# Test Plan for Link<PERSON>ilesDrawer Display Text Fix

## Issue Description
When searching for a client by ClientRef that exists on a subsequent page (offset > 0), the client gets auto-selected but only shows the client ID instead of the properly formatted display text.

## Changes Made
1. Added `selectedClientObjects` state to store full client data
2. Enhanced `getPaginatedClients` to merge selected clients into records
3. Updated `handleClientIdChange` to store both IDs and full objects
4. Improved InfinitySelect onChange handler

## Test Scenarios

### Scenario 1: Auto-selection from search (Primary Issue)
1. Open LinkFilesDrawer
2. Search for a client by ClientRef that exists on page 2+ (e.g., "111")
3. Verify client is auto-selected
4. **Expected Result**: Display text shows formatted name (e.g., "111 - Client 111") instead of just ID
5. Close and reopen dropdown
6. **Expected Result**: Display text remains properly formatted

### Scenario 2: Manual selection from pagination
1. Open LinkFilesDrawer
2. Scroll through pages to find a client
3. Manually select the client
4. **Expected Result**: Display text shows properly formatted
5. Close and reopen dropdown
6. **Expected Result**: Display text remains properly formatted

### Scenario 3: Multiple client selection
1. Select multiple clients from different pages
2. **Expected Result**: All clients show proper display text
3. Remove one client
4. **Expected Result**: Remaining clients still show proper display text

### Scenario 4: Search field switching
1. Select a client using "Client Ref" search
2. Switch to "Client Name" search
3. **Expected Result**: Previously selected client maintains proper display text
4. Search and select another client
5. **Expected Result**: Both clients show proper display text

## Files Modified
- `ReactApp/ClientApp/src/app/features/Link/LinkFilesDrawer.tsx`

## Key Functions Updated
- `handleClientIdChange()` - Now stores full client objects
- `getPaginatedClients()` - Merges selected clients into records
- `resetFields()` - Clears new state
- InfinitySelect onChange handler - Ensures full objects are passed
