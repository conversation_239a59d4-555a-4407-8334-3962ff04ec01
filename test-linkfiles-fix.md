# LinkFilesDrawer Display Text Fix

## Issue Description
When searching for a client by ClientRef that exists on a subsequent page (offset > 0), the client gets auto-selected but only shows the client ID instead of the properly formatted display text (e.g., showing "***********" instead of "111 - Client 111").

## Root Cause Analysis
The issue occurred due to a mismatch between the InfinitySelect component's internal state and the auto-selection mechanism:

1. **Search Scenario**: User searches for client "111" which exists on page 2 (offset=1)
2. **API Response**: Search returns only the single matching client record
3. **Auto-Selection**: `getPaginatedClients` detects single result and calls `handleClientIdChange([clientId], [singleClient])`
4. **State Mismatch**: InfinitySelect component's internal `options` state doesn't contain the searched client because:
   - The search result was returned directly without being added to the component's options
   - After search, dropdown resets to page 1 (offset=0), losing the client data
5. **Display Issue**: When InfinitySelect tries to format the selected value, `formatValue` function receives just the client ID instead of the full client object with `displayText` property

## Solution Implemented
Implemented a simplified state management approach with a single source of truth for selected clients:

### Before (Complex Approach):
- Maintained two separate arrays: `selectedClientsLists` (IDs) and `selectedClientObjects` (full objects)
- Required keeping both arrays synchronized
- Complex state management with potential for inconsistencies

### After (Simplified Approach):
- Single array `selectedClients` that stores full client objects
- Derive client IDs when needed using `selectedClients.map(client => client.id)`
- Enhanced `getPaginatedClients` to merge selected clients into current records
- Ensures InfinitySelect always has access to full client data for proper formatting

## Technical Implementation Details

### Key Changes Made:
1. **Replaced dual state with single state:**
   ```typescript
   // Before
   const [selectedClientsLists, setSelectedClientsList] = useState<string[]>([]);
   const [selectedClientObjects, setSelectedClientObjects] = useState<any[]>([]);

   // After
   const [selectedClients, setSelectedClients] = useState<any[]>([]);
   ```

2. **Updated getSelectedClientIds to derive IDs:**
   ```typescript
   const getSelectedClientIds = () => {
     return selectedClients.map(client => client.id);
   };
   ```

3. **Enhanced getPaginatedClients to merge selected clients:**
   ```typescript
   // Merge selected clients into current records if they're missing
   if (selectedClients.length > 0 && res.data.records) {
     const existingIds = res.data.records.map((record: any) => record.id);
     const missingClients = selectedClients.filter((client: any) => !existingIds.includes(client.id));
     if (missingClients.length > 0) {
       res.data.records = [...missingClients, ...res.data.records];
     }
   }
   ```

4. **Simplified handleClientIdChange:**
   ```typescript
   const handleClientIdChange = async (clientIdList: string[], selectedOptions: any[] = []) => {
     setSelectedClients(selectedOptions); // Store full objects only
     // ... rest of logic
   };
   ```

### How the Solution Works:
1. **Auto-selection**: When a client is auto-selected from search, full client object is stored in `selectedClients`
2. **Data Persistence**: Selected client data persists across dropdown open/close cycles
3. **Data Merging**: When dropdown reopens, selected clients are merged into the records if not present
4. **Display Formatting**: InfinitySelect always has access to full client data for proper `formatValue` execution

## Test Scenarios

### Scenario 1: Auto-selection from search (Primary Issue)
1. Open LinkFilesDrawer
2. Search for a client by ClientRef that exists on page 2+ (e.g., "111")
3. Verify client is auto-selected
4. **Expected Result**: Display text shows formatted name (e.g., "111 - Client 111") instead of just ID
5. Close and reopen dropdown
6. **Expected Result**: Display text remains properly formatted

### Scenario 2: Manual selection from pagination
1. Open LinkFilesDrawer
2. Scroll through pages to find a client
3. Manually select the client
4. **Expected Result**: Display text shows properly formatted
5. Close and reopen dropdown
6. **Expected Result**: Display text remains properly formatted

### Scenario 3: Multiple client selection
1. Select multiple clients from different pages
2. **Expected Result**: All clients show proper display text
3. Remove one client
4. **Expected Result**: Remaining clients still show proper display text

### Scenario 4: Search field switching
1. Select a client using "Client Ref" search
2. Switch to "Client Name" search
3. **Expected Result**: Previously selected client maintains proper display text
4. Search and select another client
5. **Expected Result**: Both clients show proper display text

## Benefits of the Solution
- **Simplified State Management**: Single source of truth eliminates synchronization issues
- **Better Performance**: No need to maintain duplicate data structures
- **Improved Maintainability**: Easier to understand and debug
- **Consistent Display**: Ensures proper formatting regardless of how client is selected
- **Future-Proof**: Pattern can be applied to other similar components

## Files Modified
- `ReactApp/ClientApp/src/app/features/Link/LinkFilesDrawer.tsx`
- `ReactApp/ClientApp/src/app/features/FileAreaActionPanel/MoveFiles.tsx`

## Key Functions Updated

### LinkFilesDrawer.tsx:
- `handleClientIdChange()` - Simplified to store only full client objects
- `getPaginatedClients()` - Enhanced to merge selected clients into records for consistent display
- `getSelectedClientIds()` - Updated to derive IDs from full client objects
- `resetFields()` - Updated to clear the simplified state
- InfinitySelect props - Updated to use derived values and simplified state

### MoveFiles.tsx:
- State management - Replaced `clientIdSelected` and `selectedFileArea` with full object storage
- `getPaginatedRecords()` - Enhanced to merge selected clients into records for consistent display
- `getFileAreaPaginatedRecords()` - Enhanced to merge selected file areas into records for consistent display
- Helper functions - Added `getSelectedClientId()` and `getSelectedFileAreaId()` to derive IDs from objects
- InfinitySelect components - Updated to use `returnObject={true}` and store full objects
- Form callbacks - Updated to use helper functions for ID retrieval
