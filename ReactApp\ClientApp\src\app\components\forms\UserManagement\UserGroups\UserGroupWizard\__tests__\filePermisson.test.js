import React from "react";
import renderer from "react-test-renderer";
import FilePermissionComponent from "../StepTwoFileAreaPermisson";
import initTestSuite from "@app/utils/config/TestSuite";
import { shallow } from "enzyme";
import { Form } from "antd";

describe("<FilePermissionComponent/>", () => {
  beforeAll(() => {
    initTestSuite();
  });
  it("should render <FilePermissionComponent/> component", () => {
    const component = shallow(<FilePermissionComponent formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer
      .create(<FilePermissionComponent formRef={null} />)
      .toJSON();
    expect(component).toMatchSnapshot();
  });

  it("should render <FilePermissionComponent/> component when setChannelContextList is null", () => {
    const component = shallow(
      <FilePermissionComponent setChannelContextList={null} />
    );
    expect(component.html()).not.toBe(null);
  });
  it(" <FilePermissionComponent/> should have a Form", () => {
    const component = shallow(
      <FilePermissionComponent setChannelContextList={null} />
    );
    expect(component.find(Form)).toHaveLength(1);
  });
});
