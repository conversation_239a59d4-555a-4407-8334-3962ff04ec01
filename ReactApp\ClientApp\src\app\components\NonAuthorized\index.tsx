import React from "react";

type INonAuthorized = {
  children?: any;
  title?: string;
  subTitle?: string;
  styleClassName?: any;
};

export default (props: INonAuthorized) => {
  return (
    <div className={props.styleClassName}>
      <div>
      <img src="/assets/images/Logo-placeholder-small.png"/>
      {props.title && <h1>{props.title}</h1>}
      {props.subTitle && <h6>{props.subTitle}</h6>}
      {props.children}
      </div>
    </div>
  );
};
