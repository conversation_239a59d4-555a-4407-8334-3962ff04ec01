const { override, fixBabelImports, addLess<PERSON>oader, addWebpackAlias } = require('customize-cra');
const copyWebPack = require('copy-webpack-plugin');
const path = require('path');

const addCopyWebPack = config => {
    config.plugins.push(new copyWebPack({
        patterns: [
            {
                from: 'src/styles', to: 'styles', globOptions: {
                    ignore: ['**/*.less']
                }
            },
            {
                from: './Web.config', to: './'
            }
        ]
    }))
    return config
}

module.exports = override(
    addCopyWebPack,
    fixBabelImports('import', {
        libraryName: 'antd',
        libraryDirectory: 'es',
        style: true
    }),
    addWebpackAlias({
        '@': path.resolve(__dirname, './src'),
        '@app' : path.resolve(__dirname, './src/app'),
        '@pages' : path.resolve(__dirname, './src/app/pages'),
    }),
    addLessLoader({
        lessOptions: {
            javascriptEnabled: true,
            modifyVars: {
                '@primary-color': '#419cb9',
                '@link-color': '#0e678e',
                '@font-size-base': '14px'
            }

        }
    })
);