import React from "react";
import { shallow } from "enzyme";
import { FolderTree, mapFolderResponseToTree } from "../index";
import renderer from "react-test-renderer";
import { FolderAddOutlined } from "@ant-design/icons";

const emptyTree = [
  {
    title: "Root Folder",
    className: "",
    key: "0",
    selected: false,
    isLeaf: false,
    icon: FolderAddOutlined,
    children: [],
  },
];

const folderInput = {
  siteName: "Site 1",
  folders: [
    {
      id: 1,
      name: "Folder 1",
      subFolders: [
        {
          id: 3,
          name: "Folder 1 - Sub Folder 1",
        },
        {
          id: 4,
          name: "Folder 1 - Sub Folder 2",
        },
      ],
    },
    {
      id: 2,
      name: "Folder 2",
    },
    {
      id: 3,
      name: "Folder 3",
    },
    {
      id: 4,
      name: "Folder 4",
    },
    {
      id: 5,
      name: "Folder 5",
    },
    {
      id: 6,
      name: "Folder 6",
    },
    {
      id: 7,
      name: "Folder 7",
    },
    {
      id: 8,
      name: "Folder 8",
    },
  ],
};

describe("Folder Tree Test Suit", () => {
  it("Folder Tree should render", () => {
    const component = shallow(
      <FolderTree data={null} onSelectFolder={() => {}} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("Folder Tree should render , when prop data is null", () => {
    const component = shallow(
      <FolderTree data={null} onSelectFolder={() => {}} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("Folder Tree should render , when prop data is not null", () => {
    const component = shallow(
      <FolderTree data={emptyTree} onSelectFolder={() => {}} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("mapFolderResponseToTree function should exsists", () => {
    expect(typeof mapFolderResponseToTree).toBe("function");
  });

  it("mapFolderResponseToTree function should return empty arry when folderResponse input is null", () => {
    expect(mapFolderResponseToTree(null)).toEqual([]);
  });
  it("mapFolderResponseToTree function should return empty tree array when folderResponse input is not null", () => {
    expect(mapFolderResponseToTree(folderInput)).not.toEqual([]);
  });
});
