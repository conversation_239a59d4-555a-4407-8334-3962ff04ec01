import { Modal } from "antd";
import { ExclamationCircleOutlined } from "@ant-design/icons";
import React from "react";

export default (
  onOk?: () => void,
  forPortal = false,
  onCancel?: () => void
) => {
  return Modal.confirm({
    title: forPortal
      ? "The file acceptance process will be aborted. Do you wish to continue?"
      : "File(s) will be discarded and removed from the upload process. Do you wish to continue?",
    icon: <ExclamationCircleOutlined />,
    okText: "Yes",
    cancelText: "No",
    onOk: onOk,
  });
};
