import verticalReducer, { initialState } from "../verticalReducer";
import * as actions from "../../actionTypes/verticalTypes";

describe("Vertical reducer test suite", () => {
  it("Should return the initial state", () => {
    expect(verticalReducer(undefined, {})).toBe(initialState);
  });

  it("should handle SET_MODULES action", () => {
    const action = {
      type: actions.SET_MODULES,
      payload: ["test"],
      verticalId: 100,
    };
    expect(verticalReducer({}, action)).toEqual({
      modules: ["test"],
      id: 100,
    });
  });

  it("should handle MODULES_FETCH_SUCCESSFUL action", () => {
    const action = {
      type: actions.MODULES_FETCH_SUCCESSFUL,
      isFetched: true,
    };
    expect(verticalReducer({}, action)).toEqual({
      isModulesFetched: true,
    });
  });
});
