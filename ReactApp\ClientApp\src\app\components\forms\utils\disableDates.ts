import moment from "moment";

const YEAR_LIMIT = 10000;

export const disabledDatesBeforeToday = (current: moment.Moment) =>
  current < moment().endOf("day").subtract(1, "days");

export const disabledPastDays = (current: moment.Moment) =>
  current < moment().endOf("day").subtract(0, "days");

export const genrateDateOnly = (current: any) =>
  new Date(current).toISOString().split("T")[0];
/**
 * Return true if years is more than 9999
 */
export const disabledYear = (current: moment.Moment) =>
  moment(current, "DD/MM/YYYY").year() >= YEAR_LIMIT;
  
