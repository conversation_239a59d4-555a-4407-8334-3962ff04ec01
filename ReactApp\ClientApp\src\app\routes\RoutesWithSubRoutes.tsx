import { getUserNavigationMenu, getUserPermission, getUserPreference } from "@app/api/userService";
import { SecureRoute, useOktaAuth } from "@okta/okta-react";
import { Skeleton } from "antd";
import React, { Suspense, useEffect, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import CommonLayout from "@app/layouts/CommonLayout";
import { LOGIN_SUCCESS, SET_BASE_URLS, SET_TENANT, } from "@app/redux/actionTypes/authActionTypes";
import AppError from "@app/components/AppError";
import logger from "@app/utils/logger";
import { setUserLoaded } from "@app/redux/actions/authActions";
import { HubConnection, HubConnectionBuilder, HubConnectionState, } from "@microsoft/signalr";
import config, { getApiUrl } from "@app/utils/config";
import { errorNotification, successNotification, } from "@app/utils/antNotifications";
import { updateGridHasUpdates } from "@app/redux/actions/gridsActions";
import { DiscoveryClient } from "../../index";
import Constants from "@app/constants";
import TenantSelection from "../components/TenatSelection";
import { OperationalServiceTypes, } from "@iris/discovery.fe.client";
import { setUserPermission, setUserPreference } from "@app/redux/actions/userManagementActions";
const w = window as any;

const RoutesWithSubRoutes = (route: any) => {
  const dispatch = useDispatch();
  const { authState, oktaAuth } = useOktaAuth();
  const [apiError, setApiError] = useState<any>({message:'', statusCode:''});
  const [connection, setConnection] = useState<null | HubConnection>(null);
  const { endPoints, tenant , user, menuItems} = useSelector((state: RootState) => state.auth);


  const fetchUserData = async () => {

    try {
      const permissions = await getUserPermission();
      const preferences = await getUserPreference();
      dispatch(setUserPermission(permissions));
      if (preferences == null || preferences === '') {
        dispatch(setUserPreference(w._env_ && w._env_.REACT_APP_DEFAULT_CULTURE_CODE));
      }else{
        dispatch(setUserPreference(preferences));
      }
    } catch (e) {
      logger.error("RoutesWithSubRoutes","fetchUserData Error", e);
      if (!e.response) setApiError({message:'ERR_NETWORK'});
      else setApiError(e);
    }
  };

  useEffect(() => {
    if (connection) {
      connection.on("ReceiveMessage", (subject, message) => {
        if (subject % 2 === 0) {
          errorNotification([""], message);
        } else {
          successNotification([""], message);
        }
        dispatch(updateGridHasUpdates(true));
      });
      connection
        .start()
        .then(() => {
          logger.info(
            "YJ Generic",
            "Push Notification",
            "started push notification"
          );
        })
        .catch((error) =>
          logger.error("YJ Generic", "Push Notification", error)
        );
    }
  }, [connection]);

  useEffect(() => {
    let tids = (authState?.accessToken?.claims.tid as Array<string>) ?? [];

    if (tids.length === 1 && !tenant)
      dispatch({ type: SET_TENANT, payload: tids[0] });

    if (authState?.isAuthenticated && tenant) {
      if (!tids.find((e) => e == tenant))
        dispatch({ type: SET_TENANT, payload: null });


      if (endPoints === null) {
        DiscoveryClient.getBaseURL(Constants.tokenKey, {
          TenantCode: tenant,
        })
          .then((discoveryEndpoints) => {
            if (discoveryEndpoints)
              dispatch({
                type: SET_BASE_URLS,
                payload: discoveryEndpoints,
              });
          })
          .catch(() => {
            errorNotification([""], "Discovery Service Error");
          });
      } else {
        if (menuItems.length === 0) {
          if (
            connection != null &&
            connection.state === HubConnectionState.Connected
          ) {
            connection.stop();
          }
          if (authState?.isAuthenticated) {
            oktaAuth
              .getUser()
              .then((user) => {dispatch(setUserLoaded(true, user))
                sessionStorage.setItem("loggedUserId", `${user?.preferred_username}`);
                sessionStorage.setItem("systemCode", tenant);
              });
            const connect = new HubConnectionBuilder()
              .withUrl(
                getApiUrl(
                  config.api[
                    OperationalServiceTypes.NotificationService
                  ].pushNotification.slice(
                    config.api[
                      OperationalServiceTypes.NotificationService
                    ].pushNotification.indexOf("api")
                  ),
                  OperationalServiceTypes.NotificationService
                ),
                {
                  accessTokenFactory: () =>
                    authState?.accessToken?.accessToken ?? "",
                }
              )
              .withAutomaticReconnect()
              .build();
            setConnection(connect);
          }
          getUserNavigationMenu()
            .then(async (response) => {
              dispatch({
                type: LOGIN_SUCCESS,
                payload: {
                  access_token: oktaAuth.getAccessToken(),
                  menuItems: response.data,
                },
              });
            })
            .catch((e) => {
              if (e.statusCode && e.message) {
                setApiError(e);
              } else {
                setApiError({message: 'ERR_NETWORK'});
              }
            });

          fetchUserData()
        }
      }
    }
  }, [authState?.isAuthenticated, tenant, endPoints]);

  const getApiErrorMessage = (error: any, user: any)=>{

    if (error.message === "ERR_NETWORK") {
      return "A network error occurred. Please check your connection.";
    } else if (error.statusCode === 403) {
      return `We couldn't find an account for the username ${user?.preferred_username}.`;
    } else if (error.message !== '') {
      return error.message;
    } else {
      return "An unexpected error occurred.";
    }
  }

  return (
    <SecureRoute
      path={route.path}
      render={(props) => {
        if (!tenant && authState?.isAuthenticated)
          return (
            <CommonLayout>
              <TenantSelection />
            </CommonLayout>
          );

        if (endPoints === null) return <Skeleton />;
        console.warn('location Router with sub route', route)
        return menuItems.length > 0 || !authState?.isAuthenticated ? (
          <SecureRoute
            path={route.path}
            render={(props) => (
              <Suspense fallback={<Skeleton />}>
                <route.component
                  {...props}
                  title={route.title}
                  routes={route.routes}
                />
              </Suspense>
            )}
          />
        ) : apiError.message === "" ? (
          <Skeleton />
        ) : (
          <CommonLayout>
            <AppError
              message={getApiErrorMessage(apiError, user)}
              showContactAdmin={ apiError.message !== "ERR_NETWORK" || apiError.statusCode == 403 }
              showLogoutButton={false}
            />
          </CommonLayout>
        );
      }}
    />
  );
};

export default RoutesWithSubRoutes;
