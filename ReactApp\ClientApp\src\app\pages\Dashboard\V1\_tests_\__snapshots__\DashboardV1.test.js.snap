// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Dashboard V1 Test Suite should create and match to snapshot 1`] = `
Array [
  <div
    className="ant-row"
    style={
      Object {
        "marginBottom": 12,
        "marginLeft": -12,
        "marginRight": -12,
        "marginTop": -12,
      }
    }
  >
    <div
      className="ant-col ant-col-24"
      style={
        Object {
          "paddingBottom": 12,
          "paddingLeft": 12,
          "paddingRight": 12,
          "paddingTop": 12,
        }
      }
    >
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -6,
            "marginRight": -6,
          }
        }
      >
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    License ID
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    100586
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Expiration Date
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    2020/12/31
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Remaining Days
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    45
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper yjLblStatus ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Status
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    ACTIVE
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Allocated Space
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    50GB
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Internal Users
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    200
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Support Level
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    PREMIUM
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-3"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-card yjCardWrapper ant-card-bordered"
          >
            <div
              className="ant-card-body"
              style={Object {}}
            >
              <div
                className="ant-card-meta"
              >
                <div
                  className="ant-card-meta-detail"
                >
                  <div
                    className="ant-card-meta-title"
                  >
                    Integration
                  </div>
                  <div
                    className="ant-card-meta-description"
                  >
                    SALES FORCE
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    className="ant-row"
    style={
      Object {
        "marginBottom": 6,
        "marginLeft": -6,
        "marginRight": -6,
        "marginTop": -6,
      }
    }
  >
    <div
      className="ant-col ant-col-8"
      style={
        Object {
          "paddingBottom": 6,
          "paddingLeft": 6,
          "paddingRight": 6,
          "paddingTop": 6,
        }
      }
    >
      <div
        className="ant-collapse ant-collapse-icon-position-right yjPieChartWrapper"
        role={null}
      >
        <div
          className="ant-collapse-item ant-collapse-item-active"
        >
          <div
            aria-expanded="true"
            className="ant-collapse-header"
            onClick={[Function]}
            onKeyPress={[Function]}
            role="button"
            tabIndex={0}
          >
            <span
              aria-label="right"
              className="anticon anticon-right ant-collapse-arrow"
              role="img"
            >
              <svg
                aria-hidden="true"
                className=""
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style={
                  Object {
                    "msTransform": "rotate(90deg)",
                    "transform": "rotate(90deg)",
                  }
                }
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
            Licensed Modules
          </div>
          <div
            className="ant-collapse-content ant-collapse-content-active"
            role={null}
          >
            <div
              className="ant-collapse-content-box"
            >
              <div
                className="ant-row"
                style={
                  Object {
                    "marginBottom": 6,
                    "marginLeft": -6,
                    "marginRight": -6,
                    "marginTop": -6,
                  }
                }
              >
                <div
                  className="ant-col ant-col-12"
                  style={
                    Object {
                      "paddingBottom": 6,
                      "paddingLeft": 6,
                      "paddingRight": 6,
                      "paddingTop": 6,
                    }
                  }
                >
                  <div
                    className="ant-card yjLicenedModuleCard"
                  >
                    <div
                      className="ant-card-head"
                      style={Object {}}
                    >
                      <div
                        className="ant-card-head-wrapper"
                      >
                        <div
                          className="ant-card-head-title"
                        >
                          User Management
                        </div>
                      </div>
                    </div>
                    <div
                      className="ant-card-body"
                      style={Object {}}
                    >
                      <p>
                        Users
                      </p>
                      <p>
                        User Groups
                      </p>
                      <p>
                        Permission
                      </p>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-12"
                  style={
                    Object {
                      "paddingBottom": 6,
                      "paddingLeft": 6,
                      "paddingRight": 6,
                      "paddingTop": 6,
                    }
                  }
                >
                  <div
                    className="ant-card yjLicenedModuleCard"
                  >
                    <div
                      className="ant-card-head"
                      style={Object {}}
                    >
                      <div
                        className="ant-card-head-wrapper"
                      >
                        <div
                          className="ant-card-head-title"
                        >
                          Master Data
                        </div>
                      </div>
                    </div>
                    <div
                      className="ant-card-body"
                      style={Object {}}
                    />
                  </div>
                </div>
                <div
                  className="ant-col ant-col-12"
                  style={
                    Object {
                      "paddingBottom": 6,
                      "paddingLeft": 6,
                      "paddingRight": 6,
                      "paddingTop": 6,
                    }
                  }
                >
                  <div
                    className="ant-card yjLicenedModuleCard"
                  >
                    <div
                      className="ant-card-head"
                      style={Object {}}
                    >
                      <div
                        className="ant-card-head-wrapper"
                      >
                        <div
                          className="ant-card-head-title"
                        >
                          File Area
                        </div>
                      </div>
                    </div>
                    <div
                      className="ant-card-body"
                      style={Object {}}
                    >
                      <p>
                        Assign
                      </p>
                      <p>
                        Status
                      </p>
                      <p>
                        Move
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-col ant-col-8"
      style={
        Object {
          "paddingBottom": 6,
          "paddingLeft": 6,
          "paddingRight": 6,
          "paddingTop": 6,
        }
      }
    >
      <div
        className="ant-collapse ant-collapse-icon-position-right yjPieChartWrapper"
        role={null}
      >
        <div
          className="ant-collapse-item ant-collapse-item-active"
        >
          <div
            aria-expanded="true"
            className="ant-collapse-header"
            onClick={[Function]}
            onKeyPress={[Function]}
            role="button"
            tabIndex={0}
          >
            <span
              aria-label="right"
              className="anticon anticon-right ant-collapse-arrow"
              role="img"
            >
              <svg
                aria-hidden="true"
                className=""
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style={
                  Object {
                    "msTransform": "rotate(90deg)",
                    "transform": "rotate(90deg)",
                  }
                }
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
            Resource Utilization
          </div>
          <div
            className="ant-collapse-content ant-collapse-content-active"
            role={null}
          >
            <div
              className="ant-collapse-content-box"
            />
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-col ant-col-8"
      style={
        Object {
          "paddingBottom": 6,
          "paddingLeft": 6,
          "paddingRight": 6,
          "paddingTop": 6,
        }
      }
    >
      <div
        className="ant-collapse ant-collapse-icon-position-right yjPieChartWrapper"
        role={null}
      >
        <div
          className="ant-collapse-item ant-collapse-item-active"
        >
          <div
            aria-expanded="true"
            className="ant-collapse-header"
            onClick={[Function]}
            onKeyPress={[Function]}
            role="button"
            tabIndex={0}
          >
            <span
              aria-label="right"
              className="anticon anticon-right ant-collapse-arrow"
              role="img"
            >
              <svg
                aria-hidden="true"
                className=""
                data-icon="right"
                fill="currentColor"
                focusable="false"
                height="1em"
                style={
                  Object {
                    "msTransform": "rotate(90deg)",
                    "transform": "rotate(90deg)",
                  }
                }
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                />
              </svg>
            </span>
            Allocated Space
          </div>
          <div
            className="ant-collapse-content ant-collapse-content-active"
            role={null}
          >
            <div
              className="ant-collapse-content-box"
            >
              <div
                className="bizcharts"
                style={
                  Object {
                    "height": 320,
                    "position": "relative",
                    "width": "100%",
                  }
                }
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
  <div
    className="ant-row"
    style={
      Object {
        "marginBottom": 6,
        "marginLeft": -6,
        "marginRight": -6,
        "marginTop": -6,
      }
    }
  >
    <div
      className="ant-col ant-col-12"
      style={
        Object {
          "paddingBottom": 6,
          "paddingLeft": 6,
          "paddingRight": 6,
          "paddingTop": 6,
        }
      }
    >
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -6,
            "marginRight": -6,
          }
        }
      >
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-col ant-col-12"
      style={
        Object {
          "paddingBottom": 6,
          "paddingLeft": 6,
          "paddingRight": 6,
          "paddingTop": 6,
        }
      }
    >
      <div
        className="ant-row"
        style={
          Object {
            "marginLeft": -6,
            "marginRight": -6,
          }
        }
      >
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-col ant-col-24"
          style={
            Object {
              "paddingLeft": 6,
              "paddingRight": 6,
            }
          }
        >
          <div
            className="ant-collapse ant-collapse-icon-position-right"
            role={null}
          >
            <div
              className="ant-collapse-item"
            >
              <div
                aria-expanded="false"
                className="ant-collapse-header"
                onClick={[Function]}
                onKeyPress={[Function]}
                role="button"
                tabIndex={0}
              >
                <span
                  aria-label="right"
                  className="anticon anticon-right ant-collapse-arrow"
                  role="img"
                >
                  <svg
                    aria-hidden="true"
                    className=""
                    data-icon="right"
                    fill="currentColor"
                    focusable="false"
                    height="1em"
                    viewBox="64 64 896 896"
                    width="1em"
                  >
                    <path
                      d="M765.7 486.8L314.9 134.7A7.97 7.97 0 00302 141v77.3c0 4.9 2.3 9.6 6.1 12.6l360 281.1-360 281.1c-3.9 3-6.1 7.7-6.1 12.6V883c0 6.7 7.7 10.4 12.9 6.3l450.8-352.1a31.96 31.96 0 000-50.4z"
                    />
                  </svg>
                </span>
                My Recent Documents
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>,
]
`;
