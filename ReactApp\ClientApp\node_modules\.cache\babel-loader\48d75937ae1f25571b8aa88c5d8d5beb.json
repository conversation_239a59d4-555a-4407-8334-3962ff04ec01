{"ast": null, "code": "import \"antd/es/list/style\";\nimport _List from \"antd/es/list\";\nimport \"antd/es/tag/style\";\nimport _Tag from \"antd/es/tag\";\nimport \"antd/es/tooltip/style\";\nimport _Tooltip from \"antd/es/tooltip\";\nimport \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/form/style\";\nimport _Form from \"antd/es/form\";\nimport \"antd/es/modal/style\";\nimport _Modal from \"antd/es/modal\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\pages\\\\FileAreas\\\\FileArea\\\\index.tsx\";\nimport React, { Fragment, useCallback, useContext, useEffect, useReducer, useState } from 'react';\nimport { EditOutlined, ExclamationCircleOutlined, LinkOutlined, UpOutlined, RightOutlined } from '@ant-design/icons';\nimport { useParams, withRouter } from 'react-router-dom';\nimport moment from 'moment';\nimport { useDispatch, useSelector } from 'react-redux';\nimport { PageContent } from '@app/layouts/MasterLayout';\nimport { FileAreaActionPanel } from '@app/features/FileArea/FileAreaActionPanel';\nimport GenericDataTable from '@app/components/GenericDataTable';\nimport config from '../../../utils/config';\nimport PageTitle from '@app/components/PageTitle';\nimport Uploader from '@app/components/Uploader';\nimport { FolderTree } from '@app/components/FolderTree';\nimport styles from './index.module.less';\nimport { canCheckinByFileId, pinUnPinFiles, undoCheckoutFile, updateFileDetailsByFileId, getBinderFileAreaNodes } from '@app/api/fileAreaService';\nimport { FileAreaButtonPanel } from '@app/features/FileArea/HeaderButtonPanel/fileAreaButtonPanel';\nimport ManageFiles from '@app/features/FileArea/ManageFiles';\nimport Modal from '@app/components/Modal';\nimport { updateColumnQueryParameters, updateSearchQueryParameters } from '@app/redux/actions/gridsActions';\nimport ContextMenu from '@app/components/ContextMenu';\nimport { //setHasCommonData,\nupdateContextMenuAssignOption, updateContextMenuCheckoutOption, updateContextMenuDeleteOption, updateContextMenuDownloadOption, updateContextMenuPropetiesoption, updateContextMenuPublishFiles, updateContextMenuReCategorizeOption, updateContextMenuMoveFilesOption, updateContextMenuReNameFilesOption, updateContextMenuCopyFilesOption, updateContextMenuStatusOption, updateContextMenuUnpublishFiles, updateLoadGridOption, updateContextMenuLinkFilesOption, updateContextMenuUnlinkFilesOption, updateContextMenuToBeDeleted, updateContextMenuCopyLinkFiles, setFolderTree } from '@app/redux/actions/fileAreaActions';\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\nimport { setDynamicBreadcrums } from '@app/redux/actions/configurationActions';\nimport { copyToClipboard } from '../../../components/GenericDataTable/util';\nimport logger from '@app/utils/logger';\nimport { FORBIDDEN_ERROR_CODE } from '@app/utils';\nimport { getExistingTags } from '@app/api/tagManagementService';\nimport { DataTableContext } from '@app/components/GenericDataTable/DataTableContext';\nimport { updateFilterDropDownValues } from '@app/components/GenericDataTable/DataTableContext/actions';\nimport reducer from '@app/components/GenericDataTable/DataTableContext/reducer';\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\nimport hasPermission from '@app/utils/permission';\nimport { CheckCircleOutlined, HistoryOutlined } from '@ant-design/icons/lib';\nimport { CheckinModel } from '@app/features/FileArea/CheckinModel/CheckinModel';\nimport useQuery from '@app/hooks/useQuery';\nimport { getTemplates } from \"@app/api/templateService\";\nimport { setActiveTemplates } from \"@app/redux/actions/templateActions\";\nimport { getAutocompleteOptions, getColumns, getRecords } from \"@app/api/genericDataTable\";\nimport debounce from \"lodash/debounce\";\nimport DownloadModal, { downloadTypes } from \"@app/components/DownloadModal\";\nimport ModalCustom from '../../../components/Modal';\nimport { FormattedDateTime } from \"@app/components/FormattedDateTime\";\nimport { useFileArea } from '@app/hooks/useFileArea';\nimport StatusTag from \"@app/features/FileArea/StatusTag\";\nimport ShowAllSwitch from '@app/components/Switch/ShowAll';\nconst SORTER = {\n  value: 'modified',\n  order: 'descend'\n};\nconst CHECKOUT_STATUS = 'Checked-Out';\nconst statusPermissionMap = {\n  \"ACTIVE\": {\n    fileUpload: true,\n    fileEdit: true,\n    fileDownload: true,\n    fileOptionsExceptDownload: true,\n    manageTags: true,\n    manageCheckin: true\n  },\n  \"INACTIVE\": {\n    fileUpload: false,\n    fileEdit: false,\n    fileDownload: true,\n    fileOptionsExceptDownload: false,\n    manageTags: false,\n    manageCheckin: false\n  },\n  \"ACTIVE - LH\": {\n    fileUpload: true,\n    fileEdit: false,\n    fileDownload: true,\n    fileOptionsExceptDownload: false,\n    manageTags: false,\n    manageCheckin: false\n  },\n  \"INACTIVE - LH\": {\n    fileUpload: false,\n    fileEdit: false,\n    fileDownload: true,\n    fileOptionsExceptDownload: false,\n    manageTags: false,\n    manageCheckin: false\n  }\n};\n\nconst isInvalidStatus = status => {\n  return status.name === CHECKOUT_STATUS;\n};\n\nconst isCheckout = status => {\n  return status.name === CHECKOUT_STATUS;\n};\n\nconst constructManageFileRequest = (selectedFile, data, selectedFolder, title, binderId) => {\n  const request = { ...data,\n    tags: [...data.tags, ...data.newTags],\n    binderNodeId: selectedFolder < 0 ? data.folder : selectedFolder,\n    title: title !== '' ? title : selectedFile.title,\n    binderId\n  };\n  delete request.folder;\n  delete request.newTags;\n  return request;\n};\n\nconst {\n  confirm\n} = _Modal;\nexport const formatGridColumnValue = value => {\n  return /*#__PURE__*/React.createElement(\"p\", {\n    className: styles.yjGridTextFormat,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 132,\n      columnNumber: 10\n    }\n  }, value);\n};\n\nconst hasFileAreaPermission = (fileArea, permissionType) => {\n  return fileArea && statusPermissionMap[fileArea.status.toUpperCase()][permissionType];\n};\n\nconst Page = props => {\n  var _urlParams$get;\n\n  const reactDispatch = useDispatch();\n  const [toggle, SetToggle] = useState(false);\n  const {\n    siteId,\n    siteName,\n    channelId,\n    binderId,\n    binderName\n  } = useParams();\n  const [folderId, setFolderId] = useState(0);\n  const [selectedRecords, setSelectedRecords] = useState(0);\n  const [selectedIds, setSelectedIds] = useState(['']);\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\n  const [selectedFiles, setSelectedFiles] = useState([]);\n  const [showViewModalState, setShowViewModalState] = useState(false);\n  const [editFormChange, setEditFormChange] = useState(false);\n  const [editFormAssigneeChange, setEditFormAssigneeChange] = useState(false);\n  const [hasDownloaded, setHasDownloaded] = useState(false);\n  const [undoCheckout, setUndoCheckout] = useState(true);\n  const [actionsAllowed, setActionsAllowed] = useState(true);\n  const checkinInStatus = 5;\n  const [gridUpdated, setGridUpdated] = useState(false);\n\n  const [manageFileForm] = _Form.useForm();\n\n  const [manageFileTitle, setManageFileTitle] = useState('');\n  const [selectedFolderInUpload, setSelectedFolderInUpload] = useState(-1);\n  const {\n    state\n  } = useContext(DataTableContext);\n  const [_, dispatch] = useReducer(reducer, state);\n  const urlParams = useQuery();\n  const [singleCheckinFile, setSingleCheckinFile] = useState(''); //context menu state\n\n  const [positon, setPositon] = useState({\n    x: 0,\n    y: 0\n  });\n  const [visibleContextMenu, setVisibleContextMenu] = useState(false); //const [showSelectTemplateModal, setShowSelectTemplateModal] = useState(false);\n\n  const fileArea = useFileArea(siteId);\n  const [dataTableModal, setDataTableModal] = useState({\n    visible: false,\n    title: '',\n    modalData: []\n  });\n  const {\n    folderTree,\n    hasCommonData,\n    fileAreaSettings,\n    filesUploaded\n  } = useSelector(state => state.fileArea);\n  const {\n    userPermission,\n    userPreferences\n  } = useSelector(state => state.userManagement);\n  const {\n    activeTemplates\n  } = useSelector(state => state.template);\n  const [selectedTemplateId, setSelectedTemplateId] = useState('');\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\n  const [selectedFile, setSelectedFile] = useState(undefined);\n  const [checkedOuFilesDownload, setCheckedOuFilesDownload] = useState(undefined);\n  const [downloadType, setDownloadType] = useState(downloadTypes.individual);\n  const [loading, setLoading] = useState(false);\n  const linkedFiles = selectedFiles.filter(e => e.linked);\n  const originalFileAreaWithLinked = linkedFiles && selectedFiles.some(e => e.fK_FileAreaId === (fileArea === null || fileArea === void 0 ? void 0 : fileArea.fileAreaId));\n  const tableKey = 'fileArea';\n  const initialFolderData = [{\n    id: 1,\n    name: 'Primary folder',\n    retention: false,\n    presist: false,\n    subFolders: [{\n      id: 2,\n      name: 'Secondary folder (1)',\n      subFolders: [],\n      retention: false,\n      presist: false\n    }, {\n      id: 3,\n      name: 'Secondary folder (2)',\n      subFolders: [],\n      retention: false,\n      presist: false\n    }]\n  }];\n\n  const showDatatableModal = (title, data) => {\n    setDataTableModal({\n      visible: true,\n      title: title,\n      modalData: data\n    });\n  };\n\n  const hideDatatableModal = () => {\n    setDataTableModal({\n      visible: false,\n      title: dataTableModal.title,\n      modalData: dataTableModal.modalData\n    });\n  };\n\n  const fetchBinderNodes = async () => {\n    try {\n      const response = await getBinderFileAreaNodes(binderId);\n\n      const transformFolders = folders => {\n        const primaryFolders = folders.filter(folder => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);\n        return primaryFolders.map(folder => ({\n          id: folder.id,\n          name: folder.name,\n          subFolders: folder.childNodes.map(child => ({\n            id: child.id,\n            name: child.name,\n            subFolders: [],\n            retention: child.retention || 0\n          }))\n        }));\n      };\n\n      const transformedFolders = transformFolders(response.data.folders);\n      reactDispatch(setFolderTree({\n        siteId,\n        siteName: binderName,\n        siteStatusId: 1,\n        folders: transformedFolders\n      }));\n    } catch (error) {\n      logger.error('Error fetching binder nodes:', 'fetchBinderNodes', error);\n    }\n  };\n\n  const loadGrid = useCallback(() => {\n    getExistingTags(binderId).then(res => {\n      dispatch(updateFilterDropDownValues('tags', res.data));\n    });\n    reactDispatch(updateColumnQueryParameters(siteId));\n    reactDispatch(updateSearchQueryParameters([{\n      key: 'siteid',\n      value: siteId\n    }, {\n      key: 'binderNodeId',\n      value: folderId\n    }]));\n    reactDispatch(updateLoadGridOption(true));\n    setTimeout(() => {\n      reactDispatch(updateLoadGridOption(false));\n    });\n  }, [siteId, reactDispatch, folderId, binderId]);\n  useEffect(() => {\n    fetchBinderNodes();\n  }, []);\n  useEffect(() => {\n    getTemplates({\n      'isHidden': false,\n      'templateStatus': 1\n    }).then(res => {\n      var _res$data;\n\n      logger.info('File Area Module', 'Get Templates Response', res.data.records);\n\n      if (res.status == 200 && ((_res$data = res.data) === null || _res$data === void 0 ? void 0 : _res$data.records)) {\n        var _res$data2;\n\n        reactDispatch(setActiveTemplates((_res$data2 = res.data) === null || _res$data2 === void 0 ? void 0 : _res$data2.records));\n        logger.debug(\"File Area Module\", \"Set Active Templates\", activeTemplates);\n      }\n    }).catch(e => {\n      logger.error(\"File Area Module\", \"Get Templates Error\", e);\n    });\n    const dynamicBreadcrumbs = [{\n      title: \"Client File Areas\",\n      path: \"/client-file-area\"\n    }, {\n      title: `${decodeURIComponent(siteName)} - File Areas`,\n      path: `/client-file-area/${siteId}/${siteName}/${channelId}`\n    }, {\n      title: `${decodeURIComponent(binderName)}`,\n      path: `/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}`\n    }];\n    reactDispatch(setDynamicBreadcrums(dynamicBreadcrumbs));\n    return () => {\n      //reactDispatch(setHasCommonData(false));\n      reactDispatch(setDynamicBreadcrums([]));\n    };\n  }, []);\n  useEffect(() => {\n    reactDispatch(updateColumnQueryParameters(siteId));\n    reactDispatch(updateSearchQueryParameters([{\n      key: 'siteid',\n      value: siteId\n    }, {\n      key: 'binderNodeId',\n      value: 0\n    }]));\n  }, [reactDispatch, siteId]);\n  useEffect(() => {\n    if (hasDownloaded) {\n      setGridUpdated(true);\n      loadGrid();\n      setHasDownloaded(false);\n      rowSelection.selectedRowKeys = [];\n      rowSelection.onChange([], []);\n      setGridUpdated(false);\n    }\n  }, [hasDownloaded, loadGrid]);\n  useEffect(() => {\n    if (filesUploaded) {\n      setGridUpdated(true);\n      loadGrid();\n      setGridUpdated(false);\n    }\n  }, [filesUploaded, reactDispatch, loadGrid]);\n  const rowSelection = {\n    onChange: (selectedRowKeyValues, selectedRows) => {\n      var _selectedRows$0$statu;\n\n      if ((selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.length) > 0 && (selectedRows === null || selectedRows === void 0 ? void 0 : selectedRows.find(row => {\n        var _row$status;\n\n        return (userPermission === null || userPermission === void 0 ? void 0 : userPermission.privDMSCanCheckInCheckOutInternalFiles) && (row === null || row === void 0 ? void 0 : (_row$status = row.status) === null || _row$status === void 0 ? void 0 : _row$status.value) === checkinInStatus;\n      }))) {\n        setActionsAllowed(false);\n      } else {\n        setActionsAllowed(true);\n      }\n\n      if (userPermission.privDMSCanCheckInCheckOutInternalFiles && selectedRows.length === 1 && ((_selectedRows$0$statu = selectedRows[0].status) === null || _selectedRows$0$statu === void 0 ? void 0 : _selectedRows$0$statu.value) === checkinInStatus) {\n        setUndoCheckout(true);\n      } else {\n        setUndoCheckout(false);\n        reactDispatch(updateContextMenuPropetiesoption(false));\n      }\n\n      setSelectedRowKeys(selectedRowKeyValues);\n      setSelectedRecords(selectedRows.length);\n      setSelectedFiles(selectedRows);\n\n      if (selectedRows.length > 1) {\n        setSelectedIds(selectedRowKeyValues);\n      } else {\n        setSelectedIds(['']);\n      }\n    },\n    getCheckboxProps: record => {\n      var _record$status;\n\n      return {\n        disabled: (record === null || record === void 0 ? void 0 : (_record$status = record.status) === null || _record$status === void 0 ? void 0 : _record$status.value) === checkinInStatus,\n        name: record.name\n      };\n    },\n    selectedRowKeys,\n    fixed: true\n  };\n\n  const selectedRecord = recordId => {\n    return selectedIds.includes(recordId);\n  };\n\n  const handleCheckin = fileId => {\n    canCheckinByFileId(fileId).then(() => {\n      setSingleCheckinFile(fileId);\n    }).catch(error => {\n      logger.error(\"File Area Module\", \"handleCheckin\", error);\n\n      if (error.response.status === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], 'You do not have the permission to perform this action.');\n      } else {\n        errorNotification([''], 'Something went wrong!');\n      }\n    });\n  };\n\n  const handleUndoCheckout = fileId => {\n    setHasDownloaded(false);\n    confirm({\n      title: 'Do you wish to undo-checkout for this file?',\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 395,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        undoCheckoutFile(selectedFiles[0].id).then(response => {\n          if (response) {\n            setHasDownloaded(true);\n            successNotification([''], 'Undo Check-out Successful');\n          }\n        }).catch(error => {\n          if (error.response.status === FORBIDDEN_ERROR_CODE) {\n            errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\n          } else {\n            errorNotification([''], 'File checkout could not be undone.');\n          }\n        });\n      }\n\n    });\n  };\n\n  const handleShowViewModalCancel = () => {\n    if (editFormChange) {\n      confirm({\n        title: 'Are you sure you want to discard the changes?',\n        icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 421,\n            columnNumber: 15\n          }\n        }),\n        okText: 'Yes',\n        cancelText: 'No',\n\n        onOk() {\n          setEditFormChange(false);\n          setShowViewModalState(false);\n        }\n\n      });\n    } else {\n      setEditFormChange(false);\n      setShowViewModalState(false);\n    }\n  }; // if (!hasCommonData) {\n  //   return <Spin style={{ paddingTop: 20 }} />;\n  // }\n\n\n  const onSelecteFolder = folderIdValue => {\n    reactDispatch(updateSearchQueryParameters([{\n      key: 'siteid',\n      value: siteId\n    }, {\n      key: 'binderNodeId',\n      value: folderIdValue\n    }]));\n    setFolderId(folderIdValue);\n    rowSelection.onChange([], []);\n  };\n\n  const onContextMenuClicked = (event, record) => {\n    if (rowSelection.selectedRowKeys.length <= 0) {\n      var _record$status2;\n\n      if (userPermission.privDMSCanCheckInCheckOutInternalFiles && (record === null || record === void 0 ? void 0 : (_record$status2 = record.status) === null || _record$status2 === void 0 ? void 0 : _record$status2.value) === checkinInStatus) {\n        setUndoCheckout(true);\n        setActionsAllowed(false);\n      } else {\n        setUndoCheckout(false);\n        setActionsAllowed(true);\n      }\n\n      setSelectedFiles([record]);\n    }\n\n    event.preventDefault();\n    setVisibleContextMenu(false);\n    document.addEventListener(`click`, function onClickOutside() {\n      setVisibleContextMenu(false);\n      document.removeEventListener(`click`, onClickOutside);\n    });\n    setVisibleContextMenu(true);\n    setPositon({\n      x: event.clientX,\n      y: event.clientY\n    });\n  };\n\n  const onRowClick = (event, record) => {\n    if (selectedFiles.length === 1) {\n      setSelectedFiles([record]);\n    }\n  };\n\n  const handleRowClick = (record, rowIndex) => ({\n    onContextMenu: event => onContextMenuClicked(event, record),\n    onClick: event => onRowClick(event, record)\n  });\n\n  const handleUpdateFileProperties = () => {\n    setLoading(true);\n    manageFileForm.validateFields().then(data => {\n      data.expirationDate = moment(data.expirationDate).endOf('day').subtract(1, 'minutes');\n      const requestData = constructManageFileRequest(selectedFiles[0], data, selectedFolderInUpload, manageFileTitle, binderId);\n      return updateFileDetailsByFileId(selectedFiles[0].id, requestData);\n    }).then(response => {\n      //unknown error\n      if (!response.data) {\n        errorNotification([''], 'File Properties Update Failed');\n        setLoading(false);\n\n        if (editFormAssigneeChange) {\n          errorNotification([''], 'Assignment Failed');\n        }\n\n        return response.data;\n      } //success scenario\n\n\n      loadGrid();\n      successNotification([''], 'File Properties Updated Successfully');\n\n      if (editFormAssigneeChange) {\n        successNotification([''], 'Assignment Successful');\n      }\n\n      setShowViewModalState(false);\n      setEditFormChange(false);\n      setLoading(false);\n    }).catch(error => {\n      switch (error.statusCode) {\n        //error code cases\n        case FORBIDDEN_ERROR_CODE:\n          errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\n          break;\n\n        default:\n          errorNotification([''], 'File Properties Update Failed');\n\n          if (editFormAssigneeChange) {\n            errorNotification([''], 'Assignment Failed');\n          }\n\n          break;\n      }\n\n      logger.error('Internal Files', 'Manage Files Edit', error);\n      setLoading(false);\n    });\n  };\n\n  function gridColumnValueFormatter(value) {\n    return value ? formatGridColumnValue(value) : formatGridColumnValue('N/A');\n  }\n\n  const downloadFile = (value, file) => {\n    // setDownloadType(checkoutZip ? downloadTypes.checkoutZip : downloadTypes.checkoutIndividual);\n    setSelectedFile(file);\n\n    if (hasFileAreaPermission(fileArea, 'fileDownload')) {\n      setShowDownloadModal(true);\n    }\n\n    setCheckedOuFilesDownload(value);\n  };\n\n  const colors = [\"magenta\", \"green\", \"red\", \"cyan\", \"volcano\", \"blue\", \"orange\", \"geekblue\", \"gold\", \"purple\", \"lime\"];\n  const [expandedTags, setExpandedTags] = useState({}); // Inside the Page component\n\n  const containerRefs = React.useRef({});\n  const [showExpandIcons, setShowExpandIcons] = React.useState({});\n  const [showAllFiles, setShowAllFiles] = useState(false); // Function to check overflow for all tags\n\n  const checkOverflow = recordId => {\n    const container = containerRefs.current[recordId];\n\n    if (container && container.scrollHeight && container.clientHeight && container.scrollHeight > container.clientHeight) {\n      console.log('Container:', container);\n      console.log('scrollHeight:', container.scrollHeight, 'clientHeight:', container.clientHeight); // setTimeout(() => {  // Use setTimeout to ensure the DOM is updated before checking overflow   \n      //   setShowExpandIcons((prev) => ({\n      //     ...prev,\n      //     [recordId]: container.scrollHeight > container.clientHeight,\n      //   }));\n      // }, 100)\n    } else {\n      console.warn(`Container for recordId ${recordId} is not available.`);\n    }\n  }; // // Use useEffect in the parent component to handle overflow checks\n  // useEffect(() => {\n  //   console.log('containerRefs:', containerRefs.current);\n  //   Object.keys(containerRefs.current).forEach((recordId) => {\n  //     checkOverflow(recordId);\n  //   });\n  // }, [expandedTags, containerRefs]);\n\n\n  const renderGridColumns = () => {\n    return {\n      title: (value, data) => {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: `${styles.yjFileAreaGridTextWrap} yJFileAreaRow`,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 597,\n            columnNumber: 11\n          }\n        }, /*#__PURE__*/React.createElement(_Tooltip, {\n          className: \"yJFileAreaRow\",\n          placement: \"leftTop\",\n          title: value,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 598,\n            columnNumber: 13\n          }\n        }, /*#__PURE__*/React.createElement(_Button, {\n          type: \"link\",\n          onClick: () => downloadFile(value, data),\n          style: {\n            paddingInline: 0,\n            border: 0,\n            cursor: !hasFileAreaPermission(fileArea, 'fileDownload') ? 'not-allowed' : 'pointer'\n          },\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 599,\n            columnNumber: 13\n          }\n        }, value)));\n      },\n      id: value => {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: \"yjFileAreaGridTextWrap yJFileAreaRow\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 607,\n            columnNumber: 11\n          }\n        }, /*#__PURE__*/React.createElement(_Tooltip, {\n          color: \"transparent\",\n          placement: \"right\",\n          title: /*#__PURE__*/React.createElement(LinkOutlined, {\n            style: {\n              color: '#0E678E',\n              fontSize: '14px'\n            },\n            onClick: () => copyToClipboard(value),\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 608,\n              columnNumber: 67\n            }\n          }),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 608,\n            columnNumber: 13\n          }\n        }, /*#__PURE__*/React.createElement(\"span\", {\n          className: \"yJFileAreaRow\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 609,\n            columnNumber: 15\n          }\n        }, value)));\n      },\n      created: value => {\n        return /*#__PURE__*/React.createElement(FormattedDateTime, {\n          value: value,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 615,\n            columnNumber: 16\n          }\n        });\n      },\n      createdBy: value => {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: `${styles.yjFileAreaGridTextWrap} yJFileAreaRow`,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 619,\n            columnNumber: 11\n          }\n        }, /*#__PURE__*/React.createElement(_Tooltip, {\n          className: `${styles.yjFileAreaGridTextWrap} yJFileAreaRow`,\n          placement: \"leftTop\",\n          title: value === null || value === void 0 ? void 0 : value.name,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 620,\n            columnNumber: 13\n          }\n        }, value === null || value === void 0 ? void 0 : value.name));\n      },\n      assignee: value => {\n        return value ? /*#__PURE__*/React.createElement(\"div\", {\n          className: `${styles.yjFileAreaGridTextWrap} yJFileAreaRow`,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 628,\n            columnNumber: 11\n          }\n        }, /*#__PURE__*/React.createElement(_Tooltip, {\n          className: `${styles.yjFileAreaGridTextWrap} yJFileAreaRow`,\n          placement: \"leftTop\",\n          title: value === null || value === void 0 ? void 0 : value.name,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 629,\n            columnNumber: 13\n          }\n        }, value === null || value === void 0 ? void 0 : value.name)) : 'N/A';\n      },\n      status: value => {\n        return value ? formatGridColumnValue(value.name) : formatGridColumnValue('N/A');\n      },\n      year: gridColumnValueFormatter,\n      modified: value => {\n        return /*#__PURE__*/React.createElement(FormattedDateTime, {\n          value: value,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 642,\n            columnNumber: 16\n          }\n        });\n      },\n      // isFolderExist: {\n      //   render: (value: any) => {\n      //     return (value ? <Tag color=\"blue\">File Area</Tag> : '')\n      //   },\n      //   width: 550\n      // }\n      tags: {\n        width: 300,\n        render: (value, record) => {\n          var _expandedTags$record$;\n\n          const SINGLE_LINE_HEIGHT = 24; // Adjust this value based on your CSS\n\n          const LINES_TO_DHOW_EXPANDER = 2; // Maximum number of lines to show before expanding\n\n          const isExpanded = (_expandedTags$record$ = expandedTags[record.id]) !== null && _expandedTags$record$ !== void 0 ? _expandedTags$record$ : false;\n\n          const isContentOverflowing = recordId => {\n            var _container$scrollHeig, _container$clientHeig;\n\n            const container = containerRefs.current[recordId];\n            if (!container) return false;\n            const scrollHeight = (_container$scrollHeig = container.scrollHeight) !== null && _container$scrollHeig !== void 0 ? _container$scrollHeig : 0;\n            const clientHeight = (_container$clientHeig = container.clientHeight) !== null && _container$clientHeig !== void 0 ? _container$clientHeig : 0;\n            const senario1 = scrollHeight / clientHeight >= LINES_TO_DHOW_EXPANDER;\n            const senario2 = scrollHeight > SINGLE_LINE_HEIGHT;\n            return senario1 || senario2;\n          };\n\n          const tagColorMap = {};\n          value === null || value === void 0 ? void 0 : value.forEach((value, index) => {\n            tagColorMap[value.name] = colors[index % colors.length];\n          });\n          return /*#__PURE__*/React.createElement(\"div\", {\n            ref: el => {\n              if (el) {\n                containerRefs.current[record.id] = el;\n                checkOverflow(record.id);\n              }\n            },\n            className: `yJFileAreaRow ${isExpanded ? styles.multiLine : styles.singleLine}`,\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 677,\n              columnNumber: 13\n            }\n          }, value === null || value === void 0 ? void 0 : value.map(t => /*#__PURE__*/React.createElement(_Tag, {\n            color: tagColorMap[t.name],\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 686,\n              columnNumber: 39\n            }\n          }, t === null || t === void 0 ? void 0 : t.name)), isContentOverflowing(record.id) && /*#__PURE__*/React.createElement(\"span\", {\n            onClick: () => {\n              setExpandedTags(prev => ({ ...prev,\n                [record.id]: !isExpanded\n              })); // Use requestAnimationFrame to delay the check\n\n              requestAnimationFrame(() => {\n                const container = containerRefs.current[record.id];\n\n                if (container) {\n                  console.log('After toggle:');\n                  console.log('clientHeight:', container.clientHeight);\n                  console.log('scrollHeight:', container.scrollHeight);\n                }\n              });\n            },\n            style: {\n              position: \"absolute\",\n              right: 0,\n              cursor: \"pointer\",\n              marginLeft: \"8px\"\n            },\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 689,\n              columnNumber: 17\n            }\n          }, isExpanded ? /*#__PURE__*/React.createElement(UpOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 33\n            }\n          }) : /*#__PURE__*/React.createElement(RightOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 713,\n              columnNumber: 50\n            }\n          })));\n        }\n      },\n      projects: value => {\n        return /*#__PURE__*/React.createElement(\"p\", {\n          className: `${styles.yjGridTextCenter} yJFileAreaRow`,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 722,\n            columnNumber: 11\n          }\n        }, /*#__PURE__*/React.createElement(_Button, {\n          className: `${styles.yjViewButton} yJFileAreaRow`,\n          onClick: () => showDatatableModal('Projects', value),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 723,\n            columnNumber: 13\n          }\n        }, 'View'));\n      },\n      type: gridColumnValueFormatter,\n      expirationDate: (value, record) => {\n        return record.expirationStatus.name === 'Permanent' ? '' : /*#__PURE__*/React.createElement(FormattedDateTime, {\n          value: value,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 731,\n            columnNumber: 68\n          }\n        });\n      },\n      fileCondition: gridColumnValueFormatter,\n      action: (text, record) => {\n        return /*#__PURE__*/React.createElement(\"div\", {\n          className: `${styles.yjActionIconWrapper} yJFileAreaRow`,\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 736,\n            columnNumber: 11\n          }\n        }, /*#__PURE__*/React.createElement(_Tooltip, {\n          className: \"yJFileAreaRow\",\n          title: \"Check In\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 737,\n            columnNumber: 13\n          }\n        }, /*#__PURE__*/React.createElement(_Button, {\n          className: \"yJFileAreaRow\",\n          disabled: !isCheckout(record.status) || !userPermission.privDMSCanManageFileProperties || !hasPermission(folderTree, 'FILE_AREA_FILE_EDIT') || !hasFileAreaPermission(fileArea, 'manageCheckin'),\n          onClick: () => handleCheckin(record.id),\n          icon: /*#__PURE__*/React.createElement(CheckCircleOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 742,\n              columnNumber: 23\n            }\n          }),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 738,\n            columnNumber: 15\n          }\n        })), /*#__PURE__*/React.createElement(_Tooltip, {\n          className: \"yJFileAreaRow\",\n          title: \"Edit\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 745,\n            columnNumber: 13\n          }\n        }, /*#__PURE__*/React.createElement(_Button, {\n          className: \"yJFileAreaRow\",\n          disabled: selectedRecord(record.id) || isInvalidStatus(record.status) || !userPermission.privDMSCanManageFileProperties || record.linked,\n          onClick: () => {\n            setManageFileTitle(''); // rowSelection.selectedRowKeys = [];\n\n            rowSelection.onChange([], []);\n            setSelectedFiles([record]);\n            setShowViewModalState(true);\n          },\n          icon: /*#__PURE__*/React.createElement(EditOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 756,\n              columnNumber: 23\n            }\n          }),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 746,\n            columnNumber: 15\n          }\n        })), /*#__PURE__*/React.createElement(_Tooltip, {\n          className: \"yJFileAreaRow\",\n          title: \"History\",\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 759,\n            columnNumber: 13\n          }\n        }, /*#__PURE__*/React.createElement(_Button, {\n          className: \"yJFileAreaRow\",\n          disabled: selectedRecord(record.id),\n          onClick: () => {\n            rowSelection.selectedRowKeys = [];\n            rowSelection.onChange([], []);\n            props.history.push(encodeURI(`/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}/history/${record.id}`));\n          },\n          icon: /*#__PURE__*/React.createElement(HistoryOutlined, {\n            __self: this,\n            __source: {\n              fileName: _jsxFileName,\n              lineNumber: 768,\n              columnNumber: 23\n            }\n          }),\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 760,\n            columnNumber: 15\n          }\n        })));\n      }\n    };\n  };\n\n  const setQueryParameters = () => {\n    return [{\n      key: 'siteid',\n      value: siteId\n    }, {\n      key: 'binderNodeId',\n      value: folderId\n    }, {\n      key: 'binderId',\n      value: binderId\n    }];\n  };\n\n  const fileDownloaded = value => {\n    if (value) {\n      setHasDownloaded(true);\n      successNotification([''], 'File checked-out successfully.');\n    }\n  };\n\n  function onSuccess() {\n    setSingleCheckinFile('');\n    setGridUpdated(true);\n    loadGrid();\n    setGridUpdated(false);\n    rowSelection.selectedRowKeys = [];\n    rowSelection.onChange([], []);\n  }\n\n  const fetchFiles = async (state, transformFilters, queryParams) => {\n    var _state$columns$find;\n\n    if (!showAllFiles) {\n      return fetchData(state, transformFilters, queryParams);\n    }\n\n    const activeFilter = (_state$columns$find = state.columns.find(column => column.key === 'status')) === null || _state$columns$find === void 0 ? void 0 : _state$columns$find.filter_data;\n\n    if (!activeFilter) {\n      return fetchData(state, transformFilters, queryParams);\n    }\n\n    const filters = { ...transformFilters,\n      status: transformFilters.status ? transformFilters.status : activeFilter.map(f => f.value)\n    };\n    return fetchData(state, filters, queryParams);\n  };\n\n  const fetchColumns = async () => {\n    const response = await getColumns(config.api[OperationalServiceTypes.FileManagementService].files, tableKey, [{\n      key: 'binderId',\n      value: binderId\n    }]);\n    return showAllFiles ? response : response.data.map(column => {\n      if (column.key === 'status') {\n        return { ...column,\n          filterData: column.filterData.filter(item => item.name !== 'To be Deleted')\n        };\n      }\n\n      return column;\n    });\n  };\n\n  const fetchData = useCallback(async (state, transformFilters, queryParams) => {\n    return getRecords(config.api[OperationalServiceTypes.FileManagementService].files, {\n      pagination: {\n        current: state.pagination.current,\n        pageSize: state.pagination.pageSize\n      },\n      sorter: state.sorter ? {\n        key: state.sorter.columnKey,\n        order: state.sorter.order\n      } : {},\n      filters: transformFilters,\n      columns: state.columns.filter(i => i.default === false && i.selected === true).map(i => i.key)\n    }, queryParams);\n  }, [] // Add dependencies here if needed\n  ); // Debounced API call\n\n  const debouncedApiCall = debounce((props, value, callback) => {\n    logger.info(\"File Area Module\", \"debouncedApiCall\", {\n      props,\n      propsData: props.data,\n      value\n    });\n    getAutocompleteOptions(config.api[OperationalServiceTypes.FileManagementService].files, props.data.key, value, props.searchFieldParameters).then(data => {\n      callback(data.data);\n    }).catch(() => {\n      callback([]);\n    });\n  }, config.inputDebounceInterval); // Wrapper function to return a Promise\n\n  const searchPromiseWrapper = (props, value, callback) => {\n    debouncedApiCall(props, value, data => {\n      callback(data);\n    });\n  };\n\n  const handleOnDownloadModalCancel = () => {\n    dispatch(updateContextMenuDownloadOption(false));\n    setShowDownloadModal(false);\n  };\n\n  const makeAsPinUnPin = (selectedFiles, pinned) => {\n    logger.debug(\"File Area Module\", \"makeAsPinUnPin\", {\n      selectedFiles\n    });\n    const fileIds = selectedFiles.map(f => f.id);\n    pinUnPinFiles(fileIds, pinned).then(response => {\n      if (response) {\n        setHasDownloaded(true);\n        successNotification([''], `File(s) ${pinned ? 'Pinned' : 'Unpinned'} Successfully`);\n      }\n    }).catch(error => {\n      if (error.response.status === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\n      } else {\n        errorNotification([''], `File(s) ${pinned ? 'Pinned' : 'Unpinned'} Failed`);\n      }\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(Fragment, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 912,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(PageTitle, {\n    title: decodeURIComponent(binderName),\n    tags: fileArea && /*#__PURE__*/React.createElement(StatusTag, {\n      value: fileArea.status,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 915,\n        columnNumber: 27\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 913,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(FileAreaButtonPanel, Object.assign({\n    siteId: siteId,\n    siteName: decodeURIComponent(siteName),\n    channelId: channelId,\n    fileSection: 'internal',\n    manageCheckin: hasFileAreaPermission(fileArea, 'manageCheckin'),\n    binderId: binderId\n  }, props, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 917,\n      columnNumber: 9\n    }\n  }))), /*#__PURE__*/React.createElement(PageContent, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 919,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    hidden: !userPermission.privDMSCanUploadFiles || !hasPermission(folderTree, 'FILE_AREA_UPLOADER') || !hasFileAreaPermission(fileArea, 'fileUpload'),\n    className: styles.yjFileAreaUploadWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 920,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Uploader, {\n    siteId: siteId,\n    binderId: binderId,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 926,\n      columnNumber: 11\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: toggle ? `${styles.yjFileAreaFileFinderCollapsed}` : `${styles.yjFileAreaFileFinderExpanded}`,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 928,\n      columnNumber: 9\n    }\n  }, !toggle && /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjFileFinderPanel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 929,\n      columnNumber: 23\n    }\n  }, /*#__PURE__*/React.createElement(FolderTree, {\n    onSelectFolder: folderIdValue => onSelecteFolder(folderIdValue),\n    data: folderTree,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 930,\n      columnNumber: 13\n    }\n  })), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjFileAreaDetailsGrid,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 933,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(FileAreaActionPanel, {\n    fileDownloaded: fileDownloaded,\n    syncGrid: updated => {\n      if (updated) {\n        setGridUpdated(true);\n        loadGrid();\n        setGridUpdated(false);\n        rowSelection.selectedRowKeys = [];\n        rowSelection.onChange([], []);\n      }\n    },\n    originalFileAreaWithLinked: originalFileAreaWithLinked,\n    showCheckout: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && actionsAllowed && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showDelete: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showDownload: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileDownload'),\n    showStatus: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showAssign: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showPropeties: selectedFiles.length === 1 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showReCategorize: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showMoveFiles: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showLinkFiles: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.some(file => !file.linked) && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showUnlinkFiles: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.filter(e => !e.linked).length === 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showPublish: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.some(file => !file.published) && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showUnpublish: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.filter(e => !e.published).length === 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showToBeDelete: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showReName: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showCopy: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    selectedFileList: selectedFiles,\n    siteId: siteId,\n    toggleIconClicked: e => {\n      SetToggle(!toggle);\n    },\n    showCopyLink: selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    showManageTags: hasFileAreaPermission(fileArea, 'manageTags'),\n    showManageCheckin: hasFileAreaPermission(fileArea, 'manageCheckin'),\n    additionalActionPanel: /*#__PURE__*/React.createElement(ShowAllSwitch, {\n      checked: showAllFiles,\n      onChange: setShowAllFiles,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 999,\n        columnNumber: 38\n      }\n    }),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 934,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(Modal, Object.assign({}, dataTableModal, {\n    size: 'small',\n    onCancel: hideDatatableModal,\n    footer: null,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1002,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjDataTableModal,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1003,\n      columnNumber: 15\n    }\n  }, dataTableModal.modalData && /*#__PURE__*/React.createElement(_List, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1005,\n      columnNumber: 19\n    }\n  }, dataTableModal.modalData.map(i => /*#__PURE__*/React.createElement(_List.Item, {\n    key: i.value,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1007,\n      columnNumber: 23\n    }\n  }, i.name))), dataTableModal.modalData.length === 0 && `No ${dataTableModal.title} Applied.`)), /*#__PURE__*/React.createElement(Modal, {\n    style: {\n      top: 20\n    },\n    visible: showViewModalState,\n    title: 'Update File Properties',\n    onCancel: handleShowViewModalCancel,\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      key: \"back\",\n      type: \"primary\",\n      onClick: handleShowViewModalCancel,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1022,\n        columnNumber: 17\n      }\n    }, \"cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      loading: loading,\n      key: \"update\",\n      type: \"primary\",\n      onClick: handleUpdateFileProperties,\n      disabled: !editFormChange,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1025,\n        columnNumber: 17\n      }\n    }, \"Update\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1016,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1030,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(ManageFiles, {\n    dataList: selectedFiles,\n    onFilesChange: () => {},\n    siteId: siteId,\n    binderId: binderId,\n    form: manageFileForm,\n    onFormChange: (isFormChange, isAssigneeChange) => {\n      setEditFormChange(isFormChange);\n      setEditFormAssigneeChange(isAssigneeChange);\n    },\n    onFolderChange: setSelectedFolderInUpload,\n    onTitleChange: setManageFileTitle,\n    disabledForm: !hasPermission(folderTree, 'FILE_AREA_FILE_EDIT') || !hasFileAreaPermission(fileArea, 'fileEdit'),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1031,\n      columnNumber: 17\n    }\n  }))), /*#__PURE__*/React.createElement(CheckinModel, {\n    siteId: siteId,\n    binderId: binderId,\n    fileId: singleCheckinFile,\n    onClose: () => setSingleCheckinFile(''),\n    onSuccess: onSuccess,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1048,\n      columnNumber: 13\n    }\n  }), /*#__PURE__*/React.createElement(\"div\", {\n    className: \"yjFileAreaGridWrapper\",\n    onScroll: () => {\n      setVisibleContextMenu(false);\n    },\n    onMouseLeave: () => {\n      setVisibleContextMenu(false);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1056,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(ContextMenu, {\n    displayCheckoutOption: userPermission.privDMSCanCheckInCheckOutInternalFiles && !undoCheckout && actionsAllowed && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayAssignOption: userPermission.privDMSCanManageFileAssign && actionsAllowed && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayUndoCheckoutOption: userPermission.privDMSCanCheckInCheckOutInternalFiles && undoCheckout && selectedFiles.length === 1 && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayDeleteOption: userPermission.privDMSCanDeleteFiles && actionsAllowed && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayDownloadOption: userPermission.privDMSCanViewFileArea && hasPermission(folderTree, 'FILE_AREA_DOWNLOAD') && hasFileAreaPermission(fileArea, 'fileDownload'),\n    displayPropetiesOption: selectedFiles.length === 1 && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayStatusOption: userPermission.privDMSCanManageFileProperties && actionsAllowed && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayReCategorizeOption: userPermission.privDMSCanRecategorizeFiles && actionsAllowed && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayCopyOption: userPermission.privDMSCanCopyFiles && !undoCheckout && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayEmailOption: userPermission.privDMSCanViewFileArea && !undoCheckout && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayMoveOption: userPermission.privDMSCanMoveFiles && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayRenameOption: userPermission.privDMSCanRenameFiles && !undoCheckout && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    dispalyPropertiesOption: userPermission.privDMSCanViewFileArea && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    hasSelectedLinkFiles: selectedFiles.some(e => !e.linked),\n    hasSelectedPublishedFiles: selectedFiles.some(e => !e.published),\n    displayPublishOption: !undoCheckout && actionsAllowed && userPermission.privDMSCanPublishUnpublishInternalFiles && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayLinkOption: !undoCheckout && actionsAllowed && userPermission.privDMSCanLinkUnlinkFiles && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayToBeDeleted: actionsAllowed && selectedFiles.filter(e => e.status.value === 6).length == 0 && hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayCopyLinkOption: hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    displayPinUnpinOption: hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload'),\n    onDownloadClick: () => {\n      reactDispatch(updateContextMenuDownloadOption(true));\n    },\n    onStatusClick: () => reactDispatch(updateContextMenuStatusOption(true)),\n    onAssignClick: () => reactDispatch(updateContextMenuAssignOption(true)),\n    onReCategorizeClick: () => reactDispatch(updateContextMenuReCategorizeOption(true)),\n    onMoveFilesClick: () => reactDispatch(updateContextMenuMoveFilesOption(true)),\n    onReNameFilesClick: () => reactDispatch(updateContextMenuReNameFilesOption(true)),\n    onCopyFilesClick: () => reactDispatch(updateContextMenuCopyFilesOption(true)),\n    onLinkFilesClicked: () => reactDispatch(updateContextMenuLinkFilesOption(true)),\n    onUnlinkFilesClicked: () => reactDispatch(updateContextMenuUnlinkFilesOption(true)),\n    onCheckoutClick: () => {\n      reactDispatch(updateContextMenuCheckoutOption(true));\n    },\n    onUndoCheckoutClicked: () => {\n      handleUndoCheckout('');\n    },\n    onDeleteClick: () => reactDispatch(updateContextMenuDeleteOption(true)),\n    onPropetiesClicked: () => {\n      reactDispatch(updateContextMenuPropetiesoption(true));\n    },\n    onPublishClick: () => {\n      reactDispatch(updateContextMenuPublishFiles(true));\n    },\n    onUnpublishClick: () => {\n      reactDispatch(updateContextMenuUnpublishFiles(true));\n    },\n    onToBeDeletedClick: () => {\n      reactDispatch(updateContextMenuToBeDeleted(true));\n    },\n    onCopyLink: () => {\n      reactDispatch(updateContextMenuCopyLinkFiles(true));\n    },\n    onPin: () => {\n      makeAsPinUnPin(selectedFiles, true);\n    },\n    onUnPin: () => {\n      makeAsPinUnPin(selectedFiles, false);\n    },\n    positon: positon,\n    visible: visibleContextMenu,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1065,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(GenericDataTable, {\n    filterCloudPadding: 10,\n    onFiltersChange: filters => {\n      rowSelection.selectedRowKeys = [];\n      rowSelection.onChange([], []);\n    },\n    onRow: handleRowClick,\n    selectedRecordCount: selectedRecords,\n    hideHeaderPanel: true,\n    endpoint: config.api[OperationalServiceTypes.FileManagementService].files,\n    searchPromise: searchPromiseWrapper,\n    dataPromise: fetchFiles,\n    columnPromise: fetchColumns(),\n    scrollColumnCounter: 9,\n    rowSelection: rowSelection,\n    rowKey: 'id',\n    tableKey: tableKey,\n    groupedValue: siteId,\n    sorted: SORTER,\n    columnQueryParameters: [{\n      key: 'siteid',\n      value: siteId\n    }, {\n      key: 'binderId',\n      value: binderId\n    }],\n    searchQueryParameters: setQueryParameters(),\n    searchFieldParameters: setQueryParameters(),\n    customRender: renderGridColumns(),\n    lowResolutionWidth: \"auto\",\n    highResolutionWidth: \"auto\",\n    fixedColumns: ['title'],\n    isDraggable: true,\n    onErrorLoading: error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        props.history.push('/forbidden');\n      }\n    },\n    showPublishedIcon: true,\n    filterByIds: decodeURIComponent((_urlParams$get = urlParams.get('ids')) !== null && _urlParams$get !== void 0 ? _urlParams$get : ''),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1214,\n      columnNumber: 15\n    }\n  }))))), /*#__PURE__*/React.createElement(ModalCustom, {\n    visible: showDownloadModal,\n    title: 'Download Files',\n    size: 'small',\n    onCancel: handleOnDownloadModalCancel,\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      onClick: handleOnDownloadModalCancel,\n      key: \"submit\",\n      type: \"primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 1303,\n        columnNumber: 11\n      }\n    }, \"Done\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1297,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1308,\n      columnNumber: 9\n    }\n  }, selectedFile && /*#__PURE__*/React.createElement(DownloadModal, {\n    hasDownloaded: hasDownloaded => {\n      if (hasDownloaded) {\n        setShowDownloadModal(false);\n      }\n    },\n    selectedFiles: [selectedFile],\n    downloadType: downloadType,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 1309,\n      columnNumber: 28\n    }\n  }))));\n};\n\nexport default withRouter(Page);", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/pages/FileAreas/FileArea/index.tsx"], "names": ["React", "Fragment", "useCallback", "useContext", "useEffect", "useReducer", "useState", "EditOutlined", "ExclamationCircleOutlined", "LinkOutlined", "UpOutlined", "RightOutlined", "useParams", "with<PERSON><PERSON><PERSON>", "moment", "useDispatch", "useSelector", "<PERSON><PERSON><PERSON><PERSON>", "FileAreaActionPanel", "GenericDataTable", "config", "Page<PERSON><PERSON>le", "Uploader", "FolderTree", "styles", "canCheckinByFileId", "pinUnPinFiles", "undoCheckoutFile", "updateFileDetailsByFileId", "getBinderFileAreaNodes", "FileAreaButtonPanel", "ManageFiles", "Modal", "updateColumnQueryParameters", "updateSearchQueryParameters", "ContextMenu", "updateContextMenuAssignOption", "updateContextMenuCheckoutOption", "updateContextMenuDeleteOption", "updateContextMenuDownloadOption", "updateContextMenuPropetiesoption", "updateContextMenuPublishFiles", "updateContextMenuReCategorizeOption", "updateContextMenuMoveFilesOption", "updateContextMenuReNameFilesOption", "updateContextMenuCopyFilesOption", "updateContextMenuStatusOption", "updateContextMenuUnpublishFiles", "updateLoadGridOption", "updateContextMenuLinkFilesOption", "updateContextMenuUnlinkFilesOption", "updateContextMenuToBeDeleted", "updateContextMenuCopyLinkFiles", "setFolderTree", "errorNotification", "successNotification", "setDynamicBreadcrums", "copyToClipboard", "logger", "FORBIDDEN_ERROR_CODE", "getExistingTags", "DataTableContext", "updateFilterDropDownValues", "reducer", "OperationalServiceTypes", "hasPermission", "CheckCircleOutlined", "HistoryOutlined", "CheckinModel", "useQuery", "getTemplates", "setActiveTemplates", "getAutocompleteOptions", "getColumns", "getRecords", "debounce", "DownloadModal", "downloadTypes", "ModalCustom", "FormattedDateTime", "useFileArea", "StatusTag", "ShowAllSwitch", "SORTER", "value", "order", "CHECKOUT_STATUS", "statusPermissionMap", "fileUpload", "fileEdit", "fileDownload", "fileOptionsExceptDownload", "manageTags", "manageCheckin", "isInvalidStatus", "status", "name", "isCheckout", "constructManageFileRequest", "selectedFile", "data", "selectedFolder", "title", "binderId", "request", "tags", "newTags", "binderNodeId", "folder", "confirm", "formatGridColumnValue", "yjGridTextFormat", "hasFileAreaPermission", "fileArea", "permissionType", "toUpperCase", "Page", "props", "reactDispatch", "toggle", "SetToggle", "siteId", "siteName", "channelId", "binderName", "folderId", "setFolderId", "selected<PERSON><PERSON><PERSON><PERSON>", "setSelectedRecords", "selectedIds", "setSelectedIds", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedRowKeys", "selectedFiles", "setSelectedFiles", "showViewModalState", "setShowViewModalState", "editFormChange", "setEditFormChange", "editFormAs<PERSON><PERSON><PERSON><PERSON><PERSON>", "setEditFormAssigneeChange", "hasDownloaded", "setHasDownloaded", "undoCheckout", "setUndoCheckout", "actionsAllowed", "setActionsAllowed", "checkinInStatus", "gridUpdated", "setGridUpdated", "manageFileForm", "useForm", "manageFileTitle", "setManageFileTitle", "selectedFolderInUpload", "setSelectedFolderInUpload", "state", "_", "dispatch", "urlParams", "singleCheckinFile", "setSingleCheckinFile", "positon", "setPositon", "x", "y", "visibleContextMenu", "setVisibleContextMenu", "dataTableModal", "setDataTableModal", "visible", "modalData", "folderTree", "hasCommonData", "fileAreaSettings", "filesUploaded", "userPermission", "userPreferences", "userManagement", "activeTemplates", "template", "selectedTemplateId", "setSelectedTemplateId", "showDownloadModal", "setShowDownloadModal", "setSelectedFile", "undefined", "checkedOuFilesDownload", "setCheckedOuFilesDownload", "downloadType", "setDownloadType", "individual", "loading", "setLoading", "linkedFiles", "filter", "e", "linked", "originalFileAreaWithLinked", "some", "fK_FileAreaId", "fileAreaId", "table<PERSON><PERSON>", "initialFolderData", "id", "retention", "presist", "subFolders", "showDatatableModal", "hideDatatableModal", "fetchBinderNodes", "response", "transformFolders", "folders", "primaryFolders", "parentId", "childNodes", "length", "map", "child", "transformedFolders", "siteStatusId", "error", "loadGrid", "then", "res", "key", "setTimeout", "info", "records", "debug", "catch", "dynamicBreadcrumbs", "path", "decodeURIComponent", "rowSelection", "onChange", "selected<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "selectedRows", "find", "row", "privDMSCanCheckInCheckOutInternalFiles", "getCheckboxProps", "record", "disabled", "fixed", "<PERSON><PERSON><PERSON><PERSON>", "recordId", "includes", "handleCheckin", "fileId", "handleUndoCheckout", "icon", "okText", "cancelText", "onOk", "handleShowViewModalCancel", "onSelecteFolder", "folderIdValue", "onContextMenuClicked", "event", "preventDefault", "document", "addEventListener", "onClickOutside", "removeEventListener", "clientX", "clientY", "onRowClick", "handleRowClick", "rowIndex", "onContextMenu", "onClick", "handleUpdateFileProperties", "validateFields", "expirationDate", "endOf", "subtract", "requestData", "statusCode", "gridColumnValueFormatter", "downloadFile", "file", "colors", "expandedTags", "setExpandedTags", "containerRefs", "useRef", "showExpandIcons", "setShowExpandIcons", "showAllFiles", "setShowAllFiles", "checkOverflow", "container", "current", "scrollHeight", "clientHeight", "console", "log", "warn", "renderGridColumns", "yjFileAreaGridTextWrap", "paddingInline", "border", "cursor", "color", "fontSize", "created", "created<PERSON>y", "assignee", "year", "modified", "width", "render", "SINGLE_LINE_HEIGHT", "LINES_TO_DHOW_EXPANDER", "isExpanded", "isContentOverflowing", "senario1", "senario2", "tagColorMap", "for<PERSON>ach", "index", "el", "multiLine", "singleLine", "t", "prev", "requestAnimationFrame", "position", "right", "marginLeft", "projects", "yjGridTextCenter", "y<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type", "expirationStatus", "fileCondition", "action", "text", "yjActionIconWrapper", "privDMSCanManageFileProperties", "history", "push", "encodeURI", "setQueryParameters", "fileDownloaded", "onSuccess", "fetchFiles", "transformFilters", "queryParams", "fetchData", "activeFilter", "columns", "column", "filter_data", "filters", "f", "fetchColumns", "api", "FileManagementService", "files", "filterData", "item", "pagination", "pageSize", "sorter", "column<PERSON>ey", "i", "default", "selected", "debouncedApiCall", "callback", "propsData", "searchFieldParameters", "inputDebounceInterval", "searchPromiseWrapper", "handleOnDownloadModalCancel", "makeAsPinUnPin", "pinned", "fileIds", "privDMSCanUploadFiles", "yjFileAreaUploadWrapper", "yjFileAreaFileFinderCollapsed", "yjFileAreaFileFinderExpanded", "yjFileFinderPanel", "yjFileAreaDetailsGrid", "updated", "published", "yjDataTableModal", "top", "yjModalContentWrapper", "isFormChange", "isAssigneeChange", "privDMSCanManageFileAssign", "privDMSCanDeleteFiles", "privDMSCanViewFileArea", "privDMSCanRecategorizeFiles", "privDMSCanCopyFiles", "privDMSCanMoveFiles", "privDMSCanRenameFiles", "privDMSCanPublishUnpublishInternalFiles", "privDMSCanLinkUnlinkFiles", "get"], "mappings": ";;;;;;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,WAA1B,EAAuCC,UAAvC,EAAmDC,SAAnD,EAA8DC,UAA9D,EAA0EC,QAA1E,QAA0F,OAA1F;AACA,SACEC,YADF,EACgBC,yBADhB,EAC2CC,YAD3C,EACyDC,UADzD,EAEEC,aAFF,QAGO,mBAHP;AAIA,SAASC,SAAT,EAAoBC,UAApB,QAAsC,kBAAtC;AAEA,OAAOC,MAAP,MAAmB,QAAnB;AACA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,aAAzC;AACA,SAASC,WAAT,QAA4B,2BAA5B;AACA,SAASC,mBAAT,QAAoC,4CAApC;AACA,OAAOC,gBAAP,MAA6B,kCAA7B;AACA,OAAOC,MAAP,MAAmB,uBAAnB;AACA,OAAOC,SAAP,MAAsB,2BAAtB;AACA,OAAOC,QAAP,MAAqB,0BAArB;AACA,SAASC,UAAT,QAA2B,4BAA3B;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,SACEC,kBADF,EAGEC,aAHF,EAIEC,gBAJF,EAKEC,yBALF,EAQEC,sBARF,QASO,0BATP;AAUA,SAASC,mBAAT,QAAoC,8DAApC;AACA,OAAOC,WAAP,MAAwB,oCAAxB;AACA,OAAOC,KAAP,MAAkB,uBAAlB;AACA,SAASC,2BAAT,EAAsCC,2BAAtC,QAAyE,iCAAzE;AAEA,OAAOC,WAAP,MAAwB,6BAAxB;AACA,SACE;AACAC,6BAFF,EAGEC,+BAHF,EAIEC,6BAJF,EAKEC,+BALF,EAMEC,gCANF,EAOEC,6BAPF,EAQEC,mCARF,EASEC,gCATF,EAUEC,kCAVF,EAWEC,gCAXF,EAYEC,6BAZF,EAaEC,+BAbF,EAcEC,oBAdF,EAeEC,gCAfF,EAgBEC,kCAhBF,EAiBEC,4BAjBF,EAkBEC,8BAlBF,EAmBEC,aAnBF,QAoBO,oCApBP;AAsBA,SAASC,iBAAT,EAA8CC,mBAA9C,QAAyE,6BAAzE;AACA,SAASC,oBAAT,QAAqC,yCAArC;AACA,SAAiBC,eAAjB,QAAwC,2CAAxC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,oBAAT,QAAqC,YAArC;AACA,SAASC,eAAT,QAAgC,+BAAhC;AACA,SAASC,gBAAT,QAAiC,mDAAjC;AACA,SAASC,0BAAT,QAA2C,2DAA3C;AACA,OAAOC,OAAP,MAAoB,2DAApB;AACA,SAASC,uBAAT,QAAwC,2BAAxC;AAIA,OAAOC,aAAP,MAA0B,uBAA1B;AACA,SAASC,mBAAT,EAA8BC,eAA9B,QAAqD,uBAArD;AACA,SAASC,YAAT,QAA6B,kDAA7B;AACA,OAAOC,QAAP,MAAqB,qBAArB;AAEA,SAASC,YAAT,QAA6B,0BAA7B;AACA,SAASC,kBAAT,QAAmC,oCAAnC;AACA,SAASC,sBAAT,EAAiCC,UAAjC,EAA6CC,UAA7C,QAA+D,2BAA/D;AACA,OAAOC,QAAP,MAAqB,iBAArB;AACA,OAAOC,aAAP,IAAwBC,aAAxB,QAA6C,+BAA7C;AACA,OAAOC,WAAP,MAAwB,2BAAxB;AACA,SAASC,iBAAT,QAAkC,mCAAlC;AACA,SAASC,WAAT,QAA4B,wBAA5B;AAEA,OAAOC,SAAP,MAAsB,kCAAtB;AACA,OAAOC,aAAP,MAA0B,gCAA1B;AAEA,MAAMC,MAAc,GAAG;AACrBC,EAAAA,KAAK,EAAE,UADc;AAErBC,EAAAA,KAAK,EAAE;AAFc,CAAvB;AAIA,MAAMC,eAAe,GAAG,aAAxB;AAIA,MAAMC,mBAOJ,GAAG;AACH,YAAU;AAAEC,IAAAA,UAAU,EAAE,IAAd;AAAoBC,IAAAA,QAAQ,EAAE,IAA9B;AAAoCC,IAAAA,YAAY,EAAE,IAAlD;AAAwDC,IAAAA,yBAAyB,EAAE,IAAnF;AAAyFC,IAAAA,UAAU,EAAE,IAArG;AAA0GC,IAAAA,aAAa,EAAE;AAAzH,GADP;AAEH,cAAY;AAAEL,IAAAA,UAAU,EAAE,KAAd;AAAqBC,IAAAA,QAAQ,EAAE,KAA/B;AAAsCC,IAAAA,YAAY,EAAE,IAApD;AAA0DC,IAAAA,yBAAyB,EAAE,KAArF;AAA2FC,IAAAA,UAAU,EAAE,KAAvG;AAA8GC,IAAAA,aAAa,EAAE;AAA7H,GAFT;AAGH,iBAAe;AAAEL,IAAAA,UAAU,EAAE,IAAd;AAAoBC,IAAAA,QAAQ,EAAE,KAA9B;AAAqCC,IAAAA,YAAY,EAAE,IAAnD;AAAyDC,IAAAA,yBAAyB,EAAE,KAApF;AAA2FC,IAAAA,UAAU,EAAE,KAAvG;AAA8GC,IAAAA,aAAa,EAAE;AAA7H,GAHZ;AAIH,mBAAiB;AAAEL,IAAAA,UAAU,EAAE,KAAd;AAAqBC,IAAAA,QAAQ,EAAE,KAA/B;AAAsCC,IAAAA,YAAY,EAAE,IAApD;AAA0DC,IAAAA,yBAAyB,EAAE,KAArF;AAA4FC,IAAAA,UAAU,EAAE,KAAxG;AAA+GC,IAAAA,aAAa,EAAE;AAA9H;AAJd,CAPL;;AAcA,MAAMC,eAAe,GAAIC,MAAD,IAAiB;AACvC,SAAOA,MAAM,CAACC,IAAP,KAAgBV,eAAvB;AACD,CAFD;;AAIA,MAAMW,UAAU,GAAIF,MAAD,IAAiB;AAClC,SAAOA,MAAM,CAACC,IAAP,KAAgBV,eAAvB;AACD,CAFD;;AAIA,MAAMY,0BAA0B,GAAG,CAACC,YAAD,EAAsBC,IAAtB,EAAiCC,cAAjC,EAAyDC,KAAzD,EAAwEC,QAAxE,KAA6F;AAC9H,QAAMC,OAAO,GAAG,EACd,GAAGJ,IADW;AAEdK,IAAAA,IAAI,EAAE,CAAC,GAAGL,IAAI,CAACK,IAAT,EAAe,GAAGL,IAAI,CAACM,OAAvB,CAFQ;AAGdC,IAAAA,YAAY,EAAEN,cAAc,GAAG,CAAjB,GAAqBD,IAAI,CAACQ,MAA1B,GAAmCP,cAHnC;AAIdC,IAAAA,KAAK,EAAEA,KAAK,KAAK,EAAV,GAAeA,KAAf,GAAuBH,YAAY,CAACG,KAJ7B;AAKdC,IAAAA;AALc,GAAhB;AAOA,SAAOC,OAAO,CAACI,MAAf;AACA,SAAOJ,OAAO,CAACE,OAAf;AACA,SAAOF,OAAP;AACD,CAXD;;AAaA,MAAM;AAAEK,EAAAA;AAAF,UAAN;AAEA,OAAO,MAAMC,qBAAqB,GAAI1B,KAAD,IAAgB;AACnD,sBAAO;AAAG,IAAA,SAAS,EAAE5D,MAAM,CAACuF,gBAArB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAAwC3B,KAAxC,CAAP;AACD,CAFM;;AAIP,MAAM4B,qBAAqB,GAAG,CAACC,QAAD,EAAgBC,cAAhB,KAAuD;AACnF,SAAOD,QAAQ,IAAI1B,mBAAmB,CAAC0B,QAAQ,CAAClB,MAAT,CAAgBoB,WAAhB,EAAD,CAAnB,CAAmDD,cAAnD,CAAnB;AACD,CAFD;;AAIA,MAAME,IAAI,GAAIC,KAAD,IAAgB;AAAA;;AAC3B,QAAMC,aAAa,GAAGvG,WAAW,EAAjC;AACA,QAAM,CAACwG,MAAD,EAASC,SAAT,IAAsBlH,QAAQ,CAAC,KAAD,CAApC;AACA,QAAM;AAAEmH,IAAAA,MAAF;AAAUC,IAAAA,QAAV;AAAoBC,IAAAA,SAApB;AAA+BpB,IAAAA,QAA/B;AAAyCqB,IAAAA;AAAzC,MAAwDhH,SAAS,EAAvE;AACA,QAAM,CAACiH,QAAD,EAAWC,WAAX,IAA0BxH,QAAQ,CAAC,CAAD,CAAxC;AACA,QAAM,CAACyH,eAAD,EAAkBC,kBAAlB,IAAwC1H,QAAQ,CAAC,CAAD,CAAtD;AACA,QAAM,CAAC2H,WAAD,EAAcC,cAAd,IAAgC5H,QAAQ,CAAC,CAAC,EAAD,CAAD,CAA9C;AACA,QAAM,CAAC6H,eAAD,EAAkBC,kBAAlB,IAAwC9H,QAAQ,CAAC,EAAD,CAAtD;AACA,QAAM,CAAC+H,aAAD,EAAgBC,gBAAhB,IAAoChI,QAAQ,CAAU,EAAV,CAAlD;AACA,QAAM,CAACiI,kBAAD,EAAqBC,qBAArB,IAA8ClI,QAAQ,CAAC,KAAD,CAA5D;AACA,QAAM,CAACmI,cAAD,EAAiBC,iBAAjB,IAAsCpI,QAAQ,CAAC,KAAD,CAApD;AACA,QAAM,CAACqI,sBAAD,EAAyBC,yBAAzB,IAAsDtI,QAAQ,CAAC,KAAD,CAApE;AACA,QAAM,CAACuI,aAAD,EAAgBC,gBAAhB,IAAoCxI,QAAQ,CAAC,KAAD,CAAlD;AACA,QAAM,CAACyI,YAAD,EAAeC,eAAf,IAAkC1I,QAAQ,CAAC,IAAD,CAAhD;AACA,QAAM,CAAC2I,cAAD,EAAiBC,iBAAjB,IAAsC5I,QAAQ,CAAC,IAAD,CAApD;AACA,QAAM6I,eAAe,GAAG,CAAxB;AACA,QAAM,CAACC,WAAD,EAAcC,cAAd,IAAgC/I,QAAQ,CAAC,KAAD,CAA9C;;AACA,QAAM,CAACgJ,cAAD,IAAmB,MAAKC,OAAL,EAAzB;;AACA,QAAM,CAACC,eAAD,EAAkBC,kBAAlB,IAAwCnJ,QAAQ,CAAC,EAAD,CAAtD;AACA,QAAM,CAACoJ,sBAAD,EAAyBC,yBAAzB,IAAsDrJ,QAAQ,CAAS,CAAC,CAAV,CAApE;AACA,QAAM;AAAEsJ,IAAAA;AAAF,MAAYzJ,UAAU,CAAC0D,gBAAD,CAA5B;AACA,QAAM,CAACgG,CAAD,EAAIC,QAAJ,IAAgBzJ,UAAU,CAAC0D,OAAD,EAAU6F,KAAV,CAAhC;AACA,QAAMG,SAAS,GAAG1F,QAAQ,EAA1B;AAEA,QAAM,CAAC2F,iBAAD,EAAoBC,oBAApB,IAA4C3J,QAAQ,CAAC,EAAD,CAA1D,CAxB2B,CAyB3B;;AACA,QAAM,CAAC4J,OAAD,EAAUC,UAAV,IAAwB7J,QAAQ,CAAC;AAAE8J,IAAAA,CAAC,EAAE,CAAL;AAAQC,IAAAA,CAAC,EAAE;AAAX,GAAD,CAAtC;AACA,QAAM,CAACC,kBAAD,EAAqBC,qBAArB,IAA8CjK,QAAQ,CAAC,KAAD,CAA5D,CA3B2B,CA4B3B;;AACA,QAAM2G,QAAQ,GAAGjC,WAAW,CAACyC,MAAD,CAA5B;AAEA,QAAM,CAAC+C,cAAD,EAAiBC,iBAAjB,IAAsCnK,QAAQ,CAAC;AACnDoK,IAAAA,OAAO,EAAE,KAD0C;AAEnDpE,IAAAA,KAAK,EAAE,EAF4C;AAGnDqE,IAAAA,SAAS,EAAE;AAHwC,GAAD,CAApD;AAMA,QAAM;AAAEC,IAAAA,UAAF;AAAcC,IAAAA,aAAd;AAA6BC,IAAAA,gBAA7B;AAA+CC,IAAAA;AAA/C,MAAiE/J,WAAW,CAAE4I,KAAD,IAAsBA,KAAK,CAAC3C,QAA7B,CAAlF;AACA,QAAM;AAAE+D,IAAAA,cAAF;AAAkBC,IAAAA;AAAlB,MAAsCjK,WAAW,CAAE4I,KAAD,IAAsBA,KAAK,CAACsB,cAA7B,CAAvD;AACA,QAAM;AAAEC,IAAAA;AAAF,MAAsBnK,WAAW,CAAE4I,KAAD,IAAsBA,KAAK,CAACwB,QAA7B,CAAvC;AAEA,QAAM,CAACC,kBAAD,EAAqBC,qBAArB,IAA8ChL,QAAQ,CAAC,EAAD,CAA5D;AAEA,QAAM,CAACiL,iBAAD,EAAoBC,oBAApB,IAA4ClL,QAAQ,CAAC,KAAD,CAA1D;AACA,QAAM,CAAC6F,YAAD,EAAesF,eAAf,IAAkCnL,QAAQ,CAAoBoL,SAApB,CAAhD;AACA,QAAM,CAACC,sBAAD,EAAyBC,yBAAzB,IAAsDtL,QAAQ,CAAsBoL,SAAtB,CAApE;AACA,QAAM,CAACG,YAAD,EAAeC,eAAf,IAAkCxL,QAAQ,CAA4BuE,aAAa,CAACkH,UAA1C,CAAhD;AACA,QAAM,CAACC,OAAD,EAAUC,UAAV,IAAwB3L,QAAQ,CAAC,KAAD,CAAtC;AAEA,QAAM4L,WAAW,GAAG7D,aAAa,CAAC8D,MAAd,CAAsBC,CAAD,IAAOA,CAAC,CAACC,MAA9B,CAApB;AACA,QAAMC,0BAAmC,GAAGJ,WAAW,IAAI7D,aAAa,CAACkE,IAAd,CAAoBH,CAAD,IAAOA,CAAC,CAACI,aAAF,MAAoBvF,QAApB,aAAoBA,QAApB,uBAAoBA,QAAQ,CAAEwF,UAA9B,CAA1B,CAA3D;AAEA,QAAMC,QAAQ,GAAG,UAAjB;AAEA,QAAMC,iBAAiB,GAAG,CACxB;AACEC,IAAAA,EAAE,EAAE,CADN;AAEE5G,IAAAA,IAAI,EAAE,gBAFR;AAGE6G,IAAAA,SAAS,EAAE,KAHb;AAIEC,IAAAA,OAAO,EAAE,KAJX;AAKEC,IAAAA,UAAU,EAAE,CACV;AACEH,MAAAA,EAAE,EAAE,CADN;AAEE5G,MAAAA,IAAI,EAAE,sBAFR;AAGE+G,MAAAA,UAAU,EAAE,EAHd;AAIEF,MAAAA,SAAS,EAAE,KAJb;AAKEC,MAAAA,OAAO,EAAE;AALX,KADU,EAQV;AACEF,MAAAA,EAAE,EAAE,CADN;AAEE5G,MAAAA,IAAI,EAAE,sBAFR;AAGE+G,MAAAA,UAAU,EAAE,EAHd;AAIEF,MAAAA,SAAS,EAAE,KAJb;AAKEC,MAAAA,OAAO,EAAE;AALX,KARU;AALd,GADwB,CAA1B;;AAyBA,QAAME,kBAAkB,GAAG,CAAC1G,KAAD,EAAgBF,IAAhB,KAA6B;AACtDqE,IAAAA,iBAAiB,CAAC;AAChBC,MAAAA,OAAO,EAAE,IADO;AAEhBpE,MAAAA,KAAK,EAAEA,KAFS;AAGhBqE,MAAAA,SAAS,EAAEvE;AAHK,KAAD,CAAjB;AAKD,GAND;;AAQA,QAAM6G,kBAAkB,GAAG,MAAM;AAC/BxC,IAAAA,iBAAiB,CAAC;AAChBC,MAAAA,OAAO,EAAE,KADO;AAEhBpE,MAAAA,KAAK,EAAEkE,cAAc,CAAClE,KAFN;AAGhBqE,MAAAA,SAAS,EAAEH,cAAc,CAACG;AAHV,KAAD,CAAjB;AAKD,GAND;;AAQA,QAAMuC,gBAAgB,GAAG,YAAY;AACnC,QAAI;AACF,YAAMC,QAAQ,GAAG,MAAMtL,sBAAsB,CAAC0E,QAAD,CAA7C;;AACA,YAAM6G,gBAAgB,GAAIC,OAAD,IAA+B;AACtD,cAAMC,cAAc,GAAGD,OAAO,CAAClB,MAAR,CAAgBvF,MAAD,IAAY,CAACA,MAAM,CAAC2G,QAAR,IAAoB3G,MAAM,CAAC4G,UAA3B,IAAyC5G,MAAM,CAAC4G,UAAP,CAAkBC,MAAlB,GAA2B,CAA/F,CAAvB;AAEA,eAAOH,cAAc,CAACI,GAAf,CAAoB9G,MAAD,KAAa;AACrCgG,UAAAA,EAAE,EAAEhG,MAAM,CAACgG,EAD0B;AAErC5G,UAAAA,IAAI,EAAEY,MAAM,CAACZ,IAFwB;AAGrC+G,UAAAA,UAAU,EAAEnG,MAAM,CAAC4G,UAAP,CAAkBE,GAAlB,CAAuBC,KAAD,KAAiB;AACjDf,YAAAA,EAAE,EAAEe,KAAK,CAACf,EADuC;AAEjD5G,YAAAA,IAAI,EAAE2H,KAAK,CAAC3H,IAFqC;AAGjD+G,YAAAA,UAAU,EAAE,EAHqC;AAIjDF,YAAAA,SAAS,EAAEc,KAAK,CAACd,SAAN,IAAmB;AAJmB,WAAjB,CAAtB;AAHyB,SAAb,CAAnB,CAAP;AAUD,OAbD;;AAeA,YAAMe,kBAAkB,GAAGR,gBAAgB,CAACD,QAAQ,CAAC/G,IAAT,CAAciH,OAAf,CAA3C;AACA/F,MAAAA,aAAa,CAACjE,aAAa,CAAC;AAC1BoE,QAAAA,MAD0B;AAE1BC,QAAAA,QAAQ,EAAEE,UAFgB;AAG1BiG,QAAAA,YAAY,EAAE,CAHY;AAI1BR,QAAAA,OAAO,EAAEO;AAJiB,OAAD,CAAd,CAAb;AAMD,KAxBD,CAwBE,OAAOE,KAAP,EAAc;AACdpK,MAAAA,MAAM,CAACoK,KAAP,CAAa,8BAAb,EAA6C,kBAA7C,EAAiEA,KAAjE;AACD;AACF,GA5BD;;AA8BA,QAAMC,QAAQ,GAAG7N,WAAW,CAAC,MAAM;AACjC0D,IAAAA,eAAe,CAAC2C,QAAD,CAAf,CAA0ByH,IAA1B,CAAgCC,GAAD,IAAS;AACtCnE,MAAAA,QAAQ,CAAChG,0BAA0B,CAAC,MAAD,EAASmK,GAAG,CAAC7H,IAAb,CAA3B,CAAR;AACD,KAFD;AAGAkB,IAAAA,aAAa,CAACrF,2BAA2B,CAACwF,MAAD,CAA5B,CAAb;AACAH,IAAAA,aAAa,CACXpF,2BAA2B,CAAC,CAC1B;AAAEgM,MAAAA,GAAG,EAAE,QAAP;AAAiB9I,MAAAA,KAAK,EAAEqC;AAAxB,KAD0B,EAE1B;AAAEyG,MAAAA,GAAG,EAAE,cAAP;AAAuB9I,MAAAA,KAAK,EAAEyC;AAA9B,KAF0B,CAAD,CADhB,CAAb;AAMAP,IAAAA,aAAa,CAACtE,oBAAoB,CAAC,IAAD,CAArB,CAAb;AACAmL,IAAAA,UAAU,CAAC,MAAM;AACf7G,MAAAA,aAAa,CAACtE,oBAAoB,CAAC,KAAD,CAArB,CAAb;AACD,KAFS,CAAV;AAGD,GAf2B,EAezB,CAACyE,MAAD,EAASH,aAAT,EAAwBO,QAAxB,EAAkCtB,QAAlC,CAfyB,CAA5B;AAiBAnG,EAAAA,SAAS,CAAC,MAAM;AACd8M,IAAAA,gBAAgB;AACjB,GAFQ,EAEN,EAFM,CAAT;AAIA9M,EAAAA,SAAS,CAAC,MAAM;AACdkE,IAAAA,YAAY,CAAC;AAAE,kBAAY,KAAd;AAAqB,wBAAkB;AAAvC,KAAD,CAAZ,CAAyD0J,IAAzD,CAA+DC,GAAD,IAAS;AAAA;;AACrEvK,MAAAA,MAAM,CAAC0K,IAAP,CAAY,kBAAZ,EAAgC,wBAAhC,EAA0DH,GAAG,CAAC7H,IAAJ,CAASiI,OAAnE;;AACA,UAAIJ,GAAG,CAAClI,MAAJ,IAAc,GAAd,kBAAqBkI,GAAG,CAAC7H,IAAzB,8CAAqB,UAAUiI,OAA/B,CAAJ,EAA4C;AAAA;;AAC1C/G,QAAAA,aAAa,CAAC/C,kBAAkB,eAAC0J,GAAG,CAAC7H,IAAL,+CAAC,WAAUiI,OAAX,CAAnB,CAAb;AACA3K,QAAAA,MAAM,CAAC4K,KAAP,CACE,kBADF,EAEE,sBAFF,EAGEnD,eAHF;AAKD;AACF,KAVD,EAUGoD,KAVH,CAUUnC,CAAD,IAAO;AACd1I,MAAAA,MAAM,CAACoK,KAAP,CAAa,kBAAb,EAAiC,qBAAjC,EAAwD1B,CAAxD;AACD,KAZD;AAcA,UAAMoC,kBAAkB,GAAG,CACzB;AAAElI,MAAAA,KAAK,EAAE,mBAAT;AAA8BmI,MAAAA,IAAI,EAAE;AAApC,KADyB,EAEzB;AAAEnI,MAAAA,KAAK,EAAG,GAAEoI,kBAAkB,CAAChH,QAAD,CAAW,eAAzC;AAAyD+G,MAAAA,IAAI,EAAG,qBAAoBhH,MAAO,IAAGC,QAAS,IAAGC,SAAU;AAApH,KAFyB,EAGzB;AAAErB,MAAAA,KAAK,EAAG,GAAEoI,kBAAkB,CAAC9G,UAAD,CAAa,EAA3C;AAA8C6G,MAAAA,IAAI,EAAG,qBAAoBhH,MAAO,cAAaC,QAAS,IAAGnB,QAAS,IAAGqB,UAAW,IAAGD,SAAU;AAA7I,KAHyB,CAA3B;AAKAL,IAAAA,aAAa,CAAC9D,oBAAoB,CAACgL,kBAAD,CAArB,CAAb;AACA,WAAO,MAAM;AACX;AACAlH,MAAAA,aAAa,CAAC9D,oBAAoB,CAAC,EAAD,CAArB,CAAb;AACD,KAHD;AAID,GAzBQ,EAyBN,EAzBM,CAAT;AA8BApD,EAAAA,SAAS,CAAC,MAAM;AACdkH,IAAAA,aAAa,CAACrF,2BAA2B,CAACwF,MAAD,CAA5B,CAAb;AACAH,IAAAA,aAAa,CACXpF,2BAA2B,CAAC,CAC1B;AAAEgM,MAAAA,GAAG,EAAE,QAAP;AAAiB9I,MAAAA,KAAK,EAAEqC;AAAxB,KAD0B,EAE1B;AAAEyG,MAAAA,GAAG,EAAE,cAAP;AAAuB9I,MAAAA,KAAK,EAAE;AAA9B,KAF0B,CAAD,CADhB,CAAb;AAMD,GARQ,EAQN,CAACkC,aAAD,EAAgBG,MAAhB,CARM,CAAT;AAUArH,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIyI,aAAJ,EAAmB;AACjBQ,MAAAA,cAAc,CAAC,IAAD,CAAd;AACA0E,MAAAA,QAAQ;AACRjF,MAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACA6F,MAAAA,YAAY,CAACxG,eAAb,GAA+B,EAA/B;AACAwG,MAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACAvF,MAAAA,cAAc,CAAC,KAAD,CAAd;AACD;AACF,GATQ,EASN,CAACR,aAAD,EAAgBkF,QAAhB,CATM,CAAT;AAWA3N,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI2K,aAAJ,EAAmB;AACjB1B,MAAAA,cAAc,CAAC,IAAD,CAAd;AACA0E,MAAAA,QAAQ;AACR1E,MAAAA,cAAc,CAAC,KAAD,CAAd;AACD;AACF,GANQ,EAMN,CAAC0B,aAAD,EAAgBzD,aAAhB,EAA+ByG,QAA/B,CANM,CAAT;AAQA,QAAMY,YAAY,GAAG;AACnBC,IAAAA,QAAQ,EAAE,CAACC,oBAAD,EAA4BC,YAA5B,KAAsD;AAAA;;AAC9D,UAAI,CAAAA,YAAY,SAAZ,IAAAA,YAAY,WAAZ,YAAAA,YAAY,CAAErB,MAAd,IAAuB,CAAvB,KAA4BqB,YAA5B,aAA4BA,YAA5B,uBAA4BA,YAAY,CAAEC,IAAd,CAAoBC,GAAD;AAAA;;AAAA,eAAS,CAAAhE,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAEiE,sCAAhB,KAA0D,CAAAD,GAAG,SAAH,IAAAA,GAAG,WAAH,2BAAAA,GAAG,CAAEjJ,MAAL,4DAAaX,KAAb,MAAuB+D,eAA1F;AAAA,OAAnB,CAA5B,CAAJ,EAA+J;AAC7JD,QAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACD,OAFD,MAEO;AACLA,QAAAA,iBAAiB,CAAC,IAAD,CAAjB;AACD;;AACD,UAAI8B,cAAc,CAACiE,sCAAf,IAAyDH,YAAY,CAACrB,MAAb,KAAwB,CAAjF,IAAsF,0BAAAqB,YAAY,CAAC,CAAD,CAAZ,CAAgB/I,MAAhB,gFAAwBX,KAAxB,MAAkC+D,eAA5H,EAA6I;AAC3IH,QAAAA,eAAe,CAAC,IAAD,CAAf;AACD,OAFD,MAEO;AACLA,QAAAA,eAAe,CAAC,KAAD,CAAf;AACA1B,QAAAA,aAAa,CAAC9E,gCAAgC,CAAC,KAAD,CAAjC,CAAb;AACD;;AACD4F,MAAAA,kBAAkB,CAACyG,oBAAD,CAAlB;AACA7G,MAAAA,kBAAkB,CAAC8G,YAAY,CAACrB,MAAd,CAAlB;AACAnF,MAAAA,gBAAgB,CAACwG,YAAD,CAAhB;;AACA,UAAIA,YAAY,CAACrB,MAAb,GAAsB,CAA1B,EAA6B;AAC3BvF,QAAAA,cAAc,CAAC2G,oBAAD,CAAd;AACD,OAFD,MAEO;AACL3G,QAAAA,cAAc,CAAC,CAAC,EAAD,CAAD,CAAd;AACD;AACF,KArBkB;AAsBnBgH,IAAAA,gBAAgB,EAAGC,MAAD;AAAA;;AAAA,aAAkB;AAClCC,QAAAA,QAAQ,EAAE,CAAAD,MAAM,SAAN,IAAAA,MAAM,WAAN,8BAAAA,MAAM,CAAEpJ,MAAR,kEAAgBX,KAAhB,MAA0B+D,eADF;AAElCnD,QAAAA,IAAI,EAAEmJ,MAAM,CAACnJ;AAFqB,OAAlB;AAAA,KAtBC;AA0BnBmC,IAAAA,eA1BmB;AA2BnBkH,IAAAA,KAAK,EAAE;AA3BY,GAArB;;AA8BA,QAAMC,cAAc,GAAIC,QAAD,IAAsB;AAC3C,WAAOtH,WAAW,CAACuH,QAAZ,CAAqBD,QAArB,CAAP;AACD,GAFD;;AAIA,QAAME,aAAa,GAAIC,MAAD,IAAoB;AACxCjO,IAAAA,kBAAkB,CAACiO,MAAD,CAAlB,CAA2B1B,IAA3B,CAAgC,MAAM;AACpC/D,MAAAA,oBAAoB,CAACyF,MAAD,CAApB;AACD,KAFD,EAEGnB,KAFH,CAEUT,KAAD,IAAW;AAClBpK,MAAAA,MAAM,CAACoK,KAAP,CAAa,kBAAb,EAAiC,eAAjC,EAAkDA,KAAlD;;AAEA,UAAIA,KAAK,CAACX,QAAN,CAAepH,MAAf,KAA0BpC,oBAA9B,EAAoD;AAClDL,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,wDAAP,CAAjB;AACD,OAFD,MAEO;AACLA,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,uBAAP,CAAjB;AACD;AACF,KAVD;AAWD,GAZD;;AAaA,QAAMqM,kBAAkB,GAAID,MAAD,IAAoB;AAC7C5G,IAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACAjC,IAAAA,OAAO,CAAC;AACNP,MAAAA,KAAK,EAAE,6CADD;AAENsJ,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLpO,QAAAA,gBAAgB,CAAC0G,aAAa,CAAC,CAAD,CAAb,CAAiBuE,EAAlB,CAAhB,CACGoB,IADH,CACSb,QAAD,IAAc;AAClB,cAAIA,QAAJ,EAAc;AACZrE,YAAAA,gBAAgB,CAAC,IAAD,CAAhB;AACAvF,YAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,2BAAP,CAAnB;AACD;AACF,SANH,EAOGgL,KAPH,CAOUT,KAAD,IAAW;AAChB,cAAIA,KAAK,CAACX,QAAN,CAAepH,MAAf,KAA0BpC,oBAA9B,EAAoD;AAClDL,YAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,qFAAP,CAAjB;AACD,WAFD,MAEO;AACLA,YAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,oCAAP,CAAjB;AACD;AACF,SAbH;AAcD;;AApBK,KAAD,CAAP;AAsBD,GAxBD;;AA0BA,QAAM0M,yBAAyB,GAAG,MAAM;AACtC,QAAIvH,cAAJ,EAAoB;AAClB5B,MAAAA,OAAO,CAAC;AACNP,QAAAA,KAAK,EAAE,+CADD;AAENsJ,QAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAFA;AAGNC,QAAAA,MAAM,EAAE,KAHF;AAINC,QAAAA,UAAU,EAAE,IAJN;;AAKNC,QAAAA,IAAI,GAAG;AACLrH,UAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACAF,UAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD;;AARK,OAAD,CAAP;AAUD,KAXD,MAWO;AACLE,MAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACAF,MAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD;AACF,GAhBD,CAtR2B,CAwS3B;AACA;AACA;;;AAEA,QAAMyH,eAAe,GAAIC,aAAD,IAAwB;AAC9C5I,IAAAA,aAAa,CACXpF,2BAA2B,CAAC,CAC1B;AAAEgM,MAAAA,GAAG,EAAE,QAAP;AAAiB9I,MAAAA,KAAK,EAAEqC;AAAxB,KAD0B,EAE1B;AAAEyG,MAAAA,GAAG,EAAE,cAAP;AAAuB9I,MAAAA,KAAK,EAAE8K;AAA9B,KAF0B,CAAD,CADhB,CAAb;AAMApI,IAAAA,WAAW,CAACoI,aAAD,CAAX;AACAvB,IAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACD,GATD;;AAWA,QAAMuB,oBAAoB,GAAG,CAACC,KAAD,EAAajB,MAAb,KAA+B;AAC1D,QAAIR,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,IAAuC,CAA3C,EAA8C;AAAA;;AAC5C,UAAIzC,cAAc,CAACiE,sCAAf,IAAyD,CAAAE,MAAM,SAAN,IAAAA,MAAM,WAAN,+BAAAA,MAAM,CAAEpJ,MAAR,oEAAgBX,KAAhB,MAA0B+D,eAAvF,EAAwG;AACtGH,QAAAA,eAAe,CAAC,IAAD,CAAf;AACAE,QAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACD,OAHD,MAGO;AACLF,QAAAA,eAAe,CAAC,KAAD,CAAf;AACAE,QAAAA,iBAAiB,CAAC,IAAD,CAAjB;AACD;;AAEDZ,MAAAA,gBAAgB,CAAC,CAAC6G,MAAD,CAAD,CAAhB;AACD;;AACDiB,IAAAA,KAAK,CAACC,cAAN;AACA9F,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACA+F,IAAAA,QAAQ,CAACC,gBAAT,CAA2B,OAA3B,EAAmC,SAASC,cAAT,GAA0B;AAC3DjG,MAAAA,qBAAqB,CAAC,KAAD,CAArB;AACA+F,MAAAA,QAAQ,CAACG,mBAAT,CAA8B,OAA9B,EAAsCD,cAAtC;AACD,KAHD;AAIAjG,IAAAA,qBAAqB,CAAC,IAAD,CAArB;AACAJ,IAAAA,UAAU,CAAC;AAAEC,MAAAA,CAAC,EAAEgG,KAAK,CAACM,OAAX;AAAoBrG,MAAAA,CAAC,EAAE+F,KAAK,CAACO;AAA7B,KAAD,CAAV;AACD,GApBD;;AAsBA,QAAMC,UAAU,GAAG,CAACR,KAAD,EAAajB,MAAb,KAA+B;AAChD,QAAI9G,aAAa,CAACoF,MAAd,KAAyB,CAA7B,EAAgC;AAC9BnF,MAAAA,gBAAgB,CAAC,CAAC6G,MAAD,CAAD,CAAhB;AACD;AACF,GAJD;;AAMA,QAAM0B,cAAc,GAAG,CAAC1B,MAAD,EAAgB2B,QAAhB,MAAmC;AACxDC,IAAAA,aAAa,EAAGX,KAAD,IAAgBD,oBAAoB,CAACC,KAAD,EAAQjB,MAAR,CADK;AAExD6B,IAAAA,OAAO,EAAGZ,KAAD,IAAgBQ,UAAU,CAACR,KAAD,EAAQjB,MAAR;AAFqB,GAAnC,CAAvB;;AAKA,QAAM8B,0BAA0B,GAAG,MAAM;AACvChF,IAAAA,UAAU,CAAC,IAAD,CAAV;AACA3C,IAAAA,cAAc,CACX4H,cADH,GAEGlD,IAFH,CAES5H,IAAD,IAAU;AACdA,MAAAA,IAAI,CAAC+K,cAAL,GAAsBrQ,MAAM,CAACsF,IAAI,CAAC+K,cAAN,CAAN,CAA4BC,KAA5B,CAAkC,KAAlC,EAAyCC,QAAzC,CAAkD,CAAlD,EAAqD,SAArD,CAAtB;AAEA,YAAMC,WAAW,GAAGpL,0BAA0B,CAACmC,aAAa,CAAC,CAAD,CAAd,EAAmBjC,IAAnB,EAAyBsD,sBAAzB,EAAiDF,eAAjD,EAAkEjD,QAAlE,CAA9C;AACA,aAAO3E,yBAAyB,CAACyG,aAAa,CAAC,CAAD,CAAb,CAAiBuE,EAAlB,EAAsB0E,WAAtB,CAAhC;AACD,KAPH,EAQGtD,IARH,CAQSb,QAAD,IAAc;AAClB;AACA,UAAI,CAACA,QAAQ,CAAC/G,IAAd,EAAoB;AAClB9C,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,+BAAP,CAAjB;AACA2I,QAAAA,UAAU,CAAC,KAAD,CAAV;;AACA,YAAItD,sBAAJ,EAA4B;AAC1BrF,UAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,mBAAP,CAAjB;AACD;;AAED,eAAO6J,QAAQ,CAAC/G,IAAhB;AACD,OAViB,CAYlB;;;AACA2H,MAAAA,QAAQ;AACRxK,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,sCAAP,CAAnB;;AACA,UAAIoF,sBAAJ,EAA4B;AAC1BpF,QAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,uBAAP,CAAnB;AACD;;AACDiF,MAAAA,qBAAqB,CAAC,KAAD,CAArB;AACAE,MAAAA,iBAAiB,CAAC,KAAD,CAAjB;AACAuD,MAAAA,UAAU,CAAC,KAAD,CAAV;AACD,KA7BH,EA8BGsC,KA9BH,CA8BUT,KAAD,IAAW;AAChB,cAAQA,KAAK,CAACyD,UAAd;AACE;AACA,aAAK5N,oBAAL;AACEL,UAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,qFAAP,CAAjB;AACA;;AACF;AACEA,UAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,+BAAP,CAAjB;;AACA,cAAIqF,sBAAJ,EAA4B;AAC1BrF,YAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,mBAAP,CAAjB;AACD;;AACD;AAVJ;;AAaAI,MAAAA,MAAM,CAACoK,KAAP,CAAa,gBAAb,EAA+B,mBAA/B,EAAoDA,KAApD;AACA7B,MAAAA,UAAU,CAAC,KAAD,CAAV;AACD,KA9CH;AA+CD,GAjDD;;AAmDA,WAASuF,wBAAT,CAAkCpM,KAAlC,EAA8C;AAC5C,WAAOA,KAAK,GAAG0B,qBAAqB,CAAC1B,KAAD,CAAxB,GAAkC0B,qBAAqB,CAAC,KAAD,CAAnE;AACD;;AAED,QAAM2K,YAAY,GAAG,CAACrM,KAAD,EAAYsM,IAAZ,KAA4B;AAE/C;AACAjG,IAAAA,eAAe,CAACiG,IAAD,CAAf;;AACA,QAAG1K,qBAAqB,CAACC,QAAD,EAAW,cAAX,CAAxB,EACE;AACAuE,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACC;;AACHI,IAAAA,yBAAyB,CAACxG,KAAD,CAAzB;AACD,GATD;;AAUA,QAAMuM,MAAM,GAAG,CACb,SADa,EAEb,OAFa,EAGb,KAHa,EAIb,MAJa,EAKb,SALa,EAMb,MANa,EAOb,QAPa,EAQb,UARa,EASb,MATa,EAUb,QAVa,EAWb,MAXa,CAAf;AAaA,QAAM,CAACC,YAAD,EAAeC,eAAf,IAAkCvR,QAAQ,CAA0B,EAA1B,CAAhD,CAta2B,CAua3B;;AACA,QAAMwR,aAAa,GAAG9R,KAAK,CAAC+R,MAAN,CAA0D,EAA1D,CAAtB;AACA,QAAM,CAACC,eAAD,EAAkBC,kBAAlB,IAAwCjS,KAAK,CAACM,QAAN,CAAwC,EAAxC,CAA9C;AACA,QAAM,CAAC4R,YAAD,EAAeC,eAAf,IAAkC7R,QAAQ,CAAU,KAAV,CAAhD,CA1a2B,CA4a3B;;AACA,QAAM8R,aAAa,GAAI7C,QAAD,IAAsB;AAC1C,UAAM8C,SAAS,GAAGP,aAAa,CAACQ,OAAd,CAAsB/C,QAAtB,CAAlB;;AACA,QAAI8C,SAAS,IAAIA,SAAS,CAACE,YAAvB,IAAuCF,SAAS,CAACG,YAAjD,IAAiEH,SAAS,CAACE,YAAV,GAAyBF,SAAS,CAACG,YAAxG,EAAsH;AACpHC,MAAAA,OAAO,CAACC,GAAR,CAAY,YAAZ,EAA0BL,SAA1B;AACAI,MAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BL,SAAS,CAACE,YAAvC,EAAqD,eAArD,EAAsEF,SAAS,CAACG,YAAhF,EAFoH,CAGpH;AACA;AACA;AACA;AACA;AACA;AACD,KATD,MASO;AACLC,MAAAA,OAAO,CAACE,IAAR,CAAc,0BAAyBpD,QAAS,oBAAhD;AACD;AACF,GAdD,CA7a2B,CA6b3B;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAEA,QAAMqD,iBAAiB,GAAG,MAAM;AAC9B,WAAO;AACLtM,MAAAA,KAAK,EAAE,CAAClB,KAAD,EAAagB,IAAb,KAA2B;AAChC,4BACE;AAAK,UAAA,SAAS,EAAG,GAAE5E,MAAM,CAACqR,sBAAuB,gBAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAS,UAAA,SAAS,EAAC,eAAnB;AAAmC,UAAA,SAAS,EAAC,SAA7C;AAAuD,UAAA,KAAK,EAAEzN,KAA9D;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACA;AAAQ,UAAA,IAAI,EAAC,MAAb;AAAoB,UAAA,OAAO,EAAE,MAAMqM,YAAY,CAACrM,KAAD,EAAQgB,IAAR,CAA/C;AAA8D,UAAA,KAAK,EAAE;AAAE0M,YAAAA,aAAa,EAAE,CAAjB;AAAoBC,YAAAA,MAAM,EAAE,CAA5B;AAAgCC,YAAAA,MAAM,EAAE,CAAChM,qBAAqB,CAACC,QAAD,EAAW,cAAX,CAAtB,GAAkD,aAAlD,GAAgE;AAAxG,WAArE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAA0L7B,KAA1L,CADA,CADF,CADF;AAOD,OATI;AAWLwH,MAAAA,EAAE,EAAGxH,KAAD,IAAmB;AACrB,4BACE;AAAK,UAAA,SAAS,EAAC,sCAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAS,UAAA,KAAK,EAAC,aAAf;AAA6B,UAAA,SAAS,EAAC,OAAvC;AAA+C,UAAA,KAAK,eAAE,oBAAC,YAAD;AAAc,YAAA,KAAK,EAAE;AAAE6N,cAAAA,KAAK,EAAE,SAAT;AAAoBC,cAAAA,QAAQ,EAAE;AAA9B,aAArB;AAA6D,YAAA,OAAO,EAAE,MAAMzP,eAAe,CAAC2B,KAAD,CAA3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAAtD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAM,UAAA,SAAS,EAAC,eAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WAAiCA,KAAjC,CADF,CADF,CADF;AAOD,OAnBI;AAoBL+N,MAAAA,OAAO,EAAG/N,KAAD,IAAmB;AAC1B,4BAAO,oBAAC,iBAAD;AAAmB,UAAA,KAAK,EAAEA,KAA1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAAP;AACD,OAtBI;AAuBLgO,MAAAA,SAAS,EAAGhO,KAAD,IAAgB;AACzB,4BACE;AAAK,UAAA,SAAS,EAAG,GAAE5D,MAAM,CAACqR,sBAAuB,gBAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAS,UAAA,SAAS,EAAG,GAAErR,MAAM,CAACqR,sBAAuB,gBAArD;AAAsE,UAAA,SAAS,EAAC,SAAhF;AAA0F,UAAA,KAAK,EAAEzN,KAAF,aAAEA,KAAF,uBAAEA,KAAK,CAAEY,IAAxG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WACGZ,KADH,aACGA,KADH,uBACGA,KAAK,CAAEY,IADV,CADF,CADF;AAOD,OA/BI;AAgCLqN,MAAAA,QAAQ,EAAGjO,KAAD,IAAgB;AACxB,eAAOA,KAAK,gBACV;AAAK,UAAA,SAAS,EAAG,GAAE5D,MAAM,CAACqR,sBAAuB,gBAAjD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAS,UAAA,SAAS,EAAG,GAAErR,MAAM,CAACqR,sBAAuB,gBAArD;AAAsE,UAAA,SAAS,EAAC,SAAhF;AAA0F,UAAA,KAAK,EAAEzN,KAAF,aAAEA,KAAF,uBAAEA,KAAK,CAAEY,IAAxG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WACGZ,KADH,aACGA,KADH,uBACGA,KAAK,CAAEY,IADV,CADF,CADU,GAOV,KAPF;AASD,OA1CI;AA2CLD,MAAAA,MAAM,EAAGX,KAAD,IAAgB;AACtB,eAAOA,KAAK,GAAG0B,qBAAqB,CAAC1B,KAAK,CAACY,IAAP,CAAxB,GAAuCc,qBAAqB,CAAC,KAAD,CAAxE;AACD,OA7CI;AA8CLwM,MAAAA,IAAI,EAAE9B,wBA9CD;AA+CL+B,MAAAA,QAAQ,EAAGnO,KAAD,IAAmB;AAC3B,4BAAO,oBAAC,iBAAD;AAAmB,UAAA,KAAK,EAAEA,KAA1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAAP;AACD,OAjDI;AAkDL;AACA;AACA;AACA;AACA;AACA;AACAqB,MAAAA,IAAI,EAAE;AACJ+M,QAAAA,KAAK,EAAE,GADH;AAEJC,QAAAA,MAAM,EAAE,CAACrO,KAAD,EAAa+J,MAAb,KAA6B;AAAA;;AACnC,gBAAMuE,kBAAkB,GAAG,EAA3B,CADmC,CACJ;;AAC/B,gBAAMC,sBAAsB,GAAG,CAA/B,CAFmC,CAED;;AAClC,gBAAMC,UAAU,4BAAGhC,YAAY,CAACzC,MAAM,CAACvC,EAAR,CAAf,yEAA8B,KAA9C;;AAEA,gBAAMiH,oBAAoB,GAAItE,QAAD,IAA+B;AAAA;;AAC1D,kBAAM8C,SAAS,GAAGP,aAAa,CAACQ,OAAd,CAAsB/C,QAAtB,CAAlB;AACA,gBAAI,CAAC8C,SAAL,EAAgB,OAAO,KAAP;AAEhB,kBAAME,YAAY,4BAAGF,SAAS,CAACE,YAAb,yEAA6B,CAA/C;AACA,kBAAMC,YAAY,4BAAGH,SAAS,CAACG,YAAb,yEAA6B,CAA/C;AAEA,kBAAMsB,QAAQ,GAAGvB,YAAY,GAAGC,YAAf,IAA+BmB,sBAAhD;AACA,kBAAMI,QAAQ,GAAGxB,YAAY,GAAGmB,kBAAhC;AAEA,mBAAOI,QAAQ,IAAIC,QAAnB;AACD,WAXD;;AAaA,gBAAMC,WAAgB,GAAG,EAAzB;AAEA5O,UAAAA,KAAK,SAAL,IAAAA,KAAK,WAAL,YAAAA,KAAK,CAAE6O,OAAP,CAAe,CAAC7O,KAAD,EAAa8O,KAAb,KAA+B;AAC5CF,YAAAA,WAAW,CAAC5O,KAAK,CAACY,IAAP,CAAX,GAA0B2L,MAAM,CAACuC,KAAK,GAAGvC,MAAM,CAAClE,MAAhB,CAAhC;AACD,WAFD;AAIA,8BACE;AACE,YAAA,GAAG,EAAG0G,EAAD,IAAQ;AACX,kBAAIA,EAAJ,EAAQ;AACNrC,gBAAAA,aAAa,CAACQ,OAAd,CAAsBnD,MAAM,CAACvC,EAA7B,IAAmCuH,EAAnC;AACA/B,gBAAAA,aAAa,CAACjD,MAAM,CAACvC,EAAR,CAAb;AACD;AACF,aANH;AAOE,YAAA,SAAS,EAAG,iBAAgBgH,UAAU,GAAGpS,MAAM,CAAC4S,SAAV,GAAsB5S,MAAM,CAAC6S,UAAW,EAPhF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aASGjP,KATH,aASGA,KATH,uBASGA,KAAK,CAAEsI,GAAP,CAAY4G,CAAD,iBAAY;AAAK,YAAA,KAAK,EAAEN,WAAW,CAACM,CAAC,CAACtO,IAAH,CAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAAkCsO,CAAlC,aAAkCA,CAAlC,uBAAkCA,CAAC,CAAEtO,IAArC,CAAvB,CATH,EAWG6N,oBAAoB,CAAC1E,MAAM,CAACvC,EAAR,CAApB,iBACC;AACE,YAAA,OAAO,EAAE,MAAM;AACbiF,cAAAA,eAAe,CAAE0C,IAAD,KAAW,EACzB,GAAGA,IADsB;AAEzB,iBAACpF,MAAM,CAACvC,EAAR,GAAa,CAACgH;AAFW,eAAX,CAAD,CAAf,CADa,CAMb;;AACAY,cAAAA,qBAAqB,CAAC,MAAM;AAC1B,sBAAMnC,SAAS,GAAGP,aAAa,CAACQ,OAAd,CAAsBnD,MAAM,CAACvC,EAA7B,CAAlB;;AACA,oBAAIyF,SAAJ,EAAe;AACbI,kBAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ;AACAD,kBAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BL,SAAS,CAACG,YAAvC;AACAC,kBAAAA,OAAO,CAACC,GAAR,CAAY,eAAZ,EAA6BL,SAAS,CAACE,YAAvC;AACD;AACF,eAPoB,CAArB;AAQD,aAhBH;AAiBE,YAAA,KAAK,EAAE;AACLkC,cAAAA,QAAQ,EAAE,UADL;AAELC,cAAAA,KAAK,EAAE,CAFF;AAGL1B,cAAAA,MAAM,EAAE,SAHH;AAIL2B,cAAAA,UAAU,EAAE;AAJP,aAjBT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,aAwBGf,UAAU,gBAAG,oBAAC,UAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAAH,gBAAoB,oBAAC,aAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAxBjC,CAZJ,CADF;AA0CD;AApEG,OAxDD;AA8HLgB,MAAAA,QAAQ,EAAGxP,KAAD,IAAgB;AACxB,4BACE;AAAG,UAAA,SAAS,EAAG,GAAE5D,MAAM,CAACqT,gBAAiB,gBAAzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAQ,UAAA,SAAS,EAAG,GAAErT,MAAM,CAACsT,YAAa,gBAA1C;AAA2D,UAAA,OAAO,EAAE,MAAM9H,kBAAkB,CAAC,UAAD,EAAa5H,KAAb,CAA5F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,WACG,MADH,CADF,CADF;AAOD,OAtII;AAuIL2P,MAAAA,IAAI,EAAEvD,wBAvID;AAwILL,MAAAA,cAAc,EAAE,CAAC/L,KAAD,EAAgB+J,MAAhB,KAAgC;AAC9C,eAAOA,MAAM,CAAC6F,gBAAP,CAAwBhP,IAAxB,KAAiC,WAAjC,GAA+C,EAA/C,gBAAoD,oBAAC,iBAAD;AAAmB,UAAA,KAAK,EAAEZ,KAA1B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAA3D;AACD,OA1II;AA2IL6P,MAAAA,aAAa,EAAEzD,wBA3IV;AA4IL0D,MAAAA,MAAM,EAAE,CAACC,IAAD,EAAYhG,MAAZ,KAA4B;AAClC,4BACE;AAAK,UAAA,SAAS,EAAG,GAAE3N,MAAM,CAAC4T,mBAAoB,gBAA9C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AAAS,UAAA,SAAS,EAAC,eAAnB;AAAmC,UAAA,KAAK,EAAC,UAAzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AACE,UAAA,SAAS,EAAC,eADZ;AAEE,UAAA,QAAQ,EAAE,CAACnP,UAAU,CAACkJ,MAAM,CAACpJ,MAAR,CAAX,IAA8B,CAACiF,cAAc,CAACqK,8BAA9C,IAAgF,CAACpR,aAAa,CAAC2G,UAAD,EAAa,qBAAb,CAA9F,IAAmI,CAAC5D,qBAAqB,CAACC,QAAD,EAAW,eAAX,CAFrK;AAGE,UAAA,OAAO,EAAE,MAAMwI,aAAa,CAACN,MAAM,CAACvC,EAAR,CAH9B;AAIE,UAAA,IAAI,eAAE,oBAAC,mBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAJR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UADF,CADF,eASE;AAAS,UAAA,SAAS,EAAC,eAAnB;AAAmC,UAAA,KAAK,EAAC,MAAzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AACE,UAAA,SAAS,EAAC,eADZ;AAEE,UAAA,QAAQ,EAAE0C,cAAc,CAACH,MAAM,CAACvC,EAAR,CAAd,IAA6B9G,eAAe,CAACqJ,MAAM,CAACpJ,MAAR,CAA5C,IAA+D,CAACiF,cAAc,CAACqK,8BAA/E,IAAiHlG,MAAM,CAAC9C,MAFpI;AAGE,UAAA,OAAO,EAAE,MAAM;AACb5C,YAAAA,kBAAkB,CAAC,EAAD,CAAlB,CADa,CAEb;;AACAkF,YAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACAtG,YAAAA,gBAAgB,CAAC,CAAC6G,MAAD,CAAD,CAAhB;AACA3G,YAAAA,qBAAqB,CAAC,IAAD,CAArB;AACD,WATH;AAUE,UAAA,IAAI,eAAE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YAVR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UADF,CATF,eAuBE;AAAS,UAAA,SAAS,EAAC,eAAnB;AAAmC,UAAA,KAAK,EAAC,SAAzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,wBACE;AACE,UAAA,SAAS,EAAC,eADZ;AAEE,UAAA,QAAQ,EAAE8G,cAAc,CAACH,MAAM,CAACvC,EAAR,CAF1B;AAGE,UAAA,OAAO,EAAE,MAAM;AACb+B,YAAAA,YAAY,CAACxG,eAAb,GAA+B,EAA/B;AACAwG,YAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACAvH,YAAAA,KAAK,CAACiO,OAAN,CAAcC,IAAd,CAAmBC,SAAS,CAAE,qBAAoB/N,MAAO,cAAaC,QAAS,IAAGnB,QAAS,IAAGqB,UAAW,IAAGD,SAAU,YAAWwH,MAAM,CAACvC,EAAG,EAA/G,CAA5B;AACD,WAPH;AAQE,UAAA,IAAI,eAAE,oBAAC,eAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,YARR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UADF,CAvBF,CADF;AAsCD;AAnLI,KAAP;AAqLD,GAtLD;;AAwLA,QAAM6I,kBAAkB,GAAG,MAAM;AAC/B,WAAO,CACL;AAAEvH,MAAAA,GAAG,EAAE,QAAP;AAAiB9I,MAAAA,KAAK,EAAEqC;AAAxB,KADK,EAEL;AAAEyG,MAAAA,GAAG,EAAE,cAAP;AAAuB9I,MAAAA,KAAK,EAAEyC;AAA9B,KAFK,EAGL;AAAEqG,MAAAA,GAAG,EAAE,UAAP;AAAmB9I,MAAAA,KAAK,EAAEmB;AAA1B,KAHK,CAAP;AAKD,GAND;;AAQA,QAAMmP,cAAc,GAAItQ,KAAD,IAAgB;AACrC,QAAIA,KAAJ,EAAW;AACT0D,MAAAA,gBAAgB,CAAC,IAAD,CAAhB;AACAvF,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,gCAAP,CAAnB;AACD;AACF,GALD;;AAOA,WAASoS,SAAT,GAAqB;AACnB1L,IAAAA,oBAAoB,CAAC,EAAD,CAApB;AACAZ,IAAAA,cAAc,CAAC,IAAD,CAAd;AACA0E,IAAAA,QAAQ;AACR1E,IAAAA,cAAc,CAAC,KAAD,CAAd;AACAsF,IAAAA,YAAY,CAACxG,eAAb,GAA+B,EAA/B;AACAwG,IAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACD;;AAGD,QAAMgH,UAAU,GAAG,OAAOhM,KAAP,EAAmBiM,gBAAnB,EAA0CC,WAA1C,KAA+D;AAAA;;AAChF,QAAI,CAAC5D,YAAL,EAAmB;AACjB,aAAO6D,SAAS,CAACnM,KAAD,EAAQiM,gBAAR,EAA0BC,WAA1B,CAAhB;AACD;;AAED,UAAME,YAAwB,0BAAGpM,KAAK,CAACqM,OAAN,CAAclH,IAAd,CAAoBmH,MAAD,IAAiBA,MAAM,CAAChI,GAAP,KAAe,QAAnD,CAAH,wDAAG,oBAA8DiI,WAA/F;;AACA,QAAI,CAACH,YAAL,EAAmB;AACjB,aAAOD,SAAS,CAACnM,KAAD,EAAQiM,gBAAR,EAA0BC,WAA1B,CAAhB;AACD;;AAED,UAAMM,OAAO,GAAG,EACd,GAAGP,gBADW;AAEd9P,MAAAA,MAAM,EAAE8P,gBAAgB,CAAC9P,MAAjB,GAA0B8P,gBAAgB,CAAC9P,MAA3C,GAAoDiQ,YAAY,CAACtI,GAAb,CAAiB2I,CAAC,IAAIA,CAAC,CAACjR,KAAxB;AAF9C,KAAhB;AAIA,WAAO2Q,SAAS,CAACnM,KAAD,EAAQwM,OAAR,EAAiBN,WAAjB,CAAhB;AACD,GAfD;;AAiBA,QAAMQ,YAAY,GAAG,YAAY;AAC/B,UAAMnJ,QAAQ,GAAG,MAAM1I,UAAU,CAACrD,MAAM,CAACmV,GAAP,CAAWvS,uBAAuB,CAACwS,qBAAnC,EAA0DC,KAA3D,EAAkE/J,QAAlE,EAA4E,CAAC;AAAEwB,MAAAA,GAAG,EAAE,UAAP;AAAmB9I,MAAAA,KAAK,EAAEmB;AAA1B,KAAD,CAA5E,CAAjC;AAEA,WAAO2L,YAAY,GAAG/E,QAAH,GAAcA,QAAQ,CAAC/G,IAAT,CAAcsH,GAAd,CAAmBwI,MAAD,IAAiB;AAClE,UAAIA,MAAM,CAAChI,GAAP,KAAe,QAAnB,EAA6B;AAC3B,eAAO,EACL,GAAGgI,MADE;AAELQ,UAAAA,UAAU,EAAER,MAAM,CAACQ,UAAP,CAAkBvK,MAAlB,CAA0BwK,IAAD,IAAeA,IAAI,CAAC3Q,IAAL,KAAc,eAAtD;AAFP,SAAP;AAID;;AAED,aAAOkQ,MAAP;AACD,KATgC,CAAjC;AAUD,GAbD;;AAeA,QAAMH,SAAS,GAAG7V,WAAW,CAC3B,OAAO0J,KAAP,EAAmBiM,gBAAnB,EAA0CC,WAA1C,KAA+D;AAC7D,WAAOpR,UAAU,CACftD,MAAM,CAACmV,GAAP,CAAWvS,uBAAuB,CAACwS,qBAAnC,EAA0DC,KAD3C,EAEf;AACEG,MAAAA,UAAU,EAAE;AACVtE,QAAAA,OAAO,EAAE1I,KAAK,CAACgN,UAAN,CAAiBtE,OADhB;AAEVuE,QAAAA,QAAQ,EAAEjN,KAAK,CAACgN,UAAN,CAAiBC;AAFjB,OADd;AAKEC,MAAAA,MAAM,EAAElN,KAAK,CAACkN,MAAN,GAAe;AAAE5I,QAAAA,GAAG,EAAEtE,KAAK,CAACkN,MAAN,CAAaC,SAApB;AAA+B1R,QAAAA,KAAK,EAAEuE,KAAK,CAACkN,MAAN,CAAazR;AAAnD,OAAf,GAA4E,EALtF;AAME+Q,MAAAA,OAAO,EAAEP,gBANX;AAOEI,MAAAA,OAAO,EAAErM,KAAK,CAACqM,OAAN,CACN9J,MADM,CACE6K,CAAD,IAAYA,CAAC,CAACC,OAAF,KAAc,KAAd,IAAuBD,CAAC,CAACE,QAAF,KAAe,IADnD,EAENxJ,GAFM,CAEDsJ,CAAD,IAAYA,CAAC,CAAC9I,GAFZ;AAPX,KAFe,EAaf4H,WAbe,CAAjB;AAeD,GAjB0B,EAkB3B,EAlB2B,CAkBxB;AAlBwB,GAA7B,CAvrB2B,CA4sB3B;;AACA,QAAMqB,gBAAgB,GAAGxS,QAAQ,CAC/B,CAAC0C,KAAD,EAAajC,KAAb,EAA4BgS,QAA5B,KAA8D;AAC5D1T,IAAAA,MAAM,CAAC0K,IAAP,CAAY,kBAAZ,EAAgC,kBAAhC,EAAoD;AAClD/G,MAAAA,KADkD;AAElDgQ,MAAAA,SAAS,EAAEhQ,KAAK,CAACjB,IAFiC;AAGlDhB,MAAAA;AAHkD,KAApD;AAMAZ,IAAAA,sBAAsB,CACpBpD,MAAM,CAACmV,GAAP,CAAWvS,uBAAuB,CAACwS,qBAAnC,EAA0DC,KADtC,EAEpBpP,KAAK,CAACjB,IAAN,CAAW8H,GAFS,EAGpB9I,KAHoB,EAIpBiC,KAAK,CAACiQ,qBAJc,CAAtB,CAMGtJ,IANH,CAMS5H,IAAD,IAAe;AACnBgR,MAAAA,QAAQ,CAAChR,IAAI,CAACA,IAAN,CAAR;AACD,KARH,EASGmI,KATH,CASS,MAAM;AACX6I,MAAAA,QAAQ,CAAC,EAAD,CAAR;AACD,KAXH;AAYD,GApB8B,EAqB/BhW,MAAM,CAACmW,qBArBwB,CAAjC,CA7sB2B,CAquB3B;;AACA,QAAMC,oBAAoB,GAAG,CAACnQ,KAAD,EAAajC,KAAb,EAA4BgS,QAA5B,KAA8C;AACzED,IAAAA,gBAAgB,CAAC9P,KAAD,EAAQjC,KAAR,EAAgBgB,IAAD,IAAe;AAC5CgR,MAAAA,QAAQ,CAAChR,IAAD,CAAR;AACD,KAFe,CAAhB;AAGD,GAJD;;AAMA,QAAMqR,2BAA2B,GAAG,MAAM;AACxC3N,IAAAA,QAAQ,CAACvH,+BAA+B,CAAC,KAAD,CAAhC,CAAR;AACAiJ,IAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD,GAHD;;AAKA,QAAMkM,cAAc,GAAG,CAACrP,aAAD,EAAyBsP,MAAzB,KAA6C;AAClEjU,IAAAA,MAAM,CAAC4K,KAAP,CAAa,kBAAb,EAAiC,gBAAjC,EAAmD;AAAEjG,MAAAA;AAAF,KAAnD;AACA,UAAMuP,OAAO,GAAGvP,aAAa,CAACqF,GAAd,CAAmB2I,CAAD,IAAOA,CAAC,CAACzJ,EAA3B,CAAhB;AACAlL,IAAAA,aAAa,CAACkW,OAAD,EAAUD,MAAV,CAAb,CACG3J,IADH,CACSb,QAAD,IAAc;AAClB,UAAIA,QAAJ,EAAc;AACZrE,QAAAA,gBAAgB,CAAC,IAAD,CAAhB;AACAvF,QAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAQ,WAAUoU,MAAM,GAAG,QAAH,GAAc,UAAW,eAAjD,CAAnB;AACD;AACF,KANH,EAOGpJ,KAPH,CAOUT,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACX,QAAN,CAAepH,MAAf,KAA0BpC,oBAA9B,EAAoD;AAClDL,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,qFAAP,CAAjB;AACD,OAFD,MAEO;AACLA,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAQ,WAAUqU,MAAM,GAAG,QAAH,GAAc,UAAW,SAAjD,CAAjB;AACD;AACF,KAbH;AAcD,GAjBD;;AAmBA,sBACE,oBAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,SAAD;AACE,IAAA,KAAK,EAAEjJ,kBAAkB,CAAC9G,UAAD,CAD3B;AAEE,IAAA,IAAI,EAAEX,QAAQ,iBAAI,oBAAC,SAAD;AAAW,MAAA,KAAK,EAAEA,QAAQ,CAAClB,MAA3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAFpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAIE,oBAAC,mBAAD;AAAqB,IAAA,MAAM,EAAE0B,MAA7B;AAAqC,IAAA,QAAQ,EAAEiH,kBAAkB,CAAChH,QAAD,CAAjE;AAA6E,IAAA,SAAS,EAAEC,SAAxF;AAAmG,IAAA,WAAW,EAAE,UAAhH;AAA4H,IAAA,aAAa,EAAEX,qBAAqB,CAACC,QAAD,EAAW,eAAX,CAAhK;AAA6L,IAAA,QAAQ,EAAEV;AAAvM,KAAqNc,KAArN;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAJF,CADF,eAOE,oBAAC,WAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AACE,IAAA,MAAM,EACH,CAAC2D,cAAc,CAAC6M,qBAAhB,IAAyC,CAAC5T,aAAa,CAAC2G,UAAD,EAAa,oBAAb,CAAxD,IACA,CAAC5D,qBAAqB,CAACC,QAAD,EAAW,YAAX,CAH1B;AAKE,IAAA,SAAS,EAAEzF,MAAM,CAACsW,uBALpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAME,oBAAC,QAAD;AAAU,IAAA,MAAM,EAAErQ,MAAlB;AAA0B,IAAA,QAAQ,EAAElB,QAApC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IANF,CADF,eASE;AAAK,IAAA,SAAS,EAAEgB,MAAM,GAAI,GAAE/F,MAAM,CAACuW,6BAA8B,EAA3C,GAAgD,GAAEvW,MAAM,CAACwW,4BAA6B,EAA5G;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACG,CAACzQ,MAAD,iBAAW;AAAK,IAAA,SAAS,EAAE/F,MAAM,CAACyW,iBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACV,oBAAC,UAAD;AAAY,IAAA,cAAc,EAAG/H,aAAD,IAAmBD,eAAe,CAACC,aAAD,CAA9D;AAA+E,IAAA,IAAI,EAAEtF,UAArF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADU,CADd,eAKE;AAAK,IAAA,SAAS,EAAEpJ,MAAM,CAAC0W,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,mBAAD;AACE,IAAA,cAAc,EAAExC,cADlB;AAEE,IAAA,QAAQ,EAAGyC,OAAD,IAAsB;AAC9B,UAAIA,OAAJ,EAAa;AACX9O,QAAAA,cAAc,CAAC,IAAD,CAAd;AACA0E,QAAAA,QAAQ;AACR1E,QAAAA,cAAc,CAAC,KAAD,CAAd;AACAsF,QAAAA,YAAY,CAACxG,eAAb,GAA+B,EAA/B;AACAwG,QAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACD;AACF,KAVH;AAWE,IAAA,0BAA0B,EAAEtC,0BAX9B;AAYE,IAAA,YAAY,EACVjE,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuExE,cAAvE,IAAyFjC,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAblH;AAeE,IAAA,UAAU,EACRoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAhBhG;AAkBE,IAAA,YAAY,EACVoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,cAAX,CAnBhG;AAqBE,IAAA,UAAU,EACRoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAtBhG;AAwBE,IAAA,UAAU,EACRoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAzBhG;AA2BE,IAAA,aAAa,EACXoB,aAAa,CAACoF,MAAd,KAAyB,CAAzB,IAA8BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAApE,IAAyEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA5BlG;AA8BE,IAAA,gBAAgB,EACdoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA/BhG;AAiCE,IAAA,aAAa,EACXoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAlChG;AAoCE,IAAA,aAAa,EACXoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEpF,aAAa,CAACkE,IAAd,CAAoBmF,IAAD,IAAU,CAACA,IAAI,CAACrF,MAAnC,CAAvE,IAAqHrF,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CArC9I;AAuCE,IAAA,eAAe,EACboB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEpF,aAAa,CAAC8D,MAAd,CAAsBC,CAAD,IAAO,CAACA,CAAC,CAACC,MAA/B,EAAuCoB,MAAvC,KAAkD,CAAzH,IAA8HzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAxCvJ;AA0CE,IAAA,WAAW,EACToB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEpF,aAAa,CAACkE,IAAd,CAAoBmF,IAAD,IAAU,CAACA,IAAI,CAAC0G,SAAnC,CAAvE,IAAwHpR,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA3CjJ;AA6CE,IAAA,aAAa,EACXoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEpF,aAAa,CAAC8D,MAAd,CAAsBC,CAAD,IAAO,CAACA,CAAC,CAACgM,SAA/B,EAA0C3K,MAA1C,KAAqD,CAA5H,IAAiIzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA9C1J;AAgDE,IAAA,cAAc,EACZoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAjDhG;AAmDE,IAAA,UAAU,EACRoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CApDhG;AAsDE,IAAA,QAAQ,EACNoB,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAvDhG;AAyDE,IAAA,gBAAgB,EAAEoB,aAzDpB;AA0DE,IAAA,MAAM,EAAEZ,MA1DV;AA2DE,IAAA,iBAAiB,EAAG2E,CAAD,IAAO;AACxB5E,MAAAA,SAAS,CAAC,CAACD,MAAF,CAAT;AACD,KA7DH;AA8DE,IAAA,YAAY,EAAEc,aAAa,CAACoF,MAAd,GAAuB,CAAvB,IAA4BkB,YAAY,CAACxG,eAAb,CAA6BsF,MAA7B,GAAsC,CAAlE,IAAuEzG,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA9D5G;AA+DE,IAAA,cAAc,EAAED,qBAAqB,CAACC,QAAD,EAAW,YAAX,CA/DvC;AAgEE,IAAA,iBAAiB,EAAED,qBAAqB,CAACC,QAAD,EAAW,eAAX,CAhE1C;AAiEE,IAAA,qBAAqB,eAAE,oBAAC,aAAD;AAAe,MAAA,OAAO,EAAEiL,YAAxB;AAAsC,MAAA,QAAQ,EAAEC,eAAhD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAjEzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAqEE,oBAAC,KAAD,oBAAW3H,cAAX;AAA2B,IAAA,IAAI,EAAE,OAAjC;AAA0C,IAAA,QAAQ,EAAEyC,kBAApD;AAAwE,IAAA,MAAM,EAAE,IAAhF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBACE;AAAK,IAAA,SAAS,EAAEzL,MAAM,CAAC6W,gBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACG7N,cAAc,CAACG,SAAf,iBACC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACGH,cAAc,CAACG,SAAf,CAAyB+C,GAAzB,CAA8BsJ,CAAD,iBAC5B,0BAAM,IAAN;AAAW,IAAA,GAAG,EAAEA,CAAC,CAAC5R,KAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KAA0B4R,CAAC,CAAChR,IAA5B,CADD,CADH,CAFJ,EAQGwE,cAAc,CAACG,SAAf,CAAyB8C,MAAzB,KAAoC,CAApC,IAA0C,MAAKjD,cAAc,CAAClE,KAAM,WARvE,CADF,CArEF,eAmFE,oBAAC,KAAD;AACE,IAAA,KAAK,EAAE;AAAEgS,MAAAA,GAAG,EAAE;AAAP,KADT;AAEE,IAAA,OAAO,EAAE/P,kBAFX;AAGE,IAAA,KAAK,EAAE,wBAHT;AAIE,IAAA,QAAQ,EAAEyH,yBAJZ;AAKE,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,GAAG,EAAC,MAAZ;AAAmB,MAAA,IAAI,EAAC,SAAxB;AAAkC,MAAA,OAAO,EAAEA,yBAA3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADM,eAIN;AAAQ,MAAA,OAAO,EAAEhE,OAAjB;AAA0B,MAAA,GAAG,EAAC,QAA9B;AAAuC,MAAA,IAAI,EAAC,SAA5C;AAAsD,MAAA,OAAO,EAAEiF,0BAA/D;AAA2F,MAAA,QAAQ,EAAE,CAACxI,cAAtG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAJM,CALV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAcE;AAAK,IAAA,SAAS,EAAEjH,MAAM,CAAC+W,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,WAAD;AACE,IAAA,QAAQ,EAAElQ,aADZ;AAEE,IAAA,aAAa,EAAE,MAAM,CAAG,CAF1B;AAGE,IAAA,MAAM,EAAEZ,MAHV;AAIE,IAAA,QAAQ,EAAElB,QAJZ;AAKE,IAAA,IAAI,EAAE+C,cALR;AAME,IAAA,YAAY,EAAE,CAACkP,YAAD,EAAeC,gBAAf,KAAoC;AAChD/P,MAAAA,iBAAiB,CAAC8P,YAAD,CAAjB;AACA5P,MAAAA,yBAAyB,CAAC6P,gBAAD,CAAzB;AACD,KATH;AAUE,IAAA,cAAc,EAAE9O,yBAVlB;AAWE,IAAA,aAAa,EAAEF,kBAXjB;AAYE,IAAA,YAAY,EAAE,CAACxF,aAAa,CAAC2G,UAAD,EAAa,qBAAb,CAAd,IAAqD,CAAC5D,qBAAqB,CAACC,QAAD,EAAW,UAAX,CAZ3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CAdF,CAnFF,eAmHE,oBAAC,YAAD;AACE,IAAA,MAAM,EAAEQ,MADV;AAEE,IAAA,QAAQ,EAAElB,QAFZ;AAGE,IAAA,MAAM,EAAEyD,iBAHV;AAIE,IAAA,OAAO,EAAE,MAAMC,oBAAoB,CAAC,EAAD,CAJrC;AAKE,IAAA,SAAS,EAAE0L,SALb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAnHF,eA2HE;AACE,IAAA,SAAS,EAAC,uBADZ;AAEE,IAAA,QAAQ,EAAE,MAAM;AACdpL,MAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD,KAJH;AAKE,IAAA,YAAY,EAAE,MAAM;AAClBA,MAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD,KAPH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBASE,oBAAC,WAAD;AACE,IAAA,qBAAqB,EACnBS,cAAc,CAACiE,sCAAf,IACA,CAAClG,YADD,IAEAE,cAFA,IAGAhF,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAHb,IAIA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CANzB;AAQE,IAAA,mBAAmB,EACjB+D,cAAc,CAAC0N,0BAAf,IACAzP,cADA,IAEAhF,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAZzB;AAcE,IAAA,yBAAyB,EACvB+D,cAAc,CAACiE,sCAAf,IACAlG,YADA,IAEAV,aAAa,CAACoF,MAAd,KAAyB,CAFzB,IAGAxJ,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAHb,IAIA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAnBzB;AAqBE,IAAA,mBAAmB,EACjB+D,cAAc,CAAC2N,qBAAf,IACA1P,cADA,IAEAhF,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAzBzB;AA2BE,IAAA,qBAAqB,EACnB+D,cAAc,CAAC4N,sBAAf,IACA3U,aAAa,CAAC2G,UAAD,EAAa,oBAAb,CADb,IAEA5D,qBAAqB,CAACC,QAAD,EAAW,cAAX,CA9BzB;AAgCE,IAAA,sBAAsB,EACpBoB,aAAa,CAACoF,MAAd,KAAyB,CAAzB,IACAxJ,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CADb,IAEA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAnCzB;AAqCE,IAAA,mBAAmB,EACjB+D,cAAc,CAACqK,8BAAf,IACApM,cADA,IAEAhF,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAzCzB;AA2CE,IAAA,yBAAyB,EACvB+D,cAAc,CAAC6N,2BAAf,IACA5P,cADA,IAEAhF,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA/CzB;AAiDE,IAAA,iBAAiB,EACf+D,cAAc,CAAC8N,mBAAf,IACA,CAAC/P,YADD,IAEA9E,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CArDzB;AAuDE,IAAA,kBAAkB,EAChB+D,cAAc,CAAC4N,sBAAf,IACA,CAAC7P,YADD,IAEA9E,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA3DzB;AA6DE,IAAA,iBAAiB,EACf+D,cAAc,CAAC+N,mBAAf,IACA9U,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CADb,IAEA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAhEzB;AAkEE,IAAA,mBAAmB,EACjB+D,cAAc,CAACgO,qBAAf,IACA,CAACjQ,YADD,IAEA9E,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAtEzB;AAwEE,IAAA,uBAAuB,EACrB+D,cAAc,CAAC4N,sBAAf,IACA3U,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CADb,IAEA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA3EzB;AA6EE,IAAA,oBAAoB,EAAEoB,aAAa,CAACkE,IAAd,CAAoBH,CAAD,IAAO,CAACA,CAAC,CAACC,MAA7B,CA7ExB;AA8EE,IAAA,yBAAyB,EAAEhE,aAAa,CAACkE,IAAd,CAAoBH,CAAD,IAAO,CAACA,CAAC,CAACgM,SAA7B,CA9E7B;AA+EE,IAAA,oBAAoB,EAClB,CAACrP,YAAD,IACAE,cADA,IAEA+B,cAAc,CAACiO,uCAFf,IAGAhV,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAHb,IAIA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CApFzB;AAsFE,IAAA,iBAAiB,EACf,CAAC8B,YAAD,IACAE,cADA,IAEA+B,cAAc,CAACkO,yBAFf,IAGAjV,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAHb,IAIA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CA3FzB;AA6FE,IAAA,kBAAkB,EAChBgC,cAAc,IACdZ,aAAa,CAAC8D,MAAd,CAAsBC,CAAD,IAAOA,CAAC,CAACrG,MAAF,CAASX,KAAT,KAAmB,CAA/C,EAAkDqI,MAAlD,IAA4D,CAD5D,IAEAxJ,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAFb,IAGA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAjGzB;AAmGE,IAAA,qBAAqB,EACnBhD,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAAb,IACA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CArGzB;AAuGE,IAAA,qBAAqB,EACnBhD,aAAa,CAAC2G,UAAD,EAAa,yBAAb,CAAb,IACA5D,qBAAqB,CAACC,QAAD,EAAW,2BAAX,CAzGzB;AA2GE,IAAA,eAAe,EAAE,MAAM;AACrBK,MAAAA,aAAa,CAAC/E,+BAA+B,CAAC,IAAD,CAAhC,CAAb;AACD,KA7GH;AA8GE,IAAA,aAAa,EAAE,MAAM+E,aAAa,CAACxE,6BAA6B,CAAC,IAAD,CAA9B,CA9GpC;AA+GE,IAAA,aAAa,EAAE,MAAMwE,aAAa,CAAClF,6BAA6B,CAAC,IAAD,CAA9B,CA/GpC;AAgHE,IAAA,mBAAmB,EAAE,MAAMkF,aAAa,CAAC5E,mCAAmC,CAAC,IAAD,CAApC,CAhH1C;AAiHE,IAAA,gBAAgB,EAAE,MAAM4E,aAAa,CAAC3E,gCAAgC,CAAC,IAAD,CAAjC,CAjHvC;AAkHE,IAAA,kBAAkB,EAAE,MAAM2E,aAAa,CAAC1E,kCAAkC,CAAC,IAAD,CAAnC,CAlHzC;AAmHE,IAAA,gBAAgB,EAAE,MAAM0E,aAAa,CAACzE,gCAAgC,CAAC,IAAD,CAAjC,CAnHvC;AAoHE,IAAA,kBAAkB,EAAE,MAAMyE,aAAa,CAACrE,gCAAgC,CAAC,IAAD,CAAjC,CApHzC;AAqHE,IAAA,oBAAoB,EAAE,MAAMqE,aAAa,CAACpE,kCAAkC,CAAC,IAAD,CAAnC,CArH3C;AAsHE,IAAA,eAAe,EAAE,MAAM;AACrBoE,MAAAA,aAAa,CAACjF,+BAA+B,CAAC,IAAD,CAAhC,CAAb;AACD,KAxHH;AAyHE,IAAA,qBAAqB,EAAE,MAAM;AAC3BsN,MAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACD,KA3HH;AA4HE,IAAA,aAAa,EAAE,MAAMrI,aAAa,CAAChF,6BAA6B,CAAC,IAAD,CAA9B,CA5HpC;AA6HE,IAAA,kBAAkB,EAAE,MAAM;AACxBgF,MAAAA,aAAa,CAAC9E,gCAAgC,CAAC,IAAD,CAAjC,CAAb;AACD,KA/HH;AAgIE,IAAA,cAAc,EAAE,MAAM;AACpB8E,MAAAA,aAAa,CAAC7E,6BAA6B,CAAC,IAAD,CAA9B,CAAb;AACD,KAlIH;AAmIE,IAAA,gBAAgB,EAAE,MAAM;AACtB6E,MAAAA,aAAa,CAACvE,+BAA+B,CAAC,IAAD,CAAhC,CAAb;AACD,KArIH;AAsIE,IAAA,kBAAkB,EAAE,MAAM;AACxBuE,MAAAA,aAAa,CAACnE,4BAA4B,CAAC,IAAD,CAA7B,CAAb;AACD,KAxIH;AAyIE,IAAA,UAAU,EAAE,MAAM;AAChBmE,MAAAA,aAAa,CAAClE,8BAA8B,CAAC,IAAD,CAA/B,CAAb;AACD,KA3IH;AA4IE,IAAA,KAAK,EAAE,MAAM;AACXsU,MAAAA,cAAc,CAACrP,aAAD,EAAgB,IAAhB,CAAd;AACD,KA9IH;AA+IE,IAAA,OAAO,EAAE,MAAM;AACbqP,MAAAA,cAAc,CAACrP,aAAD,EAAgB,KAAhB,CAAd;AACD,KAjJH;AAkJE,IAAA,OAAO,EAAE6B,OAlJX;AAmJE,IAAA,OAAO,EAAEI,kBAnJX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IATF,eA8JE,oBAAC,gBAAD;AACE,IAAA,kBAAkB,EAAE,EADtB;AAEE,IAAA,eAAe,EAAG8L,OAAD,IAAiB;AAChCzH,MAAAA,YAAY,CAACxG,eAAb,GAA+B,EAA/B;AACAwG,MAAAA,YAAY,CAACC,QAAb,CAAsB,EAAtB,EAA0B,EAA1B;AACD,KALH;AAME,IAAA,KAAK,EAAEiC,cANT;AAOE,IAAA,mBAAmB,EAAE9I,eAPvB;AAQE,IAAA,eAAe,EAAE,IARnB;AASE,IAAA,QAAQ,EAAE3G,MAAM,CAACmV,GAAP,CAAWvS,uBAAuB,CAACwS,qBAAnC,EAA0DC,KATtE;AAUE,IAAA,aAAa,EAAEe,oBAVjB;AAWE,IAAA,WAAW,EAAE5B,UAXf;AAYE,IAAA,aAAa,EAAEU,YAAY,EAZ7B;AAaE,IAAA,mBAAmB,EAAE,CAbvB;AAcE,IAAA,YAAY,EAAE3H,YAdhB;AAeE,IAAA,MAAM,EAAE,IAfV;AAgBE,IAAA,QAAQ,EAAEjC,QAhBZ;AAiBE,IAAA,YAAY,EAAEjF,MAjBhB;AAkBE,IAAA,MAAM,EAAEtC,MAlBV;AAmBE,IAAA,qBAAqB,EAAE,CAAC;AAAE+I,MAAAA,GAAG,EAAE,QAAP;AAAiB9I,MAAAA,KAAK,EAAEqC;AAAxB,KAAD,EAAmC;AAAEyG,MAAAA,GAAG,EAAE,UAAP;AAAmB9I,MAAAA,KAAK,EAAEmB;AAA1B,KAAnC,CAnBzB;AAoBE,IAAA,qBAAqB,EAAEkP,kBAAkB,EApB3C;AAqBE,IAAA,qBAAqB,EAAEA,kBAAkB,EArB3C;AAsBE,IAAA,YAAY,EAAE7C,iBAAiB,EAtBjC;AAuBE,IAAA,kBAAkB,EAAC,MAvBrB;AAwBE,IAAA,mBAAmB,EAAC,MAxBtB;AAyBE,IAAA,YAAY,EAAE,CAAC,OAAD,CAzBhB;AA0BE,IAAA,WAAW,EAAE,IA1Bf;AA2BE,IAAA,cAAc,EAAG9E,KAAD,IAAW;AACzB,UAAIA,KAAK,CAACyD,UAAN,KAAqB5N,oBAAzB,EAA+C;AAC7C0D,QAAAA,KAAK,CAACiO,OAAN,CAAcC,IAAd,CAAmB,YAAnB;AACD;AACF,KA/BH;AAgCE,IAAA,iBAAiB,EAAE,IAhCrB;AAiCE,IAAA,WAAW,EAAE7G,kBAAkB,mBAAC3E,SAAS,CAACoP,GAAV,CAAc,KAAd,CAAD,2DAAyB,EAAzB,CAjCjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IA9JF,CA3HF,CALF,CATF,CAPF,eAiYE,oBAAC,WAAD;AACE,IAAA,OAAO,EAAE5N,iBADX;AAEE,IAAA,KAAK,EAAE,gBAFT;AAGE,IAAA,IAAI,EAAE,OAHR;AAIE,IAAA,QAAQ,EAAEkM,2BAJZ;AAKE,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,OAAO,EAAEA,2BAAjB;AAA8C,MAAA,GAAG,EAAC,QAAlD;AAA2D,MAAA,IAAI,EAAC,SAAhE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADM,CALV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAWE;AAAK,IAAA,SAAS,EAAEjW,MAAM,CAAC+W,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACGpS,YAAY,iBAAI,oBAAC,aAAD;AACf,IAAA,aAAa,EAAG0C,aAAD,IAA4B;AACzC,UAAIA,aAAJ,EAAmB;AACjB2C,QAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD;AACF,KALc;AAMf,IAAA,aAAa,EAAE,CAACrF,YAAD,CANA;AAOf,IAAA,YAAY,EAAE0F,YAPC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADnB,CAXF,CAjYF,CADF;AA4ZD,CAhqCD;;AAkqCA,eAAehL,UAAU,CAACuG,IAAD,CAAzB", "sourcesContent": ["import React, { Fragment, useCallback, useContext, useEffect, useReducer, useState } from 'react';\r\nimport {\r\n  EditOutlined, ExclamationCircleOutlined, LinkOutlined, UpOutlined,\r\n  RightOutlined,\r\n} from '@ant-design/icons';\r\nimport { useParams, withRouter } from 'react-router-dom';\r\nimport { Button, Form, List, Modal as AntModal, Tooltip, Tag } from 'antd';\r\nimport moment from 'moment';\r\nimport { useDispatch, useSelector } from 'react-redux';\r\nimport { PageContent } from '@app/layouts/MasterLayout';\r\nimport { FileAreaActionPanel } from '@app/features/FileArea/FileAreaActionPanel';\r\nimport GenericDataTable from '@app/components/GenericDataTable';\r\nimport config from '../../../utils/config';\r\nimport PageTitle from '@app/components/PageTitle';\r\nimport Uploader from '@app/components/Uploader';\r\nimport { FolderTree } from '@app/components/FolderTree';\r\nimport styles from './index.module.less';\r\nimport {\r\n  canCheckinByFileId,\r\n  checkCheckoutFileListBySiteId,\r\n  pinUnPinFiles,\r\n  undoCheckoutFile,\r\n  updateFileDetailsByFileId,\r\n  updateFileStatus,\r\n  getFileAreaIdBySite,\r\n  getBinderFileAreaNodes\r\n} from '@app/api/fileAreaService';\r\nimport { FileAreaButtonPanel } from '@app/features/FileArea/HeaderButtonPanel/fileAreaButtonPanel';\r\nimport ManageFiles from '@app/features/FileArea/ManageFiles';\r\nimport Modal from '@app/components/Modal';\r\nimport { updateColumnQueryParameters, updateSearchQueryParameters } from '@app/redux/actions/gridsActions';\r\nimport { IFile } from '@app/types/fileAreaTypes';\r\nimport ContextMenu from '@app/components/ContextMenu';\r\nimport {\r\n  //setHasCommonData,\r\n  updateContextMenuAssignOption,\r\n  updateContextMenuCheckoutOption,\r\n  updateContextMenuDeleteOption,\r\n  updateContextMenuDownloadOption,\r\n  updateContextMenuPropetiesoption,\r\n  updateContextMenuPublishFiles,\r\n  updateContextMenuReCategorizeOption,\r\n  updateContextMenuMoveFilesOption,\r\n  updateContextMenuReNameFilesOption,\r\n  updateContextMenuCopyFilesOption,\r\n  updateContextMenuStatusOption,\r\n  updateContextMenuUnpublishFiles,\r\n  updateLoadGridOption,\r\n  updateContextMenuLinkFilesOption,\r\n  updateContextMenuUnlinkFilesOption,\r\n  updateContextMenuToBeDeleted,\r\n  updateContextMenuCopyLinkFiles,\r\n  setFolderTree,\r\n} from '@app/redux/actions/fileAreaActions';\r\nimport { RootState } from '@app/redux/reducers/state';\r\nimport { errorNotification, infoNotification, successNotification } from '@app/utils/antNotifications';\r\nimport { setDynamicBreadcrums } from '@app/redux/actions/configurationActions';\r\nimport { Sorter, copyToClipboard } from '../../../components/GenericDataTable/util';\r\nimport logger from '@app/utils/logger';\r\nimport { FORBIDDEN_ERROR_CODE } from '@app/utils';\r\nimport { getExistingTags } from '@app/api/tagManagementService';\r\nimport { DataTableContext } from '@app/components/GenericDataTable/DataTableContext';\r\nimport { updateFilterDropDownValues } from '@app/components/GenericDataTable/DataTableContext/actions';\r\nimport reducer from '@app/components/GenericDataTable/DataTableContext/reducer';\r\nimport { OperationalServiceTypes } from '@iris/discovery.fe.client';\r\nimport { renderTag } from '@app/components/Tag';\r\nimport userStatusColorSwitch from '@app/utils/css/userStatusColorSwitch';\r\nimport { SITE_STATUS } from '@app/constants';\r\nimport hasPermission from '@app/utils/permission';\r\nimport { CheckCircleOutlined, HistoryOutlined } from '@ant-design/icons/lib';\r\nimport { CheckinModel } from '@app/features/FileArea/CheckinModel/CheckinModel';\r\nimport useQuery from '@app/hooks/useQuery';\r\nimport renderOptions from \"@app/components/forms/utils/renderOptions\";\r\nimport { getTemplates } from \"@app/api/templateService\";\r\nimport { setActiveTemplates } from \"@app/redux/actions/templateActions\";\r\nimport { getAutocompleteOptions, getColumns, getRecords } from \"@app/api/genericDataTable\";\r\nimport debounce from \"lodash/debounce\";\r\nimport DownloadModal, { downloadTypes } from \"@app/components/DownloadModal\";\r\nimport ModalCustom from '../../../components/Modal';\r\nimport { FormattedDateTime } from \"@app/components/FormattedDateTime\";\r\nimport { useFileArea } from '@app/hooks/useFileArea';\r\nimport { IFolder } from '@app/types/FileAreaFolderTreeTypes';\r\nimport StatusTag from \"@app/features/FileArea/StatusTag\";\r\nimport ShowAllSwitch from '@app/components/Switch/ShowAll';\r\n\r\nconst SORTER: Sorter = {\r\n  value: 'modified',\r\n  order: 'descend',\r\n};\r\nconst CHECKOUT_STATUS = 'Checked-Out';\r\n\r\ntype fileAreaActionItem='fileUpload' | 'fileEdit' | 'fileDownload' | 'fileOptionsExceptDownload'|'manageTags'|'manageCheckin';\r\n\r\nconst statusPermissionMap: Record<string, {\r\n  fileUpload: boolean;\r\n  fileEdit: boolean;\r\n  fileDownload: boolean;\r\n  fileOptionsExceptDownload: boolean;\r\n  manageTags: boolean;\r\n  manageCheckin: boolean;\r\n}> = {\r\n  \"ACTIVE\": { fileUpload: true, fileEdit: true, fileDownload: true, fileOptionsExceptDownload: true, manageTags: true,manageCheckin: true },\r\n  \"INACTIVE\": { fileUpload: false, fileEdit: false, fileDownload: true, fileOptionsExceptDownload: false,manageTags: false, manageCheckin: false },\r\n  \"ACTIVE - LH\": { fileUpload: true, fileEdit: false, fileDownload: true, fileOptionsExceptDownload: false, manageTags: false, manageCheckin: false },\r\n  \"INACTIVE - LH\": { fileUpload: false, fileEdit: false, fileDownload: true, fileOptionsExceptDownload: false, manageTags: false, manageCheckin: false },\r\n};\r\n\r\nconst isInvalidStatus = (status: any) => {\r\n  return status.name === CHECKOUT_STATUS;\r\n};\r\n\r\nconst isCheckout = (status: any) => {\r\n  return status.name === CHECKOUT_STATUS;\r\n};\r\n\r\nconst constructManageFileRequest = (selectedFile: IFile, data: any, selectedFolder: number, title: string, binderId: string) => {\r\n  const request = {\r\n    ...data,\r\n    tags: [...data.tags, ...data.newTags],\r\n    binderNodeId: selectedFolder < 0 ? data.folder : selectedFolder,\r\n    title: title !== '' ? title : selectedFile.title,\r\n    binderId,\r\n  };\r\n  delete request.folder;\r\n  delete request.newTags;\r\n  return request;\r\n};\r\n\r\nconst { confirm } = AntModal;\r\n\r\nexport const formatGridColumnValue = (value: any) => {\r\n  return <p className={styles.yjGridTextFormat}>{value}</p>;\r\n};\r\n\r\nconst hasFileAreaPermission = (fileArea: any, permissionType: fileAreaActionItem) => {\r\n  return fileArea && statusPermissionMap[fileArea.status.toUpperCase()][permissionType];\r\n};\r\n\r\nconst Page = (props: any) => {\r\n  const reactDispatch = useDispatch();\r\n  const [toggle, SetToggle] = useState(false);\r\n  const { siteId, siteName, channelId, binderId, binderName } = useParams<any>();\r\n  const [folderId, setFolderId] = useState(0);\r\n  const [selectedRecords, setSelectedRecords] = useState(0);\r\n  const [selectedIds, setSelectedIds] = useState(['']);\r\n  const [selectedRowKeys, setSelectedRowKeys] = useState([]);\r\n  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([]);\r\n  const [showViewModalState, setShowViewModalState] = useState(false);\r\n  const [editFormChange, setEditFormChange] = useState(false);\r\n  const [editFormAssigneeChange, setEditFormAssigneeChange] = useState(false);\r\n  const [hasDownloaded, setHasDownloaded] = useState(false);\r\n  const [undoCheckout, setUndoCheckout] = useState(true);\r\n  const [actionsAllowed, setActionsAllowed] = useState(true);\r\n  const checkinInStatus = 5;\r\n  const [gridUpdated, setGridUpdated] = useState(false);\r\n  const [manageFileForm] = Form.useForm();\r\n  const [manageFileTitle, setManageFileTitle] = useState('');\r\n  const [selectedFolderInUpload, setSelectedFolderInUpload] = useState<number>(-1);\r\n  const { state } = useContext(DataTableContext);\r\n  const [_, dispatch] = useReducer(reducer, state);\r\n  const urlParams = useQuery();\r\n\r\n  const [singleCheckinFile, setSingleCheckinFile] = useState('');\r\n  //context menu state\r\n  const [positon, setPositon] = useState({ x: 0, y: 0 });\r\n  const [visibleContextMenu, setVisibleContextMenu] = useState(false);\r\n  //const [showSelectTemplateModal, setShowSelectTemplateModal] = useState(false);\r\n  const fileArea = useFileArea(siteId);\r\n\r\n  const [dataTableModal, setDataTableModal] = useState({\r\n    visible: false,\r\n    title: '',\r\n    modalData: [],\r\n  });\r\n\r\n  const { folderTree, hasCommonData, fileAreaSettings, filesUploaded } = useSelector((state: RootState) => state.fileArea);\r\n  const { userPermission, userPreferences } = useSelector((state: RootState) => state.userManagement);\r\n  const { activeTemplates } = useSelector((state: RootState) => state.template);\r\n\r\n  const [selectedTemplateId, setSelectedTemplateId] = useState('');\r\n\r\n  const [showDownloadModal, setShowDownloadModal] = useState(false);\r\n  const [selectedFile, setSelectedFile] = useState<IFile | undefined>(undefined);\r\n  const [checkedOuFilesDownload, setCheckedOuFilesDownload] = useState<IFile[] | undefined>(undefined);\r\n  const [downloadType, setDownloadType] = useState<downloadTypes | undefined>(downloadTypes.individual);\r\n  const [loading, setLoading] = useState(false);\r\n\r\n  const linkedFiles = selectedFiles.filter((e) => e.linked);\r\n  const originalFileAreaWithLinked: boolean = linkedFiles && selectedFiles.some((e) => e.fK_FileAreaId === fileArea?.fileAreaId);\r\n\r\n  const tableKey = 'fileArea';\r\n\r\n  const initialFolderData = [\r\n    {\r\n      id: 1,\r\n      name: 'Primary folder',\r\n      retention: false,\r\n      presist: false,\r\n      subFolders: [\r\n        {\r\n          id: 2,\r\n          name: 'Secondary folder (1)',\r\n          subFolders: [],\r\n          retention: false,\r\n          presist: false,\r\n        },\r\n        {\r\n          id: 3,\r\n          name: 'Secondary folder (2)',\r\n          subFolders: [],\r\n          retention: false,\r\n          presist: false,\r\n        },\r\n      ],\r\n    },\r\n  ];\r\n\r\n  const showDatatableModal = (title: string, data: []) => {\r\n    setDataTableModal({\r\n      visible: true,\r\n      title: title,\r\n      modalData: data,\r\n    });\r\n  };\r\n\r\n  const hideDatatableModal = () => {\r\n    setDataTableModal({\r\n      visible: false,\r\n      title: dataTableModal.title,\r\n      modalData: dataTableModal.modalData,\r\n    });\r\n  };\r\n\r\n  const fetchBinderNodes = async () => {\r\n    try {\r\n      const response = await getBinderFileAreaNodes(binderId);\r\n      const transformFolders = (folders: any[]): IFolder[] => {\r\n        const primaryFolders = folders.filter((folder) => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);\r\n\r\n        return primaryFolders.map((folder) => ({\r\n          id: folder.id,\r\n          name: folder.name,\r\n          subFolders: folder.childNodes.map((child: any) => ({\r\n            id: child.id,\r\n            name: child.name,\r\n            subFolders: [],\r\n            retention: child.retention || 0,\r\n          })),\r\n        }));\r\n      };\r\n\r\n      const transformedFolders = transformFolders(response.data.folders);\r\n      reactDispatch(setFolderTree({\r\n        siteId,\r\n        siteName: binderName,\r\n        siteStatusId: 1,\r\n        folders: transformedFolders,\r\n      }));\r\n    } catch (error) {\r\n      logger.error('Error fetching binder nodes:', 'fetchBinderNodes', error);\r\n    }\r\n  };\r\n\r\n  const loadGrid = useCallback(() => {\r\n    getExistingTags(binderId).then((res) => {\r\n      dispatch(updateFilterDropDownValues('tags', res.data));\r\n    });\r\n    reactDispatch(updateColumnQueryParameters(siteId));\r\n    reactDispatch(\r\n      updateSearchQueryParameters([\r\n        { key: 'siteid', value: siteId },\r\n        { key: 'binderNodeId', value: folderId },\r\n      ])\r\n    );\r\n    reactDispatch(updateLoadGridOption(true));\r\n    setTimeout(() => {\r\n      reactDispatch(updateLoadGridOption(false));\r\n    });\r\n  }, [siteId, reactDispatch, folderId, binderId]);\r\n\r\n  useEffect(() => {\r\n    fetchBinderNodes()\r\n  }, []);\r\n\r\n  useEffect(() => {\r\n    getTemplates({ 'isHidden': false, 'templateStatus': 1 }).then((res) => {\r\n      logger.info('File Area Module', 'Get Templates Response', res.data.records);\r\n      if (res.status == 200 && res.data?.records) {\r\n        reactDispatch(setActiveTemplates(res.data?.records));\r\n        logger.debug(\r\n          \"File Area Module\",\r\n          \"Set Active Templates\",\r\n          activeTemplates\r\n        );\r\n      }\r\n    }).catch((e) => {\r\n      logger.error(\"File Area Module\", \"Get Templates Error\", e);\r\n    });\r\n\r\n    const dynamicBreadcrumbs = [\r\n      { title: \"Client File Areas\", path: \"/client-file-area\" },\r\n      { title: `${decodeURIComponent(siteName)} - File Areas`, path: `/client-file-area/${siteId}/${siteName}/${channelId}` },\r\n      { title: `${decodeURIComponent(binderName)}`, path: `/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}` }\r\n    ];\r\n    reactDispatch(setDynamicBreadcrums(dynamicBreadcrumbs));\r\n    return () => {\r\n      //reactDispatch(setHasCommonData(false));\r\n      reactDispatch(setDynamicBreadcrums([]));\r\n    };\r\n  }, []);\r\n\r\n\r\n\r\n\r\n  useEffect(() => {\r\n    reactDispatch(updateColumnQueryParameters(siteId));\r\n    reactDispatch(\r\n      updateSearchQueryParameters([\r\n        { key: 'siteid', value: siteId },\r\n        { key: 'binderNodeId', value: 0 },\r\n      ])\r\n    );\r\n  }, [reactDispatch, siteId]);\r\n\r\n  useEffect(() => {\r\n    if (hasDownloaded) {\r\n      setGridUpdated(true);\r\n      loadGrid();\r\n      setHasDownloaded(false);\r\n      rowSelection.selectedRowKeys = [];\r\n      rowSelection.onChange([], []);\r\n      setGridUpdated(false);\r\n    }\r\n  }, [hasDownloaded, loadGrid]);\r\n\r\n  useEffect(() => {\r\n    if (filesUploaded) {\r\n      setGridUpdated(true);\r\n      loadGrid();\r\n      setGridUpdated(false);\r\n    }\r\n  }, [filesUploaded, reactDispatch, loadGrid]);\r\n\r\n  const rowSelection = {\r\n    onChange: (selectedRowKeyValues: any, selectedRows: IFile[]) => {\r\n      if (selectedRows?.length > 0 && selectedRows?.find((row) => userPermission?.privDMSCanCheckInCheckOutInternalFiles && row?.status?.value === checkinInStatus)) {\r\n        setActionsAllowed(false);\r\n      } else {\r\n        setActionsAllowed(true);\r\n      }\r\n      if (userPermission.privDMSCanCheckInCheckOutInternalFiles && selectedRows.length === 1 && selectedRows[0].status?.value === checkinInStatus) {\r\n        setUndoCheckout(true);\r\n      } else {\r\n        setUndoCheckout(false);\r\n        reactDispatch(updateContextMenuPropetiesoption(false));\r\n      }\r\n      setSelectedRowKeys(selectedRowKeyValues);\r\n      setSelectedRecords(selectedRows.length);\r\n      setSelectedFiles(selectedRows);\r\n      if (selectedRows.length > 1) {\r\n        setSelectedIds(selectedRowKeyValues);\r\n      } else {\r\n        setSelectedIds(['']);\r\n      }\r\n    },\r\n    getCheckboxProps: (record: any) => ({\r\n      disabled: record?.status?.value === checkinInStatus,\r\n      name: record.name,\r\n    }),\r\n    selectedRowKeys,\r\n    fixed: true,\r\n  };\r\n\r\n  const selectedRecord = (recordId: string) => {\r\n    return selectedIds.includes(recordId);\r\n  };\r\n\r\n  const handleCheckin = (fileId: string) => {\r\n    canCheckinByFileId(fileId).then(() => {\r\n      setSingleCheckinFile(fileId)\r\n    }).catch((error) => {\r\n      logger.error(\"File Area Module\", \"handleCheckin\", error);\r\n\r\n      if (error.response.status === FORBIDDEN_ERROR_CODE) {\r\n        errorNotification([''], 'You do not have the permission to perform this action.');\r\n      } else {\r\n        errorNotification([''], 'Something went wrong!');\r\n      }\r\n    });\r\n  };\r\n  const handleUndoCheckout = (fileId: string) => {\r\n    setHasDownloaded(false);\r\n    confirm({\r\n      title: 'Do you wish to undo-checkout for this file?',\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        undoCheckoutFile(selectedFiles[0].id)\r\n          .then((response) => {\r\n            if (response) {\r\n              setHasDownloaded(true);\r\n              successNotification([''], 'Undo Check-out Successful');\r\n            }\r\n          })\r\n          .catch((error) => {\r\n            if (error.response.status === FORBIDDEN_ERROR_CODE) {\r\n              errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\r\n            } else {\r\n              errorNotification([''], 'File checkout could not be undone.');\r\n            }\r\n          });\r\n      },\r\n    });\r\n  };\r\n\r\n  const handleShowViewModalCancel = () => {\r\n    if (editFormChange) {\r\n      confirm({\r\n        title: 'Are you sure you want to discard the changes?',\r\n        icon: <ExclamationCircleOutlined />,\r\n        okText: 'Yes',\r\n        cancelText: 'No',\r\n        onOk() {\r\n          setEditFormChange(false);\r\n          setShowViewModalState(false);\r\n        },\r\n      });\r\n    } else {\r\n      setEditFormChange(false);\r\n      setShowViewModalState(false);\r\n    }\r\n  };\r\n\r\n  // if (!hasCommonData) {\r\n  //   return <Spin style={{ paddingTop: 20 }} />;\r\n  // }\r\n\r\n  const onSelecteFolder = (folderIdValue: any) => {\r\n    reactDispatch(\r\n      updateSearchQueryParameters([\r\n        { key: 'siteid', value: siteId },\r\n        { key: 'binderNodeId', value: folderIdValue },\r\n      ])\r\n    );\r\n    setFolderId(folderIdValue);\r\n    rowSelection.onChange([], []);\r\n  };\r\n\r\n  const onContextMenuClicked = (event: any, record: IFile) => {\r\n    if (rowSelection.selectedRowKeys.length <= 0) {\r\n      if (userPermission.privDMSCanCheckInCheckOutInternalFiles && record?.status?.value === checkinInStatus) {\r\n        setUndoCheckout(true);\r\n        setActionsAllowed(false);\r\n      } else {\r\n        setUndoCheckout(false);\r\n        setActionsAllowed(true);\r\n      }\r\n\r\n      setSelectedFiles([record]);\r\n    }\r\n    event.preventDefault();\r\n    setVisibleContextMenu(false);\r\n    document.addEventListener(`click`, function onClickOutside() {\r\n      setVisibleContextMenu(false);\r\n      document.removeEventListener(`click`, onClickOutside);\r\n    });\r\n    setVisibleContextMenu(true);\r\n    setPositon({ x: event.clientX, y: event.clientY });\r\n  };\r\n\r\n  const onRowClick = (event: any, record: IFile) => {\r\n    if (selectedFiles.length === 1) {\r\n      setSelectedFiles([record]);\r\n    }\r\n  };\r\n\r\n  const handleRowClick = (record: IFile, rowIndex: any) => ({\r\n    onContextMenu: (event: any) => onContextMenuClicked(event, record),\r\n    onClick: (event: any) => onRowClick(event, record),\r\n  });\r\n\r\n  const handleUpdateFileProperties = () => {\r\n    setLoading(true);\r\n    manageFileForm\r\n      .validateFields()\r\n      .then((data) => {\r\n        data.expirationDate = moment(data.expirationDate).endOf('day').subtract(1, 'minutes');\r\n\r\n        const requestData = constructManageFileRequest(selectedFiles[0], data, selectedFolderInUpload, manageFileTitle, binderId);\r\n        return updateFileDetailsByFileId(selectedFiles[0].id, requestData);\r\n      })\r\n      .then((response) => {\r\n        //unknown error\r\n        if (!response.data) {\r\n          errorNotification([''], 'File Properties Update Failed');\r\n          setLoading(false);\r\n          if (editFormAssigneeChange) {\r\n            errorNotification([''], 'Assignment Failed');\r\n          }\r\n\r\n          return response.data;\r\n        }\r\n\r\n        //success scenario\r\n        loadGrid();\r\n        successNotification([''], 'File Properties Updated Successfully');\r\n        if (editFormAssigneeChange) {\r\n          successNotification([''], 'Assignment Successful');\r\n        }\r\n        setShowViewModalState(false);\r\n        setEditFormChange(false);\r\n        setLoading(false);\r\n      })\r\n      .catch((error) => {\r\n        switch (error.statusCode) {\r\n          //error code cases\r\n          case FORBIDDEN_ERROR_CODE:\r\n            errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\r\n            break;\r\n          default:\r\n            errorNotification([''], 'File Properties Update Failed');\r\n            if (editFormAssigneeChange) {\r\n              errorNotification([''], 'Assignment Failed');\r\n            }\r\n            break;\r\n        }\r\n\r\n        logger.error('Internal Files', 'Manage Files Edit', error);\r\n        setLoading(false);\r\n      });\r\n  };\r\n\r\n  function gridColumnValueFormatter(value: any) {\r\n    return value ? formatGridColumnValue(value) : formatGridColumnValue('N/A');\r\n  }\r\n\r\n  const downloadFile = (value: [], file: IFile) => {\r\n\r\n    // setDownloadType(checkoutZip ? downloadTypes.checkoutZip : downloadTypes.checkoutIndividual);\r\n    setSelectedFile(file);\r\n    if(hasFileAreaPermission(fileArea, 'fileDownload'))\r\n      {\r\n      setShowDownloadModal(true);\r\n      }\r\n    setCheckedOuFilesDownload(value)\r\n  }\r\n  const colors = [\r\n    \"magenta\",\r\n    \"green\",\r\n    \"red\",\r\n    \"cyan\",\r\n    \"volcano\",\r\n    \"blue\",\r\n    \"orange\",\r\n    \"geekblue\",\r\n    \"gold\",\r\n    \"purple\",\r\n    \"lime\"\r\n  ];\r\n  const [expandedTags, setExpandedTags] = useState<Record<string, boolean>>({});\r\n  // Inside the Page component\r\n  const containerRefs = React.useRef<Record<string, HTMLParagraphElement | null>>({});\r\n  const [showExpandIcons, setShowExpandIcons] = React.useState<Record<string, boolean>>({});\r\n  const [showAllFiles, setShowAllFiles] = useState<boolean>(false);\r\n\r\n  // Function to check overflow for all tags\r\n  const checkOverflow = (recordId: string) => {\r\n    const container = containerRefs.current[recordId];\r\n    if (container && container.scrollHeight && container.clientHeight && container.scrollHeight > container.clientHeight) {\r\n      console.log('Container:', container);\r\n      console.log('scrollHeight:', container.scrollHeight, 'clientHeight:', container.clientHeight);\r\n      // setTimeout(() => {  // Use setTimeout to ensure the DOM is updated before checking overflow   \r\n      //   setShowExpandIcons((prev) => ({\r\n      //     ...prev,\r\n      //     [recordId]: container.scrollHeight > container.clientHeight,\r\n      //   }));\r\n      // }, 100)\r\n    } else {\r\n      console.warn(`Container for recordId ${recordId} is not available.`);\r\n    }\r\n  };\r\n\r\n  // // Use useEffect in the parent component to handle overflow checks\r\n  // useEffect(() => {\r\n  //   console.log('containerRefs:', containerRefs.current);\r\n\r\n  //   Object.keys(containerRefs.current).forEach((recordId) => {\r\n  //     checkOverflow(recordId);\r\n  //   });\r\n  // }, [expandedTags, containerRefs]);\r\n\r\n  const renderGridColumns = () => {\r\n    return {\r\n      title: (value: any, data: any) => {\r\n        return (\r\n          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>\r\n            <Tooltip className=\"yJFileAreaRow\" placement=\"leftTop\" title={value}>\r\n            <Button type=\"link\" onClick={() => downloadFile(value, data)} style={{ paddingInline: 0, border: 0 , cursor: !hasFileAreaPermission(fileArea, 'fileDownload')? 'not-allowed':'pointer'}}>{value}</Button>\r\n            </Tooltip>\r\n          </div>\r\n        );\r\n      },\r\n\r\n      id: (value: string) => {\r\n        return (\r\n          <div className=\"yjFileAreaGridTextWrap yJFileAreaRow\">\r\n            <Tooltip color=\"transparent\" placement=\"right\" title={<LinkOutlined style={{ color: '#0E678E', fontSize: '14px' }} onClick={() => copyToClipboard(value)} />}>\r\n              <span className=\"yJFileAreaRow\">{value}</span>\r\n            </Tooltip>\r\n          </div>\r\n        );\r\n      },\r\n      created: (value: string) => {\r\n        return <FormattedDateTime value={value} />;\r\n      },\r\n      createdBy: (value: any) => {\r\n        return (\r\n          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>\r\n            <Tooltip className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`} placement=\"leftTop\" title={value?.name}>\r\n              {value?.name}\r\n            </Tooltip>\r\n          </div>\r\n        );\r\n      },\r\n      assignee: (value: any) => {\r\n        return value ? (\r\n          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>\r\n            <Tooltip className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`} placement=\"leftTop\" title={value?.name}>\r\n              {value?.name}\r\n            </Tooltip>\r\n          </div>\r\n        ) : (\r\n          'N/A'\r\n        );\r\n      },\r\n      status: (value: any) => {\r\n        return value ? formatGridColumnValue(value.name) : formatGridColumnValue('N/A');\r\n      },\r\n      year: gridColumnValueFormatter,\r\n      modified: (value: string) => {\r\n        return <FormattedDateTime value={value} />;\r\n      },\r\n      // isFolderExist: {\r\n      //   render: (value: any) => {\r\n      //     return (value ? <Tag color=\"blue\">File Area</Tag> : '')\r\n      //   },\r\n      //   width: 550\r\n      // }\r\n      tags: {\r\n        width: 300,\r\n        render: (value: any, record: any) => {\r\n          const SINGLE_LINE_HEIGHT = 24; // Adjust this value based on your CSS\r\n          const LINES_TO_DHOW_EXPANDER = 2; // Maximum number of lines to show before expanding\r\n          const isExpanded = expandedTags[record.id] ?? false;\r\n\r\n          const isContentOverflowing = (recordId: string): boolean => {\r\n            const container = containerRefs.current[recordId];\r\n            if (!container) return false;\r\n\r\n            const scrollHeight = container.scrollHeight ?? 0;\r\n            const clientHeight = container.clientHeight ?? 0;\r\n\r\n            const senario1 = scrollHeight / clientHeight >= LINES_TO_DHOW_EXPANDER;\r\n            const senario2 = scrollHeight > SINGLE_LINE_HEIGHT;\r\n\r\n            return senario1 || senario2;\r\n          };\r\n\r\n          const tagColorMap: any = {};\r\n\r\n          value?.forEach((value: any, index: number) => {\r\n            tagColorMap[value.name] = colors[index % colors.length];\r\n          });\r\n\r\n          return (\r\n            <div\r\n              ref={(el) => {\r\n                if (el) {\r\n                  containerRefs.current[record.id] = el;\r\n                  checkOverflow(record.id);\r\n                }\r\n              }}\r\n              className={`yJFileAreaRow ${isExpanded ? styles.multiLine : styles.singleLine}`}\r\n            >\r\n              {value?.map((t: any) => <Tag color={tagColorMap[t.name]}>{t?.name}</Tag>)}\r\n              {/* Conditionally render the expand icon only if there are tags */}\r\n              {isContentOverflowing(record.id) && (\r\n                <span\r\n                  onClick={() => {\r\n                    setExpandedTags((prev) => ({\r\n                      ...prev,\r\n                      [record.id]: !isExpanded,\r\n                    }));\r\n\r\n                    // Use requestAnimationFrame to delay the check\r\n                    requestAnimationFrame(() => {\r\n                      const container = containerRefs.current[record.id];\r\n                      if (container) {\r\n                        console.log('After toggle:');\r\n                        console.log('clientHeight:', container.clientHeight);\r\n                        console.log('scrollHeight:', container.scrollHeight);\r\n                      }\r\n                    });\r\n                  }}\r\n                  style={{\r\n                    position: \"absolute\",\r\n                    right: 0,\r\n                    cursor: \"pointer\",\r\n                    marginLeft: \"8px\",\r\n                  }}\r\n                >\r\n                  {isExpanded ? <UpOutlined /> : <RightOutlined />}\r\n                </span>\r\n              )}\r\n            </div>\r\n          );\r\n        }\r\n      },\r\n      projects: (value: any) => {\r\n        return (\r\n          <p className={`${styles.yjGridTextCenter} yJFileAreaRow`}>\r\n            <Button className={`${styles.yjViewButton} yJFileAreaRow`} onClick={() => showDatatableModal('Projects', value)}>\r\n              {'View'}\r\n            </Button>\r\n          </p>\r\n        );\r\n      },\r\n      type: gridColumnValueFormatter,\r\n      expirationDate: (value: string, record: any) => {\r\n        return record.expirationStatus.name === 'Permanent' ? '' : <FormattedDateTime value={value} />;\r\n      },\r\n      fileCondition: gridColumnValueFormatter,\r\n      action: (text: any, record: any) => {\r\n        return (\r\n          <div className={`${styles.yjActionIconWrapper} yJFileAreaRow`}>\r\n            <Tooltip className=\"yJFileAreaRow\" title=\"Check In\">\r\n              <Button\r\n                className=\"yJFileAreaRow\"\r\n                disabled={!isCheckout(record.status) || !userPermission.privDMSCanManageFileProperties || !hasPermission(folderTree, 'FILE_AREA_FILE_EDIT')||!hasFileAreaPermission(fileArea, 'manageCheckin')}\r\n                onClick={() => handleCheckin(record.id)}\r\n                icon={<CheckCircleOutlined />}\r\n              />\r\n            </Tooltip>\r\n            <Tooltip className=\"yJFileAreaRow\" title=\"Edit\">\r\n              <Button\r\n                className=\"yJFileAreaRow\"\r\n                disabled={selectedRecord(record.id) || isInvalidStatus(record.status) || !userPermission.privDMSCanManageFileProperties || record.linked}\r\n                onClick={() => {\r\n                  setManageFileTitle('');\r\n                  // rowSelection.selectedRowKeys = [];\r\n                  rowSelection.onChange([], []);\r\n                  setSelectedFiles([record]);\r\n                  setShowViewModalState(true);\r\n                }}\r\n                icon={<EditOutlined />}\r\n              />\r\n            </Tooltip>\r\n            <Tooltip className=\"yJFileAreaRow\" title=\"History\">\r\n              <Button\r\n                className=\"yJFileAreaRow\"\r\n                disabled={selectedRecord(record.id)}\r\n                onClick={() => {\r\n                  rowSelection.selectedRowKeys = [];\r\n                  rowSelection.onChange([], []);\r\n                  props.history.push(encodeURI(`/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}/history/${record.id}`));\r\n                }}\r\n                icon={<HistoryOutlined />}\r\n              />\r\n            </Tooltip>\r\n          </div>\r\n        );\r\n      },\r\n    };\r\n  };\r\n\r\n  const setQueryParameters = () => {\r\n    return [\r\n      { key: 'siteid', value: siteId },\r\n      { key: 'binderNodeId', value: folderId },\r\n      { key: 'binderId', value: binderId },\r\n    ];\r\n  };\r\n\r\n  const fileDownloaded = (value: any) => {\r\n    if (value) {\r\n      setHasDownloaded(true);\r\n      successNotification([''], 'File checked-out successfully.');\r\n    }\r\n  };\r\n\r\n  function onSuccess() {\r\n    setSingleCheckinFile('');\r\n    setGridUpdated(true);\r\n    loadGrid();\r\n    setGridUpdated(false);\r\n    rowSelection.selectedRowKeys = [];\r\n    rowSelection.onChange([], []);\r\n  }\r\n\r\n\r\n  const fetchFiles = async (state: any, transformFilters: any, queryParams: any) => {\r\n    if (!showAllFiles) {\r\n      return fetchData(state, transformFilters, queryParams);\r\n    }\r\n\r\n    const activeFilter: Array<any> = state.columns.find((column: any) => column.key === 'status')?.filter_data;\r\n    if (!activeFilter) {\r\n      return fetchData(state, transformFilters, queryParams);\r\n    }\r\n\r\n    const filters = {\r\n      ...transformFilters,\r\n      status: transformFilters.status ? transformFilters.status : activeFilter.map(f => f.value),\r\n    }\r\n    return fetchData(state, filters, queryParams);\r\n  };\r\n\r\n  const fetchColumns = async () => {\r\n    const response = await getColumns(config.api[OperationalServiceTypes.FileManagementService].files, tableKey, [{ key: 'binderId', value: binderId }]);\r\n\r\n    return showAllFiles ? response : response.data.map((column: any) => {\r\n      if (column.key === 'status') {\r\n        return {\r\n          ...column,\r\n          filterData: column.filterData.filter((item: any) => item.name !== 'To be Deleted'),\r\n        };\r\n      }\r\n\r\n      return column;\r\n    });\r\n  };\r\n\r\n  const fetchData = useCallback(\r\n    async (state: any, transformFilters: any, queryParams: any) => {\r\n      return getRecords(\r\n        config.api[OperationalServiceTypes.FileManagementService].files,\r\n        {\r\n          pagination: {\r\n            current: state.pagination.current,\r\n            pageSize: state.pagination.pageSize,\r\n          },\r\n          sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},\r\n          filters: transformFilters,\r\n          columns: state.columns\r\n            .filter((i: any) => i.default === false && i.selected === true)\r\n            .map((i: any) => i.key),\r\n        },\r\n        queryParams\r\n      );\r\n    },\r\n    [] // Add dependencies here if needed\r\n  );\r\n\r\n  // Debounced API call\r\n  const debouncedApiCall = debounce(\r\n    (props: any, value: string, callback: (data: any) => void) => {\r\n      logger.info(\"File Area Module\", \"debouncedApiCall\", {\r\n        props,\r\n        propsData: props.data,\r\n        value,\r\n      });\r\n\r\n      getAutocompleteOptions(\r\n        config.api[OperationalServiceTypes.FileManagementService].files,\r\n        props.data.key,\r\n        value,\r\n        props.searchFieldParameters\r\n      )\r\n        .then((data: any) => {\r\n          callback(data.data);\r\n        })\r\n        .catch(() => {\r\n          callback([]);\r\n        });\r\n    },\r\n    config.inputDebounceInterval\r\n  );\r\n\r\n  // Wrapper function to return a Promise\r\n  const searchPromiseWrapper = (props: any, value: string, callback: any) => {\r\n    debouncedApiCall(props, value, (data: any) => {\r\n      callback(data)\r\n    });\r\n  };\r\n\r\n  const handleOnDownloadModalCancel = () => {\r\n    dispatch(updateContextMenuDownloadOption(false));\r\n    setShowDownloadModal(false);\r\n  };\r\n\r\n  const makeAsPinUnPin = (selectedFiles: IFile[], pinned: boolean) => {\r\n    logger.debug(\"File Area Module\", \"makeAsPinUnPin\", { selectedFiles });\r\n    const fileIds = selectedFiles.map((f) => f.id);\r\n    pinUnPinFiles(fileIds, pinned)\r\n      .then((response) => {\r\n        if (response) {\r\n          setHasDownloaded(true);\r\n          successNotification([''], `File(s) ${pinned ? 'Pinned' : 'Unpinned'} Successfully`);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        if (error.response.status === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');\r\n        } else {\r\n          errorNotification([''], `File(s) ${pinned ? 'Pinned' : 'Unpinned'} Failed`);\r\n        }\r\n      });\r\n  };\r\n\r\n  return (\r\n    <Fragment>\r\n      <PageTitle\r\n        title={decodeURIComponent(binderName)}\r\n        tags={fileArea && <StatusTag value={fileArea.status} />}\r\n      >\r\n        <FileAreaButtonPanel siteId={siteId} siteName={decodeURIComponent(siteName)} channelId={channelId} fileSection={'internal'} manageCheckin={hasFileAreaPermission(fileArea, 'manageCheckin')} binderId={binderId} {...props} />\r\n      </PageTitle>\r\n      <PageContent>\r\n        <div\r\n          hidden={\r\n            (!userPermission.privDMSCanUploadFiles || !hasPermission(folderTree, 'FILE_AREA_UPLOADER')) ||\r\n            !hasFileAreaPermission(fileArea, 'fileUpload')\r\n          }\r\n          className={styles.yjFileAreaUploadWrapper}>\r\n          <Uploader siteId={siteId} binderId={binderId} />\r\n        </div>\r\n        <div className={toggle ? `${styles.yjFileAreaFileFinderCollapsed}` : `${styles.yjFileAreaFileFinderExpanded}`}>\r\n          {!toggle && <div className={styles.yjFileFinderPanel}>\r\n            <FolderTree onSelectFolder={(folderIdValue) => onSelecteFolder(folderIdValue)} data={folderTree} />\r\n          </div>}\r\n\r\n          <div className={styles.yjFileAreaDetailsGrid}>\r\n            <FileAreaActionPanel\r\n              fileDownloaded={fileDownloaded}\r\n              syncGrid={(updated: boolean) => {\r\n                if (updated) {\r\n                  setGridUpdated(true);\r\n                  loadGrid();\r\n                  setGridUpdated(false);\r\n                  rowSelection.selectedRowKeys = [];\r\n                  rowSelection.onChange([], []);\r\n                }\r\n              }}\r\n              originalFileAreaWithLinked={originalFileAreaWithLinked}\r\n              showCheckout={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && actionsAllowed && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showDelete={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showDownload={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileDownload')\r\n              }\r\n              showStatus={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showAssign={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showPropeties={\r\n                selectedFiles.length === 1 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showReCategorize={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showMoveFiles={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showLinkFiles={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.some((file) => !file.linked) && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showUnlinkFiles={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.filter((e) => !e.linked).length === 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showPublish={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.some((file) => !file.published) && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showUnpublish={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.filter((e) => !e.published).length === 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showToBeDelete={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showReName={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              showCopy={\r\n                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n              }\r\n              selectedFileList={selectedFiles}\r\n              siteId={siteId}\r\n              toggleIconClicked={(e) => {\r\n                SetToggle(!toggle);\r\n              }}\r\n              showCopyLink={selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')}\r\n              showManageTags={hasFileAreaPermission(fileArea, 'manageTags')}\r\n              showManageCheckin={hasFileAreaPermission(fileArea, 'manageCheckin')}\r\n              additionalActionPanel={<ShowAllSwitch checked={showAllFiles} onChange={setShowAllFiles} />}\r\n            />\r\n\r\n            <Modal {...dataTableModal} size={'small'} onCancel={hideDatatableModal} footer={null}>\r\n              <div className={styles.yjDataTableModal}>\r\n                {dataTableModal.modalData && (\r\n                  <List>\r\n                    {dataTableModal.modalData.map((i: any) => (\r\n                      <List.Item key={i.value}>{i.name}</List.Item>\r\n                    ))}\r\n                  </List>\r\n                )}\r\n                {dataTableModal.modalData.length === 0 && `No ${dataTableModal.title} Applied.`}\r\n              </div>\r\n            </Modal>\r\n\r\n            {/* Record Edit Modal */}\r\n            <Modal\r\n              style={{ top: 20 }}\r\n              visible={showViewModalState}\r\n              title={'Update File Properties'}\r\n              onCancel={handleShowViewModalCancel}\r\n              footer={[\r\n                <Button key=\"back\" type=\"primary\" onClick={handleShowViewModalCancel}>\r\n                  cancel\r\n                </Button>,\r\n                <Button loading={loading} key=\"update\" type=\"primary\" onClick={handleUpdateFileProperties} disabled={!editFormChange}>\r\n                  Update\r\n                </Button>,\r\n              ]}\r\n            >\r\n              <div className={styles.yjModalContentWrapper}>\r\n                <ManageFiles\r\n                  dataList={selectedFiles}\r\n                  onFilesChange={() => { }}\r\n                  siteId={siteId}\r\n                  binderId={binderId}\r\n                  form={manageFileForm}\r\n                  onFormChange={(isFormChange, isAssigneeChange) => {\r\n                    setEditFormChange(isFormChange);\r\n                    setEditFormAssigneeChange(isAssigneeChange);\r\n                  }}\r\n                  onFolderChange={setSelectedFolderInUpload}\r\n                  onTitleChange={setManageFileTitle}\r\n                  disabledForm={!hasPermission(folderTree, 'FILE_AREA_FILE_EDIT') || !hasFileAreaPermission(fileArea, 'fileEdit')}\r\n                />\r\n              </div>\r\n            </Modal>\r\n            {/* Checkin Model */}\r\n            <CheckinModel\r\n              siteId={siteId}\r\n              binderId={binderId}\r\n              fileId={singleCheckinFile}\r\n              onClose={() => setSingleCheckinFile('')}\r\n              onSuccess={onSuccess}\r\n            />\r\n\r\n            <div\r\n              className=\"yjFileAreaGridWrapper\"\r\n              onScroll={() => {\r\n                setVisibleContextMenu(false);\r\n              }}\r\n              onMouseLeave={() => {\r\n                setVisibleContextMenu(false);\r\n              }}\r\n            >\r\n              <ContextMenu\r\n                displayCheckoutOption={\r\n                  userPermission.privDMSCanCheckInCheckOutInternalFiles &&\r\n                  !undoCheckout &&\r\n                  actionsAllowed &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayAssignOption={\r\n                  userPermission.privDMSCanManageFileAssign &&\r\n                  actionsAllowed &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayUndoCheckoutOption={\r\n                  userPermission.privDMSCanCheckInCheckOutInternalFiles &&\r\n                  undoCheckout &&\r\n                  selectedFiles.length === 1 &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayDeleteOption={\r\n                  userPermission.privDMSCanDeleteFiles &&\r\n                  actionsAllowed &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayDownloadOption={\r\n                  userPermission.privDMSCanViewFileArea &&\r\n                  hasPermission(folderTree, 'FILE_AREA_DOWNLOAD') &&\r\n                  hasFileAreaPermission(fileArea, 'fileDownload')\r\n                }\r\n                displayPropetiesOption={\r\n                  selectedFiles.length === 1 &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayStatusOption={\r\n                  userPermission.privDMSCanManageFileProperties &&\r\n                  actionsAllowed &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayReCategorizeOption={\r\n                  userPermission.privDMSCanRecategorizeFiles &&\r\n                  actionsAllowed &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayCopyOption={\r\n                  userPermission.privDMSCanCopyFiles &&\r\n                  !undoCheckout &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayEmailOption={\r\n                  userPermission.privDMSCanViewFileArea &&\r\n                  !undoCheckout &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayMoveOption={\r\n                  userPermission.privDMSCanMoveFiles &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayRenameOption={\r\n                  userPermission.privDMSCanRenameFiles &&\r\n                  !undoCheckout &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                dispalyPropertiesOption={\r\n                  userPermission.privDMSCanViewFileArea &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                hasSelectedLinkFiles={selectedFiles.some((e) => !e.linked)}\r\n                hasSelectedPublishedFiles={selectedFiles.some((e) => !e.published)}\r\n                displayPublishOption={\r\n                  !undoCheckout &&\r\n                  actionsAllowed &&\r\n                  userPermission.privDMSCanPublishUnpublishInternalFiles &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayLinkOption={\r\n                  !undoCheckout &&\r\n                  actionsAllowed &&\r\n                  userPermission.privDMSCanLinkUnlinkFiles &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayToBeDeleted={\r\n                  actionsAllowed &&\r\n                  selectedFiles.filter((e) => e.status.value === 6).length == 0 &&\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayCopyLinkOption={\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                displayPinUnpinOption={\r\n                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&\r\n                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')\r\n                }\r\n                onDownloadClick={() => {\r\n                  reactDispatch(updateContextMenuDownloadOption(true));\r\n                }}\r\n                onStatusClick={() => reactDispatch(updateContextMenuStatusOption(true))}\r\n                onAssignClick={() => reactDispatch(updateContextMenuAssignOption(true))}\r\n                onReCategorizeClick={() => reactDispatch(updateContextMenuReCategorizeOption(true))}\r\n                onMoveFilesClick={() => reactDispatch(updateContextMenuMoveFilesOption(true))}\r\n                onReNameFilesClick={() => reactDispatch(updateContextMenuReNameFilesOption(true))}\r\n                onCopyFilesClick={() => reactDispatch(updateContextMenuCopyFilesOption(true))}\r\n                onLinkFilesClicked={() => reactDispatch(updateContextMenuLinkFilesOption(true))}\r\n                onUnlinkFilesClicked={() => reactDispatch(updateContextMenuUnlinkFilesOption(true))}\r\n                onCheckoutClick={() => {\r\n                  reactDispatch(updateContextMenuCheckoutOption(true));\r\n                }}\r\n                onUndoCheckoutClicked={() => {\r\n                  handleUndoCheckout('');\r\n                }}\r\n                onDeleteClick={() => reactDispatch(updateContextMenuDeleteOption(true))}\r\n                onPropetiesClicked={() => {\r\n                  reactDispatch(updateContextMenuPropetiesoption(true));\r\n                }}\r\n                onPublishClick={() => {\r\n                  reactDispatch(updateContextMenuPublishFiles(true));\r\n                }}\r\n                onUnpublishClick={() => {\r\n                  reactDispatch(updateContextMenuUnpublishFiles(true));\r\n                }}\r\n                onToBeDeletedClick={() => {\r\n                  reactDispatch(updateContextMenuToBeDeleted(true));\r\n                }}\r\n                onCopyLink={() => {\r\n                  reactDispatch(updateContextMenuCopyLinkFiles(true));\r\n                }}\r\n                onPin={() => {\r\n                  makeAsPinUnPin(selectedFiles, true)\r\n                }}\r\n                onUnPin={() => {\r\n                  makeAsPinUnPin(selectedFiles, false)\r\n                }}\r\n                positon={positon}\r\n                visible={visibleContextMenu}\r\n              />\r\n              <GenericDataTable\r\n                filterCloudPadding={10}\r\n                onFiltersChange={(filters: []) => {\r\n                  rowSelection.selectedRowKeys = [];\r\n                  rowSelection.onChange([], []);\r\n                }}\r\n                onRow={handleRowClick}\r\n                selectedRecordCount={selectedRecords}\r\n                hideHeaderPanel={true}\r\n                endpoint={config.api[OperationalServiceTypes.FileManagementService].files}\r\n                searchPromise={searchPromiseWrapper}\r\n                dataPromise={fetchFiles}\r\n                columnPromise={fetchColumns()}\r\n                scrollColumnCounter={9}\r\n                rowSelection={rowSelection}\r\n                rowKey={'id'}\r\n                tableKey={tableKey}\r\n                groupedValue={siteId}\r\n                sorted={SORTER}\r\n                columnQueryParameters={[{ key: 'siteid', value: siteId }, { key: 'binderId', value: binderId }]}\r\n                searchQueryParameters={setQueryParameters()}\r\n                searchFieldParameters={setQueryParameters()}\r\n                customRender={renderGridColumns()}\r\n                lowResolutionWidth=\"auto\"\r\n                highResolutionWidth=\"auto\"\r\n                fixedColumns={['title']}\r\n                isDraggable={true}\r\n                onErrorLoading={(error) => {\r\n                  if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n                    props.history.push('/forbidden');\r\n                  }\r\n                }}\r\n                showPublishedIcon={true}\r\n                filterByIds={decodeURIComponent(urlParams.get('ids') ?? '')}\r\n              />\r\n            </div>\r\n          </div>\r\n        </div>\r\n      </PageContent>\r\n      {/* <Modal\r\n          closable={false}\r\n          style={{ top: 20, width: '60wv' }}\r\n          visible={showSelectTemplateModal}\r\n          title={'Select Template'}\r\n          footer={[\r\n            <Button key=\"update\" type=\"primary\" onClick={handleCancel} >\r\n              Cancel\r\n            </Button>,\r\n            <Button key=\"update\" type=\"primary\" onClick={handleSelectedTemplate} disabled={!selectedTemplateId}>\r\n              Apply\r\n            </Button>,\r\n          ]}\r\n      >\r\n        <Row>\r\n        <Col span={24}>\r\n          <Form.Item label=\"Template\" name=\"template\">\r\n            <Select\r\n                showSearch\r\n                style={{ width: \"100%\" }}\r\n                getPopupContainer={(trigger) =>\r\n                  trigger.parentNode as HTMLElement\r\n                }\r\n                onChange={(e: any) => {\r\n                  setSelectedTemplateId(e)\r\n                }}\r\n            >\r\n              {activeTemplates &&\r\n              renderOptions(activeTemplates)}\r\n            </Select>\r\n          {<FolderTree\r\n              key={selectedTemplateId}\r\n              showTitle={false}\r\n              maxHeight=\"500px\"\r\n              data={{ siteId, siteName: decodeURIComponent(siteName), siteStatusId: 1, folders: getSelectedTemplateFolders(selectedTemplateId) }}\r\n              disableRoot={true}\r\n              controlSelection={true}\r\n              defaultExpandAll={true}\r\n              disabled={true}\r\n              onSelectFolder={(e) => logger.debug('File Area','Folder tree-Folder selected', e)} />}\r\n          </Col>\r\n        </Row>\r\n      </Modal> */}\r\n      {/*  Download Option Menu Modal */}\r\n      <ModalCustom\r\n        visible={showDownloadModal}\r\n        title={'Download Files'}\r\n        size={'small'}\r\n        onCancel={handleOnDownloadModalCancel}\r\n        footer={[\r\n          <Button onClick={handleOnDownloadModalCancel} key=\"submit\" type=\"primary\">\r\n            Done\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          {selectedFile && <DownloadModal\r\n            hasDownloaded={(hasDownloaded: boolean) => {\r\n              if (hasDownloaded) {\r\n                setShowDownloadModal(false);\r\n              }\r\n            }}\r\n            selectedFiles={[selectedFile]}\r\n            downloadType={downloadType}\r\n          />}\r\n        </div>\r\n      </ModalCustom>\r\n\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default withRouter(Page);\r\n"]}, "metadata": {}, "sourceType": "module"}