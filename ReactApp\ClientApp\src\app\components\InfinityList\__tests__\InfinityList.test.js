import React from "react";
import renderer from "react-test-renderer";
import initTestSuite from "@app/utils/config/TestSuite";
import { shallow, mount } from "enzyme";
import { List, Input } from "antd";

import InfinityList from "..";

const Search = Input.Search;

describe("<InfinityList/>", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render component", () => {
    const component = shallow(<InfinityList />);
    expect(component.html()).not.toBe(null);
  });

  it("should render component with props", () => {
    const component = shallow(
      <InfinityList
        notFoundContent="No Results Found"
        hasCheckbox={true}
        hasSearch={true}
        paginatedLimit={10}
        heightInPx={400}
        formatValue={(value) => {
          return <div>{value.displayText}</div>;
        }}
        onSelect={(selectedData) => {
          console.log(selectedData);
        }}
      />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer.create(<InfinityList />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("should contain a List component", () => {
    const component = mount(<InfinityList />);
    expect(component.find(List).length).toBe(1);
  });

  it("should contain a Search component", () => {
    const component = mount(<InfinityList hasSearch={true} />);
    expect(component.find(Search).length).toBe(1);
  });

  it("should not contain a Search component", () => {
    const component = mount(<InfinityList />);
    expect(component.find(Search).length).toBe(0);
  });
});
