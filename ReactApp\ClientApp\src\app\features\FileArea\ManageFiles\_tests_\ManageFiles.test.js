import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";
import ManageFiles from "../index";
import { Provider } from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import initTestSuite from "@app/utils/config/TestSuite";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const CustomManageFiles = (props) => {
  const INITIAL_STATE = {
    fileDetails: {},
    fileArea: {},
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <ManageFiles {...props} />
    </ReduxProvider>
  );
};

describe("Manage Files Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render", () => {
    const mfComponent = shallow(<CustomManageFiles />);
    expect(mfComponent.html()).not.toBe(null);
  });

  it("should create and match to snapshot", () => {
    const mfComponent = renderer
      .create(
        <CustomManageFiles
          siteId={"xxx"}
          dataList={[]}
          onFilesChange={() => {}}
          onFormChange={() => {}}
        />
      )
      .toJSON();
    expect(mfComponent).toMatchSnapshot();
  });

  it("should render with props", () => {
    const mfComponent = shallow(
      <CustomManageFiles
        siteId={"xxx"}
        dataList={[]}
        onFilesChange={() => {}}
        onFormChange={() => {}}
      />
    );
    expect(mfComponent.html()).not.toBe(null);
  });

  it("should render with props are null", () => {
    const mfComponent = shallow(
      <CustomManageFiles
        siteId={null}
        dataList={null}
        onFilesChange={null}
        onFormChange={null}
      />
    );
    expect(mfComponent.html()).not.toBe(null);
  });

  it("should render with props are undefined", () => {
    const mfComponent = shallow(
      <CustomManageFiles
        siteId={undefined}
        dataList={undefined}
        onFilesChange={undefined}
        onFormChange={undefined}
      />
    );
    expect(mfComponent.html()).not.toBe(null);
  });
});
