import moment from "moment";
import { LicenseDetails } from "@app/types/LicenseDetails";
import { ValueType } from "@app/types/ValueType";

export default (details: LicenseDetails | undefined) => {
  return {
    companyName: details?.companyName,
    vertical: details?.vertical.value || details?.vertical,
    status: details?.status?.value || details?.status,
    effectiveDate: details?.effectiveDate
      ? moment(details?.effectiveDate)
      : null,
    expirationDate: details?.expirationDate
      ? moment(details?.expirationDate)
      : null,
    compliances: details?.compliances?.map((i: ValueType) => i.value || i),
    licenseId: details?.id,
    allocatedSpace: details?.allocatedSpace?.value,
    userCount: details?.userCount?.value,
    supportLevel:
      details?.supportLevel?.value || details?.supportLevel || undefined,
    integrations: details?.integrations?.map((i: ValueType) => i.value || i),
    modules: details?.modules,
  };
};
