import { publishFilesRequest } from "@app/api/fileAreaService";
import {
  errorNotification,
  successNotification,
} from "@app/utils/antNotifications";
import logger from "@app/utils/logger";

export const publishFiles = async (
  fileIds: string[],
  siteId: string,
  expirationDate: Date | undefined
): Promise<{
  data: boolean;
  hasError: boolean;
  errorCode: number | null;
}> => {
  try {
    const response = await publishFilesRequest(fileIds, siteId, expirationDate);
    var errorOccured = false;
    if (!response) {
      errorOccured = true;
      errorNotification([""], "File(s) could not be published.");
    } else {
      successNotification([""], "File(s) published successfully.");
    }
    return { data: true, errorCode: null, hasError: errorOccured };
  } catch (error) {
    logger.error("File Area", "File Area Action Panel", error);
    if (error.statusCode == 403)
      errorNotification(
        [""],
        "You do not have the permission to perform this action. Please refresh and try again."
      );
    else errorNotification([""], "File(s) could not be published.");
    return {
      data: false,
      errorCode: error.statusCode ? error.statusCode : null,
      hasError: true,
    };
  }
};
