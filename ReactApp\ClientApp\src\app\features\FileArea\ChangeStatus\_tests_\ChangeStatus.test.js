import React from "react";
import {Select, Form, Row, Col} from "antd";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import ChangeStatus from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import SelectedFilesGrid from "../../SelectedFilesGrid";

jest.mock("../index.module.less", () => ({
    yjModalContentWrapper: "yjModalContentWrapper",
    yjChangeStatusGrid: "yjChangeStatusGrid",
    yjChangeStatusNew: "yjChangeStatusNew",
    yjChangeStatusSelectNew: "yjChangeStatusSelectNew",
}));

describe("Change Status Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

   it("should render",() => {
       const csComponent = shallow(<ChangeStatus />);
       expect(csComponent.html()).not.toBe(null);
   }) ;

   it("should create and match to snapshot",() => {
       const tmComponent = renderer.create(<ChangeStatus />).toJSON();
       expect(tmComponent).toMatchSnapshot();
   });

   it("should render with props",() => {
       const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
       expect(csComponent.html()).not.toBe(null);
   });

    it("should render with props are null",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={null} onClosePopup={null} onNewStatusSelect={null} />);
        expect(csComponent.html()).not.toBe(null);
    });

    it("should render with props are undefined",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={undefined} onClosePopup={undefined} onNewStatusSelect={undefined} />);
        expect(csComponent.html()).not.toBe(null);
    });

    it("should have a Select element",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(Select)).toHaveLength(1);
    });

    it("should have a Form element",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(Form)).toHaveLength(1);
    });

    it("should have a Form.Item element",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(Form.Item)).toHaveLength(1);
    });

    it("should have a Row element",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(Row)).toHaveLength(1);
    });

    it("should have Col elements",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(Col)).toHaveLength(2);
    });

    it("should have a SelectedFilesGrid component",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(SelectedFilesGrid)).toHaveLength(1);
    });

    it("should have div elements",() => {
        const csComponent = shallow(<ChangeStatus selectedFiles={[]} onClosePopup={() => {}} onNewStatusSelect={() => {}} />);
        expect(csComponent.find(".yjModalContentWrapper")).toHaveLength(1);
        expect(csComponent.find(".yjChangeStatusGrid")).toHaveLength(1);
        expect(csComponent.find(".yjChangeStatusNew")).toHaveLength(1);
        expect(csComponent.find(".yjChangeStatusSelectNew")).toHaveLength(1);
    });

});
