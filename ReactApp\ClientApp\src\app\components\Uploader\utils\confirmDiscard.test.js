import confirmDiscard from "./confirmDiscard";

describe("Confirm Discard Popup Test Suite", () => {
  it("confirmDiscard function should exsist", () => {
    expect(typeof confirmDiscard).toBe("function");
  });
  it("confirmDiscard function should return a value", () => {
    expect(confirmDiscard(() => {})).not.toBe(null);
  });
  it("confirmDiscard function should return a object", () => {
    expect(typeof confirmDiscard(() => {})).toBe("object");
  });
});
