import { shallow } from "enzyme";
import React from "react";
import { UploadProgress } from "..";
import ProgressingFile from "../ProgressingFile";
import { Button } from "antd";

describe("<UploadProgress />", () => {
  it("should render component", () => {
    const component = shallow(<UploadProgress files={{}} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render with props", () => {
    const component = shallow(
      <UploadProgress
        files={{}}
        onRetry={() => null}
        onRetryAll={() => null}
        onDelete={() => null}
        onDeleteAll={() => null}
        onComplete={() => null}
      />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should render ProgressingFile once for a single file", () => {
    const component = shallow(
      <UploadProgress files={{ "test-id": { name: "file1", size: 123 } }} />
    );
    expect(component.find(ProgressingFile)).toHaveLength(1);
  });

  it("should render ProgressingFile twice for two files", () => {
    const component = shallow(
      <UploadProgress
        files={{
          "test-id1": { name: "file1", size: 123 },
          "test-id2": { name: "file2", size: 123 },
        }}
      />
    );
    expect(component.find(ProgressingFile)).toHaveLength(2);
  });

  it("should pass given onRetry function", () => {
    const onRetry = () => null;
    const component = shallow(
      <UploadProgress
        files={{ "test-id": { name: "file1", size: 123 } }}
        onRetry={onRetry}
      />
    );
    expect(component.find(ProgressingFile).prop("onRetry")).toEqual(onRetry);
  });

  it("should pass given onDelete function", () => {
    const onDelete = () => null;
    const component = shallow(
      <UploadProgress
        files={{ "test-id": { name: "file1", size: 123 } }}
        onDelete={onDelete}
      />
    );
    expect(component.find(ProgressingFile).prop("onDelete")).toEqual(onDelete);
  });

  it("should pass given onRetryAll function", () => {
    const onRetryAll = () => null;
    const component = shallow(
      <UploadProgress
        files={{ "test-id": { name: "file1", size: 123 } }}
        onRetryAll={onRetryAll}
      />
    );
    expect(component.find(Button).at(0).prop("onClick")).toEqual(onRetryAll);
  });

  it("should pass given onDeleteAll function", () => {
    const onDeleteAll = () => null;
    const component = shallow(
      <UploadProgress
        files={{ "test-id": { name: "file1", size: 123 } }}
        onDeleteAll={onDeleteAll}
      />
    );
    expect(component.find(Button).at(1).prop("onClick")).toEqual(onDeleteAll);
  });
});
