import { Col, Form, FormInstance, Input, Row, Switch, Tooltip } from "antd";
import React, { useEffect, useState } from "react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { Rule } from "antd/lib/form";

import {
  required,
  type,
  typeWithPattern,
} from "../../components/forms/validators";
import { AvoidWhitespace, EmailPattern } from "@app/utils/regex";
import "./contactinput.css";
import {
  prevenPrecedingSpaces,
  prevenPrecedingSpacesOnPaste,
} from "@app/utils/forms";
import { checkContactExsistance } from "@app/api/contactService";
import AddressDetails from "@app/features/Site/AddressDetails";

export interface Contact {
  formRef?: FormInstance;
  contacts?: any[];
  onFinish?: (values: any) => void;
  onChange?: () => void;
  isEmailDiert?: boolean;
}

const layout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

const EMAIL_PLACEHOLDER = "<EMAIL>";
const MAX_LENGTH_FIFTY = 50;
const MAX_LENGTH_TWO_FIVE_FOUR = 254;
const CONTACT_ERROR_CODE = "40003";

export default ({
  formRef,
  contacts,
  onChange,
  onFinish,
  isEmailDiert,
}: Contact) => {
  const [currentEmail, setCurrentEmail] = useState("");

  const exsisEmail = (emailInput: any): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (isEmailDiert && emailInput === currentEmail) resolve(true);
      if (EmailPattern.test(emailInput)) {
        if (contacts && contacts.length > 0) {
          contacts.forEach((contact: any) => {
            if (contact.email === emailInput) {
              reject("The contact already exists");
            }
          });
        }
        checkContactExsistance(emailInput)
          .then((response) => {
            const errorCode = response.headers["errorcode"];
            if (errorCode && errorCode === CONTACT_ERROR_CODE) {
              reject("The contact already exists");
            } else {
              reject("The user already exists");
            }
          })
          .catch(() => {
            resolve(true);
          });
      } else {
        resolve(true);
      }
    });
  };

  const emailExsistance = (): Rule => ({
    validator(_rule, value) {
      return value ? exsisEmail(value) : Promise.resolve();
    },
  });

  useEffect(() => {
    if (!isEmailDiert) {
      formRef?.resetFields();
      formRef?.setFieldsValue({ countryCode: "us" });
    } else {
      setCurrentEmail(formRef?.getFieldValue("email"));
    }
  }, [formRef]);

  return (
    <div>
      <Form
        className="yjModalContainerWrapper"
        form={formRef}
        key="contactForm"
        onFinish={onFinish}
        {...layout}
        layout="horizontal"
        onChange={onChange}
      >
        <Row gutter={24}>
          <Col span={8}>
            <Form.Item hidden name="tmpId" noStyle>
              <Input type="hidden" />
            </Form.Item>
            <Form.Item
              label={"First Name"}
              name="firstName"
              rules={[required, typeWithPattern("string", AvoidWhitespace)]}
            >
              <Input
                maxLength={MAX_LENGTH_FIFTY}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                autoComplete="off"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={"Last Name"}
              name="lastName"
              rules={[required, typeWithPattern("string", AvoidWhitespace)]}
            >
              <Input
                maxLength={MAX_LENGTH_FIFTY}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                autoComplete="off"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={"Email Address"}
              name="email"
              rules={[

                typeWithPattern("string", AvoidWhitespace),
                type("email", `Invalid Input. E.g. ${EMAIL_PLACEHOLDER}`),
                emailExsistance(),
                required,
              ]}
            >
              <Input
                maxLength={MAX_LENGTH_TWO_FIVE_FOUR}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                placeholder={EMAIL_PLACEHOLDER}
                autoComplete="off"
              />
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label={"Contact Number"}
              name="contactNumber"
              rules={[typeWithPattern("string", AvoidWhitespace)]}
            >
              <PhoneInput
                searchNotFound="No Country Codes Available"
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                onChange={(_value, data: any) => {
                  formRef?.setFieldsValue({ countryCode: data.countryCode });
                }}
                enableAreaCodeStretch={true}
                country="us"
                enableSearch
              />
            </Form.Item>
            <Form.Item hidden name="countryCode">
              <></>
            </Form.Item>
          </Col>
          <Col span={8}>
            <Form.Item
              label="Portal Access"
              name="portalAccess"
              valuePropName="checked"
            >
              <Switch onChange={onChange} />
            </Form.Item>
          </Col>
        </Row>

        <h2 className="yjModuleSubHeading">Address Details</h2>
        <hr />
        <AddressDetails />
      </Form>
    </div>
  );
};
