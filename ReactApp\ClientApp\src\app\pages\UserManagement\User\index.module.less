@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjModalContentWrapper {
  max-height: 65vh;
  overflow-x: hidden;
  overflow-y: auto;
  padding: 1.5em;
}

.yjGridTextCenter {

  span {
    font-size: 13px;
    text-decoration: underline;
  }
}

.yjManageUsersInfinityListComponent {

  li {
    margin: 0;
    padding: 7px;

    p {
      margin-bottom: 5px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;
    }
  }
}
