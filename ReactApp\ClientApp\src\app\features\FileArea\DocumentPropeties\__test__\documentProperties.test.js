import DocumentProperties from "../index";
import { shallow, mount } from "enzyme";
import React from "react";
import renderer from "react-test-renderer";
import { Drawer, List } from "antd";
import initTestSuite from "@app/utils/config/TestSuite";

const file = {
  title: "test File",
  size: "30 KB",
  type: "TXT",
  year: 2010,
  status: { name: "test", value: 1 },
  assignee: { assignee: "test", value: 1 },
  created: "2020-08-31T18:26:02.047",
  id: "AA-00290",
};

describe("Document properties test suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("Document properties should render", () => {
    const component = shallow(<DocumentProperties />);
    expect(component.html()).not.toBe(null);
  });
  it("Document properties should render , and create the snapshot properly", () => {
    const component = renderer.create(<DocumentProperties />).toJSON();
    expect(component).toMatchSnapshot();
  });
  it("Document properties should render when prop file is null ", () => {
    const component = shallow(<DocumentProperties file={null} />);
    expect(component.html()).not.toBe(null);
  });
  it("Document properties should render when prop file is not null ", () => {
    const component = shallow(<DocumentProperties file={file} />);
    expect(component.html()).not.toBe(null);
  });

  it("Document properties should contain a Drawer", () => {
    const component = mount(<DocumentProperties file={file} />);
    expect(component.find(Drawer).length).toBe(1);
  });
  it("Document properties should contain a list", () => {
    const component = mount(
      <DocumentProperties displayDrawer={true} file={file} />
    );
    expect(component.find(List).length).toBe(1);
  });
});
