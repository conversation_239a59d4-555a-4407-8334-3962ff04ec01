import React, { Fragment, useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON> } from "antd";
import {
  EditOutlined,
  FolderFilled,
  PlusOutlined,
  FolderOutlined,
  FolderOpenOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { withRouter } from "react-router-dom";
import styles from "./index.module.less";
import PageContent from "../../../components/PageContent";
import PageTitle from "../../../components/PageTitle";
import Modal from "@app/components/Modal";
import { orderByAlphaNumericTitle } from "@app/components/FolderTreeEditor";
import GenericDataTable from "@app/components/GenericDataTable";
import config from "@app/utils/config";
import { Sorter } from "../../../components/GenericDataTable/util";
import { getFolderDetails } from "@app/api/channelService";
import userStatusColorSwitch from "@app/utils/css/userStatusColorSwitch";
import InfinityList from "@app/components/InfinityList";
import { getParameterizedUrl } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import {renderTag} from "@app/components/Tag";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const { DirectoryTree } = Tree;

const SORTER: Sorter = {
  value: "created",
  order: "descend",
};

const MANAGE_CHANNELS_EDIT_PAGE = "/master-data/manage-channels/edit";
const FOLDER_COLOR_BLUE = "#419cb9";

const Page = (props: any) => {
  const [selectedRequestRowKeys, setSelectedRequestRowKeys] = useState([]);
  const [showFolderStructureModal, setShowFolderStructureModal] = useState<
    boolean
  >(false);
  const [showViewSitesModal, setViewSitesModal] = useState<boolean>(false);
  const [channelName, setChannelName] = useState("");
  const [channelId, setChanneId] = useState("");
  const [folderTree, setfolderTree] = useState([]);

  const rowSelectionRequest = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRequestRowKeys(selectedRowKeys);
    },
    selectedRowKeys: selectedRequestRowKeys,
    fixed: true,
  };

  const onRowClick = (event: any, record: any) => {
    setChannelName(record.name);
    setChanneId(record.channelId);
  };

  const handleRowClick = (record: any, rowIndex: any) => ({
    onClick: (event: any) => onRowClick(event, record),
  });

  const renderSubFolders = (
    subFolders: Array<any>,
    parentFolderIndex: number
  ) => {
    return orderByAlphaNumericTitle(
      subFolders.map((subNode: any, index: number) => {
        return {
          key: `${parentFolderIndex}_${index}`,
          title: subNode.name,
          selectable: false,
          icon: <FolderFilled style={{ color: FOLDER_COLOR_BLUE }} />,
          isLeaf: true,
        };
      })
    );
  };

  const handleDataToTree = (tree: any) => {
    return tree.map((node: any, index: number) => {
      return {
        key: index,
        parent: true,
        title: node.name,
        children: renderSubFolders(node.subFolders, index),
        selectable: true,
      };
    });
  };

  const showFolderStructure = (folderStructurePath: string) => {
    setfolderTree([]);
    getFolderDetails(folderStructurePath).then((res: any) => {
      setfolderTree(orderByAlphaNumericTitle(handleDataToTree(res.data)));
      setShowFolderStructureModal(true);
    });
  };

  const renderGridColumns = () => {
    return {
      status: (record: any) => {
        return record
          ? renderTag(
              record.name,
              record.value,
              userStatusColorSwitch(record.value)
            )
          : renderTag("Inactive", -1, userStatusColorSwitch(-1));
      },
      sitesCount: (record: any) => {
        return record > 0 ? (
          <Button
            onClick={() => setViewSitesModal(true)}
            type="link"
            className={styles.yjGridTextCenter}
          >
            {record === 1 ? `1 Site` : `${record} Site(s) `}
          </Button>
        ) : (
          `No Sites`
        );
      },
      folderStructure: (record: string) => {
        return (
          <Button
            type="link"
            onClick={() => {
              showFolderStructure(record);
            }}
          >
            view
          </Button>
        );
      },
      created: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      modified: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      name: (record: any) => {
        return (
          <Tooltip placement="leftTop" title={record}>
            <p className={styles.yjChannelGridTextWrap}>{record}</p>
          </Tooltip>
        );
      },
      action: (text: any, record: any) => {
        return (
          <div className={`${styles.yjActionIconWrapper} yJFileAreaRow`}>
            <Tooltip className="yJFileAreaRow" title="Edit">
              <Button
                className="yJFileAreaRow"
                onClick={() => {
                  props.history.push(
                    `${MANAGE_CHANNELS_EDIT_PAGE}/${record.officeKey}`
                  );
                }}
                icon={<EditOutlined />}
              />
            </Tooltip>
          </div>
        );
      },
    };
  };

  const redirectToCreateChannelsPage = () => {
    props.history.push("/master-data/manage-channels/create");
  };

  const renderFolderTreeModal = () => {
    return (
      <Modal
        visible={showFolderStructureModal}
        title={`Folder Structure of ${channelName}`}
        onCancel={() => setShowFolderStructureModal(false)}
        footer={[
          <Button
            key="back"
            type="default"
            onClick={() => setShowFolderStructureModal(false)}
          >
            CLOSE
          </Button>,
        ]}
        size="small"
      >
        <div>
          <DirectoryTree
            multiple
            treeData={folderTree}
            icon={(e: any) =>
              e.data.children.length > 0 && e.expanded ? (
                <FolderOpenOutlined />
              ) : (
                <FolderOutlined />
              )
            }
          />
        </div>
      </Modal>
    );
  };

  const renderViewSitesModal = () => {
    return (
      <Modal
        visible={showViewSitesModal}
        title={`View Sites of ${channelName}`}
        onCancel={() => setViewSitesModal(false)}
        footer={[
          <Button
            key="back"
            type="default"
            onClick={() => setViewSitesModal(false)}
          >
            cancel
          </Button>,
        ]}
        size="small"
      >
        <div>
          <InfinityList
            paginatedLimit={20}
            endpoint={getParameterizedUrl(
              config.api[OperationalServiceTypes.MasterDataService]
                .getSitesByChannel,
              channelId
            )}
            idKeyValue="siteId"
            notFoundContent="No Site(s) Found"
            listClassName={"yjInfinityListClass"}
            formatValue={(value: any) => {
              return (
                <div className={styles.yjManageChannelsInfinityListComponent}>
                  <div className={styles.yjManageChannelsListItemValue}>
                    <Tooltip placement={"topLeft"} title={value.name}>
                      <p>{value.name}</p>
                    </Tooltip>
                  </div>
                  <div className={styles.yjManageChannelsListItemAction}>
                    <Button
                      className="yJFileAreaRow"
                      onClick={() => {
                        const encryptedName = encodeURIComponent(value.name);
                        const url = `/client-file-area/${value.siteId}/${encryptedName}/${channelId}`;
                        const encodedUrl = encodeURI(url);
                        props.history.push(encodedUrl);
                      }}
                      icon={<FileTextOutlined />}
                    />
                  </div>
                </div>
              );
            }}
          />
        </div>
      </Modal>
    );
  };

  return (
    <Fragment>
      {renderFolderTreeModal()}
      {renderViewSitesModal()}
      <PageTitle title={props.title}>
        <Button
          style={{ display: "none" }}
          onClick={redirectToCreateChannelsPage}
          type="primary"
          icon={<PlusOutlined />}
        >
          Create Office
        </Button>
      </PageTitle>
      <PageContent>
        <div className="yjCustomTblHeader">
          <GenericDataTable
            tableKey={"manageChannels"}
            rowSelection={rowSelectionRequest}
            endpoint={
              config.api[OperationalServiceTypes.MasterDataService]
                .createChannel
            }
            rowKey={"channelId"}
            onRow={handleRowClick}
            scrollColumnCounter={7}
            selectedRecordCount={selectedRequestRowKeys.length}
            customRender={renderGridColumns()}
            sorted={SORTER}
            fixedColumns={["channelId", "name"]}
            isDraggable={true}
          />
        </div>
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
