@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.stepContentWrapper {
  background: @color-bg-wizard-container;
  margin-top: 1em;
  padding: 1.5em;

  .stepContentHeader {
    color: @color-secondary;
    font-size: @font-size-lg;
    margin-bottom: 1em;
    margin-top: 20px;
    text-transform: @yj-transform;

    .font-mixin(@font-primary, @yjff-bold);
  }
}

.stepButtonWrapper {
  background: @color-bg-wizard-button-section;
  border-top: 1px solid @border-color-base;
  padding: 1em;

  .yjWizardPageTransitionButton {
    background-color: darken(@color-primary, 20%);
    border-color: darken(@color-primary, 20%);
    margin-right: 0;
  }

  .stepButtonGroupWrapper {

    button {
      margin: 0 .3em;
    }

    .yjWizardPageTransitionButton {
      background-color: darken(@color-primary, 20%);
      border-color: darken(@color-primary, 20%);
      margin-right: 0;
    }
  }

  .flex-mixin(center, flex, space-between);
}

.yjInfoIcon {
  color: @color-button-primary;
  position: relative;
  top: -4px;
}
