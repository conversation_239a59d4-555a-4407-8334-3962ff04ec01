export const getTableHeaderElement = (tableKey: string): HTMLElement => {
  return document
    .getElementById(tableKey)
    ?.closest(".ant-table-header") as HTMLElement;
};

export const handleScrollPlacement = (
  key: any,
  formRef: React.MutableRefObject<any>
) => {
  const hideDropDown = () => {
    formRef.current?.blur();
  };

  getTableHeaderElement(key)?.nextSibling?.addEventListener(
    "scroll",
    hideDropDown
  );
  return () => {
    getTableHeaderElement(key)?.nextSibling?.removeEventListener(
      "scroll",
      hideDropDown
    );
  };
};
