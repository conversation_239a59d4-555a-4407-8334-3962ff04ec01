import config from "@app/utils/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import GenericDataTable from "@app/components/GenericDataTable";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { getAutocompleteOptions, getColumns, getRecords } from "@app/api/genericDataTable";
import styles from "@app/features/DashboardV3/index.module.less";
import { Button, Modal as AntModal } from "antd";
import { DeleteOutlined, EditOutlined, ExclamationCircleOutlined } from "@ant-design/icons/lib";
import debounce from "lodash/debounce";
import { Sorter } from "@app/components/GenericDataTable/util";
import logger from "@app/utils/logger";
import { deleteAutoFilingRule } from "@app/api/autoFilingService";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const SORTER: Sorter = {
  value: "ruleName",
  order: "ascend",
};
const { confirm } = AntModal;

const AutoFilingGrid = forwardRef(({ onEdit }: any, ref) => {
  const [tableKey, setTableKey] = useState('autoFilingManagement');
  const [updatedUploadtGrid, setUpdatedUploadedGrid] = useState<boolean>(false);
  const columnPromise = getColumns(config.api[OperationalServiceTypes.FileManagementService].autoFilingRules, 'AutoFilingRulesGrid');

  useImperativeHandle(ref, () => ({
    refresh: tableKeyUpdate
  }));

  const tableKeyUpdate = () => {
    /**
     * TODO Refresh should address with forwardRef and useImperativeHandle method inside GenericDataTable as well but
     * GenericDataTable need some refactor
     * As soon as got time we need to refactor this component
     */
    setTableKey(`autoFilingManagement`)
  }

  const handleEdit = (record: any) => {
    onEdit(record);
    tableKeyUpdate();
  };
  const onDelete = (record: any) => {
    logger.verbose('AutoFilingGrid', 'onDelete', record);
    deleteAutoFilingRule(record).then((res) => {
      logger.verbose('AutoFilingGrid', 'deleteAutoFilingRule', res);
      tableKeyUpdate();
      setUpdatedUploadedGrid(true);
      setTimeout(() => {
        setUpdatedUploadedGrid(false);
      });
    })
  }

  const handleDelete = (record: any) => {
    confirm({
      title: "The Rule will be deleted. Are you sure you want to proceed?",
      icon: <ExclamationCircleOutlined />,
      okText: "Yes",
      cancelText: "No",
      onOk() {
        onDelete(record);
      },
    });
  };



  let fetchData = (state: any, transformFilters: any, queryParams: any) => {
    logger.debug("AutoFilingGrid", "fetchData", {
      pagination: {
        current: state.pagination.current,
        pageSize: state.pagination.pageSize,
      },
      sorterInfo: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
    });

    return getRecords(
      config.api[OperationalServiceTypes.FileManagementService].autoFilingRules,
      {
        pagination: {
          current: state.pagination.current,
          pageSize: state.pagination.pageSize,
        },
        sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
        filters: transformFilters,
        columns: state.columns.filter((i: any) => i.default === false && i.selected === true).map((i: any) => i.key),
      }, queryParams
    )
  };

  const renderSiteGridColumns = () => {
    return {
      hide: (value: string) => {
        return value ? 'Yes' : 'No';
      },
      created: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      modified: (value: string) => {
        return <FormattedDateTime value={value} />;
      },

      action: (text: any, record: any) => {
        return (
          <div className={"yjActionIconWrapper"}>
            <div className={`${styles.yjActionIconWrapper} `}>
              <Button onClick={() => handleEdit(record)} icon={<EditOutlined />} />
            </div>
            <div className={`${styles.yjActionIconWrapper} `}>
              <Button onClick={() => handleDelete(record)} icon={<DeleteOutlined />} />
            </div>
          </div>

        );
      },
    };
  };

  // Debounced API call
  const debouncedApiCall = debounce(
    (props: any, value: string, callback: (data: any) => void) => {
      logger.debug('AutoFiling', 'debouncedApiCall', {
        key: props.data?.key,
        value,
      });

      getAutocompleteOptions(
        config.api[OperationalServiceTypes.FileManagementService].autoFilingRules,
        props.data.key,
        value,
        props.searchFieldParameters
      )
        .then((data: any) => {
          callback(data.data);
        })
        .catch(() => {
          callback([]);
        });
    },
    config.inputDebounceInterval
  );

  // Wrapper function to return a Promise
  const searchPromiseWrapper = (props: any, value: string, callback: any) => {
    debouncedApiCall(props, value, (data: any) => {
      callback(data)
    });
  };

  return (
    <GenericDataTable
      searchPromise={searchPromiseWrapper}
      dataPromise={(state, transformFilters, queryParams) => fetchData(state, transformFilters, queryParams)}
      columnPromise={columnPromise}
      rowKey={"siteId"}
      scrollColumnCounter={7}
      tableKey={tableKey}
      sorted={SORTER}
      fixedColumns={["name"]}
      isDraggable={true}
      noRecordsAvilableMessage={"No Auto Filing Rule Available"}
      hasFilterManagement={true}
      customRender={renderSiteGridColumns()}
      onGridStateChange={console.info}
      showRefreshButton={true}
      updateGrid={() => {
        setUpdatedUploadedGrid(true);
        setTimeout(() => {
          setUpdatedUploadedGrid(false);
        });
      }}
      updatedGrid={updatedUploadtGrid}
    />);
});

export default AutoFilingGrid;
