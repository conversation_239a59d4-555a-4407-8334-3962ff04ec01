{"ast": null, "code": "import \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/modal/style\";\nimport _Modal from \"antd/es/modal\";\nimport \"antd/es/select/style\";\nimport _Select from \"antd/es/select\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\FileArea\\\\HeaderButtonPanel\\\\fileAreaButtonPanel.tsx\";\nimport React, { useCallback, useEffect, useState } from \"react\";\nimport { ExclamationCircleOutlined, ShareAltOutlined } from \"@ant-design/icons\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { useParams } from \"react-router-dom\";\nimport styles from \"./index.module.less\";\nimport Request from \"@app/components/PortalFiles/Request\";\nimport Modal from \"@app/components/Modal\";\nimport { useForm } from \"antd/lib/form/Form\";\nimport { getPortalFileRequestById, saveRequest, updateRequest } from \"@app/api/portalServices\";\nimport { errorNotification, infoNotification, successNotification, warningNotification } from \"@app/utils/antNotifications\";\nimport logger from \"@app/utils/logger\";\nimport { updateFileRequestSent, updateLoadGridOption, updatePortalFilesSelectedRequest } from \"@app/redux/actions/fileAreaActions\";\nimport { checkCheckoutFileListBySiteId, checkInFiles, removeFiles } from \"@app/api/fileAreaService\";\nimport CheckIn from \"../CheckIn\";\nimport { formActions } from \"@app/types\";\nimport { encrypt } from \"@app/utils/crypto/cryptoText\";\nimport moment from \"moment\";\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\nconst {\n  Option\n} = _Select;\nconst {\n  confirm\n} = _Modal;\nconst INTERNEL_FILE_SECTION = \"internal\";\nconst PORTAL_FILE_SECTION = \"portal\";\nexport const FileAreaButtonPanel = props => {\n  const [showCheckinModal, setShowCheckinModal] = useState(false);\n  const [allowCheckinButton, setAllowCheckinButton] = useState(false);\n  const [checkinList, setCheckinList] = useState([]);\n  const [hasCheckinList, setHasCheckinList] = useState(false);\n  const {\n    fileAreaSettings,\n    loadGrid\n  } = useSelector(state => state.fileArea);\n  const {\n    userPermission\n  } = useSelector(state => state.userManagement);\n  const [requestFormValidated, setRequestFormValidated] = useState(false);\n  const {\n    siteId,\n    siteName,\n    channelId,\n    binderId,\n    binderName\n  } = useParams();\n\n  const handleSuccessfulCheckins = () => {\n    infoNotification([\"\"], \"File(s) are being checked in. Please wait.\");\n\n    if (emailForm.getFieldsValue().emailUser || emailForm.getFieldsValue().emailContact) {\n      successNotification([\"\"], \"File Check-in Email sent Successfully\");\n    }\n\n    setShowCheckinModal(false);\n  };\n\n  const onCheckin = () => {\n    checkInFiles(props.binderId, checkinList).then(response => {\n      handleSuccessfulCheckins();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([\"\"], \"You do not have the permission to perform this action. Please refresh and try again\");\n      } else {\n        errorNotification([\"\"], \"Check-In Failed\");\n      }\n\n      logger.error(\"File Are Section\", \"File Area Button Panel\", error);\n    });\n  };\n\n  useEffect(() => {\n    setFileSelection(props.fileSection ? props.fileSection : \"\");\n  }, [props.fileSection]);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setFileSelection(props.fileSection ? props.fileSection : \"\");\n  }, [props.fileSection]);\n  useEffect(() => {\n    onCheckCheckoutFileListBySiteId();\n  }, [props.siteId, dispatch, fileAreaSettings]);\n  useEffect(() => {\n    if (loadGrid) {\n      onCheckCheckoutFileListBySiteId();\n    }\n  }, [loadGrid]);\n\n  const onCheckCheckoutFileListBySiteId = () => {\n    userPermission.privDMSCanCheckInCheckOutInternalFiles && checkCheckoutFileListBySiteId(props.binderId).then(() => {\n      setHasCheckinList(true);\n      dispatch(updateLoadGridOption(false));\n    }).catch(() => setHasCheckinList(false));\n  };\n\n  const [fileSelection, setFileSelection] = useState(INTERNEL_FILE_SECTION);\n  const [showRequestModal, setShowRequestModel] = useState(false);\n  const [requestData, setRequestData] = useState(null);\n  const [form] = useForm();\n  const [emailForm] = useForm();\n  const handleShowRequestModal = useCallback(showRequest => {\n    if (!showRequest) {\n      dispatch(updatePortalFilesSelectedRequest({\n        action: null,\n        requestId: null\n      }));\n    }\n\n    setShowRequestModel(showRequest);\n  }, [dispatch]);\n  const [requestFormChanged, setRequestFormChanged] = useState(false);\n  const [checkingFormChanged, setCheckinFormChanged] = useState(false);\n  const [checkingEmailFormChanged, setCheckinEmailFormChanged] = useState(false);\n  const {\n    portalFilesSelectedRequest\n  } = useSelector(state => state.fileArea);\n\n  const logSuccessfulRequests = updateRequestInput => {\n    successNotification([\"\"], updateRequestInput ? \"Request Updated Successfully\" : \"Request Sent Successfully\");\n    handleShowRequestModal(false);\n    dispatch(updateFileRequestSent(true));\n  };\n\n  const logFailedRequests = (logUpdateRequest, error) => {\n    if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n      errorNotification([\"\"], \"You do not have the permission to perform this action. Please refresh and try again\");\n    } else {\n      errorNotification([\"\"], logUpdateRequest ? \"Request Update Failed\" : \"Request Sending Failed\");\n    }\n\n    logger.error(\"File Area\", \"File Area Button Panel\", error);\n  };\n\n  const updateFileRequest = (request, requestId) => {\n    updateRequest(request, requestId).then(response => {\n      if (response) {\n        logSuccessfulRequests(true);\n      }\n    }).catch(error => {\n      logFailedRequests(true, error);\n    });\n  };\n\n  const saveFileRequest = request => {\n    saveRequest(request).then(response => {\n      if (response) {\n        logSuccessfulRequests(false);\n      }\n    }).catch(error => {\n      logFailedRequests(false, error);\n    });\n  };\n\n  const sendRequest = () => {\n    form.validateFields().then(values => {\n      var _values$linkExpireDat;\n\n      const request = {\n        description: values.description,\n        expirationDate: values.linkExpireDate ? values === null || values === void 0 ? void 0 : (_values$linkExpireDat = values.linkExpireDate) === null || _values$linkExpireDat === void 0 ? void 0 : _values$linkExpireDat.format(\"YYYY-MM-DD\") : \"\",\n        name: values.request,\n        securityKey: values.securityKeyInput ? values.securityKeyInput : null,\n        siteId: props.siteId ? props.siteId : \"\"\n      };\n\n      if (portalFilesSelectedRequest.action && portalFilesSelectedRequest.requestId) {\n        updateFileRequest(request, portalFilesSelectedRequest.requestId);\n      } else {\n        saveFileRequest(request);\n      }\n    });\n  };\n\n  useEffect(() => {\n    if (portalFilesSelectedRequest.requestId) {\n      getPortalFileRequestById(portalFilesSelectedRequest.requestId).then(response => {\n        setRequestData(response.data);\n        setRequestFormValidated(false);\n        handleShowRequestModal(true);\n      }).catch(error => {\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n          props.history.push(\"/forbidden\");\n        }\n      });\n    } else {\n      setRequestData(null);\n    }\n  }, [portalFilesSelectedRequest, handleShowRequestModal, setRequestData]);\n\n  const handleDisplayCheckinModal = () => {\n    if (checkingEmailFormChanged || checkingFormChanged) {\n      confirm({\n        title: \"Are you sure you want to discard the changes?\",\n        icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 244,\n            columnNumber: 15\n          }\n        }),\n        okText: \"Yes\",\n        cancelText: \"No\",\n\n        onOk() {\n          removeFiles(props.siteId ? props.siteId : \"\", checkinList.map(file => file.referenceNumber));\n          setShowCheckinModal(false);\n        }\n\n      });\n    } else {\n      setShowCheckinModal(false);\n    }\n\n    setCheckinEmailFormChanged(false);\n    setCheckinFormChanged(false);\n  };\n\n  const onClickFileAreaHistory = () => {\n    if (fileSelection === INTERNEL_FILE_SECTION) {\n      const url = `/client-file-area/${channelId}/${siteId}/${siteName}/internal/history`;\n      const encodedUrl = encodeURI(url);\n      props.history.push(encodedUrl);\n    } else {\n      const url = `/client-file-area/${channelId}/${siteId}/${siteName}/portal/history`;\n      const encodedUrl = encodeURI(url);\n      props.history.push(encodedUrl);\n    }\n  };\n\n  const SecurityKeyValidation = () => {\n    let isValidated = true;\n\n    if (form.getFieldValue(\"securityKey\")) {\n      if (form.getFieldValue(\"securityKeyInput\") === \"\") {\n        isValidated = false;\n      } else {\n        isValidated = true;\n      }\n    }\n\n    return isValidated;\n  };\n\n  const ExpireDateValidation = () => {\n    let isValidated = true;\n\n    if (form.getFieldValue(\"expireLink\")) {\n      if (form.getFieldValue(\"linkExpireDate\") === undefined || form.getFieldValue(\"linkExpireDate\") === \"\" || form.getFieldValue(\"linkExpireDate\") === null) {\n        isValidated = false;\n      } else {\n        isValidated = true;\n      }\n    }\n\n    return isValidated;\n  };\n\n  const onClickCopyToClipboard = () => {\n    const encryptedRequestId = encrypt(portalFilesSelectedRequest.requestId ? portalFilesSelectedRequest.requestId : \"\");\n    navigator.clipboard.writeText(`${window.location.origin}/requestFiles/${encryptedRequestId}`);\n    successNotification([\"\"], \"Link Copied to Clipboard\");\n  };\n\n  const onClickCancelRequest = () => {\n    if (portalFilesSelectedRequest.action !== formActions.VIEW && requestFormChanged) {\n      confirm({\n        title: \"Are you sure you want to discard the changes?\",\n        icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n          __self: this,\n          __source: {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 15\n          }\n        }),\n        okText: \"Yes\",\n        cancelText: \"No\",\n\n        onOk() {\n          handleShowRequestModal(false);\n          setRequestFormValidated(false);\n        }\n\n      });\n    } else {\n      handleShowRequestModal(false);\n      setRequestFormValidated(false);\n    }\n\n    setRequestFormChanged(false);\n  };\n\n  const onChangeRequest = () => {\n    setRequestFormChanged(true);\n    form.validateFields().then(values => {\n      const expiratonDateValidated = values.linkExpireDate ? moment(values.linkExpireDate).isAfter(new Date(), \"D\") : true;\n\n      if (SecurityKeyValidation() && ExpireDateValidation() && expiratonDateValidated) {\n        setRequestFormValidated(true);\n      } else {\n        setRequestFormValidated(false);\n      }\n    }).catch(() => {\n      setRequestFormValidated(false);\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(Modal, {\n    destroyOnClose: true,\n    size: \"medium\",\n    visible: showRequestModal,\n    title: portalFilesSelectedRequest.action === formActions.EDIT ? \"Edit Request\" : portalFilesSelectedRequest.action === formActions.VIEW ? \"Request Details\" : \"Request\",\n    onCancel: () => onClickCancelRequest(),\n    footer: [/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(\"span\", {\n      hidden: !requestData,\n      className: styles.yjCopyLink,\n      key: \"copyLink\",\n      onClick: () => onClickCopyToClipboard(),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 376,\n        columnNumber: 13\n      }\n    }, /*#__PURE__*/React.createElement(ShareAltOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 382,\n        columnNumber: 15\n      }\n    }), \" Copy Link\"), /*#__PURE__*/React.createElement(_Button, {\n      key: \"back\",\n      type: \"default\",\n      onClick: () => onClickCancelRequest(),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 384,\n        columnNumber: 13\n      }\n    }, \"cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      disabled: !requestFormValidated || portalFilesSelectedRequest.action === formActions.VIEW,\n      onClick: () => sendRequest(),\n      key: \"send\",\n      type: \"primary\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 391,\n        columnNumber: 13\n      }\n    }, portalFilesSelectedRequest.action === formActions.EDIT ? \"Update\" : \"Send\"))],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 407,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(Request, {\n    onChange: () => onChangeRequest(),\n    actionType: portalFilesSelectedRequest.action,\n    formRef: form,\n    onFinish: () => sendRequest(),\n    data: requestData,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 408,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(Modal, {\n    destroyOnClose: true,\n    key: \"100\",\n    visible: showCheckinModal,\n    title: \"Check-In File(s)\",\n    onCancel: handleDisplayCheckinModal,\n    footer: [/*#__PURE__*/React.createElement(_Button, {\n      key: \"back\",\n      type: \"default\",\n      onClick: handleDisplayCheckinModal,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 426,\n        columnNumber: 11\n      }\n    }, \"cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      disabled: !allowCheckinButton,\n      key: \"checkin\",\n      type: \"primary\",\n      onClick: onCheckin,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 430,\n        columnNumber: 11\n      }\n    }, \"check-in\")],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjModalContentWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 440,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(CheckIn, {\n    emailForm: emailForm,\n    onChangeEmailForm: () => {\n      setCheckinEmailFormChanged(true);\n    },\n    onChangedCheckinForm: () => {\n      setCheckinFormChanged(true);\n    },\n    onCheckin: requestList => {\n      setCheckinList(requestList);\n    },\n    siteId: props.siteId,\n    binderId: props.binderId,\n    allowCheckinFunction: isAllowed => {\n      setAllowCheckinButton(isAllowed);\n    },\n    form: form,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 11\n    }\n  }))), /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjFileAreaButtonGroupWrapper,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 462,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(_Button, {\n    disabled: !hasCheckinList && !props.manageCheckin,\n    hidden: !userPermission.privDMSCanCheckInCheckOutInternalFiles || fileSelection === PORTAL_FILE_SECTION,\n    onClick: () => {\n      setShowCheckinModal(true);\n      warningNotification([\"\"], \"The file you are checking-in must match to the downloaded file name and the extension.\");\n    },\n    type: \"primary\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 480,\n      columnNumber: 9\n    }\n  }, \"CHECK-IN\")));\n};", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/FileArea/HeaderButtonPanel/fileAreaButtonPanel.tsx"], "names": ["React", "useCallback", "useEffect", "useState", "ExclamationCircleOutlined", "ShareAltOutlined", "useDispatch", "useSelector", "useParams", "styles", "Request", "Modal", "useForm", "getPortalFileRequestById", "saveRequest", "updateRequest", "errorNotification", "infoNotification", "successNotification", "warningNotification", "logger", "updateFileRequestSent", "updateLoadGridOption", "updatePortalFilesSelectedRequest", "checkCheckoutFileListBySiteId", "checkInFiles", "removeFiles", "CheckIn", "formActions", "encrypt", "moment", "FORBIDDEN_ERROR_CODE", "Option", "confirm", "INTERNEL_FILE_SECTION", "PORTAL_FILE_SECTION", "FileAreaButtonPanel", "props", "showCheckinModal", "setShowCheckinModal", "allowCheckinButton", "setAllow<PERSON><PERSON><PERSON>nButton", "checkinList", "setCheckinList", "hasCheckinList", "setHasCheckinList", "fileAreaSettings", "loadGrid", "state", "fileArea", "userPermission", "userManagement", "requestFormValidated", "setRequestFormValidated", "siteId", "siteName", "channelId", "binderId", "binderName", "handleSuccessfulCheckins", "emailForm", "getFieldsValue", "emailUser", "emailContact", "onCheckin", "then", "response", "catch", "error", "statusCode", "setFileSelection", "fileSection", "dispatch", "onCheckCheckoutFileListBySiteId", "privDMSCanCheckInCheckOutInternalFiles", "fileSelection", "showRequestModal", "setShowRequestModel", "requestData", "setRequestData", "form", "handleShowRequestModal", "showRequest", "action", "requestId", "requestFormChanged", "setRequestFormChanged", "checkingFormChanged", "set<PERSON>heckinFormChanged", "checkingEmailFormChanged", "setCheckinEmailFormChanged", "portalFilesSelectedRequest", "logSuccessfulRequests", "updateRequestInput", "logFailedRequests", "logUpdateRequest", "updateFileRequest", "request", "saveFileRequest", "sendRequest", "validateFields", "values", "description", "expirationDate", "linkExpireDate", "format", "name", "security<PERSON>ey", "securityKeyInput", "data", "history", "push", "handleDisplayCheckinModal", "title", "icon", "okText", "cancelText", "onOk", "map", "file", "referenceNumber", "onClickFileAreaHistory", "url", "encodedUrl", "encodeURI", "SecurityKeyValidation", "isValidated", "getFieldValue", "ExpireDateValidation", "undefined", "onClickCopyToClipboard", "encryptedRequestId", "navigator", "clipboard", "writeText", "window", "location", "origin", "onClickCancelRequest", "VIEW", "onChangeRequest", "expiratonDateValidated", "isAfter", "Date", "EDIT", "yjCopyLink", "yjModalContentWrapper", "requestList", "isAllowed", "yjFileAreaButtonGroupWrapper", "manageCheckin"], "mappings": ";;;;;;;AAAA,OAAOA,KAAP,IAAgBC,WAAhB,EAA6BC,SAA7B,EAAwCC,QAAxC,QAAwD,OAAxD;AAEA,SAASC,yBAAT,EAAoCC,gBAApC,QAA4D,mBAA5D;AACA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,aAAzC;AACA,SAASC,SAAT,QAA0B,kBAA1B;AAEA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,OAAP,MAAoB,qCAApB;AACA,OAAOC,KAAP,MAAkB,uBAAlB;AACA,SAASC,OAAT,QAAwB,oBAAxB;AAEA,SAASC,wBAAT,EAAmCC,WAAnC,EAAgDC,aAAhD,QAAsE,yBAAtE;AACA,SACEC,iBADF,EAEEC,gBAFF,EAGEC,mBAHF,EAIEC,mBAJF,QAKO,6BALP;AAMA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SACEC,qBADF,EAEEC,oBAFF,EAGEC,gCAHF,QAIO,oCAJP;AAMA,SAASC,6BAAT,EAAwCC,YAAxC,EAAsDC,WAAtD,QAA0E,0BAA1E;AACA,OAAOC,OAAP,MAAoB,YAApB;AACA,SAASC,WAAT,QAA4B,YAA5B;AACA,SAASC,OAAT,QAAwB,8BAAxB;AACA,OAAOC,MAAP,MAAmB,QAAnB;AACA,SAASC,oBAAT,QAAqC,YAArC;AAEA,MAAM;AAAEC,EAAAA;AAAF,WAAN;AAEA,MAAM;AAAEC,EAAAA;AAAF,UAAN;AAaA,MAAMC,qBAAqB,GAAG,UAA9B;AACA,MAAMC,mBAAmB,GAAG,QAA5B;AAEA,OAAO,MAAMC,mBAAmD,GAAIC,KAAD,IAAW;AAC5E,QAAM,CAACC,gBAAD,EAAmBC,mBAAnB,IAA0CpC,QAAQ,CAAC,KAAD,CAAxD;AACA,QAAM,CAACqC,kBAAD,EAAqBC,qBAArB,IAA8CtC,QAAQ,CAAC,KAAD,CAA5D;AACA,QAAM,CAACuC,WAAD,EAAcC,cAAd,IAAgCxC,QAAQ,CAAC,EAAD,CAA9C;AACA,QAAM,CAACyC,cAAD,EAAiBC,iBAAjB,IAAsC1C,QAAQ,CAAC,KAAD,CAApD;AACA,QAAM;AAAE2C,IAAAA,gBAAF;AAAoBC,IAAAA;AAApB,MAAiCxC,WAAW,CAC/CyC,KAAD,IAAsBA,KAAK,CAACC,QADoB,CAAlD;AAGA,QAAM;AAAEC,IAAAA;AAAF,MAAqB3C,WAAW,CACnCyC,KAAD,IAAsBA,KAAK,CAACG,cADQ,CAAtC;AAGA,QAAM,CAACC,oBAAD,EAAuBC,uBAAvB,IAAkDlD,QAAQ,CAAC,KAAD,CAAhE;AACA,QAAM;AAAEmD,IAAAA,MAAF;AAAUC,IAAAA,QAAV;AAAoBC,IAAAA,SAApB;AAA+BC,IAAAA,QAA/B;AAAyCC,IAAAA;AAAzC,MAAwDlD,SAAS,EAAvE;;AAEA,QAAMmD,wBAAwB,GAAG,MAAM;AACrC1C,IAAAA,gBAAgB,CAAC,CAAC,EAAD,CAAD,EAAO,4CAAP,CAAhB;;AACA,QACE2C,SAAS,CAACC,cAAV,GAA2BC,SAA3B,IACAF,SAAS,CAACC,cAAV,GAA2BE,YAF7B,EAGE;AACA7C,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,uCAAP,CAAnB;AACD;;AACDqB,IAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACD,GATD;;AAWA,QAAMyB,SAAS,GAAG,MAAM;AACtBvC,IAAAA,YAAY,CAACY,KAAK,CAACoB,QAAP,EAAiBf,WAAjB,CAAZ,CACGuB,IADH,CACSC,QAAD,IAAc;AAClBP,MAAAA,wBAAwB;AACzB,KAHH,EAIGQ,KAJH,CAIUC,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACC,UAAN,KAAqBtC,oBAAzB,EAA+C;AAC7Cf,QAAAA,iBAAiB,CACf,CAAC,EAAD,CADe,EAEf,qFAFe,CAAjB;AAID,OALD,MAKO;AACLA,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAO,iBAAP,CAAjB;AACD;;AACDI,MAAAA,MAAM,CAACgD,KAAP,CAAa,kBAAb,EAAiC,wBAAjC,EAA2DA,KAA3D;AACD,KAdH;AAeD,GAhBD;;AAkBAlE,EAAAA,SAAS,CAAC,MAAM;AACdoE,IAAAA,gBAAgB,CAACjC,KAAK,CAACkC,WAAN,GAAoBlC,KAAK,CAACkC,WAA1B,GAAwC,EAAzC,CAAhB;AACD,GAFQ,EAEN,CAAClC,KAAK,CAACkC,WAAP,CAFM,CAAT;AAIA,QAAMC,QAAQ,GAAGlE,WAAW,EAA5B;AACAJ,EAAAA,SAAS,CAAC,MAAM;AACdoE,IAAAA,gBAAgB,CAACjC,KAAK,CAACkC,WAAN,GAAoBlC,KAAK,CAACkC,WAA1B,GAAwC,EAAzC,CAAhB;AACD,GAFQ,EAEN,CAAClC,KAAK,CAACkC,WAAP,CAFM,CAAT;AAIArE,EAAAA,SAAS,CAAC,MAAM;AACduE,IAAAA,+BAA+B;AAChC,GAFQ,EAEN,CAACpC,KAAK,CAACiB,MAAP,EAAekB,QAAf,EAAyB1B,gBAAzB,CAFM,CAAT;AAIA5C,EAAAA,SAAS,CAAC,MAAM;AACd,QAAI6C,QAAJ,EAAc;AACZ0B,MAAAA,+BAA+B;AAChC;AACF,GAJQ,EAIN,CAAC1B,QAAD,CAJM,CAAT;;AAMA,QAAM0B,+BAA+B,GAAG,MAAM;AAC5CvB,IAAAA,cAAc,CAACwB,sCAAf,IACElD,6BAA6B,CAACa,KAAK,CAACoB,QAAP,CAA7B,CACGQ,IADH,CACQ,MAAM;AACVpB,MAAAA,iBAAiB,CAAC,IAAD,CAAjB;AACA2B,MAAAA,QAAQ,CAAClD,oBAAoB,CAAC,KAAD,CAArB,CAAR;AACD,KAJH,EAKG6C,KALH,CAKS,MAAMtB,iBAAiB,CAAC,KAAD,CALhC,CADF;AAOD,GARD;;AAUA,QAAM,CAAC8B,aAAD,EAAgBL,gBAAhB,IAAoCnE,QAAQ,CAAC+B,qBAAD,CAAlD;AACA,QAAM,CAAC0C,gBAAD,EAAmBC,mBAAnB,IAA0C1E,QAAQ,CAAC,KAAD,CAAxD;AACA,QAAM,CAAC2E,WAAD,EAAcC,cAAd,IAAgC5E,QAAQ,CAAY,IAAZ,CAA9C;AACA,QAAM,CAAC6E,IAAD,IAASpE,OAAO,EAAtB;AACA,QAAM,CAACgD,SAAD,IAAchD,OAAO,EAA3B;AACA,QAAMqE,sBAAsB,GAAGhF,WAAW,CACvCiF,WAAD,IAA0B;AACxB,QAAI,CAACA,WAAL,EAAkB;AAChBV,MAAAA,QAAQ,CACNjD,gCAAgC,CAAC;AAAE4D,QAAAA,MAAM,EAAE,IAAV;AAAgBC,QAAAA,SAAS,EAAE;AAA3B,OAAD,CAD1B,CAAR;AAGD;;AACDP,IAAAA,mBAAmB,CAACK,WAAD,CAAnB;AACD,GARuC,EASxC,CAACV,QAAD,CATwC,CAA1C;AAWA,QAAM,CAACa,kBAAD,EAAqBC,qBAArB,IAA8CnF,QAAQ,CAAC,KAAD,CAA5D;AACA,QAAM,CAACoF,mBAAD,EAAsBC,qBAAtB,IAA+CrF,QAAQ,CAAC,KAAD,CAA7D;AACA,QAAM,CAACsF,wBAAD,EAA2BC,0BAA3B,IAAyDvF,QAAQ,CACrE,KADqE,CAAvE;AAIA,QAAM;AAAEwF,IAAAA;AAAF,MAAiCpF,WAAW,CAC/CyC,KAAD,IAAsBA,KAAK,CAACC,QADoB,CAAlD;;AAIA,QAAM2C,qBAAqB,GAAIC,kBAAD,IAAiC;AAC7D3E,IAAAA,mBAAmB,CACjB,CAAC,EAAD,CADiB,EAEjB2E,kBAAkB,GACd,8BADc,GAEd,2BAJa,CAAnB;AAMAZ,IAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACAT,IAAAA,QAAQ,CAACnD,qBAAqB,CAAC,IAAD,CAAtB,CAAR;AACD,GATD;;AAWA,QAAMyE,iBAAiB,GAAG,CAACC,gBAAD,EAA4B3B,KAA5B,KAA2C;AACnE,QAAIA,KAAK,CAACC,UAAN,KAAqBtC,oBAAzB,EAA+C;AAC7Cf,MAAAA,iBAAiB,CACf,CAAC,EAAD,CADe,EAEf,qFAFe,CAAjB;AAID,KALD,MAKO;AACLA,MAAAA,iBAAiB,CACf,CAAC,EAAD,CADe,EAEf+E,gBAAgB,GAAG,uBAAH,GAA6B,wBAF9B,CAAjB;AAID;;AAED3E,IAAAA,MAAM,CAACgD,KAAP,CAAa,WAAb,EAA0B,wBAA1B,EAAoDA,KAApD;AACD,GAdD;;AAgBA,QAAM4B,iBAAiB,GAAG,CAACC,OAAD,EAA0Bb,SAA1B,KAAgD;AACxErE,IAAAA,aAAa,CAACkF,OAAD,EAAUb,SAAV,CAAb,CACGnB,IADH,CACSC,QAAD,IAAc;AAClB,UAAIA,QAAJ,EAAc;AACZ0B,QAAAA,qBAAqB,CAAC,IAAD,CAArB;AACD;AACF,KALH,EAMGzB,KANH,CAMUC,KAAD,IAAW;AAChB0B,MAAAA,iBAAiB,CAAC,IAAD,EAAO1B,KAAP,CAAjB;AACD,KARH;AASD,GAVD;;AAYA,QAAM8B,eAAe,GAAID,OAAD,IAA6B;AACnDnF,IAAAA,WAAW,CAACmF,OAAD,CAAX,CACGhC,IADH,CACSC,QAAD,IAAc;AAClB,UAAIA,QAAJ,EAAc;AACZ0B,QAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD;AACF,KALH,EAMGzB,KANH,CAMUC,KAAD,IAAW;AAChB0B,MAAAA,iBAAiB,CAAC,KAAD,EAAQ1B,KAAR,CAAjB;AACD,KARH;AASD,GAVD;;AAYA,QAAM+B,WAAW,GAAG,MAAM;AACxBnB,IAAAA,IAAI,CAACoB,cAAL,GAAsBnC,IAAtB,CAA4BoC,MAAD,IAAY;AAAA;;AACrC,YAAMJ,OAAuB,GAAG;AAC9BK,QAAAA,WAAW,EAAED,MAAM,CAACC,WADU;AAE9BC,QAAAA,cAAc,EAAEF,MAAM,CAACG,cAAP,GACZH,MADY,aACZA,MADY,gDACZA,MAAM,CAAEG,cADI,0DACZ,sBAAwBC,MAAxB,CAA+B,YAA/B,CADY,GAEZ,EAJ0B;AAK9BC,QAAAA,IAAI,EAAEL,MAAM,CAACJ,OALiB;AAM9BU,QAAAA,WAAW,EAAEN,MAAM,CAACO,gBAAP,GAA0BP,MAAM,CAACO,gBAAjC,GAAoD,IANnC;AAO9BtD,QAAAA,MAAM,EAAEjB,KAAK,CAACiB,MAAN,GAAejB,KAAK,CAACiB,MAArB,GAA8B;AAPR,OAAhC;;AASA,UACEqC,0BAA0B,CAACR,MAA3B,IACAQ,0BAA0B,CAACP,SAF7B,EAGE;AACAY,QAAAA,iBAAiB,CAACC,OAAD,EAAUN,0BAA0B,CAACP,SAArC,CAAjB;AACD,OALD,MAKO;AACLc,QAAAA,eAAe,CAACD,OAAD,CAAf;AACD;AACF,KAlBD;AAmBD,GApBD;;AAsBA/F,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIyF,0BAA0B,CAACP,SAA/B,EAA0C;AACxCvE,MAAAA,wBAAwB,CAAC8E,0BAA0B,CAACP,SAA5B,CAAxB,CACGnB,IADH,CACSC,QAAD,IAAc;AAClBa,QAAAA,cAAc,CAACb,QAAQ,CAAC2C,IAAV,CAAd;AACAxD,QAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACA4B,QAAAA,sBAAsB,CAAC,IAAD,CAAtB;AACD,OALH,EAMGd,KANH,CAMUC,KAAD,IAAW;AAChB,YAAIA,KAAK,CAACC,UAAN,KAAqBtC,oBAAzB,EAA+C;AAC7CM,UAAAA,KAAK,CAACyE,OAAN,CAAcC,IAAd,CAAmB,YAAnB;AACD;AACF,OAVH;AAWD,KAZD,MAYO;AACLhC,MAAAA,cAAc,CAAC,IAAD,CAAd;AACD;AACF,GAhBQ,EAgBN,CAACY,0BAAD,EAA6BV,sBAA7B,EAAqDF,cAArD,CAhBM,CAAT;;AAkBA,QAAMiC,yBAAyB,GAAG,MAAM;AACtC,QAAIvB,wBAAwB,IAAIF,mBAAhC,EAAqD;AACnDtD,MAAAA,OAAO,CAAC;AACNgF,QAAAA,KAAK,EAAE,+CADD;AAENC,QAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAFA;AAGNC,QAAAA,MAAM,EAAE,KAHF;AAINC,QAAAA,UAAU,EAAE,IAJN;;AAKNC,QAAAA,IAAI,GAAG;AACL3F,UAAAA,WAAW,CACTW,KAAK,CAACiB,MAAN,GAAejB,KAAK,CAACiB,MAArB,GAA8B,EADrB,EAETZ,WAAW,CAAC4E,GAAZ,CAAiBC,IAAD,IAAeA,IAAI,CAACC,eAApC,CAFS,CAAX;AAIAjF,UAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACD;;AAXK,OAAD,CAAP;AAaD,KAdD,MAcO;AACLA,MAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACD;;AACDmD,IAAAA,0BAA0B,CAAC,KAAD,CAA1B;AACAF,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD,GApBD;;AAsBA,QAAMiC,sBAAsB,GAAG,MAAM;AACnC,QAAI9C,aAAa,KAAKzC,qBAAtB,EAA6C;AAC3C,YAAMwF,GAAG,GAAI,qBAAoBlE,SAAU,IAAGF,MAAO,IAAGC,QAAS,mBAAjE;AACA,YAAMoE,UAAU,GAAGC,SAAS,CAACF,GAAD,CAA5B;AACArF,MAAAA,KAAK,CAACyE,OAAN,CAAcC,IAAd,CAAmBY,UAAnB;AACD,KAJD,MAIO;AACL,YAAMD,GAAG,GAAI,qBAAoBlE,SAAU,IAAGF,MAAO,IAAGC,QAAS,iBAAjE;AACA,YAAMoE,UAAU,GAAGC,SAAS,CAACF,GAAD,CAA5B;AACArF,MAAAA,KAAK,CAACyE,OAAN,CAAcC,IAAd,CAAmBY,UAAnB;AACD;AACF,GAVD;;AAYA,QAAME,qBAAqB,GAAG,MAAe;AAC3C,QAAIC,WAAW,GAAG,IAAlB;;AACA,QAAI9C,IAAI,CAAC+C,aAAL,CAAmB,aAAnB,CAAJ,EAAuC;AACrC,UAAI/C,IAAI,CAAC+C,aAAL,CAAmB,kBAAnB,MAA2C,EAA/C,EAAmD;AACjDD,QAAAA,WAAW,GAAG,KAAd;AACD,OAFD,MAEO;AACLA,QAAAA,WAAW,GAAG,IAAd;AACD;AACF;;AACD,WAAOA,WAAP;AACD,GAVD;;AAYA,QAAME,oBAAoB,GAAG,MAAe;AAC1C,QAAIF,WAAW,GAAG,IAAlB;;AACA,QAAI9C,IAAI,CAAC+C,aAAL,CAAmB,YAAnB,CAAJ,EAAsC;AACpC,UACE/C,IAAI,CAAC+C,aAAL,CAAmB,gBAAnB,MAAyCE,SAAzC,IACAjD,IAAI,CAAC+C,aAAL,CAAmB,gBAAnB,MAAyC,EADzC,IAEA/C,IAAI,CAAC+C,aAAL,CAAmB,gBAAnB,MAAyC,IAH3C,EAIE;AACAD,QAAAA,WAAW,GAAG,KAAd;AACD,OAND,MAMO;AACLA,QAAAA,WAAW,GAAG,IAAd;AACD;AACF;;AACD,WAAOA,WAAP;AACD,GAdD;;AAgBA,QAAMI,sBAAsB,GAAG,MAAM;AACnC,UAAMC,kBAAkB,GAAGtG,OAAO,CAChC8D,0BAA0B,CAACP,SAA3B,GACIO,0BAA0B,CAACP,SAD/B,GAEI,EAH4B,CAAlC;AAKAgD,IAAAA,SAAS,CAACC,SAAV,CAAoBC,SAApB,CACG,GAAEC,MAAM,CAACC,QAAP,CAAgBC,MAAO,iBAAgBN,kBAAmB,EAD/D;AAGAjH,IAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,0BAAP,CAAnB;AACD,GAVD;;AAYA,QAAMwH,oBAAoB,GAAG,MAAM;AACjC,QACE/C,0BAA0B,CAACR,MAA3B,KAAsCvD,WAAW,CAAC+G,IAAlD,IACAtD,kBAFF,EAGE;AACApD,MAAAA,OAAO,CAAC;AACNgF,QAAAA,KAAK,EAAE,+CADD;AAENC,QAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,UAFA;AAGNC,QAAAA,MAAM,EAAE,KAHF;AAINC,QAAAA,UAAU,EAAE,IAJN;;AAKNC,QAAAA,IAAI,GAAG;AACLpC,UAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACA5B,UAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACD;;AARK,OAAD,CAAP;AAUD,KAdD,MAcO;AACL4B,MAAAA,sBAAsB,CAAC,KAAD,CAAtB;AACA5B,MAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACD;;AACDiC,IAAAA,qBAAqB,CAAC,KAAD,CAArB;AACD,GApBD;;AAsBA,QAAMsD,eAAe,GAAG,MAAM;AAC5BtD,IAAAA,qBAAqB,CAAC,IAAD,CAArB;AACAN,IAAAA,IAAI,CACDoB,cADH,GAEGnC,IAFH,CAESoC,MAAD,IAAY;AAChB,YAAMwC,sBAAsB,GAAGxC,MAAM,CAACG,cAAP,GAC3B1E,MAAM,CAACuE,MAAM,CAACG,cAAR,CAAN,CAA8BsC,OAA9B,CAAsC,IAAIC,IAAJ,EAAtC,EAAkD,GAAlD,CAD2B,GAE3B,IAFJ;;AAIA,UACElB,qBAAqB,MACrBG,oBAAoB,EADpB,IAEAa,sBAHF,EAIE;AACAxF,QAAAA,uBAAuB,CAAC,IAAD,CAAvB;AACD,OAND,MAMO;AACLA,QAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACD;AACF,KAhBH,EAiBGc,KAjBH,CAiBS,MAAM;AACXd,MAAAA,uBAAuB,CAAC,KAAD,CAAvB;AACD,KAnBH;AAoBD,GAtBD;;AAwBA,sBACE,uDACE,oBAAC,KAAD;AACE,IAAA,cAAc,EAAE,IADlB;AAEE,IAAA,IAAI,EAAE,QAFR;AAGE,IAAA,OAAO,EAAEuB,gBAHX;AAIE,IAAA,KAAK,EACHe,0BAA0B,CAACR,MAA3B,KAAsCvD,WAAW,CAACoH,IAAlD,GACI,cADJ,GAEIrD,0BAA0B,CAACR,MAA3B,KAAsCvD,WAAW,CAAC+G,IAAlD,GACE,iBADF,GAEE,SATV;AAWE,IAAA,QAAQ,EAAE,MAAMD,oBAAoB,EAXtC;AAYE,IAAA,MAAM,EAAE,cACN,uDACE;AACE,MAAA,MAAM,EAAE,CAAC5D,WADX;AAEE,MAAA,SAAS,EAAErE,MAAM,CAACwI,UAFpB;AAGE,MAAA,GAAG,EAAC,UAHN;AAIE,MAAA,OAAO,EAAE,MAAMf,sBAAsB,EAJvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAME,oBAAC,gBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MANF,eADF,eASE;AACE,MAAA,GAAG,EAAC,MADN;AAEE,MAAA,IAAI,EAAC,SAFP;AAGE,MAAA,OAAO,EAAE,MAAMQ,oBAAoB,EAHrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBATF,eAgBE;AACE,MAAA,QAAQ,EACN,CAACtF,oBAAD,IACAuC,0BAA0B,CAACR,MAA3B,KAAsCvD,WAAW,CAAC+G,IAHtD;AAKE,MAAA,OAAO,EAAE,MAAMxC,WAAW,EAL5B;AAME,MAAA,GAAG,EAAC,MANN;AAOE,MAAA,IAAI,EAAC,SAPP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OASGR,0BAA0B,CAACR,MAA3B,KAAsCvD,WAAW,CAACoH,IAAlD,GACG,QADH,GAEG,MAXN,CAhBF,CADM,CAZV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA6CE;AAAK,IAAA,SAAS,EAAEvI,MAAM,CAACyI,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,OAAD;AACE,IAAA,QAAQ,EAAE,MAAMN,eAAe,EADjC;AAEE,IAAA,UAAU,EAAEjD,0BAA0B,CAACR,MAFzC;AAGE,IAAA,OAAO,EAAEH,IAHX;AAIE,IAAA,QAAQ,EAAE,MAAMmB,WAAW,EAJ7B;AAKE,IAAA,IAAI,EAAErB,WALR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CA7CF,CADF,eA0DE,oBAAC,KAAD;AACE,IAAA,cAAc,EAAE,IADlB;AAEE,IAAA,GAAG,EAAC,KAFN;AAGE,IAAA,OAAO,EAAExC,gBAHX;AAIE,IAAA,KAAK,EAAE,kBAJT;AAKE,IAAA,QAAQ,EAAE0E,yBALZ;AAME,IAAA,MAAM,EAAE,cACN;AAAQ,MAAA,GAAG,EAAC,MAAZ;AAAmB,MAAA,IAAI,EAAC,SAAxB;AAAkC,MAAA,OAAO,EAAEA,yBAA3C;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADM,eAKN;AACE,MAAA,QAAQ,EAAE,CAACxE,kBADb;AAEE,MAAA,GAAG,EAAC,SAFN;AAGE,MAAA,IAAI,EAAC,SAHP;AAIE,MAAA,OAAO,EAAEwB,SAJX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBALM,CANV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAqBE;AAAK,IAAA,SAAS,EAAEvD,MAAM,CAACyI,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,OAAD;AACE,IAAA,SAAS,EAAEtF,SADb;AAEE,IAAA,iBAAiB,EAAE,MAAM;AACvB8B,MAAAA,0BAA0B,CAAC,IAAD,CAA1B;AACD,KAJH;AAKE,IAAA,oBAAoB,EAAE,MAAM;AAC1BF,MAAAA,qBAAqB,CAAC,IAAD,CAArB;AACD,KAPH;AAQE,IAAA,SAAS,EAAG2D,WAAD,IAAsB;AAC/BxG,MAAAA,cAAc,CAACwG,WAAD,CAAd;AACD,KAVH;AAWE,IAAA,MAAM,EAAE9G,KAAK,CAACiB,MAXhB;AAYE,IAAA,QAAQ,EAAEjB,KAAK,CAACoB,QAZlB;AAaE,IAAA,oBAAoB,EAAG2F,SAAD,IAAwB;AAC5C3G,MAAAA,qBAAqB,CAAC2G,SAAD,CAArB;AACD,KAfH;AAgBE,IAAA,IAAI,EAAEpE,IAhBR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CArBF,CA1DF,eAqGE;AAAK,IAAA,SAAS,EAAEvE,MAAM,CAAC4I,4BAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAkBE;AACE,IAAA,QAAQ,EAAE,CAACzG,cAAD,IAAiB,CAACP,KAAK,CAACiH,aADpC;AAEE,IAAA,MAAM,EACJ,CAACpG,cAAc,CAACwB,sCAAhB,IACAC,aAAa,KAAKxC,mBAJtB;AAME,IAAA,OAAO,EAAE,MAAM;AACbI,MAAAA,mBAAmB,CAAC,IAAD,CAAnB;AACApB,MAAAA,mBAAmB,CACjB,CAAC,EAAD,CADiB,EAEjB,wFAFiB,CAAnB;AAID,KAZH;AAaE,IAAA,IAAI,EAAC,SAbP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBAlBF,CArGF,CADF;AAmKD,CAxdM", "sourcesContent": ["import React, { useC<PERSON>back, useEffect, useState } from \"react\";\r\nimport { <PERSON>ton, Modal as AntModal, Select } from \"antd\";\r\nimport { ExclamationCircleOutlined, ShareAltOutlined } from \"@ant-design/icons\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { useParams } from \"react-router-dom\";\r\n\r\nimport styles from \"./index.module.less\";\r\nimport Request from \"@app/components/PortalFiles/Request\";\r\nimport Modal from \"@app/components/Modal\";\r\nimport { useForm } from \"antd/lib/form/Form\";\r\nimport { ICreateRequest } from \"@app/types/portalTypes\";\r\nimport { getPortalFileRequestById, saveRequest, updateRequest, } from \"@app/api/portalServices\";\r\nimport {\r\n  errorNotification,\r\n  infoNotification,\r\n  successNotification,\r\n  warningNotification,\r\n} from \"@app/utils/antNotifications\";\r\nimport logger from \"@app/utils/logger\";\r\nimport {\r\n  updateFileRequestSent,\r\n  updateLoadGridOption,\r\n  updatePortalFilesSelectedRequest,\r\n} from \"@app/redux/actions/fileAreaActions\";\r\nimport { RootState } from \"@app/redux/reducers/state\";\r\nimport { checkCheckoutFileListBySiteId, checkInFiles, removeFiles, } from \"@app/api/fileAreaService\";\r\nimport CheckIn from \"../CheckIn\";\r\nimport { formActions } from \"@app/types\";\r\nimport { encrypt } from \"@app/utils/crypto/cryptoText\";\r\nimport moment from \"moment\";\r\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\r\n\r\nconst { Option } = Select;\r\n\r\nconst { confirm } = AntModal;\r\n\r\nexport interface IfileAreaButtonPanel {\r\n  history?: any;\r\n  fileSection?: string;\r\n  siteId?: string;\r\n  binderId?: string;\r\n  siteName?: string;\r\n  channelId?: string;\r\n  key?: string;\r\n  manageCheckin?: boolean;\r\n}\r\n\r\nconst INTERNEL_FILE_SECTION = \"internal\";\r\nconst PORTAL_FILE_SECTION = \"portal\";\r\n\r\nexport const FileAreaButtonPanel: React.FC<IfileAreaButtonPanel> = (props) => {\r\n  const [showCheckinModal, setShowCheckinModal] = useState(false);\r\n  const [allowCheckinButton, setAllowCheckinButton] = useState(false);\r\n  const [checkinList, setCheckinList] = useState([]);\r\n  const [hasCheckinList, setHasCheckinList] = useState(false);\r\n  const { fileAreaSettings, loadGrid } = useSelector(\r\n    (state: RootState) => state.fileArea\r\n  );\r\n  const { userPermission } = useSelector(\r\n    (state: RootState) => state.userManagement\r\n  );\r\n  const [requestFormValidated, setRequestFormValidated] = useState(false);\r\n  const { siteId, siteName, channelId, binderId, binderName } = useParams<any>();\r\n\r\n  const handleSuccessfulCheckins = () => {\r\n    infoNotification([\"\"], \"File(s) are being checked in. Please wait.\");\r\n    if (\r\n      emailForm.getFieldsValue().emailUser ||\r\n      emailForm.getFieldsValue().emailContact\r\n    ) {\r\n      successNotification([\"\"], \"File Check-in Email sent Successfully\");\r\n    }\r\n    setShowCheckinModal(false);\r\n  };\r\n\r\n  const onCheckin = () => {\r\n    checkInFiles(props.binderId, checkinList)\r\n      .then((response) => {\r\n        handleSuccessfulCheckins();\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification(\r\n            [\"\"],\r\n            \"You do not have the permission to perform this action. Please refresh and try again\"\r\n          );\r\n        } else {\r\n          errorNotification([\"\"], \"Check-In Failed\");\r\n        }\r\n        logger.error(\"File Are Section\", \"File Area Button Panel\", error);\r\n      });\r\n  };\r\n\r\n  useEffect(() => {\r\n    setFileSelection(props.fileSection ? props.fileSection : \"\");\r\n  }, [props.fileSection]);\r\n\r\n  const dispatch = useDispatch();\r\n  useEffect(() => {\r\n    setFileSelection(props.fileSection ? props.fileSection : \"\");\r\n  }, [props.fileSection]);\r\n\r\n  useEffect(() => {\r\n    onCheckCheckoutFileListBySiteId();\r\n  }, [props.siteId, dispatch, fileAreaSettings]);\r\n\r\n  useEffect(() => {\r\n    if (loadGrid) {\r\n      onCheckCheckoutFileListBySiteId();\r\n    }\r\n  }, [loadGrid]);\r\n\r\n  const onCheckCheckoutFileListBySiteId = () => {\r\n    userPermission.privDMSCanCheckInCheckOutInternalFiles &&\r\n      checkCheckoutFileListBySiteId(props.binderId)\r\n        .then(() => {\r\n          setHasCheckinList(true);\r\n          dispatch(updateLoadGridOption(false));\r\n        })\r\n        .catch(() => setHasCheckinList(false));\r\n  };\r\n\r\n  const [fileSelection, setFileSelection] = useState(INTERNEL_FILE_SECTION);\r\n  const [showRequestModal, setShowRequestModel] = useState(false);\r\n  const [requestData, setRequestData] = useState<{} | null>(null) as any;\r\n  const [form] = useForm();\r\n  const [emailForm] = useForm();\r\n  const handleShowRequestModal = useCallback(\r\n    (showRequest: boolean) => {\r\n      if (!showRequest) {\r\n        dispatch(\r\n          updatePortalFilesSelectedRequest({ action: null, requestId: null })\r\n        );\r\n      }\r\n      setShowRequestModel(showRequest);\r\n    },\r\n    [dispatch]\r\n  );\r\n  const [requestFormChanged, setRequestFormChanged] = useState(false);\r\n  const [checkingFormChanged, setCheckinFormChanged] = useState(false);\r\n  const [checkingEmailFormChanged, setCheckinEmailFormChanged] = useState(\r\n    false\r\n  );\r\n\r\n  const { portalFilesSelectedRequest } = useSelector(\r\n    (state: RootState) => state.fileArea\r\n  );\r\n\r\n  const logSuccessfulRequests = (updateRequestInput: boolean) => {\r\n    successNotification(\r\n      [\"\"],\r\n      updateRequestInput\r\n        ? \"Request Updated Successfully\"\r\n        : \"Request Sent Successfully\"\r\n    );\r\n    handleShowRequestModal(false);\r\n    dispatch(updateFileRequestSent(true));\r\n  };\r\n\r\n  const logFailedRequests = (logUpdateRequest: boolean, error: any) => {\r\n    if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n      errorNotification(\r\n        [\"\"],\r\n        \"You do not have the permission to perform this action. Please refresh and try again\"\r\n      );\r\n    } else {\r\n      errorNotification(\r\n        [\"\"],\r\n        logUpdateRequest ? \"Request Update Failed\" : \"Request Sending Failed\"\r\n      );\r\n    }\r\n\r\n    logger.error(\"File Area\", \"File Area Button Panel\", error);\r\n  };\r\n\r\n  const updateFileRequest = (request: ICreateRequest, requestId: string) => {\r\n    updateRequest(request, requestId)\r\n      .then((response) => {\r\n        if (response) {\r\n          logSuccessfulRequests(true);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        logFailedRequests(true, error);\r\n      });\r\n  };\r\n\r\n  const saveFileRequest = (request: ICreateRequest) => {\r\n    saveRequest(request)\r\n      .then((response) => {\r\n        if (response) {\r\n          logSuccessfulRequests(false);\r\n        }\r\n      })\r\n      .catch((error) => {\r\n        logFailedRequests(false, error);\r\n      });\r\n  };\r\n\r\n  const sendRequest = () => {\r\n    form.validateFields().then((values) => {\r\n      const request: ICreateRequest = {\r\n        description: values.description,\r\n        expirationDate: values.linkExpireDate\r\n          ? values?.linkExpireDate?.format(\"YYYY-MM-DD\")\r\n          : \"\",\r\n        name: values.request,\r\n        securityKey: values.securityKeyInput ? values.securityKeyInput : null,\r\n        siteId: props.siteId ? props.siteId : \"\",\r\n      };\r\n      if (\r\n        portalFilesSelectedRequest.action &&\r\n        portalFilesSelectedRequest.requestId\r\n      ) {\r\n        updateFileRequest(request, portalFilesSelectedRequest.requestId);\r\n      } else {\r\n        saveFileRequest(request);\r\n      }\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    if (portalFilesSelectedRequest.requestId) {\r\n      getPortalFileRequestById(portalFilesSelectedRequest.requestId)\r\n        .then((response) => {\r\n          setRequestData(response.data);\r\n          setRequestFormValidated(false);\r\n          handleShowRequestModal(true);\r\n        })\r\n        .catch((error) => {\r\n          if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n            props.history.push(\"/forbidden\");\r\n          }\r\n        });\r\n    } else {\r\n      setRequestData(null);\r\n    }\r\n  }, [portalFilesSelectedRequest, handleShowRequestModal, setRequestData]);\r\n\r\n  const handleDisplayCheckinModal = () => {\r\n    if (checkingEmailFormChanged || checkingFormChanged) {\r\n      confirm({\r\n        title: \"Are you sure you want to discard the changes?\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        okText: \"Yes\",\r\n        cancelText: \"No\",\r\n        onOk() {\r\n          removeFiles(\r\n            props.siteId ? props.siteId : \"\",\r\n            checkinList.map((file: any) => file.referenceNumber)\r\n          );\r\n          setShowCheckinModal(false);\r\n        },\r\n      });\r\n    } else {\r\n      setShowCheckinModal(false);\r\n    }\r\n    setCheckinEmailFormChanged(false);\r\n    setCheckinFormChanged(false);\r\n  };\r\n\r\n  const onClickFileAreaHistory = () => {\r\n    if (fileSelection === INTERNEL_FILE_SECTION) {\r\n      const url = `/client-file-area/${channelId}/${siteId}/${siteName}/internal/history`;\r\n      const encodedUrl = encodeURI(url);\r\n      props.history.push(encodedUrl);\r\n    } else {\r\n      const url = `/client-file-area/${channelId}/${siteId}/${siteName}/portal/history`;\r\n      const encodedUrl = encodeURI(url);\r\n      props.history.push(encodedUrl);\r\n    }\r\n  };\r\n\r\n  const SecurityKeyValidation = (): boolean => {\r\n    let isValidated = true;\r\n    if (form.getFieldValue(\"securityKey\")) {\r\n      if (form.getFieldValue(\"securityKeyInput\") === \"\") {\r\n        isValidated = false;\r\n      } else {\r\n        isValidated = true;\r\n      }\r\n    }\r\n    return isValidated;\r\n  };\r\n\r\n  const ExpireDateValidation = (): boolean => {\r\n    let isValidated = true;\r\n    if (form.getFieldValue(\"expireLink\")) {\r\n      if (\r\n        form.getFieldValue(\"linkExpireDate\") === undefined ||\r\n        form.getFieldValue(\"linkExpireDate\") === \"\" ||\r\n        form.getFieldValue(\"linkExpireDate\") === null\r\n      ) {\r\n        isValidated = false;\r\n      } else {\r\n        isValidated = true;\r\n      }\r\n    }\r\n    return isValidated;\r\n  };\r\n\r\n  const onClickCopyToClipboard = () => {\r\n    const encryptedRequestId = encrypt(\r\n      portalFilesSelectedRequest.requestId\r\n        ? portalFilesSelectedRequest.requestId\r\n        : \"\"\r\n    );\r\n    navigator.clipboard.writeText(\r\n      `${window.location.origin}/requestFiles/${encryptedRequestId}`\r\n    );\r\n    successNotification([\"\"], \"Link Copied to Clipboard\");\r\n  };\r\n\r\n  const onClickCancelRequest = () => {\r\n    if (\r\n      portalFilesSelectedRequest.action !== formActions.VIEW &&\r\n      requestFormChanged\r\n    ) {\r\n      confirm({\r\n        title: \"Are you sure you want to discard the changes?\",\r\n        icon: <ExclamationCircleOutlined />,\r\n        okText: \"Yes\",\r\n        cancelText: \"No\",\r\n        onOk() {\r\n          handleShowRequestModal(false);\r\n          setRequestFormValidated(false);\r\n        },\r\n      });\r\n    } else {\r\n      handleShowRequestModal(false);\r\n      setRequestFormValidated(false);\r\n    }\r\n    setRequestFormChanged(false);\r\n  };\r\n\r\n  const onChangeRequest = () => {\r\n    setRequestFormChanged(true);\r\n    form\r\n      .validateFields()\r\n      .then((values) => {\r\n        const expiratonDateValidated = values.linkExpireDate\r\n          ? moment(values.linkExpireDate).isAfter(new Date(), \"D\")\r\n          : true;\r\n\r\n        if (\r\n          SecurityKeyValidation() &&\r\n          ExpireDateValidation() &&\r\n          expiratonDateValidated\r\n        ) {\r\n          setRequestFormValidated(true);\r\n        } else {\r\n          setRequestFormValidated(false);\r\n        }\r\n      })\r\n      .catch(() => {\r\n        setRequestFormValidated(false);\r\n      });\r\n  };\r\n\r\n  return (\r\n    <>\r\n      <Modal\r\n        destroyOnClose={true}\r\n        size={\"medium\"}\r\n        visible={showRequestModal}\r\n        title={\r\n          portalFilesSelectedRequest.action === formActions.EDIT\r\n            ? \"Edit Request\"\r\n            : portalFilesSelectedRequest.action === formActions.VIEW\r\n              ? \"Request Details\"\r\n              : \"Request\"\r\n        }\r\n        onCancel={() => onClickCancelRequest()}\r\n        footer={[\r\n          <>\r\n            <span\r\n              hidden={!requestData}\r\n              className={styles.yjCopyLink}\r\n              key=\"copyLink\"\r\n              onClick={() => onClickCopyToClipboard()}\r\n            >\r\n              <ShareAltOutlined /> Copy Link\r\n            </span>\r\n            <Button\r\n              key=\"back\"\r\n              type=\"default\"\r\n              onClick={() => onClickCancelRequest()}\r\n            >\r\n              cancel\r\n            </Button>\r\n            <Button\r\n              disabled={\r\n                !requestFormValidated ||\r\n                portalFilesSelectedRequest.action === formActions.VIEW\r\n              }\r\n              onClick={() => sendRequest()}\r\n              key=\"send\"\r\n              type=\"primary\"\r\n            >\r\n              {portalFilesSelectedRequest.action === formActions.EDIT\r\n                ? \"Update\"\r\n                : \"Send\"}\r\n            </Button>\r\n          </>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <Request\r\n            onChange={() => onChangeRequest()}\r\n            actionType={portalFilesSelectedRequest.action}\r\n            formRef={form}\r\n            onFinish={() => sendRequest()}\r\n            data={requestData}\r\n          />\r\n        </div>\r\n      </Modal>\r\n\r\n      {/* Checkin Model */}\r\n      <Modal\r\n        destroyOnClose={true}\r\n        key=\"100\"\r\n        visible={showCheckinModal}\r\n        title={\"Check-In File(s)\"}\r\n        onCancel={handleDisplayCheckinModal}\r\n        footer={[\r\n          <Button key=\"back\" type=\"default\" onClick={handleDisplayCheckinModal}>\r\n            cancel\r\n          </Button>,\r\n\r\n          <Button\r\n            disabled={!allowCheckinButton}\r\n            key=\"checkin\"\r\n            type=\"primary\"\r\n            onClick={onCheckin}\r\n          >\r\n            check-in\r\n          </Button>,\r\n        ]}\r\n      >\r\n        <div className={styles.yjModalContentWrapper}>\r\n          <CheckIn\r\n            emailForm={emailForm}\r\n            onChangeEmailForm={() => {\r\n              setCheckinEmailFormChanged(true);\r\n            }}\r\n            onChangedCheckinForm={() => {\r\n              setCheckinFormChanged(true);\r\n            }}\r\n            onCheckin={(requestList: any) => {\r\n              setCheckinList(requestList);\r\n            }}\r\n            siteId={props.siteId}\r\n            binderId={props.binderId}\r\n            allowCheckinFunction={(isAllowed: boolean) => {\r\n              setAllowCheckinButton(isAllowed);\r\n            }}\r\n            form={form}\r\n          />\r\n        </div>\r\n      </Modal>\r\n\r\n      <div className={styles.yjFileAreaButtonGroupWrapper}>\r\n        {/* <Tooltip\r\n          placement=\"topLeft\"\r\n          title={\"This feature is coming soon\"}\r\n          color=\"#78bf59\"\r\n        > */}\r\n        {/* Removed below condition since, there is not mapped new permission\r\n           !fileAreaSettings.fileAreaManageEmailSubscription ||\r\n           */}\r\n        {/* <Button\r\n            hidden={\r\n              fileSelection === PORTAL_FILE_SECTION\r\n            }\r\n            type=\"primary\"\r\n          >\r\n            MANAGE EMAIL SUBSCRIPTION\r\n          </Button>\r\n        </Tooltip> */}\r\n        <Button\r\n          disabled={!hasCheckinList&&!props.manageCheckin}\r\n          hidden={\r\n            !userPermission.privDMSCanCheckInCheckOutInternalFiles ||\r\n            fileSelection === PORTAL_FILE_SECTION\r\n          }\r\n          onClick={() => {\r\n            setShowCheckinModal(true);\r\n            warningNotification(\r\n              [\"\"],\r\n              \"The file you are checking-in must match to the downloaded file name and the extension.\"\r\n            );\r\n          }}\r\n          type=\"primary\"\r\n        >\r\n          CHECK-IN\r\n        </Button>\r\n        {/* <Tooltip\r\n          placement=\"topLeft\"\r\n          title={\"This feature is coming soon\"}\r\n          color=\"#78bf59\"\r\n        >\r\n          <Button\r\n            hidden={!userPermission.privDMSCanViewFileHistory}\r\n            type=\"primary\"\r\n            onClick={onClickFileAreaHistory}\r\n          >\r\n            HISTORY\r\n          </Button>\r\n        </Tooltip> */}\r\n        {/*<Button*/}\r\n        {/*  onClick={() => {*/}\r\n        {/*    setRequestFormValidated(false);*/}\r\n        {/*    handleShowRequestModal(true);*/}\r\n        {/*  }}*/}\r\n        {/*  hidden={fileSelection === INTERNEL_FILE_SECTION}*/}\r\n        {/*  type=\"primary\"*/}\r\n        {/*>*/}\r\n        {/*  REQUEST*/}\r\n        {/*</Button>*/}\r\n      </div>\r\n    </>\r\n  );\r\n};\r\n"]}, "metadata": {}, "sourceType": "module"}