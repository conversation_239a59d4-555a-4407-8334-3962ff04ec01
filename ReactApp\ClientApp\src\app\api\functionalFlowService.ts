import HTTPResponse from "../utils/http/interfaces/HttpResponse";
import config from "../utils/config";
import httpVerbs from "../utils/http/httpVerbs";
import http from "../utils/http";
import { getParameterizedUrl } from "../utils";

export const getFunctionalFlowData = (
  licenseId: string
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api.organizationAPI.functionalFlowByLicence,
      licenseId
    ),
  });
};

export const UpdateFunctionalFlow = (
  modulesInput: any,
  licenceId: string
): Promise<HTTPResponse<any>> => {
  try {
    return http({
      method: httpVerbs.PUT,
      url: getParameterizedUrl(
        config.api.organizationAPI.updateFunctionalFlows,
        licenceId
      ),
      data: { modules: modulesInput },
    });
  } catch (e) {
    return Promise.reject(false);
  }
};
