import styles from "./index.module.less";
import React, { useCallback, useEffect, useRef, useState } from "react";
import {
  Button,
  Form,
  Checkbox,
  Row,
  Col,
  Radio,
  Upload,
  Tooltip,
  notification,
  Skeleton,
} from "antd";
import { UploadOutlined, DeleteOutlined } from "@ant-design/icons";
import FormItem from "antd/lib/form/FormItem";
import { FormInstance } from "antd/lib/form";
import { useSelector } from "react-redux";
import axios from "axios";
import {
  removeFiles,
  getCheckoutFileListBySiteId,
} from "@app/api/fileAreaService";
import { UploadChangeParam, RcFile } from "antd/lib/upload";

import EmailDocuments from "../EmailDocuments";
import UploadItem from "./UploadItem";
import useSimpleUploader from "../../../components/Uploader/hooks/UseSimpleUploader";
import { errorNotification } from "@app/utils/antNotifications";
import logger from "@app/utils/logger";
import { RootState } from "@app/redux/reducers/state";
import {
  FULL_PERCENTAGE,
  LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,
} from "@app/components/Uploader";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";

const FILE_MISMATCH_NOTIFICATION =
  "The selected file(s) name and file extension has to be the same as of the checked-out file(s)";
const FILE_TOO_LARGE_NOTIFICATION =
  "File is too large. Maximum uploadable file size is 1GB";
const FAILURE_PERCENTAGE = 15;

export interface ICheckin {
  key?: string;
  form: FormInstance;
  allowCheckinFunction: any;
  siteId: any;
  binderId: any;
  onCheckin: any;
  onChangedCheckinForm: any;
  onChangeEmailForm: any;
  emailForm: FormInstance;
}

export interface ICheckedOutFiles {
  id: string;
  fileId: string;
  title: string;
  downloadTitle: string;
  fileName: string | null;
  checked: false;
}

export const getFileId = (title: string): string | null => {
  if (title) {
    const fileIdValue = title.slice(0, title.lastIndexOf("."));
    return fileIdValue.substring(fileIdValue.lastIndexOf("-") - 2);
  } else {
    return null;
  }
};

export default ({
  form,
  allowCheckinFunction,
  siteId,
  binderId,
  onCheckin,
  onChangeEmailForm,
  onChangedCheckinForm,
  emailForm,
}: ICheckin) => {
  const { files, action } = useSimpleUploader();
  const {
    initializeInternalUpload,
    addInternalFile,
    remove,
    removeAll,
  } = action;
  const [currentUploaderUid, setCurrentUploaderUid] = useState<
    string | undefined
  >(undefined);

  const [fileListCollection, setCollection] = useState([]);
  const [singleUpload, setSingleUpload] = useState(true);
  const [bulkUpload, setBulkUpload] = useState(false);
  const [progress, setProgress] = useState(0);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const [reloadUploadItem, setReloadUpladItem] = useState(false);
  const [selectedFiles, setSelectedFiles] = useState<Array<any>>([]);
  const [bulkUploadList, setBulkUploadList] = useState<Array<any>>([]);
  const [fileUploaded, setFileUploaded] = useState(true);
  const [documentUpdated, setDocumentUpdated] = useState(false);
  const [requestList, setRequestList] = useState([]) as any;
  const [loaded, setLoaded] = useState<boolean>(false);
  const MaximumFileSize = 1073741824;
  const isShowingError = useRef(false);
  const uploadRef = useRef() as any;
  const { permissions } = useSelector((state: RootState) => {
    return { permissions: state.fileArea.fileAreaSettings };
  });
  const [cancelledUploadedFiles, setCancelledUploadedFiles] = useState<
    Array<any>
  >([]);
  const [nonMatchedList, setNonMatchedList] = useState<Array<any>>([]);

  const cancelFileUpload = useRef([]) as any;

  const isFileUploading = () => {
    return progress > 0 && progress < FULL_PERCENTAGE;
  };

  useEffect(() => {
    getCheckoutFileListBySiteId(binderId).then((response: HTTPResponse<any>) => {
      const checkedoutList = response.data as [];
      const checkingOutFileList = [] as ICheckedOutFiles[];
      const initArray = [] as any;
      checkedoutList.forEach((file: any) => {
        checkingOutFileList.push({
          id: file.id,
          checked: false,
          fileName: null,
          title: file.fileName,
          downloadTitle: file.title,
          fileId: file.id,
        });
      });
      checkingOutFileList.forEach((file: any) => {
        initArray.push([]);
      });
      setCollection(initArray);
      form?.setFieldsValue({ files: checkingOutFileList, commonNote: null });
      setLoaded(true);
    });
  }, [form]);

  const validateCheckinFiles = useCallback(
    (minimumFileCount: number) => {
      const additonalFiles = selectedFiles.filter(
        (file) => !requestList.some((other: any) => file.id === other.id)
      ) as [];
      if (
        selectedFiles.length > minimumFileCount &&
        selectedFiles.length === requestList.length &&
        additonalFiles?.length <= 0 &&
        cancelledUploadedFiles.length <= 0
      ) {
        allowCheckinFunction(true);
      } else {
        allowCheckinFunction(false);
      }
    },
    [allowCheckinFunction, requestList, selectedFiles, cancelledUploadedFiles]
  );

  useEffect(() => {
    if (allowCheckinFunction) {
      if (bulkUpload) {
        validateCheckinFiles(1);
        setDocumentUpdated(true);
      } else {
        validateCheckinFiles(0);
      }
    }
  }, [
    allowCheckinFunction,
    bulkUpload,
    validateCheckinFiles,
    documentUpdated,
    selectedFiles,
    requestList,
  ]);

  useEffect(() => {
    if (onCheckin) {
      onCheckin(requestList);
    }
  }, [onCheckin, requestList]);

  useEffect(() => {
    if (currentUploaderUid !== undefined) {
      if (files[currentUploaderUid].error) {
        if (singleUpload) {
          if (progress === FULL_PERCENTAGE) {
            setProgress(FAILURE_PERCENTAGE);
          }
          handleSingleFileUploadFail(
            files[currentUploaderUid].error,
            files[currentUploaderUid].id
          );
        } else {
          logUploadFileError(files[currentUploaderUid].error);
        }
      } else {
        const { file } = files[currentUploaderUid].uploadOptions;
        if (singleUpload) {
          setSingleUploadFileSelection(
            files[currentUploaderUid].referenceNumber,
            files[currentUploaderUid].id,
            file
          );
        } else {
          setMultipleUploadFileSelection(
            files[currentUploaderUid].referenceNumber,
            file
          );
          setDocumentUpdated(!documentUpdated);
        }
      }
    }
  }, [currentUploaderUid]);

  const addToRequestList = (referenceId: any, fileId: string) => {
    const uploadFileList = requestList;
    uploadFileList.push({
      id: getFileId(fileId),
      referenceNumber: referenceId,
    });
    setRequestList(uploadFileList);
    setDocumentUpdated(true);
  };
  const removeFromRequestList = (fileIdValue: string) => {
    const filterdList = requestList.filter(
      (filter: any) => filter.id !== fileIdValue
    );
    setRequestList(filterdList);
    setDocumentUpdated(true);
  };

  const setSingleFileUploadFormValues = (file: any, index: number) => {
    setSelectedIndex(index);
    form.setFields([{ name: ["files", index, "showProgress"], value: true }]);
    form.setFields([{ name: ["files", index, "fileName"], value: file.name }]);
  };

  const setSingleUploadFileSelection = (
    referenceNumber: string | null,
    index: number,
    file: any
  ) => {
    const fileCollectionArray = fileListCollection as any;
    fileCollectionArray[index] = [file];
    setCollection(fileCollectionArray);
    addToRequestList(referenceNumber, file.name);
  };

  const logUploadFileError = (error: any) => {
    if (!axios.isCancel(error)) {
      logger.error("File Area Module", "Checkin Files", error);
      errorNotification([""], "Upload Document Failed");
    }

    if (error.statusCode && error.statusCode === 409) {
      errorNotification([""], LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE);
    }

    return false;
  };

  const handleSingleFileUploadFail = (error: any, index: number) => {
    if (error) {
      logUploadFileError(error);
    }
  };

  const uploadFile = async (options: any, index: number) => {
    const { file } = options;
    setProgress(0);
    setFileUploaded(true);
    setDocumentUpdated(false);
    try {
      if (!onBeforuploadFile(file, index)) {
        errorNotification([""], FILE_MISMATCH_NOTIFICATION);
        return false;
      }
      if (file.size > MaximumFileSize) {
        errorNotification([""], FILE_TOO_LARGE_NOTIFICATION);
        return false;
      }
      setSingleFileUploadFormValues(file, index);
      initializeInternalUpload(siteId);
      addInternalFile(options, index, setCurrentUploaderUid, (percent) => {
        setProgress(Math.round(percent || 0));
      });
    } catch (error) {
      logUploadFileError(error);
    }
  };

  const setMultipleUploadFileSelection = (
    referenceNumber: string | null,
    file: any
  ) => {
    if (cancelledUploadedFiles.length > 0) {
      setBulkUploadList([]);
    } else {
      addToRequestList(referenceNumber, file.name);
    }
    setDocumentUpdated(true);
  };

  const uploadMultipleFiles = (options: any) => {
    initializeInternalUpload(siteId);
    addInternalFile(options, undefined, setCurrentUploaderUid);
  };

  const onChangeMulipleFilesUpload = (info: UploadChangeParam) => {
    const result = info.fileList.filter(
      (file) => !nonMatchedList.some((item) => item === file.uid)
    );
    setBulkUploadList(result);
  };

  const onBeforuploadFile = (file: RcFile, index: number): boolean => {
    const fileName = form.getFieldValue(["files", index, "title"]) as string;
    if (file.name.toLowerCase() === fileName.toLowerCase()) {
      return true;
    } else {
      return false;
    }
  };

  const removeUploadedFile = (index: number) => {
    const selectedUid = Object.entries(files).filter(
      ([uid, info]) => info.id === index
    );
    remove(selectedUid[0][0]);
    const fileName = form.getFieldValue(["files", index, "id"]) as string;
    if (isFileUploading()) {
      const cancelledFiles = cancelledUploadedFiles;
      cancelledFiles.push(fileName);
      setCancelledUploadedFiles(cancelledFiles);
      removeFiles(siteId, [selectedUid[0][1].referenceNumber], true);
    } else {
      removeFiles(siteId, [selectedUid[0][1].referenceNumber]);
      removeFromRequestList(fileName);
    }
  };

  const onChangeCheckbox = (event: any, index: number) => {
    form?.setFieldsValue([
      {
        name: ["files", index, "checked"],
        value: event.target.checked,
      },
    ]);
    const listFiles = form.getFieldValue("files") as [];
    const uploadedFiles = listFiles.filter((file: any) => file.checked) as any;
    removeCancelledItems(index);
    setSelectedFiles(uploadedFiles);
  };

  const beforeUploadFiles = (file: RcFile, inputFileList: RcFile[]) => {
    const nonMatchingFileList = nonMatchedList;
    if (file.size > MaximumFileSize) {
      nonMatchingFileList.push(file.uid);
      setNonMatchedList(nonMatchingFileList);
      if (uploadRef.current.state) {
        uploadRef.current.state.fileList = [];
      }
      if (!isShowingError.current) {
        isShowingError.current = true;
        notification.error({
          message: FILE_TOO_LARGE_NOTIFICATION,
          onClose: () => (isShowingError.current = false),
          className: "yjErrorMsg",
        });
      }
      return false;
    }

    if (
      !selectedFiles.find(
        (selecledFIle: any) =>
          selecledFIle.title.toString() === file.name.toString()
      )
    ) {
      nonMatchingFileList.push(file.uid);
      setNonMatchedList(nonMatchingFileList);
      if (uploadRef.current.state) {
        uploadRef.current.state.fileList = [];
      }

      if (!isShowingError.current) {
        isShowingError.current = true;
        notification.error({
          message: FILE_MISMATCH_NOTIFICATION,
          onClose: () => (isShowingError.current = false),
          className: "yjErrorMsg",
        });
      }
      return false;
    }
    return true;
  };

  const onSelectedCheckinOption = (isBulkUpload: boolean) => {
    setBulkUpload(isBulkUpload);
    setSingleUpload(!isBulkUpload);
    setDocumentUpdated(true);
    setBulkUploadList([]);
    setRequestList([]);
    if (isBulkUpload) {
      const upFiles = form.getFieldValue("files") as [];
      upFiles.forEach((file, index) => {
        form.setFields([
          {
            name: ["files", index, "fileName"],
            value: null,
          },
        ]);
      });
    }
  };

  const onRemoveSingleUploadItem = (field: any) => {
    form.setFields([
      {
        name: ["files", field.name, "fileName"],
        value: null,
      },
    ]);
    form.setFields([
      { name: ["files", field.name, "showProgress"], value: false },
    ]);

    if (
      isFileUploading() &&
      cancelFileUpload !== undefined &&
      cancelFileUpload.current
    ) {
      cancelFileUpload.current.forEach((cancelCurrentUpload: any) => {
        cancelCurrentUpload();
      });
    }

    setProgress(0);
    setReloadUpladItem(!reloadUploadItem);
    removeUploadedFile(field.name);
  };

  const removeCancelledItems = (index: any) => {
    const fileId = getFileId(form.getFieldValue(["files", index, "title"]));
    if (
      fileId &&
      cancelledUploadedFiles.find((fileIdValue) => fileIdValue === fileId) &&
      singleUpload
    ) {
      const UpFileList = requestList.filter((file: any) => file.id !== fileId);
      setRequestList(UpFileList);
      const cancelledUploadedList = cancelledUploadedFiles.filter(
        (fileIdValue) => fileIdValue !== fileId
      );
      setCancelledUploadedFiles(cancelledUploadedList);
    }
  };

  const renderDynamicCheckinFields = (field: any) => {
     logger.debug('File Area Module', 'renderDynamicCheckinFields', { field });
    return (
      <Row
        key={field.name}
        gutter={24}
        className={styles.yjCheckInIndividualItem}
      >
        <Col span={1} className={styles.yjCheckInActionFormItem}>
          <FormItem valuePropName="checked" name={[field.name, "checked"]}>
            <Checkbox
              disabled={isFileUploading()}
              onChange={(e) => onChangeCheckbox(e, field.name)}
              className={styles.yjCheckInActionCheckbox}
            ></Checkbox>
          </FormItem>
        </Col>

        <Col span={3} className={styles.yjCheckInActionFormItem}>
          <FormItem name={[field.name, "fileId"]} colon={false}>
            {form.getFieldValue(["files", field.name, "fileId"])}
          </FormItem>
        </Col>

        <Col span={5} className={styles.yjCheckInActionFormItem}>
          <Form.Item name={[field.name, "downloadTitle"]} colon={false}>
            <div className={styles.yjCheckInFileName}>
              <Tooltip
                placement="topLeft"
                title={form.getFieldValue([
                  "files",
                  field.name,
                  "downloadTitle",
                ])}
              >
                {form.getFieldValue(["files", field.name, "downloadTitle"])}
              </Tooltip>
            </div>
          </Form.Item>
        </Col>

        <Col span={5} className={styles.yjCheckInActionFormItem}>
          <Form.Item name={[field.name, "title"]} colon={false}>
            <div className={styles.yjCheckInFileName}>
              <Tooltip
                placement="topLeft"
                title={form.getFieldValue(["files", field.name, "title"])}
              >
                {form.getFieldValue(["files", field.name, "title"])}
              </Tooltip>
            </div>
          </Form.Item>
        </Col>

        <Col span={10} className={styles.yjCheckInActionFormItem}>
          <Form.Item>
            <Upload
              showUploadList={false}
              customRequest={(options) => uploadFile(options, field.name)}
              fileList={fileListCollection[field.name]}
            >
              <Button
                onClick={() => {
                  removeCancelledItems(field.name);
                }}
                disabled={
                  !form.getFieldValue(["files", field.name, "checked"]) ||
                  !singleUpload ||
                  isFileUploading()
                }
                type="primary"
              >
                <UploadOutlined /> Browse
              </Button>
            </Upload>
            {singleUpload && (
              <UploadItem
                selectedIndex={selectedIndex}
                reload={reloadUploadItem}
                index={field.name}
                onRemove={(index) => onRemoveSingleUploadItem(field)}
                title={form.getFieldValue(["files", field.name, "fileName"])}
                displayFile={
                  form.getFieldValue(["files", field.name, "fileName"]) !==
                    null && fileUploaded
                }
                displayProgressBar={
                  form.getFieldValue(["files", field.name, "showProgress"]) &&
                  field.name === selectedIndex &&
                  fileUploaded
                }
                percent={progress}
              />
            )}
          </Form.Item>
        </Col>
      </Row>
    );
  };

  const onBulkUploadDelete = () => {
    if (requestList.length > 0) {
      removeFiles(
        siteId,
        requestList.map((file: any) => file.referenceNumber)
      );
    }
    const onProcessFiles = Object.entries(files).filter(
      ([uid, info]) =>
        info.referenceNumber &&
        (info.percent ? info.percent : 0) > 0 &&
        (info.percent ? info.percent : 0) < FULL_PERCENTAGE
    ) as any;

    const flattenedFiles = [].concat.apply([], onProcessFiles);
    const chunckedReferenceNumbers = flattenedFiles
      .map((file: any) => {
        return file.referenceNumber && file.referenceNumber;
      })
      .filter((reference) => reference);

    if (chunckedReferenceNumbers.length > 0) {
      removeFiles(siteId, chunckedReferenceNumbers, true);
    }
    setBulkUploadList([]);
    setRequestList([]);
    removeAll();
    setProgress(0);
  };

  const onSelectIndividualUpload = () => {
    if (requestList.length > 0) {
      removeFiles(
        siteId,
        requestList.map((file: any) => file.referenceNumber)
      );
    }
    setRequestList([]);
    setCancelledUploadedFiles([]);
    onSelectedCheckinOption(false);
  };

  const onSelectBulklUpload = () => {
    if (requestList.length > 0) {
      removeFiles(
        siteId,
        requestList.map((file: any) => file.referenceNumber)
      );
    }
    setRequestList([]);
    setCancelledUploadedFiles([]);
    removeAll();
    onSelectedCheckinOption(true);
  };

  return (
    <>
      <div className={styles.yjModalContentWrapper}>
        <div className={styles.yjCheckInActionWrapper}>
          <div className={styles.yjCheckInActionUpperSection}>
            {loaded ? (
              <Form
                onChange={onChangedCheckinForm}
                form={form}
                autoComplete="off"
                className={styles.yjCheckInActionForm}
              >
                <Row gutter={24}>
                  <Col span={24}>
                    <Form.Item>
                      <Radio
                        disabled={
                          singleUpload
                            ? isFileUploading()
                            : requestList.length !== bulkUploadList.length
                        }
                        onChange={onSelectIndividualUpload}
                        checked={singleUpload}
                      >
                        <b>INDIVIDUAL CHECK-IN</b>
                      </Radio>
                    </Form.Item>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={24}>
                    <div className={styles.yjCheckInActionFormHeader}>
                      <Row gutter={24}>
                        <Col span={1}></Col>
                        <Col span={3}>
                          <Form.Item
                            children={null}
                            label="ID"
                            colon={false}
                          ></Form.Item>
                        </Col>
                        <Col span={5}>
                          <Form.Item
                            children={null}
                            label="File"
                            colon={false}
                          ></Form.Item>
                        </Col>
                        <Col span={5}>
                          <Form.Item
                            children={null}
                            label="Downloaded File Name"
                            colon={false}
                          ></Form.Item>
                        </Col>
                      </Row>
                    </div>
                    <div className={styles.yjCheckInActionFormGrid}>
                      <Form.List name="files">
                        {(fields: any) => {
                          return (
                            <div>
                              {fields.map((field: any) =>
                                renderDynamicCheckinFields(field)
                              )}
                            </div>
                          );
                        }}
                      </Form.List>
                    </div>
                  </Col>
                </Row>
                <Row gutter={24}>
                  <Col span={4}>
                    <Form.Item>
                      <Radio
                        disabled={isFileUploading()}
                        onChange={onSelectBulklUpload}
                        checked={bulkUpload}
                      >
                        <b>BULK UPLOAD</b>
                      </Radio>
                    </Form.Item>
                  </Col>
                  <Col span={20}>
                    <Form.Item>
                      <Upload
                        ref={uploadRef}
                        beforeUpload={beforeUploadFiles}
                        showUploadList={{ showRemoveIcon: false }}
                        fileList={bulkUploadList}
                        onChange={(info) => onChangeMulipleFilesUpload(info)}
                        customRequest={(options) =>
                          uploadMultipleFiles(options)
                        }
                        multiple={true}
                        style={{ height: 600 }}
                        className="yjCheckinBulkUploader"
                      >
                        <Button
                          onClick={() => {
                            setFileUploaded(false);
                            setBulkUploadList([]);
                            setRequestList([]);
                            setCancelledUploadedFiles([]);
                          }}
                          disabled={singleUpload || selectedFiles.length <= 1}
                          type="primary"
                        >
                          <UploadOutlined /> Browse
                        </Button>
                      </Upload>
                      <Button
                        className={styles.yjBulkDelete}
                        type="primary"
                        disabled={!bulkUpload || bulkUploadList.length <= 0}
                        onClick={onBulkUploadDelete}
                      >
                        <DeleteOutlined /> Delete
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            ) : (
              <Skeleton active={true} />
            )}
          </div>
        </div>
        {permissions.internalFilesEmail && (
          <EmailDocuments
            hideAttchments={true}
            form={emailForm}
            onChangeEmailForm={onChangeEmailForm}
          />
        )}
      </div>
    </>
  );
};
