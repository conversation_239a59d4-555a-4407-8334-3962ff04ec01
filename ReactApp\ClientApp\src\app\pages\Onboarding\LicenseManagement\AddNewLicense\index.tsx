import React, { Fragment, useRef, useState, useEffect } from "react";
import { useSelector } from "react-redux";
import { Form } from "antd";
import { Beforeunload } from "react-beforeunload";

import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";
import LicenseDetailsContainer from "../../../../components/forms/LicenseManagement/LicenseDetailsContainer";
import WizardConfigType from "../../../../types/WizardConfigType";
import Wizard from "../../../../components/Wizard";
import OrganizationManagementContainer from "../../../Onboarding/OrganizationManagement/Container";
import FunctionalFlowDetailsContainer from "../../../../components/forms/FunctionalFlowManagement/FunctionalFlowDetailsContainer";
import { PromptOnload } from "../../../../utils/confirm/onloadPrompt";
import { RootState } from "../../../../redux/reducers/state";

const WIZARD_REDIRECT_TIMEOUT = 200;

export default (props: any) => {
  const [form] = Form.useForm();
  const wizardRef = useRef({ continue: () => {} });
  const [licenseId, setLicenseId] = useState("");
  const [licenseModulesTouchedState, setLicenseModulesTouchedState] = useState(
    false
  );
  const [organizationFormState, setOrganizationFormState] = useState(false);
  const [licenseForm, setLicenseForm] = useState(false);
  const [wizardForm, setWizardForm] = useState(false);
  const [editFunctionalFlow, SetEditFunctionalFlow] = useState(false);
  const { isEdited } = useSelector((state: RootState) => state.functionalFlow);

  useEffect(() => {
    setWizardForm(!isEdited);
  }, [isEdited]);

  useEffect(() => {
    setWizardForm(!licenseModulesTouchedState);
  }, [licenseModulesTouchedState]);

  useEffect(() => {
    setWizardForm(!licenseForm);
  }, [licenseForm]);

  const steps: WizardConfigType[] = [
    {
      key: 1,
      name: "License Details",
      content: (
        <LicenseDetailsContainer
          form={form}
          licenseId={licenseId}
          isModulesTouched={setLicenseModulesTouchedState}
          onSaveSuccessed={(id) => handleSuccess(id)}
          onChangeLicenceForm={() => setLicenseForm(true)}
        />
      ),
    },
    {
      key: 2,
      name: "Organization Details",
      content: (
        <OrganizationManagementContainer
          form={form}
          licenseId={licenseId}
          type={"wizard"}
          formChangedHandle={() => {
            setOrganizationFormState(true);
            setWizardForm(false);
          }}
          onSaveSucceed={() => handleSuccess()}
        />
      ),
    },
    {
      key: 3,
      name: "Functional Flow Details",
      content: (
        <FunctionalFlowDetailsContainer
          actionType="edit"
          licenceId={licenseId}
          onEditFunctionFlow={() => {
            SetEditFunctionalFlow(true);
          }}
        />
      ),
    },
  ];

  const handleSuccess = (id?: string) => {
    if (id) {
      setWizardForm(true);
      setLicenseId(id);
    }
    wizardRef.current.continue();
  };

  const handleWizardActions = () => {
    setTimeout(() => {
      setWizardForm(true);
    }, WIZARD_REDIRECT_TIMEOUT);
  };

  return (
    <Fragment>
      <PageTitle title={props.title} />
      <PageContent>
        <Beforeunload onBeforeunload={(event) => event.preventDefault()} />
        {!wizardForm &&
          (form.isFieldsTouched() || licenseModulesTouchedState) && (
            <PromptOnload isBlocking={true} isSaving={false} />
          )}

        {!wizardForm && editFunctionalFlow && (
          <PromptOnload isBlocking={true} isSaving={false} />
        )}

        {!wizardForm && organizationFormState && (
          <PromptOnload isBlocking={true} isSaving={false} />
        )}

        <Wizard
          history={props.history}
          ref={wizardRef}
          formRef={form}
          steps={steps}
          licenseId={licenseId}
          licenseModulesTouched={licenseModulesTouchedState}
          organizationFormTouched={organizationFormState}
          onSave={() => {
            handleWizardActions();
          }}
          onCancel={() => {
            handleWizardActions();
          }}
          onDiscardedCancel={() => {
            setWizardForm(false);
          }}
        />
      </PageContent>
    </Fragment>
  );
};
