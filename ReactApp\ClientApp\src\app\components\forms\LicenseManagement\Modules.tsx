import React, { useState, useRef } from "react";
import { Collapse, Checkbox, Row, Col, Tree, Tooltip } from "antd";
import { splitArrayToColumns } from "@app/utils/array";
import styles from "./index.module.less";
import { InfoCircleOutlined } from "@ant-design/icons";

const { Panel } = Collapse;

const DEFAULT_NUMBER_OF_COLUMNS = 3;

type ModulesProps = {
  modules: any;
  disabled?: boolean;
  numberOfColumns?: number;
  onChange?: Function;
};

type ModuleProps = {
  module: any;
  disabled?: boolean;
  onChange?: Function;
};

type SubModuleProps = {
  modules: any[];
  disabled: boolean;
  onSelect: Function;
  isParentChecked: boolean;
};

const mapModulesToTree = (modules: any[], disabled: boolean) => {
  const selectedKeys: any[] = [];
  const treeData = modules.map((i) => {
    if ((!disabled && i.isMandatory) || i.checked) {
      selectedKeys.push(i.id.toString());
    }
    return {
      key: i.id.toString(),
      title: i.name,
      id: i.id,
      isMandatory: i.isMandatory,
      isSubModule: true,
      disabled: i.isMandatory || disabled,
      children: i.functions.map((j: any) => {
        const childKey = `${i.id}-${j.id}`;
        if ((!disabled && j.isMandatory) || j.checked) {
          selectedKeys.push(childKey);
        }
        return {
          title: j.name,
          parentId: i.id,
          id: j.id,
          isFunction: true,
          disabled: j.isMandatory || disabled,
          key: childKey,
        };
      }),
    };
  });
  return { selectedKeys, treeData };
};

export default ({
  modules,
  disabled = false,
  numberOfColumns = DEFAULT_NUMBER_OF_COLUMNS,
  onChange,
}: ModulesProps) => {
  const onChangeModule = (e: any) => {
    onChange?.(e);
  };
  return (
    <>
      {/* <Row gutter={16}>
        <Col span={24}>
          <div className={styles.yjSectionHeading}>
            <h6 className={styles.yjModuleSubHeading}>
              Modules{" "}
              <Tooltip
                color="#78bf59"
                title="The following Modules will be coming soon, 
              Client Administration, Communications & Notifications, 
              Reporting, Retention, Compliance, Workflows, Platform Monitoring - Usage Statistics"
              >
                <InfoCircleOutlined
                  className={styles.yjInfoIcon}
                  style={{ top: "0" }}
                />
              </Tooltip>{" "}
            </h6>
          </div>
        </Col>
      </Row> */}
      <Row gutter={16}>
        {splitArrayToColumns(modules, numberOfColumns).map(
          (colModules: any, index: any) => {
            return (
              <Col key={index} span={8} className={styles.yjAccordianWrapper}>
                {colModules.map((element: any, key: any) => {
                  return (
                    <Module
                      module={element}
                      onChange={onChangeModule}
                      disabled={disabled}
                      key={key}
                    />
                  );
                })}
              </Col>
            );
          }
        )}
      </Row>
    </>
  );
};

export const Module = ({ module, disabled = false, onChange }: ModuleProps) => {
  const { id, name, checked, isMandatory, subModules } = module;
  const [parentCheck, setParentCheck] = useState(checked || isMandatory);

  const onSelect = (e: any[]) => {
    onChange?.({
      parentId: id,
      parentChecked: parentCheck,
      isSubModuleUpdated: true,
      subModules: e,
    });
  };

  const onParentCheck = (e: any) => {
    setParentCheck(e.target.checked);
    onChange?.({
      parentId: id,
      parentChecked: e.target.checked,
      isSubModuleUpdated: false,
      subModules: [],
    });
  };

  return (
    <div className={"yjCommonAccordian"}>
      <Collapse expandIconPosition={"right"}>
        <Panel
          header={
            <>
              <Checkbox
                className={"yjAccordianCheckbox"}
                id={id}
                checked={parentCheck}
                disabled={isMandatory || disabled}
                onChange={onParentCheck}
                onClick={(e) => e.stopPropagation()}
              />
              <span className={"yjAccordianLabel"}>{name}</span>
            </>
          }
          key={id}
          disabled={!subModules?.length}
          showArrow={!!subModules?.length}
        >
          <SubModule
            modules={subModules}
            onSelect={onSelect}
            disabled={disabled}
            isParentChecked={parentCheck}
          />
        </Panel>
      </Collapse>
    </div>
  );
};

export const SubModule = ({
  modules,
  disabled,
  onSelect,
  isParentChecked,
}: SubModuleProps) => {
  const { selectedKeys, treeData } = mapModulesToTree(modules, disabled);
  const [selKeys, setSelKeys] = useState(selectedKeys);

  const TreeRef = useRef(null);

  const generateReturnObject = (nodes: any[]) => {
    return nodes
      .filter((i) => i.isSubModule)
      .map((j) => {
        return {
          moduleId: j.id,
          checked: true,
          functions: nodes.filter((k) => k.parentId === j.id).map((f) => f.id),
        };
      });
  };
  const onCheck = (e: any, g: any) => {
    if (isParentChecked) {
      setSelKeys(e);
    }
    onSelect(generateReturnObject(g.checkedNodes));
  };

  return (
    <Tree
      checkable
      ref={TreeRef}
      onCheck={onCheck}
      checkedKeys={isParentChecked ? selKeys : []}
      treeData={treeData}
      disabled={!isParentChecked}
      defaultExpandAll={true}
      className={"yjAccordianCheckboxList"}
    />
  );
};
