import configureMockStore from "redux-mock-store";
import axios from "axios";
import thunk from "redux-thunk";
import MockAdapter from "axios-mock-adapter";

import {
  ADD_GRID_FILTER,
  APPLY_SAVED_FILTER,
  CLEAR_ALL_FILTERS,
  FILTER_CLOUD_UPDATED,
  GET_CURRENT_FILTERS,
  GET_SAVED_FILTERS,
  REMOVE_GRID_FILTER,
  UPDATE_FILTERS,
  UPDATE_SAVED_FILTER_TEMPLATES,
} from "@app/redux/actionTypes/gridActionTypes";
import * as actions from "@app/redux/actions/gridsActions";

const middleware = [thunk];
const mockStore = configureMockStore(middleware);
const mock = new MockAdapter(axios);
const store = mockStore({});
const API_FILTER = "api/filter";

describe("grid actions unit tests", () => {
  beforeEach(() => {
    store.clearActions();
  });

  it("should create an action to save a new filter", () => {
    const gridFilters = [];
    const expectedAction = {
      type: APPLY_SAVED_FILTER,
      payload: gridFilters,
    };
    expect(actions.applyASavedFilter(gridFilters)).toEqual(expectedAction);
  });
  it("should create an action to remove a filter", () => {
    const filterKey = "xxxx";
    const value = "xxxx";
    const expectedAction = {
      type: REMOVE_GRID_FILTER,
      payload: { key: filterKey, value },
    };
    expect(actions.removeGridFilter(filterKey, value)).toEqual(expectedAction);
  });
  it("should create an action to add a grid filter", () => {
    const value = {};
    const expectedAction = {
      type: ADD_GRID_FILTER,
      payload: value,
    };
    expect(actions.addGridFilter(value)).toEqual(expectedAction);
  });
  it("should create an action to update saved filters", () => {
    const filters = [];
    const expectedAction = {
      type: UPDATE_SAVED_FILTER_TEMPLATES,
      payload: filters,
    };
    expect(actions.updateSavedFilterTemplates(filters)).toEqual(expectedAction);
  });
  it("should create an action to clear all filters", () => {
    const expectedAction = {
      type: CLEAR_ALL_FILTERS,
      payload: [],
    };
    expect(actions.clearGridFilters()).toEqual(expectedAction);
  });
  it("should create an action to update grid filters", () => {
    const value = true;
    const expectedAction = {
      type: UPDATE_FILTERS,
      payload: value,
    };
    expect(actions.updateGridFilters(value)).toEqual(expectedAction);
  });
  it("should create an action to get current filters", () => {
    const expectedAction = {
      type: GET_CURRENT_FILTERS,
    };
    expect(actions.getCurrentGridFilters()).toEqual(expectedAction);
  });
  it("should create an action to filter cloud update", () => {
    const filters = [];
    const expectedAction = {
      type: FILTER_CLOUD_UPDATED,
      payload: filters,
    };
    expect(actions.filterCloudUpdated(filters)).toEqual(expectedAction);
  });

  it("dispatches UPDATE_FILTERS after a successfull API requets to save new filter template", () => {
    mock.onPost(API_FILTER).reply(200, {});
    const store = mockStore({});
    store.dispatch(actions.saveFilterTemplate("xxxx", {})).then(() => {
      const expectedActions = {
        type: APPLY_SAVED_FILTER,
        payload: {},
      };
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it("dispatches UPDATE_FILTERS after a successfull API requets to save get saved filter templates", () => {
    mock.onGet(API_FILTER).reply(200, {});
    const savedFilters = {};
    const store = mockStore(savedFilters);
    store.dispatch(actions.getSavedFilters("xxxx")).then(() => {
      const expectedActions = {
        type: GET_SAVED_FILTERS,
        payload: savedFilters,
      };
      expect(store.getActions()).toEqual(expectedActions);
    });
  });

  it("dispatches UPDATE_FILTERS after a successfull API requets to save delete saved filter templates", () => {
    mock.onDelete(API_FILTER).reply(200, {});
    const store = mockStore({});
    store
      .dispatch(actions.deleteSavedFilterTemplate("xxxx", "xxxx"))
      .then(() => {
        const expectedActions = {
          type: REMOVE_GRID_FILTER,
          payload: { sideId: "xxxx", templateId: "xxxx" },
        };
        expect(store.getActions()).toEqual(expectedActions);
      });
  });

  it("dispatches UPDATE_FILTERS after a successfull API requets to edit a saved filter templates", () => {
    mock.onPut(API_FILTER).reply(200, {});
    const savedFilters = {};
    const store = mockStore(savedFilters);
    store
      .dispatch(actions.editSavedFilterTemplate("xxxx", "xxxx", {}))
      .then(() => {
        const expectedActions = {
          type: UPDATE_SAVED_FILTER_TEMPLATES,
          payload: savedFilters,
        };
        expect(store.getActions()).toEqual(expectedActions);
      });
  });
});
