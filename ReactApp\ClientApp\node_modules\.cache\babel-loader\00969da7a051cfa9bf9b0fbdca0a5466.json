{"ast": null, "code": "import \"antd/es/alert/style\";\nimport _Alert from \"antd/es/alert\";\nimport \"antd/es/skeleton/style\";\nimport _Skeleton from \"antd/es/skeleton\";\nimport \"antd/es/tag/style\";\nimport _Tag from \"antd/es/tag\";\nimport \"antd/es/tooltip/style\";\nimport _Tooltip from \"antd/es/tooltip\";\nimport \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\pages\\\\FileAreas\\\\index.tsx\";\nimport React, { Fragment, useEffect, useState } from \"react\";\nimport { withRouter } from \"react-router-dom\";\nimport { FolderOpenOutlined, LockOutlined } from \"@ant-design/icons\";\nimport PageTitle from \"../../components/PageTitle\";\nimport { PageContent } from \"../../layouts/MasterLayout\";\nimport GenericDataTable from \"../../components/GenericDataTable\";\nimport config from \"../../utils/config\";\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\nimport styles from \"./index.module.less\";\nimport NonAuthorized from \"@app/components/NonAuthorized\";\nimport { OperationalServiceTypes } from \"@iris/discovery.fe.client\";\nimport { getInfiniteRecords } from \"@app/api/infiniteRecordsService\";\nimport logger from \"@app/utils/logger\";\nimport { getAutocompleteOptions, getColumns, getRecords } from \"@app/api/genericDataTable\";\nimport debounce from \"lodash/debounce\";\nimport { useDispatch, useSelector } from \"react-redux\";\nimport { setDynamicBreadcrums } from \"@app/redux/actions/configurationActions\";\nimport StatusTag from \"@app/features/FileArea/StatusTag\";\nconst SORTER = {\n  value: \"siteName\",\n  order: \"ascend\"\n};\n\nconst Page = props => {\n  const [channelId, setChannelId] = useState();\n  const [isChannelLoaded, setIsChannelLoaded] = useState(false);\n  const {\n    userPermission\n  } = useSelector(state => state.userManagement);\n  const reactDispatch = useDispatch(); //Refresh action button in rerender\n\n  function gTCustomRenderAction(text, record) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"yjActionIconWrapper\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 44,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(_Tooltip, {\n      title: \"View\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 45,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(_Button, {\n      onClick: () => {\n        const encryptedName = encodeURIComponent(record.siteName);\n        const url = `/client-file-area/${record.siteId}/${encryptedName}/${channelId}`;\n        const encodedUrl = encodeURI(url);\n        props.history.push(encodedUrl);\n      },\n      icon: /*#__PURE__*/React.createElement(FolderOpenOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 53,\n          columnNumber: 19\n        }\n      }),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 46,\n        columnNumber: 11\n      }\n    })));\n  } //Refresh action button in rerender\n\n\n  function renderClientIdColumn(text, record) {\n    return /*#__PURE__*/React.createElement(\"div\", {\n      className: \"yjActionIconWrapper\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 63,\n        columnNumber: 7\n      }\n    }, text, \" \", record.isFolderExist === \"YES\" && /*#__PURE__*/React.createElement(_Tag, {\n      color: \"blue\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 64,\n        columnNumber: 53\n      }\n    }, \"File Area\"));\n  } //Error in loading GT\n\n\n  function gTOnErrorLoading(error) {\n    switch (error.statusCode) {\n      case FORBIDDEN_ERROR_CODE:\n        props.history.push(\"/forbidden\");\n        break;\n\n      default:\n        logger.error('FileArea', 'gTOnErrorLoading', error);\n        break;\n    }\n  }\n\n  let fetchData = (state, transformFilters, queryParams) => {\n    logger.debug(\"FileArea\", \"fetchData\", {\n      state,\n      transformFilters,\n      queryParams\n    });\n    return getRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel, {\n      pagination: {\n        current: state.pagination.current,\n        pageSize: state.pagination.pageSize\n      },\n      sorter: state.sorter ? {\n        key: state.sorter.columnKey,\n        order: state.sorter.order\n      } : {},\n      filters: transformFilters,\n      columns: state.columns.filter(i => i.default === false && i.selected === true).map(i => i.key)\n    }, queryParams);\n  }; // Debounced API call\n\n\n  const debouncedApiCall = debounce((props, value, callback) => {\n    getAutocompleteOptions(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel, props.data.key, value, props.searchFieldParameters).then(data => {\n      callback(data.data);\n    }).catch(() => {\n      callback([]);\n    });\n  }, config.inputDebounceInterval); // Wrapper function to return a Promise\n\n  const searchPromiseWrapper = (props, value, callback) => {\n    debouncedApiCall(props, value, data => {\n      callback(data);\n    });\n  };\n\n  const rowClassName = (record, index) => {\n    return record.status.toLowerCase().replace(/\\s+/g, '-').trim();\n  }; //Channel Status Toggler\n\n\n  function channelLoader(channelId, isChannelLoaded) {\n    const tableKey = \"FileAreaLandingPageGrid\";\n\n    if (channelId) {\n      return /*#__PURE__*/React.createElement(GenericDataTable, {\n        rowClassName: rowClassName // endpoint={getApiUrl( config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel )}\n        ,\n        searchPromise: searchPromiseWrapper,\n        dataPromise: (state, transformFilters, queryParams) => fetchData(state, transformFilters, queryParams),\n        columnPromise: getColumns(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel, tableKey),\n        searchFieldParameters: [{\n          key: \"channelId\",\n          value: channelId\n        }],\n        searchQueryParameters: [{\n          key: \"channelId\",\n          value: channelId\n        }],\n        rowKey: \"siteId\",\n        tableKey: tableKey,\n        sorted: SORTER,\n        key: channelId,\n        customRender: {\n          action: gTCustomRenderAction,\n          isFolderExist: value => {\n            return value ? /*#__PURE__*/React.createElement(_Tag, {\n              color: \"blue\",\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 145,\n                columnNumber: 31\n              }\n            }, \"File Area\") : '';\n          },\n          // isFolderExist: {\n          //   render: (value: any) => {\n          //     return (value ? <Tag color=\"blue\">File Area</Tag> : '')\n          //   },\n          //   width: 550\n          // }\n          status: value => {\n            return /*#__PURE__*/React.createElement(\"div\", {\n              style: {\n                textAlign: 'center'\n              },\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 155,\n                columnNumber: 17\n              }\n            }, /*#__PURE__*/React.createElement(StatusTag, {\n              value: value,\n              __self: this,\n              __source: {\n                fileName: _jsxFileName,\n                lineNumber: 156,\n                columnNumber: 19\n              }\n            }));\n          }\n        },\n        onErrorLoading: gTOnErrorLoading,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 130,\n          columnNumber: 9\n        }\n      });\n    } else if (isChannelLoaded) {\n      return /*#__PURE__*/React.createElement(NonAuthorized, {\n        styleClassName: styles.yjNonAuthorizedFileAreaWrapper,\n        title: \"You do not have the permission to access any offices\",\n        subTitle: \"Contact your organization's adminstrator for assistance\",\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 166,\n          columnNumber: 9\n        }\n      });\n    } else {\n      return /*#__PURE__*/React.createElement(_Skeleton, {\n        active: true,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 173,\n          columnNumber: 14\n        }\n      });\n    }\n  }\n\n  const getPaginatedRecords = async (page, method, searchValue) => {\n    const transformFilters = {};\n    /**\r\n     * Will add the keyvalue if dropdown still visible\r\n     */\n\n    if (searchValue) {\n      transformFilters.search = searchValue;\n    }\n\n    const options = {\n      limit: 10,\n      offset: page - 1,\n      ...transformFilters\n    };\n    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].channels, options).then(res => {\n      logger.info('SideSelection', 'getPaginatedRecords', res.data);\n\n      if (res.data) {\n        setChannelId(res.data.records[0].id); // TODO it's better to use redux store to store this and use Redux Persist library\n\n        localStorage.setItem('selectedChannelId', res.data.records[0].id);\n        return res.data;\n      } else {\n        logger.error('SideSelection', 'getPaginatedRecords', res.error);\n        return [];\n      }\n    }).catch(error => {\n      logger.error('SideSelection', 'getPaginatedRecords', error);\n      return [];\n    });\n  };\n\n  useEffect(() => {\n    getPaginatedRecords(1, 'load', ''); // Set dynamic breadcrumbs for the current page\n\n    const dynamicBreadcrumbs = [{\n      title: \"Client File Areas\",\n      path: \"/client-file-area\"\n    }];\n    reactDispatch(setDynamicBreadcrums(dynamicBreadcrumbs));\n    return () => {\n      reactDispatch(setDynamicBreadcrums([]));\n    };\n  }, []);\n  return /*#__PURE__*/React.createElement(Fragment, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 225,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(PageTitle, {\n    title: props.title,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 226,\n      columnNumber: 7\n    }\n  }), /*#__PURE__*/React.createElement(PageContent, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 247,\n      columnNumber: 7\n    }\n  }, userPermission.privDMSCanViewFileArea && channelLoader(channelId, isChannelLoaded), userPermission.privDMSCanViewFileArea === false && /*#__PURE__*/React.createElement(_Alert, {\n    message: \"You do not have permission to access the File Area. Contact your organization's administrator for assistance\",\n    icon: /*#__PURE__*/React.createElement(LockOutlined, {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 252,\n        columnNumber: 19\n      }\n    }),\n    type: \"error\",\n    showIcon: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 250,\n      columnNumber: 11\n    }\n  }), userPermission.privDMSCanViewFileArea === undefined && /*#__PURE__*/React.createElement(_Skeleton, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 257,\n      columnNumber: 11\n    }\n  })));\n};\n\nexport default withRouter(Page);", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/pages/FileAreas/index.tsx"], "names": ["React", "Fragment", "useEffect", "useState", "with<PERSON><PERSON><PERSON>", "FolderOpenOutlined", "LockOutlined", "Page<PERSON><PERSON>le", "<PERSON><PERSON><PERSON><PERSON>", "GenericDataTable", "config", "FORBIDDEN_ERROR_CODE", "styles", "NonAuthorized", "OperationalServiceTypes", "getInfiniteRecords", "logger", "getAutocompleteOptions", "getColumns", "getRecords", "debounce", "useDispatch", "useSelector", "setDynamicBreadcrums", "StatusTag", "SORTER", "value", "order", "Page", "props", "channelId", "setChannelId", "isChannelLoaded", "setIsChannelLoaded", "userPermission", "state", "userManagement", "reactDispatch", "gTCustomRenderAction", "text", "record", "encryptedName", "encodeURIComponent", "siteName", "url", "siteId", "encodedUrl", "encodeURI", "history", "push", "renderClientIdColumn", "isFolderExist", "gTOnErrorLoading", "error", "statusCode", "fetchData", "transformFilters", "queryParams", "debug", "api", "FileManagementService", "fileAreasByChannel", "pagination", "current", "pageSize", "sorter", "key", "column<PERSON>ey", "filters", "columns", "filter", "i", "default", "selected", "map", "debouncedApiCall", "callback", "data", "searchFieldParameters", "then", "catch", "inputDebounceInterval", "searchPromiseWrapper", "rowClassName", "index", "status", "toLowerCase", "replace", "trim", "channelLoader", "table<PERSON><PERSON>", "action", "textAlign", "yjNonAuthorizedFileAreaWrapper", "getPaginatedRecords", "page", "method", "searchValue", "search", "options", "limit", "offset", "MasterDataService", "channels", "res", "info", "records", "id", "localStorage", "setItem", "dynamicBreadcrumbs", "title", "path", "privDMSCanViewFileArea", "undefined"], "mappings": ";;;;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,QAAhB,EAA0BC,SAA1B,EAA0DC,QAA1D,QAA0E,OAA1E;AACA,SAASC,UAAT,QAA2B,kBAA3B;AAEA,SAASC,kBAAT,EAA6BC,YAA7B,QAAiD,mBAAjD;AAEA,OAAOC,SAAP,MAAsB,4BAAtB;AACA,SAASC,WAAT,QAA4B,4BAA5B;AACA,OAAOC,gBAAP,MAA6B,mCAA7B;AACA,OAAOC,MAAP,MAAkC,oBAAlC;AAEA,SAASC,oBAAT,QAAqC,YAArC;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,aAAP,MAA0B,+BAA1B;AAEA,SAASC,uBAAT,QAAwC,2BAAxC;AACA,SAASC,kBAAT,QAAmC,iCAAnC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,sBAAT,EAAiCC,UAAjC,EAA6CC,UAA7C,QAA+D,2BAA/D;AACA,OAAOC,QAAP,MAAqB,iBAArB;AACA,SAASC,WAAT,EAAsBC,WAAtB,QAAyC,aAAzC;AAEA,SAASC,oBAAT,QAAqC,yCAArC;AACA,OAAOC,SAAP,MAAsB,kCAAtB;AAOA,MAAMC,MAAc,GAAG;AACrBC,EAAAA,KAAK,EAAE,UADc;AAErBC,EAAAA,KAAK,EAAE;AAFc,CAAvB;;AAKA,MAAMC,IAAI,GAAIC,KAAD,IAAsB;AACjC,QAAM,CAACC,SAAD,EAAYC,YAAZ,IAA4B5B,QAAQ,EAA1C;AACA,QAAM,CAAC6B,eAAD,EAAkBC,kBAAlB,IAAwC9B,QAAQ,CAAU,KAAV,CAAtD;AACA,QAAM;AAAE+B,IAAAA;AAAF,MAAqBZ,WAAW,CAAEa,KAAD,IAAsBA,KAAK,CAACC,cAA7B,CAAtC;AACA,QAAMC,aAAa,GAAGhB,WAAW,EAAjC,CAJiC,CAMjC;;AACA,WAASiB,oBAAT,CAA8BC,IAA9B,EAAyCC,MAAzC,EAAsD;AACpD,wBACE;AAAK,MAAA,SAAS,EAAE,qBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AAAS,MAAA,KAAK,EAAC,MAAf;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AACE,MAAA,OAAO,EAAE,MAAM;AACb,cAAMC,aAAa,GAAGC,kBAAkB,CAACF,MAAM,CAACG,QAAR,CAAxC;AACA,cAAMC,GAAG,GAAI,qBAAoBJ,MAAM,CAACK,MAAO,IAAGJ,aAAc,IAAGX,SAAU,EAA7E;AACA,cAAMgB,UAAU,GAAGC,SAAS,CAACH,GAAD,CAA5B;AACAf,QAAAA,KAAK,CAACmB,OAAN,CAAcC,IAAd,CAAmBH,UAAnB;AACD,OANH;AAOE,MAAA,IAAI,eAAE,oBAAC,kBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAPR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MADF,CADF,CADF;AAeD,GAvBgC,CAyBjC;;;AACA,WAASI,oBAAT,CAA8BX,IAA9B,EAAyCC,MAAzC,EAAsD;AACpD,wBACE;AAAK,MAAA,SAAS,EAAE,qBAAhB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OACGD,IADH,OACWC,MAAM,CAACW,aAAP,KAAyB,KAA1B,iBAAoC;AAAK,MAAA,KAAK,EAAC,MAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAD9C,CADF;AAKD,GAhCgC,CAkCjC;;;AACA,WAASC,gBAAT,CAA0BC,KAA1B,EAAsC;AACpC,YAAQA,KAAK,CAACC,UAAd;AACE,WAAK3C,oBAAL;AACEkB,QAAAA,KAAK,CAACmB,OAAN,CAAcC,IAAd,CAAmB,YAAnB;AACA;;AAEF;AACEjC,QAAAA,MAAM,CAACqC,KAAP,CAAa,UAAb,EAAyB,kBAAzB,EAA6CA,KAA7C;AACA;AAPJ;AASD;;AACD,MAAIE,SAAS,GAAG,CAACpB,KAAD,EAAaqB,gBAAb,EAAoCC,WAApC,KAAyD;AACvEzC,IAAAA,MAAM,CAAC0C,KAAP,CAAa,UAAb,EAAyB,WAAzB,EAAsC;AAAEvB,MAAAA,KAAF;AAASqB,MAAAA,gBAAT;AAA2BC,MAAAA;AAA3B,KAAtC;AACA,WAAOtC,UAAU,CAACT,MAAM,CAACiD,GAAP,CAAW7C,uBAAuB,CAAC8C,qBAAnC,EAA0DC,kBAA3D,EACf;AACEC,MAAAA,UAAU,EAAE;AACVC,QAAAA,OAAO,EAAE5B,KAAK,CAAC2B,UAAN,CAAiBC,OADhB;AAEVC,QAAAA,QAAQ,EAAE7B,KAAK,CAAC2B,UAAN,CAAiBE;AAFjB,OADd;AAKEC,MAAAA,MAAM,EAAE9B,KAAK,CAAC8B,MAAN,GAAe;AAAEC,QAAAA,GAAG,EAAE/B,KAAK,CAAC8B,MAAN,CAAaE,SAApB;AAA+BxC,QAAAA,KAAK,EAAEQ,KAAK,CAAC8B,MAAN,CAAatC;AAAnD,OAAf,GAA4E,EALtF;AAMEyC,MAAAA,OAAO,EAAEZ,gBANX;AAOEa,MAAAA,OAAO,EAAElC,KAAK,CAACkC,OAAN,CAAcC,MAAd,CAAsBC,CAAD,IAAYA,CAAC,CAACC,OAAF,KAAc,KAAd,IAAuBD,CAAC,CAACE,QAAF,KAAe,IAAvE,EAA6EC,GAA7E,CAAkFH,CAAD,IAAYA,CAAC,CAACL,GAA/F;AAPX,KADe,EASZT,WATY,CAAjB;AAWD,GAbD,CA9CiC,CA6DjC;;;AACA,QAAMkB,gBAAgB,GAAGvD,QAAQ,CAC/B,CAACS,KAAD,EAAaH,KAAb,EAA4BkD,QAA5B,KAA8D;AAE5D3D,IAAAA,sBAAsB,CACpBP,MAAM,CAACiD,GAAP,CAAW7C,uBAAuB,CAAC8C,qBAAnC,EAA0DC,kBADtC,EAEpBhC,KAAK,CAACgD,IAAN,CAAWX,GAFS,EAGpBxC,KAHoB,EAIpBG,KAAK,CAACiD,qBAJc,CAAtB,CAMGC,IANH,CAMSF,IAAD,IAAe;AACnBD,MAAAA,QAAQ,CAACC,IAAI,CAACA,IAAN,CAAR;AACD,KARH,EASGG,KATH,CASS,MAAM;AACXJ,MAAAA,QAAQ,CAAC,EAAD,CAAR;AACD,KAXH;AAYD,GAf8B,EAgB/BlE,MAAM,CAACuE,qBAhBwB,CAAjC,CA9DiC,CAiFjC;;AACA,QAAMC,oBAAoB,GAAG,CAACrD,KAAD,EAAaH,KAAb,EAA4BkD,QAA5B,KAA8C;AACzED,IAAAA,gBAAgB,CAAC9C,KAAD,EAAQH,KAAR,EAAgBmD,IAAD,IAAe;AAC5CD,MAAAA,QAAQ,CAACC,IAAD,CAAR;AACD,KAFe,CAAhB;AAGD,GAJD;;AAKA,QAAMM,YAAY,GAAG,CAAC3C,MAAD,EAAa4C,KAAb,KAAuC;AAC1D,WAAO5C,MAAM,CAAC6C,MAAP,CAAcC,WAAd,GAA4BC,OAA5B,CAAoC,MAApC,EAA4C,GAA5C,EAAiDC,IAAjD,EAAP;AACD,GAFD,CAvFiC,CA0FjC;;;AACA,WAASC,aAAT,CAAuB3D,SAAvB,EAAuCE,eAAvC,EAA6D;AAC3D,UAAM0D,QAAQ,GAAG,yBAAjB;;AACA,QAAI5D,SAAJ,EAAe;AACb,0BACE,oBAAC,gBAAD;AACE,QAAA,YAAY,EAAEqD,YADhB,CAEE;AAFF;AAGE,QAAA,aAAa,EAAED,oBAHjB;AAIE,QAAA,WAAW,EAAE,CAAC/C,KAAD,EAAQqB,gBAAR,EAA0BC,WAA1B,KAA0CF,SAAS,CAACpB,KAAD,EAAQqB,gBAAR,EAA0BC,WAA1B,CAJlE;AAKE,QAAA,aAAa,EAAEvC,UAAU,CAACR,MAAM,CAACiD,GAAP,CAAW7C,uBAAuB,CAAC8C,qBAAnC,EAA0DC,kBAA3D,EAA+E6B,QAA/E,CAL3B;AAME,QAAA,qBAAqB,EAAE,CAAC;AAAExB,UAAAA,GAAG,EAAE,WAAP;AAAoBxC,UAAAA,KAAK,EAAEI;AAA3B,SAAD,CANzB;AAOE,QAAA,qBAAqB,EAAE,CAAC;AAAEoC,UAAAA,GAAG,EAAE,WAAP;AAAoBxC,UAAAA,KAAK,EAAEI;AAA3B,SAAD,CAPzB;AAQE,QAAA,MAAM,EAAE,QARV;AASE,QAAA,QAAQ,EAAE4D,QATZ;AAUE,QAAA,MAAM,EAAEjE,MAVV;AAWE,QAAA,GAAG,EAAEK,SAXP;AAYE,QAAA,YAAY,EAAE;AACZ6D,UAAAA,MAAM,EAAErD,oBADI;AAEZa,UAAAA,aAAa,EAAGzB,KAAD,IAAgB;AAC7B,mBAAQA,KAAK,gBAAG;AAAK,cAAA,KAAK,EAAC,MAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,2BAAH,GAAuC,EAApD;AACD,WAJW;AAKZ;AACA;AACA;AACA;AACA;AACA;AACA2D,UAAAA,MAAM,EAAG3D,KAAD,IAAmB;AACzB,gCACE;AAAK,cAAA,KAAK,EAAE;AAAEkE,gBAAAA,SAAS,EAAE;AAAb,eAAZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,4BACE,oBAAC,SAAD;AAAW,cAAA,KAAK,EAAElE,KAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cADF,CADF;AAKD;AAjBW,SAZhB;AA+BE,QAAA,cAAc,EAAE0B,gBA/BlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QADF;AAmCD,KApCD,MAoCO,IAAIpB,eAAJ,EAAqB;AAC1B,0BACE,oBAAC,aAAD;AACE,QAAA,cAAc,EAAEpB,MAAM,CAACiF,8BADzB;AAEE,QAAA,KAAK,EAAE,sDAFT;AAGE,QAAA,QAAQ,EAAE,yDAHZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QADF;AAOD,KARM,MAQA;AACL,0BAAO;AAAU,QAAA,MAAM,EAAE,IAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAAP;AACD;AACF;;AAED,QAAMC,mBAAmB,GAAG,OAAOC,IAAP,EAAqBC,MAArB,EAAuDC,WAAvD,KAAqG;AAC/H,UAAMzC,gBAAqB,GAAG,EAA9B;AACA;AACJ;AACA;;AACI,QAAIyC,WAAJ,EAAiB;AACfzC,MAAAA,gBAAgB,CAAC0C,MAAjB,GAA0BD,WAA1B;AACD;;AAED,UAAME,OAAO,GAAG;AACdC,MAAAA,KAAK,EAAE,EADO;AAEdC,MAAAA,MAAM,EAAEN,IAAI,GAAG,CAFD;AAGd,SAAGvC;AAHW,KAAhB;AAKA,WAAOzC,kBAAkB,CAACL,MAAM,CAACiD,GAAP,CAAW7C,uBAAuB,CAACwF,iBAAnC,EAAsDC,QAAvD,EAAiEJ,OAAjE,CAAlB,CACJpB,IADI,CACEyB,GAAD,IAAc;AAClBxF,MAAAA,MAAM,CAACyF,IAAP,CAAY,eAAZ,EAA6B,qBAA7B,EAAoDD,GAAG,CAAC3B,IAAxD;;AACA,UAAI2B,GAAG,CAAC3B,IAAR,EAAc;AACZ9C,QAAAA,YAAY,CAACyE,GAAG,CAAC3B,IAAJ,CAAS6B,OAAT,CAAiB,CAAjB,EAAoBC,EAArB,CAAZ,CADY,CAEZ;;AACAC,QAAAA,YAAY,CAACC,OAAb,CAAqB,mBAArB,EAA0CL,GAAG,CAAC3B,IAAJ,CAAS6B,OAAT,CAAiB,CAAjB,EAAoBC,EAA9D;AACA,eAAOH,GAAG,CAAC3B,IAAX;AACD,OALD,MAKO;AACL7D,QAAAA,MAAM,CAACqC,KAAP,CAAa,eAAb,EAA8B,qBAA9B,EAAqDmD,GAAG,CAACnD,KAAzD;AACA,eAAO,EAAP;AACD;AACF,KAZI,EAaJ2B,KAbI,CAaG3B,KAAD,IAAgB;AACrBrC,MAAAA,MAAM,CAACqC,KAAP,CAAa,eAAb,EAA8B,qBAA9B,EAAqDA,KAArD;AAEA,aAAO,EAAP;AACD,KAjBI,CAAP;AAkBD,GAhCD;;AAkCAnD,EAAAA,SAAS,CAAC,MAAM;AACd4F,IAAAA,mBAAmB,CAAC,CAAD,EAAI,MAAJ,EAAY,EAAZ,CAAnB,CADc,CAGd;;AACA,UAAMgB,kBAAkB,GAAG,CACzB;AAAEC,MAAAA,KAAK,EAAE,mBAAT;AAA8BC,MAAAA,IAAI,EAAE;AAApC,KADyB,CAA3B;AAGA3E,IAAAA,aAAa,CAACd,oBAAoB,CAACuF,kBAAD,CAArB,CAAb;AACA,WAAO,MAAM;AACXzE,MAAAA,aAAa,CAACd,oBAAoB,CAAC,EAAD,CAArB,CAAb;AACD,KAFD;AAGD,GAXQ,EAWN,EAXM,CAAT;AAaA,sBACE,oBAAC,QAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,SAAD;AAAW,IAAA,KAAK,EAAEM,KAAK,CAACkF,KAAxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAsBE,oBAAC,WAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACG7E,cAAc,CAAC+E,sBAAf,IAAyCxB,aAAa,CAAC3D,SAAD,EAAYE,eAAZ,CADzD,EAEGE,cAAc,CAAC+E,sBAAf,KAA0C,KAA1C,iBACC;AACE,IAAA,OAAO,EAAC,8GADV;AAEE,IAAA,IAAI,eAAE,oBAAC,YAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAFR;AAGE,IAAA,IAAI,EAAC,OAHP;AAIE,IAAA,QAAQ,MAJV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAHJ,EASG/E,cAAc,CAAC+E,sBAAf,KAA0CC,SAA1C,iBACC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAVJ,CAtBF,CADF;AAsCD,CAnOD;;AAqOA,eAAe9G,UAAU,CAACwB,IAAD,CAAzB", "sourcesContent": ["import React, { Fragment, useEffect, useImperative<PERSON><PERSON><PERSON>, useState } from \"react\";\r\nimport { with<PERSON>out<PERSON> } from \"react-router-dom\";\r\nimport { Tooltip, Button, Skeleton, Tag, Alert } from \"antd\";\r\nimport { FolderOpenOutlined, LockOutlined } from \"@ant-design/icons\";\r\n\r\nimport PageTitle from \"../../components/PageTitle\";\r\nimport { PageContent } from \"../../layouts/MasterLayout\";\r\nimport GenericDataTable from \"../../components/GenericDataTable\";\r\nimport config, { getApiUrl } from \"../../utils/config\";\r\nimport InfinitySelect, { InfinitySelectGetOptions } from \"@app/components/InfinitySelect\";\r\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\r\nimport styles from \"./index.module.less\";\r\nimport NonAuthorized from \"@app/components/NonAuthorized\";\r\nimport { Sorter } from \"@app/components/GenericDataTable/util\";\r\nimport { OperationalServiceTypes } from \"@iris/discovery.fe.client\";\r\nimport { getInfiniteRecords } from \"@app/api/infiniteRecordsService\";\r\nimport logger from \"@app/utils/logger\";\r\nimport { getAutocompleteOptions, getColumns, getRecords } from \"@app/api/genericDataTable\";\r\nimport debounce from \"lodash/debounce\";\r\nimport { useDispatch, useSelector } from \"react-redux\";\r\nimport { RootState } from \"@app/redux/reducers/state\";\r\nimport { setDynamicBreadcrums } from \"@app/redux/actions/configurationActions\";\r\nimport StatusTag from \"@app/features/FileArea/StatusTag\";\r\n\r\nexport interface IFileAres {\r\n  title?: string;\r\n  history?: any;\r\n}\r\n\r\nconst SORTER: Sorter = {\r\n  value: \"siteName\",\r\n  order: \"ascend\",\r\n};\r\n\r\nconst Page = (props: IFileAres) => {\r\n  const [channelId, setChannelId] = useState<string | undefined>();\r\n  const [isChannelLoaded, setIsChannelLoaded] = useState<boolean>(false);\r\n  const { userPermission } = useSelector((state: RootState) => state.userManagement);\r\n  const reactDispatch = useDispatch();\r\n\r\n  //Refresh action button in rerender\r\n  function gTCustomRenderAction(text: any, record: any) {\r\n    return (\r\n      <div className={\"yjActionIconWrapper\"}>\r\n        <Tooltip title=\"View\">\r\n          <Button\r\n            onClick={() => {\r\n              const encryptedName = encodeURIComponent(record.siteName);\r\n              const url = `/client-file-area/${record.siteId}/${encryptedName}/${channelId}`;\r\n              const encodedUrl = encodeURI(url);\r\n              props.history.push(encodedUrl);\r\n            }}\r\n            icon={<FolderOpenOutlined />}\r\n          />\r\n        </Tooltip>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  //Refresh action button in rerender\r\n  function renderClientIdColumn(text: any, record: any) {\r\n    return (\r\n      <div className={\"yjActionIconWrapper\"}>\r\n        {text} {(record.isFolderExist === \"YES\") && <Tag color=\"blue\">File Area</Tag>}\r\n      </div>\r\n    );\r\n  }\r\n\r\n  //Error in loading GT\r\n  function gTOnErrorLoading(error: any) {\r\n    switch (error.statusCode) {\r\n      case FORBIDDEN_ERROR_CODE:\r\n        props.history.push(\"/forbidden\");\r\n        break;\r\n\r\n      default:\r\n        logger.error('FileArea', 'gTOnErrorLoading', error);\r\n        break;\r\n    }\r\n  }\r\n  let fetchData = (state: any, transformFilters: any, queryParams: any) => {\r\n    logger.debug(\"FileArea\", \"fetchData\", { state, transformFilters, queryParams });\r\n    return getRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel,\r\n      {\r\n        pagination: {\r\n          current: state.pagination.current,\r\n          pageSize: state.pagination.pageSize,\r\n        },\r\n        sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},\r\n        filters: transformFilters,\r\n        columns: state.columns.filter((i: any) => i.default === false && i.selected === true).map((i: any) => i.key),\r\n      }, queryParams\r\n    )\r\n  };\r\n\r\n  // Debounced API call\r\n  const debouncedApiCall = debounce(\r\n    (props: any, value: string, callback: (data: any) => void) => {\r\n\r\n      getAutocompleteOptions(\r\n        config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel,\r\n        props.data.key,\r\n        value,\r\n        props.searchFieldParameters\r\n      )\r\n        .then((data: any) => {\r\n          callback(data.data);\r\n        })\r\n        .catch(() => {\r\n          callback([]);\r\n        });\r\n    },\r\n    config.inputDebounceInterval\r\n  );\r\n\r\n  // Wrapper function to return a Promise\r\n  const searchPromiseWrapper = (props: any, value: string, callback: any) => {\r\n    debouncedApiCall(props, value, (data: any) => {\r\n      callback(data)\r\n    });\r\n  };\r\n  const rowClassName = (record:any, index: number): string => {\r\n    return record.status.toLowerCase().replace(/\\s+/g, '-').trim();\r\n  };\r\n  //Channel Status Toggler\r\n  function channelLoader(channelId: any, isChannelLoaded: any) {\r\n    const tableKey = \"FileAreaLandingPageGrid\";\r\n    if (channelId) {\r\n      return (\r\n        <GenericDataTable\r\n          rowClassName={rowClassName}\r\n          // endpoint={getApiUrl( config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel )}\r\n          searchPromise={searchPromiseWrapper}\r\n          dataPromise={(state, transformFilters, queryParams) => fetchData(state, transformFilters, queryParams)}\r\n          columnPromise={getColumns(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannel, tableKey)}\r\n          searchFieldParameters={[{ key: \"channelId\", value: channelId }]}\r\n          searchQueryParameters={[{ key: \"channelId\", value: channelId }]}\r\n          rowKey={\"siteId\"}\r\n          tableKey={tableKey}\r\n          sorted={SORTER}\r\n          key={channelId}\r\n          customRender={{\r\n            action: gTCustomRenderAction,\r\n            isFolderExist: (value: any) => {\r\n              return (value ? <Tag color=\"blue\">File Area</Tag> : '')\r\n            },\r\n            // isFolderExist: {\r\n            //   render: (value: any) => {\r\n            //     return (value ? <Tag color=\"blue\">File Area</Tag> : '')\r\n            //   },\r\n            //   width: 550\r\n            // }\r\n            status: (value: string) => {\r\n              return (\r\n                <div style={{ textAlign: 'center' }}>\r\n                  <StatusTag value={value} />\r\n                </div>\r\n              );\r\n            },\r\n          }}\r\n          onErrorLoading={gTOnErrorLoading}\r\n        />\r\n      );\r\n    } else if (isChannelLoaded) {\r\n      return (\r\n        <NonAuthorized\r\n          styleClassName={styles.yjNonAuthorizedFileAreaWrapper}\r\n          title={\"You do not have the permission to access any offices\"}\r\n          subTitle={\"Contact your organization's adminstrator for assistance\"}\r\n        />\r\n      );\r\n    } else {\r\n      return <Skeleton active={true} />;\r\n    }\r\n  }\r\n\r\n  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {\r\n    const transformFilters: any = {};\r\n    /**\r\n     * Will add the keyvalue if dropdown still visible\r\n     */\r\n    if (searchValue) {\r\n      transformFilters.search = searchValue;\r\n    }\r\n\r\n    const options = {\r\n      limit: 10,\r\n      offset: page - 1,\r\n      ...transformFilters\r\n    }\r\n    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].channels, options)\r\n      .then((res: any) => {\r\n        logger.info('SideSelection', 'getPaginatedRecords', res.data);\r\n        if (res.data) {\r\n          setChannelId(res.data.records[0].id);\r\n          // TODO it's better to use redux store to store this and use Redux Persist library\r\n          localStorage.setItem('selectedChannelId', res.data.records[0].id);\r\n          return res.data;\r\n        } else {\r\n          logger.error('SideSelection', 'getPaginatedRecords', res.error);\r\n          return []\r\n        }\r\n      })\r\n      .catch((error: any) => {\r\n        logger.error('SideSelection', 'getPaginatedRecords', error);\r\n\r\n        return [];\r\n      });\r\n  };\r\n\r\n  useEffect(() => {\r\n    getPaginatedRecords(1, 'load', '')\r\n\r\n    // Set dynamic breadcrumbs for the current page\r\n    const dynamicBreadcrumbs = [\r\n      { title: \"Client File Areas\", path: \"/client-file-area\" },\r\n    ];\r\n    reactDispatch(setDynamicBreadcrums(dynamicBreadcrumbs));\r\n    return () => {\r\n      reactDispatch(setDynamicBreadcrums([]));\r\n    };\r\n  }, [])\r\n\r\n  return (\r\n    <Fragment>\r\n      <PageTitle title={props.title}>\r\n        {/* <div className={\"yjChannelSelector\"}>\r\n          {channelId && <InfinitySelect\r\n            getPaginatedRecords={getPaginatedRecords}\r\n            formatValue={(value) => {\r\n              return `${value.displayText}`;\r\n            }}\r\n            onLoaded={(isLoaded: boolean) => setIsChannelLoaded(isLoaded)}\r\n            isDefault={true}\r\n            notFoundContent=\"No Offices Available\"\r\n            notLoadContent=\"Failed to load values in office dropdown\"\r\n            onChange={(e) => {\r\n              setChannelId(e);\r\n            }}\r\n            placeholder=\"Office Name\"\r\n            defaultValues={channelId}\r\n            disabled={true}\r\n          />}\r\n        </div> */}\r\n      </PageTitle>\r\n\r\n      <PageContent>\r\n        {userPermission.privDMSCanViewFileArea && channelLoader(channelId, isChannelLoaded)}\r\n        {userPermission.privDMSCanViewFileArea === false &&\r\n          <Alert\r\n            message=\"You do not have permission to access the File Area. Contact your organization's administrator for assistance\"\r\n            icon={<LockOutlined />}\r\n            type=\"error\"\r\n            showIcon\r\n          />}\r\n        {userPermission.privDMSCanViewFileArea === undefined && (\r\n          <Skeleton />\r\n        )}\r\n      </PageContent>\r\n    </Fragment>\r\n  );\r\n};\r\n\r\nexport default withRouter(Page);\r\n"]}, "metadata": {}, "sourceType": "module"}