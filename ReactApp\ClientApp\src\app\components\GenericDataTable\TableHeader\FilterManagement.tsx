import React, { useState, useEffect } from "react";
import { getGridFilterFromIFilterTemplate } from "@app/components/GenericDataTable/FilterTemplateManagement/util";
import { Button, Select } from "antd";
import { DownOutlined, FilterOutlined } from "@ant-design/icons";
import { useSelector, useDispatch } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import {
  applyASavedFilter,
  saveFilterTemplate,
  updateFilterTemplateSaved,
  onSelectedSavedFilter,
  getSavedFilters,
} from "@app/redux/actions/gridsActions";
import { GenericFilter } from "@app/components/GenericDataTable/types";

import Modal from "@app/components/Modal";
import FilterTemplateManagementEdit from "@app/components/GenericDataTable/FilterTemplateManagement/edit";
import FilterTemplateManagementSave from "@app/components/GenericDataTable/FilterTemplateManagement/save";
import {
  CreateFilterTemplateRequest,
  IFilterTemplate,
  SavedFilterTemplate,
} from "@app/types/filterTemplateTypes";

import styles from "./index.module.less";

const { Option } = Select;
const elementName = "FilterChangeTrigger";

export default (props: any) => {
  const [sortedSavedFilterList, setSortedFilterList] = useState([] as any[]);
  const [showEditFiltersModal, setShowEditFiltersModal] = useState(false);
  const [showAddNewFilterModal, setShowAddNewFilterModal] = useState(false);
  const [showFiltersButton, setShowFiltersButton] = useState(true);
  const [newFilterName, setNewFilterName] = useState("");
  const [isNameValid, setNameValid] = useState(true);

  const {
    columns,
    showFilterSaveButton,
    savedFilters,
    filter_template_saved,
    gridFilters,
  } = useSelector((store: RootState) => store.grid);
  const dispatch = useDispatch();

  useEffect(() => {
    if (savedFilters.length === 0) {
      setShowEditFiltersModal(false);
    }
    const sortedList = (savedFilters as any[]).sort(
      (filter1: any, filter2: any) => {
        return filter1.name.localeCompare(filter2.name);
      }
    );
    setSortedFilterList(sortedList);
  }, [savedFilters]);

  useEffect(() => {
    dispatch(getSavedFilters(props.tableKey, props.groupedValue));
  }, []);

  const onClickSavedFilter = (value: any) => {
    const savedFilterIndex = savedFilters.findIndex(
      (filter: SavedFilterTemplate) => filter.id === value
    );
    const savedFilter: SavedFilterTemplate = savedFilters[savedFilterIndex];
    if (savedFilter) {
      const gridFilterTemplates = getGridFilterFromIFilterTemplate(
        savedFilter.content,
        columns
      );
      const savedFilterColumns = Object.getOwnPropertyNames(
        savedFilter.content
      );
      const optionalFilterColumns = columns
        .filter(
          (column: any) =>
            !column.default && savedFilterColumns.includes(column.key)
        )
        .map((column: any) => column.key);
      const filterColumns = columns.map((column: any) => {
        if (optionalFilterColumns.includes(column.key)) {
          return { ...column, selected: true };
        } else {
          return column;
        }
      });

      dispatch(
        applyASavedFilter({
          gridFilterTemplates,
          filterColumns,
          selected: optionalFilterColumns.length > 0,
          selectedElement: {
            name: elementName,
            checked: true,
            multiple: true,
          },
        })
      );
      dispatch(onSelectedSavedFilter(savedFilter));
      setShowFiltersButton(true);
    }
  };

  const onSearchSavedFilters = (input: any, option: any) => {
    if (typeof option?.children === "string") {
      return option.children.toLowerCase().startsWith(input.toLowerCase());
    } else {
      return false;
    }
  };

  const handleAddNewFilterCancel = () => {
    setNewFilterName("");
    setShowAddNewFilterModal(false);
    setNameValid(true);
  };

  const onSaveFilterTemplate = () => {
    dispatch(updateFilterTemplateSaved(true));

    const createFilterTemplateRequest: CreateFilterTemplateRequest = {
      name: newFilterName.trim(),
      content: {},
    };
    gridFilters.forEach((filter: GenericFilter) => {
      const field = filter["key"];
      if (filter?.isArray && 'value' in filter) {
        if ((createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) === undefined) {
          (createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) = [];
        }
        (createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]) = [...(createFilterTemplateRequest.content[field as keyof IFilterTemplate] as any[]), filter["value"],];
      } else {
        createFilterTemplateRequest.content[field as keyof IFilterTemplate] = filter["value"];
      }
    });

    dispatch(
      saveFilterTemplate(
        createFilterTemplateRequest,
        props.tableKey,
        props.groupedValue
      )
    );
    setShowAddNewFilterModal(false);
    setNewFilterName("");
  };

  const onChangeSaveNewFilterName = (name: string) => {
    if (
      savedFilters.findIndex((filter: SavedFilterTemplate) => {
        return filter.name.toLowerCase() === name.toLowerCase().trim();
      }) !== -1
    ) {
      setNameValid(false);
      setNewFilterName(name);
    } else {
      setNameValid(true);
      setNewFilterName(name);
    }
  };

  return (
    <>
      {/* Manage existing Filters Modal */}
      <Modal
        visible={showEditFiltersModal}
        title={"Manage Filter"}
        size={"small"}
        onCancel={() => setShowEditFiltersModal(false)}
        footer={[
          <Button
            key="back"
            type="primary"
            onClick={() => setShowEditFiltersModal(false)}
          >
            cancel
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <FilterTemplateManagementEdit
            tableKey={props.tableKey}
            groupedValue={props.groupedValue}
          />
        </div>
      </Modal>

      {/* Add New Filter Modal */}
      <Modal
        visible={showAddNewFilterModal}
        title={"Save as a New Filter"}
        size={"small"}
        onCancel={handleAddNewFilterCancel}
        footer={[
          <Button key="back" onClick={handleAddNewFilterCancel} type="default">
            cancel
          </Button>,
          <Button
            disabled={newFilterName.trim().length < 1 || !isNameValid}
            key="submit"
            type="primary"
            onClick={() => onSaveFilterTemplate()}
          >
            Save
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <FilterTemplateManagementSave
            isNameValid={isNameValid}
            newFilterName={newFilterName}
            onFilterNameChangeHandler={(name: string) =>
              onChangeSaveNewFilterName(name)
            }
          />
        </div>
      </Modal>

      <div className={styles.yjFilterManagementModule}>
        <div className={styles.yjFileAreaFilterDropdownWrapper}>
          {showFiltersButton ? (
            <Button
              icon={<FilterOutlined />}
              className={styles.yjFiltersButton}
              onClick={() => setShowFiltersButton(false)}
            >
              Filters <DownOutlined />
            </Button>
          ) : (
            <Select
              showSearch
              style={{ width: 200 }}
              placeholder="Select a filter"
              optionFilterProp="children"
              autoFocus={true}
              defaultOpen={true}
              onBlur={() => setShowFiltersButton(true)}
              showArrow={false}
              onSelect={onClickSavedFilter}
              notFoundContent={`No Results Found`}
              filterOption={onSearchSavedFilters}
            >
              {sortedSavedFilterList.length > 0 && (
                <Option disabled={true} key={-1} value={"Manage Filters"}>
                  <Button
                    className={"yjDropdownManageFilters"}
                    type="primary"
                    onClick={() => setShowEditFiltersModal(true)}
                  >
                    Manage Filters
                  </Button>
                </Option>
              )}

              {sortedSavedFilterList &&
                sortedSavedFilterList.map(
                  (savedFilter: SavedFilterTemplate) => {
                    return (
                      <Option key={savedFilter.id} value={savedFilter.id}>
                        {savedFilter.name}
                      </Option>
                    );
                  }
                )}
            </Select>
          )}
        </div>

        <div className={styles.yjFileAreaFilterActionButtonWrapper}>
          {showFilterSaveButton && (
            <Button
              disabled={filter_template_saved}
              type="primary"
              className="yjSaveFilterButton"
              onClick={() => setShowAddNewFilterModal(true)}
            >
              Save as a new filter
            </Button>
          )}
        </div>
      </div>
    </>
  );
};
