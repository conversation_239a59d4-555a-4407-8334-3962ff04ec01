import { Card, Skeleton } from "antd";
import Meta from "antd/lib/card/Meta";
import React from "react";

export interface IPendingRequestCard {
  response?: any;
}

export default ({ response }: IPendingRequestCard) => {
  return !response ? (
    <Skeleton active />
  ) : (
    <Card className="yjPendingFilesCarddWrapper">
      <Meta
        title={
          response && response.filesCount
            ? response.filesCount === 1
              ? "1 File"
              : `${response.filesCount} Files`
            : ""
        }
        description={
          response && response.requestCount
            ? response.requestCount === 1
              ? "IN 1 REQUEST"
              : `IN ${response.requestCount} REQUESTS`
            : ""
        }
      />
    </Card>
  );
};
