import { Col, Form, Input, Radio, Row, Switch, Button, Space } from 'antd';
import React, { useState, useEffect } from 'react';
import { required } from '@app/components/forms/validators';
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import config from "@app/utils/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from '@app/utils/logger';
import { exportRetentions } from "@app/api/retentionService";
import download from "downloadjs";
import { errorNotification } from "@app/utils/antNotifications";

const LIMIT = 10;

export type RetentionSearchPropType = {
    onSubmit: (values: any) => void;
}
export type RetentionClient = {
    id: string;
    displayText: string;
    clientRef: string;
};

const RetentionSearch = (props: RetentionSearchPropType) => {
    const [form] = Form.useForm();
    const [clientId, setClientId] = useState();
    const [isToBeDeleted, setIsToBeDeleted] = useState(false); // Track the state of the switch
    const [clients, setClients] = useState<RetentionClient[]>([]);
    const [searchByField, setSearchByField] = useState("clientRef");
    const [clientSelectKey, setClientSelectKey] = useState(0);

    const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
        const transformFilters: any = {};
        if (searchValue) {
            transformFilters.search = searchValue;
        }

        const options = {
            limit: LIMIT,
            offset: page - 1,
            field: searchByField, 
            ...transformFilters,
        }

        return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].clients, options)
            .then((res: any) => {
                logger.info('ClientDropdown', 'getPaginatedRecords', res.data);
                if (res.data) {
                    //res.data.records = res.data.records.map((rec: any) => { return { ...rec, id: rec.siteId } });
                    setClients(res.data.records);
                    return res.data;
                } else {
                    logger.error('ClientDropdown', 'getPaginatedRecords', res.error);
                    return []
                }
            })
            .catch((error: any) => {
                logger.error('ClientDropdown', 'getPaginatedRecords', error);
                return [];
            });
    };

    function clearForm() {
        form.resetFields();
        setClientId(undefined);
        setIsToBeDeleted(false); // Reset the state of the switch
        setSearchByField("clientRef");
        setClientSelectKey(prevKey => prevKey + 1);
        form.setFieldsValue({ expiringDays: isToBeDeleted ? undefined : (form.getFieldValue('expiringDays') || 0) });
    }



    const getFileName = (responseFile: any): string => {
        const fileDepositon = responseFile.headers["content-disposition"] as string;
        return fileDepositon.split(";")[1].split("=")[1].trim().replace(/"/g, "");
    };

    const downloadFiles = (downnloadedData: any) => {
        const blob = new Blob([downnloadedData.data]);
        download(blob, getFileName(downnloadedData));
    };

    async function exportRecords() {
        const formValues = form.getFieldsValue();
        logger.debug('RetentionSearch', 'exportRecords', formValues);
        formValues?.toBeDeleted && delete formValues['expiringDays'];
        const searchQuery = { ...formValues, siteId: formValues.clientID };

        exportRetentions(searchQuery)
            .then((response: any) => {
                if (response.status === 204) {
                    errorNotification([''], 'No records to export. Please adjust your search criteria');
                } else {
                    const contentDisposition = response.headers['content-disposition'];
                    logger.info('RetentionSearch', 'exportRetentions-Content-Disposition:', contentDisposition);
                    downloadFiles(response);
                }
            })
            .catch((error) => {
                errorNotification([''], 'Something when wong');
            });

    }

    useEffect(() => {
        // Update the state whenever the 'toBeDeleted' switch changes
        logger.debug('RetentionSearch', 'useEffect - isToBeDeleted changed', {
            isToBeDeleted,
            formValues: form.getFieldsValue(),
            expiringDays: isToBeDeleted ? undefined : (form.getFieldValue('expiringDays') || 0),
        });
        form.setFieldsValue({ expiringDays: isToBeDeleted ? undefined : (form.getFieldValue('expiringDays') || 0) });
    }, [isToBeDeleted]);

    return (
        <>
            <Form
                key="createClientForm"
                layout="vertical"
                onFinish={props.onSubmit}
                form={form}
            >
                <Row gutter={24}>
                    <Col span={12}>
                        <Form.Item label={"SEARCH BY"}>
                            <Radio.Group
                                value={searchByField}
                                onChange={(e) => {
                                    setSearchByField(e.target.value);
                                    setClientSelectKey(prevKey => prevKey + 1); // Force re-render of InfinitySelect
                                }}
                            >
                                <Space>
                                    <Radio value="clientRef">Client Ref</Radio>
                                    <Radio value="name">Client Name</Radio>
                                </Space>
                            </Radio.Group>
                        </Form.Item>
                    </Col>
                </Row>
                <Row gutter={24}>
                    <Col span={12}>
                        <Form.Item label={'Client'} name="clientID" >
                            <InfinitySelect
                                key={`client-${clientSelectKey}`}
                                getPaginatedRecords={(page, method, searchValue) => getPaginatedRecords(page, method, searchValue)}
                                formatValue={(value) => {
                                    return `${value.displayText}`;
                                }}
                                notFoundContent="No Clients Available"
                                notLoadContent="Failed to load values in clients dropdown"
                                onChange={(e) => {
                                    setClientId(e);
                                    const selectedClient = clients?.find((client) => client.id === e);
                                    form?.setFieldsValue({ clientRef: selectedClient?.clientRef });
                                }}
                                placeholder="Select an Client"
                                waitCharCount={3}
                            />
                        </Form.Item>
                    </Col>
                    {/*<Col span={12}>*/}
                    {/*    <Form.Item label={'Client Ref'} name="clientRef" rules={[required]} >*/}
                    {/*        <Input maxLength={100} autoComplete="off" disabled={!!clientId} />*/}
                    {/*    </Form.Item>*/}
                    {/*</Col>*/}
                    <Col span={12}>
                        <Form.Item label={'Expiring Days'} name="expiringDays">
                            <Radio.Group optionType="button" buttonStyle="solid" defaultValue={0} disabled={isToBeDeleted}>
                                <Radio.Button value={0}>Expired</Radio.Button>
                                <Radio.Button value={30}>30</Radio.Button>
                                <Radio.Button value={60}>60</Radio.Button>
                                <Radio.Button value={90}>90</Radio.Button>
                                <Radio.Button value={120}>120</Radio.Button>
                                <Radio.Button value={365}>Year</Radio.Button>
                            </Radio.Group>
                        </Form.Item>
                    </Col>
                    <Col span={12}>
                        <Form.Item valuePropName="checked" label={'To be deleted'} name="toBeDeleted" initialValue={false}>
                            <Switch style={{ margin: 0 }} onChange={(checked) => setIsToBeDeleted(checked)} />
                        </Form.Item>
                    </Col>
                </Row>
                <Row>
                    <Col span={24}>
                        <Row justify={'end'}>
                            <Button type="default" onClick={() => clearForm()} htmlType="button">
                                Clear Filter
                            </Button>
                            <Button type="primary" onClick={() => exportRecords()} htmlType="button" disabled={!clientId}>
                                Export
                            </Button>
                            <Button type="primary" htmlType="submit">
                                Search
                            </Button>
                        </Row>
                    </Col>
                </Row>
            </Form>
        </>
    );
};

export default RetentionSearch;
