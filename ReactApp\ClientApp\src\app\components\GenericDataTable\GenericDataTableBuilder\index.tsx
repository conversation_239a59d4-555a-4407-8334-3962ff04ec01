import React, { useContext, useEffect, useState } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import { HolderOutlined } from '@ant-design/icons';

import { Columns, getColumns } from '../../../api/genericDataTable';
import TableHeader from '../TableHeader';
import AntTableContainer from '../AntTableContainer';
import TextFilter from '../filters/TextFilter';
import { DataTableContext } from '../DataTableContext';
import { Sorter } from '../util';

import DateRangeFilter from '../filters/DateRangeFilter';
import { setTableLoading, updateColumns, updateFilterDropDownValues } from '../DataTableContext/actions';
import logger from '../../../utils/logger';
import { findByKey } from '../DataTableContext/persistColumns';
import { findReOrderedColumnByKey, setReOrderedColumns } from '../DataTableContext/persistReorderedColumns';
import SearchableMultiselect from '../filters/SearchableMultiselect';
import { updateGridColumns, updatePreAppliedFilters } from '@app/redux/actions/gridsActions';
import { RootState } from '@app/redux/reducers/state';
import HTTPResponse from '@app/utils/http/interfaces/HttpResponse';
import { DISPATCH_ACTION_FILTERS } from '../hooks/useFilter';
import { GenericFilter } from '../types';

export type DataTablePropType = {
  endpoint?: string;
  dataPromise?: (state: any, transformFilters: any, queryParams: any) => Promise<HTTPResponse<any>>;
  columnPromise?: Promise<HTTPResponse<any> | Columns[]>;
  searchPromise?: (props: any, value: string, callback: any) => void;
  onGridStateChange?: (state: any) => void;
  tableKey: string;
  customRender?: any;
  rowKey?: string;
  onRow?: any;
  onFiltersChange?: any;
  rowSelection?: any;
  filterEndpoint?: string;
  scrollColumnCounter?: number;
  updatedGrid?: boolean;
  hideHeaderPanel?: boolean;
  columnQueryParameters?: any;
  searchQueryParameters?: any;
  searchFieldParameters?: any;
  selectedRecordCount?: number;
  filterCloudPadding?: any;
  lowResolutionWidth?: string;
  highResolutionWidth?: string;
  headerActionPanel?: JSX.Element;
  sorted?: Sorter;
  fixedColumns?: (string | undefined)[];
  noRecordsAvilableMessage?: string;
  noResultAvilableMessage?: string;
  onErrorLoading?: (error: any) => void;
  preAppliedFiltes?: GenericFilter[];
  groupedValue?: string;
  hasFilterManagement?: boolean;
  isDraggable?: boolean;
  showRefreshButton?: boolean;
  updateGrid?: () => void;
  showPublishedIcon?: boolean;
  filterByIds?: string;
  hidePagination?: boolean;
  rowClassName?: (record: any, _: number) => string;
};

export default (props: DataTablePropType) => {
  const { state, dispatch } = useContext(DataTableContext);
  const { selectSavedFilter } = useSelector((store: RootState) => store.grid);


  const [draggedcolumnNdIndex, setDraggedcolumnNdIndex] = useState<{ index: number; column: any } | undefined>(undefined);

  const [customizedColumn, setCustomizedColumn] = useState<Array<any>>([]);
  const [scroll, setScroll] = useState<number>(0);
  const [scrollInterval, setScrollInterval] = useState<any>(null);

  const dispatchGrid = useDispatch();
  const columns = state.columns;
  const scrollSpeed = 10;

  useEffect(() => {
    if (scroll == 0 && scrollInterval) {
      clearInterval(scrollInterval);
    } else if (scroll != 0) {
      if (scrollInterval) clearInterval(scrollInterval);
      setScrollInterval(
        setInterval(() => {
          if (scroll > 0) {
            document.getElementsByClassName('ant-table-body')[0].scrollBy(-scrollSpeed, 0);
          }
          if (scroll < 0) {
            document.getElementsByClassName('ant-table-body')[0].scrollBy(scrollSpeed, 0);
          }
        }, 50)
      );
    }
  }, [scroll]);

  useEffect(() => {
    if (props.isDraggable && props.tableKey) {
      setCustomizedColumn(findReOrderedColumnByKey(props.tableKey));
    }
  }, [props.isDraggable, props.tableKey]);

  const dispatchColumnData = (data: HTTPResponse<Columns[]> | Columns[], canceled: boolean) => {
    if (!canceled) {
      /**
       * defaultSortOrder => to make a column sorted by default.
       * sortDirections: ['descend', 'ascend','descend]
       */
      const columns = data instanceof Array ? data : data.data;
      const mapData = columns.map((column) => {
        const getPersistedColumnData = findByKey(props.tableKey, column.key);
        return {
          title: column.displayValue,
          key: column.key,
          data_type: column.dataType,
          sorter: column.sortable,
          filter: !!column.filterType,
          filter_type: column.filterType,
          filter_data: column.filterData,
          default: column.defaultVisible,
          selected: getPersistedColumnData ? getPersistedColumnData.selected : column.defaultVisible,
          defaultSortOrder: column.key === props.sorted?.value ? props.sorted?.order : null,
          sortDirections: column.key === props.sorted?.value ? ['ascend', 'descend', 'ascend'] : ['ascend', 'descend', null],
          fixed: props.fixedColumns && props.fixedColumns.find((key) => key === column.key) ? 'left' : '',
        };
      });

      const columnsWithMultiSelectFilter = mapData.filter((column) => column.filter_type === 'multiSelect');
      columnsWithMultiSelectFilter.forEach((column) => {
        dispatch(updateFilterDropDownValues(column.key, column.filter_data));
      });
      dispatch(updateColumns(mapData, props.tableKey));
      dispatchGrid(updateGridColumns({ columns: mapData, tableKey: props.tableKey }));
    }
  };

  useEffect(() => {
    if (props.preAppliedFiltes && props.preAppliedFiltes.length > 0) {
      const fiters = [] as any;
      props.preAppliedFiltes?.forEach((filter) => {
        if (!state.filters.find((value) => value.key === filter.key)) {
          fiters.push(filter);
        }
      });

      if (!!fiters.length) {
        dispatch({
          type: DISPATCH_ACTION_FILTERS,
          payload: fiters,
        });
        dispatchGrid(updatePreAppliedFilters([]));
      }
    }
  }, [props.preAppliedFiltes, state.filters]);

  useEffect(() => {
    let canceled = false;
    dispatch(setTableLoading(true));
    if (!props.columnPromise) {
      getColumns(props.endpoint!, props.tableKey, props.columnQueryParameters)
        .then((data) => {
          dispatchColumnData(data, canceled);
        })
        .catch((e) => {
          logger.error('GenericDataTable', 'GenericDataTableBuilder', e);
          props.onErrorLoading && props.onErrorLoading(e);
        });
    } else {
      props.columnPromise.then((data) => {
        dispatchColumnData(data, canceled);
      }).catch((e) => {
        logger.error('GenericDataTable', 'GenericDataTableBuilder', e);
        props.onErrorLoading && props.onErrorLoading(e);
      });
    }

    return () => {
      canceled = true;
    };
  }, [props.endpoint, props.tableKey, props.columnPromise]);

  const filterEndpoint = props.filterEndpoint ? props.filterEndpoint : props.endpoint;

  const filterSwitch = (data: any) => {
    switch (data.filter_type) {
      case 'search':
      case 'TEXT':
        return <TextFilter {...props} searchFieldParameters={props.searchFieldParameters} data={data} endpoint={filterEndpoint} />;
      case 'range':
      case 'DATE_RANGE':
        return <DateRangeFilter data={data} />;
      case 'multiSelect':
      case 'AUTOCOMP_MULTI_SELECT':
        return <SearchableMultiselect data={data} endpoint={filterEndpoint}
          searchFieldParameters={props.searchFieldParameters} />;
      default:
        return '';
    }
  };

  const handleDefaultRender = (item: any) => {
    try {
      if (!item && item !== 0 && item !== '0') {
        return '-';
      }
      if (Array.isArray(item)) {
        return item.map((i) => i.name).join(', ');
      }
      if (typeof item === 'object') {
        return item.name;
      }
      return JSON.stringify(item);
    } catch (e) {
      return JSON.stringify(item);
    }
  };

  const handleDrag = (e: any, index: number) => {
    setDraggedcolumnNdIndex({ index: index, column: e });
  };

  const handleDrop = (e: any, index: number) => {
    if (draggedcolumnNdIndex) {
      const customizedColumns = !!customizedColumn.length ? [...customizedColumn] : [...columns];

      const trueDraggedColumnIndex = customizedColumns.findIndex((col) => col.key === draggedcolumnNdIndex.column.key);
      const trueDroppedColumnIndex = customizedColumns.findIndex((col) => col.key === e.key);

      if (index < draggedcolumnNdIndex?.index) {
        customizedColumns.splice(trueDroppedColumnIndex, 0, draggedcolumnNdIndex?.column);
        customizedColumns.splice(trueDraggedColumnIndex + 1, 1);
      } else {
        customizedColumns.splice(trueDroppedColumnIndex + 1, 0, draggedcolumnNdIndex?.column);
        customizedColumns.splice(trueDraggedColumnIndex, 1);
      }

      setCustomizedColumn(customizedColumns);
      props.tableKey && setReOrderedColumns(props.tableKey, customizedColumns);
    }
  };

  const isColumndraggable = (columnKey: string) => {
    return props.isDraggable && props.fixedColumns && !props.fixedColumns.find((key) => key === columnKey);
  };

  const removeHoverClasses = () => {
    const elements = document.querySelectorAll('.yjDraggableColumn');
    elements.forEach((e) => {
      if (e.classList.contains('yjDraggableColumnHover')) {
        e.classList.remove('yjDraggableColumnHover', 'yjDraggableColumnHoverLeft', 'yjDraggableColumnHoverRight');
      }
    });
  };

  const onDragOver = (ev: any, i: any, index: number) => {
    ev.preventDefault();
    if (isColumndraggable(i.key)) {
      removeHoverClasses();
      const element = (ev.target as HTMLElement).closest('.yjDraggableColumn') as HTMLElement;
      element.classList.add('yjDraggableColumnHover');
      if (index < (draggedcolumnNdIndex?.index ?? 0)) {
        element.classList.add('yjDraggableColumnHoverLeft');
      } else if (index > (draggedcolumnNdIndex?.index ?? 0)) {
        element.classList.add('yjDraggableColumnHoverRight');
      }
      const table = document.getElementById(props.tableKey);
      if (table) {
        let scrollbody = table.querySelector('.ant-table-body');
        let tableHeader = table.querySelector('.ant-table-header');
        let leftFixedCols = tableHeader?.querySelectorAll('.ant-table-cell-fix-left');
        let rightFixedCols = tableHeader?.querySelectorAll('.ant-table-cell-fix-right');
        if (scrollbody) {
          let scrollRect = scrollbody.getBoundingClientRect();
          let start = scrollRect.left;
          let end = scrollRect.right;
          let leftFixedSpace = 0;
          let rightFixedSpace = 0;
          let scrollOffset = 100;
          if (leftFixedCols) {
            leftFixedCols.forEach((col) => {
              leftFixedSpace += col.getBoundingClientRect().width;
            });
          }
          if (rightFixedCols) {
            rightFixedCols.forEach((col) => {
              rightFixedSpace += col.getBoundingClientRect().width;
            });
          }
          let scrollPointA = start + leftFixedSpace + scrollOffset;
          let sccrollPointB = end - rightFixedSpace - scrollOffset;
          let mouseX = ev.clientX;
          if (mouseX < scrollPointA) {
            setScroll(1);
          } else if (mouseX > sccrollPointB) {
            setScroll(-1);
          } else setScroll(0);
        }
      }
    }
  };

  const buildTableColumns = ((columnsInput: any[]) => {
    const hasColumnsWithFilters = columns.every((column) => column.filter_type === 'none');

    const selectedColumns = columns.filter((col) => col.selected).map((col) => col.key);

    var finalizedColums;
    if (customizedColumn?.length > 0) {
      finalizedColums = customizedColumn.map((col) => {
        return {
          ...col,
          selected: selectedColumns.includes(col.key) ? true : false,
        };
      });
    } else {
      finalizedColums = columns;
    }

    const columnsWithFilters = finalizedColums
      .filter((i) => i.selected)
      .map((i, index) => ({
        ...i,
        className: isColumndraggable(i.key) ? 'yjDraggableColumn' : undefined,
        render: (item: any, record: any) => {
          return typeof item === 'string' ? item : handleDefaultRender(item);
        },
        dataIndex: i.key,
        title: (
          <div
            key={i.key}
            className={'yjGenericGridColumnTitle'}
            style={{
              paddingBottom: !hasColumnsWithFilters && i.filter_type === 'none' ? '36px' : undefined,
            }}
          >
            <div
              style={{
                fontSize: '14px',
                whiteSpace: 'nowrap',
                lineHeight: '32px',
              }}
              draggable={isColumndraggable(i.key)}
              onDragOver={(ev) => onDragOver(ev, i, index)}
              onDragEnd={() => {
                removeHoverClasses();
                setScroll(0);
              }}
              onDragStart={() => handleDrag(i, index)}
              onDrop={() => isColumndraggable(i.key) && handleDrop(i, index)}
            >
              {isColumndraggable(i.key) && (
                <HolderOutlined
                  style={{
                    paddingRight: '5px',
                  }}
                />
              )}
              {i.title}
            </div>
            <div
              style={{
                display: i.filter_type === 'none' ? 'none' : 'inline-block',
                width: '100%',
              }}
            >
              {filterSwitch(i)}
            </div>
          </div>
        ),
      }));

    if (props.customRender.hasOwnProperty('action')) {
      const actionCustomConfig = props.customRender['action'];
      const { width: actionWidth = 120 } = typeof actionCustomConfig === 'object' ? actionCustomConfig : {};
      columnsWithFilters.push({
        title: <span> Action </span>,
        key: 'action',
        default: true,
        selected: true,
        sorter: false,
        fixed: 'right',
        width: actionWidth,
        dataIndex: 'action',
        className: 'yjActionColumn',
      });
    }

    if (props.customRender != null) {
      return columnsWithFilters.map((i) => {
        const customConfig = props.customRender[i.dataIndex];
        const { render: customRender = i.render, width: customWidth = i.width } = typeof customConfig === 'function' ? { render: customConfig } : customConfig || {};

        return {
          ...i,
          render: customRender,
          width: customWidth,
        };
      });
    }

    return columnsWithFilters;
  })(columns);

  return (
    <div id={props.tableKey} className={'yjMainContainer'}>
      <div>
        <TableHeader
          filterCloudPadding={props.filterCloudPadding}
          hideHeaderPanel={props.hideHeaderPanel}
          tableKey={props.tableKey}
          headerActionPanel={props.headerActionPanel}
          groupedValue={props.groupedValue}
          hasFilterManagement={props.hasFilterManagement}
          hasRefreshButton={props.showRefreshButton}
          updateGrid={props.updateGrid}
        />
      </div>
      <AntTableContainer {...props} selectedSavedFilter={selectSavedFilter} columns={buildTableColumns}
        filters={state.filters} />
    </div>
  );
};
