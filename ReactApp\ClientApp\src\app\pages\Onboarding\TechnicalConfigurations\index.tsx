import React, { Fragment, useState } from "react";
import { withRouter } from "react-router-dom";
import { Button } from "antd";

import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import Modal from "../../../components/Modal";
import TechnicalConfigurations from "../../../components/forms/TechnicalConfigurations";

const Page = (props: any) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const handleCancel = (e: any) => {
    setShowEditModal(false);
  };

  return (
    <Fragment>
      <Modal
        visible={showEditModal}
        title={"Technical Configuration Details"}
        onCancel={handleCancel}
        footer={[
          <Button key="back" type="default" onClick={handleCancel}>
            cancel
          </Button>,
          <Button key="submit" type="primary" onClick={handleCancel}>
            Manage
          </Button>,
        ]}
      >
        <TechnicalConfigurations />
      </Modal>
      <PageTitle title={props.title} />
      <PageContent></PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
