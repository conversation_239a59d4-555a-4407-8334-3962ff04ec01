import config from "@app/utils/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import GenericDataTable from "@app/components/GenericDataTable";
import React, { forwardRef, useImperativeHandle, useState } from "react";
import { getAutocompleteOptions, getColumns, getRecords } from "@app/api/genericDataTable";
import styles from "@app/features/DashboardV3/index.module.less";
import { Button } from "antd";
import { EditOutlined } from "@ant-design/icons/lib";
import debounce from "lodash/debounce";
import { Sorter } from "@app/components/GenericDataTable/util";
import logger from "@app/utils/logger";
import { FormattedDateTime } from "@app/components/FormattedDateTime";
import ShowAllSwitch from "@app/components/Switch/ShowAll";

const SORTER: Sorter = {
  value: "name",
  order: "ascend",
};

const TemplateGrid = forwardRef(({ onEdit }: any, ref) => {
  const [tableKey, setTableKey] = useState('templateManagement');
  const [showAllTemplates, setShowAllTemplates] = useState<boolean>(false);

  useImperativeHandle(ref, () => ({
    refresh: () => {
      /**
       * TODO Refresh should address with forwardRef and useImperativeHandle method inside GenericDataTable as well but
       * GenericDataTable need some refactor
       * As soon as got time we need to refactor this component
       */
      setTableKey(`templateManagement`)
    }
  }));

  const handleEdit = (record: any) => {
    onEdit(record);
  };

  const fetchTemplates = async (state: any, transformFilters: any, queryParams: any) => {
    if (showAllTemplates) {
      return fetchData(state, transformFilters, queryParams);
    }

    const activeFilter: Array<any> = state.columns.find((column: any) => column.key === 'status')?.filter_data?.filter((item: any) => item.name !== 'Inactive');
    if (!activeFilter) {
      return fetchData(state, transformFilters, queryParams);
    }

    const filters = {
      ...transformFilters,
      status: transformFilters.status ? transformFilters.status : activeFilter.map(f => f.value),
    }
    return fetchData(state, filters, queryParams);
  };

  const fetchColumns = async () => {
    const response = await getColumns(config.api[OperationalServiceTypes.FileManagementService].binderTemplates, 'TemplateGridx');

    return showAllTemplates ? response : response.data.map((column: any) => {
      if (column.key === 'status') {
        return {
          ...column,
          filterData: column.filterData.filter((item: any) => item.name !== 'Inactive'),
        };
      }

      return column;
    });
  };

  let fetchData = (state: any, transformFilters: any, queryParams: any) => {
    logger.debug("TemplateGrid", "fetchData", { state, transformFilters, queryParams });
    return getRecords(
      config.api[OperationalServiceTypes.FileManagementService].binderTemplates,
      {
        pagination: {
          current: state.pagination.current,
          pageSize: state.pagination.pageSize,
        },
        sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
        filters: transformFilters,
        columns: state.columns.filter((i: any) => i.default === false && i.selected === true).map((i: any) => i.key),
      }, queryParams
    )
  };

  const renderSiteGridColumns = () => {
    return {
      hide: (value: string) => {
        return value ? 'Yes' : 'No';
      },
      created: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      modified: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      action: (text: any, record: any) => {
        return (
          <div className={"yjActionIconWrapper"}>
            <div className={`${styles.yjActionIconWrapper} `}>
              {/* <Tooltip title="Coming Soon Feature">
                <Button
                  onClick={() => console.log('clone')}
                  icon={<CopyFilled />}
                />
              </Tooltip> */}
            </div>
            <div className={`${styles.yjActionIconWrapper} `}>
              <Button onClick={() => handleEdit(record)} icon={<EditOutlined />} />
            </div>
            {/*<div className={`${styles.yjActionIconWrapper} `}>*/}
            {/*  <Tooltip title="Coming Soon Feature">*/}
            {/*    <Button*/}
            {/*      onClick={() => console.log('delete')}*/}
            {/*      icon={<DeleteOutlined />}*/}
            {/*    />*/}
            {/*  </Tooltip>*/}
            {/*</div>*/}
          </div>
        );
      },
    };
  };

  // Debounced API call
  const debouncedApiCall = debounce(
    (props: any, value: string, callback: (data: any) => void) => {
      logger.debug('Template', 'debouncedApiCall', { props, value });

      getAutocompleteOptions(
        config.api[OperationalServiceTypes.FileManagementService].binderTemplates,
        props.data.key,
        value,
        props.searchFieldParameters
      )
        .then((data: any) => {
          callback(data.data);
        })
        .catch(() => {
          callback([]);
        });
    },
    config.inputDebounceInterval
  );

  // Wrapper function to return a Promise
  const searchPromiseWrapper = (props: any, value: string, callback: any) => {
    debouncedApiCall(props, value, (data: any) => {
      callback(data)
    });
  };

  const getRowClassName = (record: any, _: number) => {
    return record.hide ? ' table-row-gray' : '';
  }

  return (
    <GenericDataTable
      searchPromise={searchPromiseWrapper}
      dataPromise={fetchTemplates}
      columnPromise={fetchColumns()}
      rowKey={"siteId"}
      scrollColumnCounter={7}
      tableKey={tableKey}
      sorted={SORTER}
      fixedColumns={["name"]}
      isDraggable={true}
      noRecordsAvilableMessage={"No Sites Available"}
      hasFilterManagement={true}
      customRender={renderSiteGridColumns()}
      onGridStateChange={console.info}
      rowClassName={getRowClassName}
      headerActionPanel={<ShowAllSwitch checked={showAllTemplates} onChange={setShowAllTemplates} />}

    />);
});

export default TemplateGrid;
