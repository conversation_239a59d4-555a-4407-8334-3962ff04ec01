import { DateRange, IFilterTemplate } from "@app/types/filterTemplateTypes";
import { GenericGridFilterTypes } from "../../types";

const ASSIGNEE = "assignee";
const CREATED_BY = "createdby";
const CREATED = "created";
const EXPIRATION_DATE = "expirationdate";
const MODIFIED = "modified";
const EFFECTIVE_DATE = "effectivedate";
const EXPIRED = "expired";
const STATUS = "status";
const PROJECTS = "projects";
const TAGS = "tags";
const EXPIRATION_STATUS = "expirationstatus";
const FILE_CONDITION = "filecondition";
const TYPE = "type";
const FILE_TYPE = "filetype";
const YEAR = "year";
const HIDE = "hide";

export const getGridFilterFromIFilterTemplate = (
  filter: IFilterTemplate,
  columns: any
) => {
  const filterNames = Object.getOwnPropertyNames(filter);
  const filters: any[] = [];
  debugger
  filterNames?.forEach((filterName) => {
    const value = filter[filterName as keyof IFilterTemplate];
    let displayText: any = "";
    let gridFilterList: any = null;
    if (!filterName) {
      return;
    }
    const filterNameCamelCase = filterName.replace(/\w\S*/g, (txt) => {
      return txt?.charAt(0).toUpperCase() + txt?.substring(1).toLowerCase();
    });
    switch (filterName.trim().toLowerCase()) {
      case ASSIGNEE: {
        const column = columns.find((col: any) => col.key === ASSIGNEE);
        const filterDataList = column.filter_data;
        const displayTextList = filterDataList.filter((filterData: any) => {
          const assigneeIdStringList = value as string[];
          const assigneeIdList = assigneeIdStringList.map((id) => parseInt(id));
          return assigneeIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList.map((assignee: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: assignee.value,
            displayText: assignee.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case CREATED_BY: {
        const column = columns.find((col: any) => col.key === "createdBy");
        const filterDataList = column.filter_data;
        const displayTextList = filterDataList.filter((filterData: any) => {
          const userIdStringList = value as string[];
          const userIdList = userIdStringList.map((id) => parseInt(id));
          return userIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList.map((user: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: user.value,
            displayText: user.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case CREATED:
      case EXPIRATION_DATE:
      case EFFECTIVE_DATE:
      case MODIFIED:
      case EXPIRED: {
        const filterValue: DateRange = value as DateRange;
        displayText = `From ${filterValue.from} To ${filterValue.to}`;
        break;
      }
      case STATUS: {
        const column = columns.find((col: any) => col.key === STATUS);
        const filterDataList = column.filter_data;
        const displayTextList = filterDataList.filter((filterData: any) => {
          const statusIdStringList = value as string[];
          const statusIdList = statusIdStringList.map((id) => parseInt(id));
          return statusIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList.map((status: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: status.value,
            displayText: status.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.RANGE,
          };
        });
        break;
      }
      case PROJECTS: {
        const column = columns.find((col: any) => col.key === PROJECTS);
        const filterDataList = column?.filter_data;
        const displayTextList = filterDataList?.filter((filterData: any) => {
          const projectIdStringList = value as string[];
          const projectIdList = projectIdStringList.map((id) => parseInt(id));
          return projectIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList?.map((project: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: project.value,
            displayText: project.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case TAGS: {
        const column = columns.find((col: any) => col.key === TAGS);
        const filterDataList = column?.filter_data;
        const displayTextList = filterDataList.filter((filterData: any) => {
          const tagIdStringList = value as string[];
          const tagIdList = tagIdStringList.map((id) => parseInt(id));
          return tagIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList.map((tag: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: tag.value,
            displayText: tag.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case EXPIRATION_STATUS: {
        const column = columns.find(
          (col: any) => col.key === "expirationStatus"
        );
        const filterDataList = column?.filter_data;
        const displayTextList = filterDataList.filter((filterData: any) => {
          const expirationStatusIdStringList = value as string[];
          const expirationStatusIdList = expirationStatusIdStringList.map(
            (id) => parseInt(id)
          );
          return expirationStatusIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList.map((expirationStatus: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: expirationStatus.value,
            displayText: expirationStatus.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case FILE_CONDITION: {
        const column = columns.find((col: any) => col.key === "fileCondition");
        const filterDataList = column?.filter_data;
        const displayTextList = filterDataList.filter((filterData: any) => {
          const fileConditionIdStringList = value as string[];
          const fileConditionIdList = fileConditionIdStringList.map((id) =>
            parseInt(id)
          );
          return fileConditionIdList.includes(filterData.value);
        });
        gridFilterList = displayTextList.map((fileCondition: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: fileCondition.value,
            displayText: fileCondition.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case FILE_TYPE:
      case TYPE:
      case YEAR: {
        const filterValueLst: string[] = value as string[];
        gridFilterList = filterValueLst.map((val: any) => {
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: value,
            value: val,
            displayText: val,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.MULTI,
          };
        });
        break;
      }
      case HIDE: {
        const column = columns.find((col: any) => col.key === HIDE);
        const filterDataList = column?.filter_data;

        // Ensure value is treated as an array
        const statusIdStringList = Array.isArray(value) ? value : [value];

        const displayTextList = filterDataList.filter((filterData: any) =>
          statusIdStringList.includes(filterData.value)
        );

        gridFilterList = displayTextList.map((option: any) => {
          console.info('=========================== ', option);
          debugger
          return {
            key: filterName,
            filterName: filterNameCamelCase,
            name: option.value,
            value: option.value,
            displayText: option.name,
            isArray: Array.isArray(value),
            filterType: GenericGridFilterTypes.RANGE,
          };
        });
        break;
      }
      case "legalHold":
        displayText = value ? "Yes" : "No";
        break;
      default:
        displayText = value;
    }
    if (gridFilterList !== null) {
      gridFilterList?.forEach((filterInput: any) => filters.push(filterInput));
    } else {
      const gridFilter = {
        key: filterName,
        filterName: filterNameCamelCase,
        name: value,
        value: value,
        displayText: displayText,
        isArray: Array.isArray(value),
        filterType: "",
      };
      filters.push(gridFilter);
    }
  });
  return filters;
};
