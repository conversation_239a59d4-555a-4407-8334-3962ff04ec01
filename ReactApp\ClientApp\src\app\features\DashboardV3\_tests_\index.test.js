import React from 'react';
import {act, fireEvent, render, screen} from '@testing-library/react';

import '@testing-library/jest-dom';
import DashboardV3 from '../index';

fdescribe('<DashboardV3> page component', () => {
    it('should render and get parent component', () => {
        const { container } = render(<DashboardV3 />);


        const recentFilesGrid = screen.getByText('RECENT FILES');
        const recentSitesGrid = screen.getByText('RECENT SITES');
        expect(recentFilesGrid).toBeInTheDocument()
        expect(recentSitesGrid).toBeInTheDocument()
    });


});
