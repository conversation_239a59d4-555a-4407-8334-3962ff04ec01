export interface IFilterTemplate {
  assignee?: string[];
  id?: string;
  status?: string[];
  title?: string;
  type?: string[];
  year?: string[];
  created?: DateRange;
  createdBy?: string[];
  expirationDate?: DateRange;
  expirationStatus?: FilterExpirationStatus[];
  fileCondition?: FileCondition[];
  modified?: DateRange;
  projects?: string[];
  tags?: string[];
}

export interface DateRange {
  from: string;
  to: string;
}

export enum FilterExpirationStatus {
  PENDING = "Pending",
  EXPIRED = "Expired",
  PERMANENT = "Permanent",
}

export enum FileCondition {
  ACTIVE = "Active",
  INACTIVE = "Inactive",
  NONE = "None",
}

export interface CreateFilterTemplateRequest {
  name: string;
  content: IFilterTemplate;
  gridKey?: string;
  groupedValue?: string;
}

export interface SavedFilterTemplate {
  id: string;
  name: string;
  content: IFilterTemplate;
}
