import React, { Fragment, useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { <PERSON><PERSON>, Modal } from "antd";
import { useDispatch, useSelector } from "react-redux";
import { Beforeunload } from "react-beforeunload";
import { ExclamationCircleOutlined } from "@ant-design/icons";

import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";
import styles from "./index.module.less";
import FunctionalFlowDetailsContainer from "../../../../components/forms/FunctionalFlowManagement/FunctionalFlowDetailsContainer";
import { SaveFunctionalFlow } from "../../../../redux/actions/functionalFlowActions";
import { RootState } from "../../../../redux/reducers/state";
import {
  errorNotification,
  successNotification,
} from "../../../../utils/antNotifications";
import { PromptOnload } from "../../../../utils/confirm/onloadPrompt";

const REDIRECT_TIMEOUT = 200;

const Page = (props: any) => {
  const { id } = useParams();
  const { confirm } = Modal;
  const dispatch = useDispatch();
  const [formAction, setFormAction] = useState(false);
  const {
    errors,
    modules,
    hasErrors,
    savedSuccessfully,
    isEdited,
  } = useSelector((state: RootState) => state.functionalFlow);

  useEffect(() => {
    if (savedSuccessfully) {
      props.history.push(`/onboarding/flow-management`);
      successNotification([""], "Successfully Saved");
    }
  }, [props.history, savedSuccessfully]);

  useEffect(() => {
    if (hasErrors) {
      errorNotification([""], errors[0]);
    }
  }, [errors, hasErrors]);

  const redirectToFunctionalFlowHomePage = () => {
    setFormAction(true);
    setTimeout(() => {
      props.history.push(`/onboarding/flow-management`);
    }, REDIRECT_TIMEOUT);
  };

  const onClickCancelFunctionalFlow = (
    event: React.MouseEvent<HTMLElement, MouseEvent>
  ) => {
    if (isEdited) {
      confirm({
        title: "Discard Changes",
        content: <p>Are you sure you want to discard the changes?</p>,
        icon: <ExclamationCircleOutlined />,
        okText: "Discard",
        onOk() {
          redirectToFunctionalFlowHomePage();
        },
      });
    } else {
      redirectToFunctionalFlowHomePage();
    }
  };

  return (
    <Fragment>
      <PageTitle title={props.title} />
      <PageContent>
        <div className={styles.yjFunctionalMgtModuleWrapper}>
          {/* <div className={styles.yjCollapseModules}>
            {!formAction && isEdited && (
              <PromptOnload isBlocking={true} isSaving={false} />
            )}
            <Beforeunload onBeforeunload={(event) => event.preventDefault()} />
            <FunctionalFlowDetailsContainer licenceId={id} actionType="edit" />
          </div>
          <div className={styles.yjEditButtonWrapper}>
            <span style={{ color: "#0e678e", fontStyle: "italic" }}>
              * This feature is coming soon
            </span>
            <Button type="default" onClick={onClickCancelFunctionalFlow}>
              {" "}
              Cancel
            </Button>
            <Button
              onClick={() => {
                setFormAction(true);
                dispatch(SaveFunctionalFlow(modules, id));
              }}
              type="primary"
            >
              Save
            </Button>
          </div> */}
        </div>
      </PageContent>
    </Fragment>
  );
};
export default Page;
