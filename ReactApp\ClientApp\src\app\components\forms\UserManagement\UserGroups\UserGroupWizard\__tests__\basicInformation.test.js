import React from "react";
import BasicDetaisComponent from "../StepOneBasicUserGroupInformation";
import initTestSuite from "@app/utils/config/TestSuite";
import { shallow, mount } from "enzyme";
import { Form, Button, Select, Modal } from "antd";
import InfinitySelect from "@app/components/InfinitySelect";

describe("<BasicDetaisComponent/>", () => {
  beforeAll(() => {
    initTestSuite();
  });
  it("should render <BasicDetaisComponent/> component", () => {
    const component = shallow(<BasicDetaisComponent formRef={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("should render <BasicDetaisComponent/> component when userGroupFormDetails is null", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} userGroupFormDetails={null} />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should render <BasicDetaisComponent/> component when setContactsContext is null", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.html()).not.toBe(null);
  });

  it(" <UserGroupWizard/> should have a Form", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 5 form input fields", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Form.Item)).toHaveLength(5);
  });
  it("First form item should have the label  User Group Name", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(0).props();
    expect(firstFormItem.label).toEqual("User Group Name");
  });
  it("First form item should have the name ,name", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(0).props();
    expect(firstFormItem.name).toEqual("name");
  });
  it("Second form item should have the label Description", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(1).props();
    expect(firstFormItem.label).toEqual("Description");
  });
  it("Second form item should have the name ,description", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(1).props();
    expect(firstFormItem.name).toEqual("description");
  });
  it("Third form item should have the label Status", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(2).props();
    expect(firstFormItem.label).toEqual("Status");
  });
  it("Third form item should have the name ,status", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(2).props();
    expect(firstFormItem.name).toEqual("status");
  });

  it("Fourth form item should have the label User(s)", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(3).props();
    expect(firstFormItem.label).toEqual("User(s)");
  });
  it("Fourth form item should have the name ,User(s)", () => {
    const component = shallow(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const firstFormItem = component.find(Form.Item).at(3).props();
    expect(firstFormItem.name).toEqual("User(s)");
  });

  it("should have 1 InfinitySelect input Field", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(InfinitySelect).length).toEqual(1);
  });

  it("should have one Button", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Button).length).toEqual(1);
  });
  it("should have 2 Select fields", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Select).length).toEqual(2);
  });

  it("Panel should contain 1 Modal", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Modal).length).toBe(1);
  });

  it("First Modal should have a title called Selected Contact(s)", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Modal).at(0).props().title).toBe("Selected Users");
  });

  it("First Modal visible property should be false by default", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Modal).at(0).props().visible).toBe(false);
  });

  it("First Modal destroyOnClose property should be true by default", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    expect(component.find(Modal).at(0).props().destroyOnClose).toBe(true);
  });

  it("First Modal should have a button as the first element in the  footer ", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const contactModalfotter = component.find(Modal).at(0).props().footer[0];
    expect(contactModalfotter.type.displayName).toBe("Button");
  });

  it("First Modal should have a button labled Close the first element in the footer ", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const contactModalfotter = component.find(Modal).at(0).props().footer[0];
    expect(contactModalfotter.props.children).toBe("CLOSE");
  });

  it("First Modal should have a button type of primary the first element in the footer ", () => {
    const component = mount(
      <BasicDetaisComponent formRef={null} setContactsContext={null} />
    );
    const contactModalfotter = component.find(Modal).at(0).props().footer[0];
    expect(contactModalfotter.props.type).toBe("default");
  });
});
