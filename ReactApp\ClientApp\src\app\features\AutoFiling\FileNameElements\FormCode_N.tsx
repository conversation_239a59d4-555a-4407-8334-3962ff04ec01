import {Form, Row, Select} from 'antd';
import React, { useEffect, useState } from 'react';
import styles from "./index.module.less";

const { Option } = Select;

const FormCode_N = ({index, value }:any) => (<Form.Item label={`Form Code`} name={`${index}-FormCode`} initialValue={value}>
	<Select placeholder="Select Form Code">
		<Option value="I">I - Individual</Option>
		<Option value="C">C - Corporation</Option>
		<Option value="S">S - S-Corporation</Option>
		<Option value="P">P - Partnership</Option>
		<Option value="F">F - Fiduciary</Option>
		<Option value="K">K - Deferred Comp</Option>
		<Option value="X">X - Exempt</Option>
		<Option value="Y">Y - Gift-Estate</Option>
	</Select>
</Form.Item>);
export default FormCode_N;