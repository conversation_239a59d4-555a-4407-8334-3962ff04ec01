// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<Contact/> test suite should render <Contact/> and create the snapshot properly 1`] = `
<div>
  <form
    className="ant-form ant-form-horizontal"
    onReset={[Function]}
    onSubmit={[Function]}
  >
    <div
      className="ant-row"
      style={
        Object {
          "marginLeft": -8,
          "marginRight": -8,
        }
      }
    >
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className="ant-form-item-required"
              htmlFor="firstName"
              title="First Name"
            >
              First Name
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <input
                  autoComplete="off"
                  className="ant-input"
                  id="firstName"
                  maxLength={50}
                  onBlur={[Function]}
                  onChange={[Function]}
                  onFocus={[Function]}
                  onInput={[Function]}
                  onKeyDown={[Function]}
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className="ant-form-item-required"
              htmlFor="lastName"
              title="Last Name"
            >
              Last Name
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <input
                  autoComplete="off"
                  className="ant-input"
                  id="lastName"
                  maxLength={50}
                  onBlur={[Function]}
                  onChange={[Function]}
                  onFocus={[Function]}
                  onInput={[Function]}
                  onKeyDown={[Function]}
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className="ant-form-item-required"
              htmlFor="email"
              title="Email Address"
            >
              Email Address
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <input
                  autoComplete="off"
                  className="ant-input"
                  id="email"
                  maxLength={254}
                  onBlur={[Function]}
                  onChange={[Function]}
                  onFocus={[Function]}
                  onInput={[Function]}
                  onKeyDown={[Function]}
                  placeholder="<EMAIL>"
                  type="text"
                  value=""
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-col ant-col-8"
        style={
          Object {
            "paddingLeft": 8,
            "paddingRight": 8,
          }
        }
      >
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-label"
            style={Object {}}
          >
            <label
              className=""
              htmlFor="contactNumber"
              title="Contact Number"
            >
              Contact Number
            </label>
          </div>
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              >
                <div
                  className=" react-tel-input"
                  onKeyDown={[Function]}
                  style={Object {}}
                >
                  <div
                    className="special-label"
                  >
                    Phone
                  </div>
                  <input
                    className=" form-control"
                    disabled={false}
                    onBlur={[Function]}
                    onChange={[Function]}
                    onClick={[Function]}
                    onCopy={[Function]}
                    onDoubleClick={[Function]}
                    onFocus={[Function]}
                    onKeyDown={[Function]}
                    placeholder="****************"
                    style={Object {}}
                    type="tel"
                    value="+1"
                  />
                  <div
                    className=" flag-dropdown"
                    style={Object {}}
                  >
                    <div
                      aria-haspopup="listbox"
                      className="selected-flag"
                      onClick={[Function]}
                      role="button"
                      tabIndex="0"
                      title="United States: + 1"
                    >
                      <div
                        className="flag us"
                      >
                        <div
                          className="arrow"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-row ant-form-item"
          style={Object {}}
        >
          <div
            className="ant-col ant-col-24 ant-form-item-control"
            style={Object {}}
          >
            <div
              className="ant-form-item-control-input"
            >
              <div
                className="ant-form-item-control-input-content"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </form>
</div>
`;
