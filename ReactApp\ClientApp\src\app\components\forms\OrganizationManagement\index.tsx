import React, { useEffect, useState } from "react";
import styles from "./index.module.less";
import { Button, Form, Input, Tooltip, Divider, Select, Row, Col } from "antd";
import { DeleteOutlined, PlusCircleOutlined } from "@ant-design/icons";
import { FormInstance, Rule } from "antd/lib/form";

import { getCountries } from "../../../api/commonService";
import {
  errorNotification,
  warningNotification,
} from "../../../utils/antNotifications";
import logger from "../../../utils/logger";
import {
  type,
  typeWithPattern,
  required,
} from "../../../components/forms/validators";
import {
  AvoidWhitespace,
  EmailPattern,
  PhoneNoPattern,
  PositiveNumberPattern,
  WebsitePattern,
} from "@app/utils/regex";
import { checkCrossOrganizationUserExsistance } from "@app/api/userService";
import {
  prevenPrecedingSpaces,
  prevenPrecedingSpacesOnPaste,
} from "@app/utils/forms";

const { Option } = Select;

const INPUT_LENGTH_THREE_HUNDRED = 300;
const INPUT_LENGTH_HUNDRED = 100;
const INPUT_LENGTH_FIFTEEN = 15;
const INPUT_LENGTH_TWENTY = 20;
const INPUT_LENGTH_TWO_HUNDRED = 200;
const INPUT_LENGTH_EIGHT = 8;
const INPUT_LENGTH_FIFTY = 50;
const INPUT_TWO_FIVE_FOUR = 254;
const INPUT_LENGTH_TWO_THOUSAND_ONE_HUNDRED = 2100;
const ACTION_VIEW = "view";
const ACTION_SAVE = "save";

export interface IOraganizationManagement {
  handleFormChanged: (event: any) => void;
  formRef: FormInstance;
  onFinish: (event: any) => void;
  action: "view" | "save" | "edit";
  savedEmailList: any;
  organizationId?: string;
}

const exsistOnOtherOrganization = (emailInput: any, organizationId: string) => {
  return new Promise((resolve, reject) => {
    if (EmailPattern.test(emailInput)) {
      checkCrossOrganizationUserExsistance(emailInput, organizationId)
        .then(() => {
          reject("The user already exist");
        })
        .catch(() => {
          checkCrossOrganizationUserExsistance(emailInput, organizationId, true)
            .then(() => {
              reject("The contact already exist");
            })
            .catch(() => {
              resolve(true);
            });
        });
    } else {
      resolve(true);
    }
  });
};

const getEmailList = (primaryEmail: string, additionalEmails: any) => {
  const emailList = [] as any;
  additionalEmails?.forEach((email: any) => {
    emailList.push(email?.emailAddress);
  });
  if (primaryEmail) {
    emailList.push(primaryEmail);
  }
  return emailList;
};

export default (props: IOraganizationManagement) => {
  const layout = {
    labelCol: {
      span: 24,
    },
    wrapperCol: {
      span: 24,
    },
  };

  const [countryList, setCountryList] = useState([]);

  useEffect(() => {
    getCountries()
      .then((response) => {
        setCountryList(response.data);
      })
      .catch((error) => {
        setCountryList([]);
        logger.error(
          "OrganizationManagement",
          "OrganizationManagementForm",
          error
        );
        errorNotification([error]);
      });
  }, []);

  const exsistEmail = (emailList: [], savedEmailList: [], emailInput: any) => {
    return (
      savedEmailList.length > 0
        ? props.action === ACTION_SAVE
          ? emailList.filter((email) => email === emailInput)?.length >= 2
          : emailList.filter((email) => email === emailInput)?.length > 2
        : emailList.filter((email) => email === emailInput)?.length > 1
    )
      ? Promise.reject(
          "The email address has been entered for another primary admin"
        )
      : Promise.resolve();
  };

  const generateAddressBlock = () => {
    return (
      <>
        <Form.List name="additionalAddresses">
          {(fields, { add, remove }) => {
            return (
              <>
                <Row gutter={16} className={styles.yjAddNewFieldWrapper}>
                  <Col span={8}>
                    <Tooltip title="Add New Address">
                      <Button
                        hidden={props.action === ACTION_VIEW}
                        onClick={() => {
                          if (fields.length > 1) {
                            warningNotification(
                              [
                                "You have reached the maximum number of records",
                              ],
                              "Warning"
                            );
                          } else {
                            add();
                          }
                        }}
                        type="primary"
                        className={styles.yjAddNewAddressButton}
                        icon={<PlusCircleOutlined />}
                      >
                        Add New Address{" "}
                      </Button>
                    </Tooltip>
                  </Col>
                </Row>
                {fields.map((field, index) => (
                  <div
                    data-block="addressBlock"
                    className={styles.yjListWrapper}
                    key={index}
                  >
                    <Row className={styles.yjListHeader}>
                      <Col className={styles.yjSubHeading}>
                        Address Details - {index + 2}
                      </Col>
                      <Col>
                        <div>
                          <Tooltip title="Remove Address">
                            <Button
                              hidden={props.action === ACTION_VIEW}
                              className={styles.yjRemoveButton}
                              type="primary"
                              key="back"
                              icon={<DeleteOutlined />}
                              onClick={() => remove(field.name)}
                            />
                          </Tooltip>
                        </div>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="COUNTRY"
                            name={[field.name, "country"]}
                            fieldKey={field.fieldKey}
                            rules={[required]}
                          >
                            <Select
                              onChange={() => props.handleFormChanged(true)}
                              disabled={props.action === ACTION_VIEW}
                              showSearch
                              notFoundContent={`No Results Found`}
                              getPopupContainer={(trigger) =>
                                trigger.parentNode as HTMLElement
                              }
                            >
                              {countryList.map((country: any) => (
                                <Option
                                  key={country.value}
                                  value={country.value}
                                >
                                  {country.name}
                                </Option>
                              ))}
                            </Select>
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="ADDRESS"
                            name={[field.name, "streetAddress"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,
                              typeWithPattern("string", AvoidWhitespace),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_THREE_HUNDRED}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="CITY"
                            name={[field.name, "city"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,
                              typeWithPattern("string", AvoidWhitespace),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_HUNDRED}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                    </Row>
                    <Row gutter={16}>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="STATE/PROVINCE/REGION"
                            name={[field.name, "state"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,
                              typeWithPattern("string", AvoidWhitespace),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_HUNDRED}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="ZIP CODE"
                            name={[field.name, "zipCode"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,
                              typeWithPattern("string", AvoidWhitespace),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_FIFTEEN}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                    </Row>
                  </div>
                ))}
              </>
            );
          }}
        </Form.List>
      </>
    );
  };

  const defaultAddressBlock = () => {
    return (
      <div data-block="addressBlock" className={styles.yjListWrapper}>
        <Row className={styles.yjListHeader}>
          <Col className={styles.yjSubHeading}>Address Details - Default</Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <div>
              <Form.Item
                label="COUNTRY"
                name={["primaryAddress", "country"]}
                rules={[required]}
              >
                <Select
                  onChange={() => props.handleFormChanged(true)}
                  disabled={props.action === ACTION_VIEW}
                  showSearch
                  notFoundContent={`No Results Found`}
                  getPopupContainer={(trigger) =>
                    trigger.parentNode as HTMLElement
                  }
                >
                  {countryList.map((country: any) => (
                    <Option key={country.value} value={country.value}>
                      {country.name}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="ADDRESS"
                name={["primaryAddress", "streetAddress"]}
                rules={[required, typeWithPattern("string", AvoidWhitespace)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_THREE_HUNDRED}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="CITY"
                name={["primaryAddress", "city"]}
                rules={[required, typeWithPattern("string", AvoidWhitespace)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_HUNDRED}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
        </Row>

        <Row gutter={16}>
          <Col span={8}>
            <div>
              <Form.Item
                label="STATE/PROVINCE/REGION"
                name={["primaryAddress", "state"]}
                rules={[required, typeWithPattern("string", AvoidWhitespace)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_HUNDRED}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="ZIP CODE"
                name={["primaryAddress", "zipCode"]}
                rules={[required, typeWithPattern("string", AvoidWhitespace)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_FIFTEEN}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
        </Row>
      </div>
    );
  };

  const emailValidatorRule = (): Rule => ({
    validator(rule, value) {
      return value
        ? exsistEmail(
            getEmailList(
              props.formRef?.getFieldValue([
                "defaultPrimaryAdmin",
                "emailAddress",
              ]),
              props.formRef?.getFieldValue("additionalPrimaryAdmins")
            ),
            props.savedEmailList,
            value
          )
        : Promise.resolve();
    },
  });

  const userExistsInOtherOrganizations = (): Rule => ({
    validator(rule, value) {
      const organizationId = props.organizationId ? props.organizationId : "";
      return value
        ? exsistOnOtherOrganization(value, organizationId)
        : Promise.resolve();
    },
  });

  const generateContactListBlock = () => {
    const MAXIMUM_NUMBER_OF_CONTACTS = 4;
    return (
      <>
        <Form.List name="additionalPrimaryAdmins">
          {(fields, { add, remove }) => {
            return (
              <>
                <Row gutter={16} className={styles.yjAddNewFieldWrapper}>
                  <Col span={8}>
                    <Tooltip title="Add Another Primary Admin">
                      <Button
                        hidden={props.action === ACTION_VIEW}
                        onClick={() => {
                          if (fields.length > MAXIMUM_NUMBER_OF_CONTACTS) {
                            warningNotification(
                              [
                                "You have reached the maximum number of records",
                              ],
                              "Warning"
                            );
                          } else {
                            add();
                          }
                        }}
                        type="primary"
                        className={styles.yjAddNewAddressButton}
                        icon={<PlusCircleOutlined />}
                      >
                        ADD ANOTHER PRIMARY ADMIN
                      </Button>
                    </Tooltip>
                  </Col>
                </Row>
                {fields.map((field, index) => (
                  <div
                    data-contactlistblock="contactList"
                    className={styles.yjListWrapper}
                    key={index}
                  >
                    <Row className={styles.yjListHeader}>
                      <Col className={styles.yjSubHeading}>
                        Primary Admin - {index + 2}
                      </Col>
                      <Col>
                        <div>
                          <Tooltip title="Remove Primary Admin">
                            <Button
                              hidden={props.formRef?.getFieldValue([
                                "additionalPrimaryAdmins",
                                index,
                                "disabled",
                              ])}
                              className={styles.yjRemoveButton}
                              type="primary"
                              shape="circle"
                              key="back"
                              icon={<DeleteOutlined />}
                              onClick={() => remove(field.name)}
                            />
                          </Tooltip>
                        </div>
                      </Col>
                    </Row>

                    <Row gutter={16}>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="FIRST NAME"
                            name={[field.name, "firstName"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,

                              typeWithPattern("string", AvoidWhitespace),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_FIFTY}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="LAST NAME"
                            name={[field.name, "lastName"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,
                              typeWithPattern("string", AvoidWhitespace),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_FIFTY}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="Email Address"
                            name={[field.name, "emailAddress"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,
                              type(
                                "email",
                                "Invalid Input. E.g. <EMAIL>’"
                              ),
                              emailValidatorRule(),
                              !props.formRef?.getFieldValue([
                                "additionalPrimaryAdmins",
                                index,
                                "disabled",
                              ])
                                ? userExistsInOtherOrganizations()
                                : {},
                            ]}
                          >
                            <Input
                              maxLength={INPUT_TWO_FIVE_FOUR}
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              disabled={
                                props.action === ACTION_VIEW ||
                                props.formRef?.getFieldValue([
                                  "additionalPrimaryAdmins",
                                  index,
                                  "disabled",
                                ])
                              }
                              autoComplete="off"
                              placeholder="<EMAIL>"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="PRIMARY CONTACT NUMBER"
                            name={[field.name, "primaryContactNumber"]}
                            fieldKey={field.fieldKey}
                            rules={[
                              required,

                              typeWithPattern("string", PhoneNoPattern),
                            ]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_TWENTY}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                      <Col span={8}>
                        <div>
                          <Form.Item
                            label="SECONDARY CONTACT NUMBER"
                            name={[field.name, "secondaryContactNumber"]}
                            fieldKey={field.fieldKey}
                            rules={[typeWithPattern("string", PhoneNoPattern)]}
                          >
                            <Input
                              onInput={(event) =>
                                prevenPrecedingSpacesOnPaste(event)
                              }
                              onKeyDown={(event) =>
                                prevenPrecedingSpaces(event)
                              }
                              maxLength={INPUT_LENGTH_TWENTY}
                              disabled={props.action === ACTION_VIEW}
                              autoComplete="off"
                            />
                          </Form.Item>
                        </div>
                      </Col>
                    </Row>
                  </div>
                ))}
              </>
            );
          }}
        </Form.List>
      </>
    );
  };

  const defaultContactListBlock = () => {
    return (
      <div data-contactlistblock="contactList" className={styles.yjListWrapper}>
        <Row className={styles.yjListHeader}>
          <Col className={styles.yjSubHeading}>Primary Admin - Default</Col>
        </Row>
        <Row gutter={16}>
          <Col span={8}>
            <div>
              <Form.Item
                label="FIRST NAME"
                name={["defaultPrimaryAdmin", "firstName"]}
                rules={[required, typeWithPattern("string", AvoidWhitespace)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_FIFTY}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="LAST NAME"
                name={["defaultPrimaryAdmin", "lastName"]}
                rules={[required, typeWithPattern("string", AvoidWhitespace)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_FIFTY}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="Email Address"
                name={["defaultPrimaryAdmin", "emailAddress"]}
                rules={[
                  required,
                  type("email", "Invalid Input. E.g. <EMAIL>’"),
                  !props.formRef?.getFieldValue([
                    "defaultPrimaryAdmin",
                    "disabled",
                  ])
                    ? userExistsInOtherOrganizations()
                    : {},
                ]}
              >
                <Input
                  maxLength={INPUT_TWO_FIVE_FOUR}
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  disabled={
                    props.action === ACTION_VIEW ||
                    props.formRef?.getFieldValue([
                      "defaultPrimaryAdmin",
                      "disabled",
                    ])
                  }
                  placeholder="<EMAIL>"
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="PRIMARY CONTACT NUMBER"
                name={["defaultPrimaryAdmin", "primaryContactNumber"]}
                rules={[required, typeWithPattern("string", PhoneNoPattern)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_TWENTY}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
          <Col span={8}>
            <div>
              <Form.Item
                label="SECONDARY CONTACT NUMBER"
                name={["defaultPrimaryAdmin", "secondaryContactNumber"]}
                rules={[typeWithPattern("string", PhoneNoPattern)]}
              >
                <Input
                  onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                  onKeyDown={(event) => prevenPrecedingSpaces(event)}
                  maxLength={INPUT_LENGTH_TWENTY}
                  disabled={props.action === ACTION_VIEW}
                  autoComplete="off"
                />
              </Form.Item>
            </div>
          </Col>
        </Row>
      </div>
    );
  };

  return (
    <div className={styles.yjOrganizationMgtManage}>
      <Form
        form={props.formRef}
        onFinish={props.onFinish}
        size="middle"
        {...layout}
        name="basic"
        initialValues={{
          remember: true,
        }}
        layout="horizontal"
        onChange={() => props.handleFormChanged(true)}
      >
        <Row className={styles.yjOrganizationMgtManageInputWrapper} gutter={16}>
          <Col span={8}>
            <Form.Item
              label="DBA"
              name="dba"
              rules={[required, typeWithPattern("string", AvoidWhitespace)]}
            >
              <Input
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                maxLength={INPUT_LENGTH_TWO_HUNDRED}
                disabled={props.action === ACTION_VIEW}
                autoComplete="off"
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="EMPLOYEE COUNT"
              name="employeeCount"
              rules={[typeWithPattern("string", PositiveNumberPattern)]}
            >
              <Input
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                maxLength={INPUT_LENGTH_EIGHT}
                disabled={props.action === ACTION_VIEW}
                autoComplete="off"
              />
            </Form.Item>
          </Col>

          <Col span={8}>
            <Form.Item
              label="WEBSITE"
              name="webSite"
              rules={[typeWithPattern("string", WebsitePattern)]}
            >
              <Input
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                maxLength={INPUT_LENGTH_TWO_THOUSAND_ONE_HUNDRED}
                disabled={props.action === ACTION_VIEW}
                autoComplete="off"
              />
            </Form.Item>
          </Col>
        </Row>

        <Row>
          <Col span={24}>
            <div className={styles.yjSectionHeading}>Addresses</div>
          </Col>
        </Row>
        {defaultAddressBlock()}
        {generateAddressBlock()}

        <Divider className={styles.yjSectionDivider} />

        <Row>
          <Col span={24}>
            <div className={styles.yjSectionHeading}>PRIMARY ADMIN DETAILS</div>
          </Col>
        </Row>

        {defaultContactListBlock()}
        {generateContactListBlock()}
      </Form>
    </div>
  );
};
