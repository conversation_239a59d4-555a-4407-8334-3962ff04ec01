@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjCheckInActionWrapper {

  .yjCheckInActionUpperSection {

    .yjCheckInActionForm {

      .yjCheckInActionFormHeader {
        font-weight: @yjff-bold;
        text-transform: @yj-transform;
      }

      .yjCheckInActionFormItem {

        .yjCheckInFileName {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 99%;

          &:focus {
            border: none;
            box-shadow: none;
          }
        }
      }
    }
  }
}

.yjCheckInActionFormGrid {
  border-bottom: 1px solid fade(@color-accent-border, 10%);
  margin-bottom: 10px;
  max-height: 40vh;
  overflow: hidden auto;
}

.yjCheckInIndividualItem {
  border-bottom: 1px solid rgba(102, 102, 102, .1);
  height: 40px;
  margin-bottom: 5px;
}

.yjBulkDelete {
  left: 110px;
  position: absolute;
  top: 0;
}
