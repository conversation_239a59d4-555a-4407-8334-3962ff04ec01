import React, { useContext, useState, useEffect } from "react";
import { AutoComplete } from "antd";
import { DataTableContext } from "../../DataTableContext";
import {
  updateFilterValue,
  removeFilterValue,
} from "../../DataTableContext/actions";
import { getOptions } from "../../../../api/genericDataTable";

export default ({ data, endpoint }: any) => {
  const { state, dispatch } = useContext(DataTableContext);
  const [options, setOptions] = useState([]);
  const [value, setValue] = useState("");

  const filter = state.filters[data.key] || { value: "" };
  const MINIMUM_CHARACTER_LENGTH = 2;

  useEffect(() => {
    setValue(filter.value);
    setOptions([]);
  }, [filter.value]);

  useEffect(() => {
    if (value.length > MINIMUM_CHARACTER_LENGTH) {
      getOptions(endpoint, data.key).then((i) => {
        setOptions(i.data);
      });
    } else {
      setOptions([]);
    }
  }, [data.key, endpoint, value.length]);

  const onSearch = (searchText: string) => {
    setValue(searchText);
    if (!searchText) {
      dispatch(removeFilterValue(data.key));
    }
  };

  const onSelect = (searchText: string) => {
    setValue(searchText);
    if (searchText) {
      dispatch(
        updateFilterValue(data.key, {
          value: searchText,
          displayText: `${data.title}: ${searchText}`,
        })
      );
    }
  };

  return (
    <AutoComplete
      value={value}
      options={options}
      onSearch={onSearch}
      onSelect={onSelect}
      onClick={(e) => e.stopPropagation()}
    />
  );
};
