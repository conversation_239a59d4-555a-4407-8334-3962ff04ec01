import React, { Fragment, useEffect, useState } from "react";
import { Button, Form, Modal, Skeleton } from "antd";
import { Beforeunload } from "react-beforeunload";
import { ExclamationCircleOutlined } from "@ant-design/icons/lib";

import OrganizationManagement from "../../../../components/forms/OrganizationManagement";
import styles from "./index.module.less";
import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";
import {
  getOrganizationDetailsByLicenseId,
  modifyOrganization,
} from "../../../../api/organizationService";
import {
  errorNotification,
  successNotification,
} from "../../../../utils/antNotifications";
import logger from "../../../../utils/logger";
import { PromptOnload } from "../../../../utils/confirm/onloadPrompt";

const SET_TIMEOUT_TWO_HUNDRED = 200;
const SET_TIMEOUT_HUNDRED = 100;
const USER_EXISTS_ERROR_CODE = 10001;

export default (props: any) => {
  const [formChangedStatus, setFormChangedState] = useState(false);
  const [form] = Form.useForm(props.form);
  const { confirm } = Modal;
  const { match } = props;
  const { licenseId } = props;
  const [formAction, setFormAction] = useState(false);
  const [emailListValues, setEmailList] = useState<any[]>([]);
  const [organizationId, setOrganizationId] = useState<string>();
  const [dataLoaded, setDataLoaded] = useState(false);
  const failNotificationMessage = "Failed to Save";
  const organizationManagementLandingPage =
    "/onboarding/organization-management";
  const [primaryAdminId, setPrimaryAdminId] = useState(0);

  const setEmailData = (data: any) => {
    const emailList = [];
    data.additionalPrimaryAdmins?.map((contact: any) => {
      if (contact && contact !== null) {
        emailList.push(contact.emailAddress);
        contact.disabled = contact ? true : false;
      }
    });
    if (data.defaultPrimaryAdmin) {
      emailList.push(data.defaultPrimaryAdmin?.emailAddress);
      data.defaultPrimaryAdmin.disabled = true;
    } else {
      data.defaultPrimaryAdmin = {};
      data.defaultPrimaryAdmin.disabled = false;
    }
    form.setFieldsValue(data);
    setEmailList(emailList);
  };

  useEffect(() => {
    getOrganizationDetailsByLicenseId(match ? match.params.id : licenseId)
      .then((response) => {
        response.data.employeeCount = response.data.employeeCount.toString();
        setEmailData(response.data);
        setOrganizationId(response.data.id);
        setPrimaryAdminId(response.data.defaultPrimaryAdmin?.id);
        setDataLoaded(true);
      })
      .catch((error) => {
        logger.error(
          "OrganizationModify",
          "OrganizationModifyRetrieveOrgDetails",
          error
        );
        errorNotification([""], failNotificationMessage);
      });
  }, [licenseId, match]);

  const logErrorMessage = (error: any) => {
    logger.error("OrganizationModify", "OrganizationModifyRequest", error);
  };

  const handleSave = () => {
    setFormAction(true);
    form
      .validateFields()
      .then((values) => {
        values.employeeCount = parseInt(
          values.employeeCount === "" ? 0 : values.employeeCount
        );
        values.defaultPrimaryAdmin.id = primaryAdminId;
        modifyOrganization(values, match ? match.params.id : licenseId)
          .then((response) => {
            if (response.data) {
              successNotification([""], "Successfully Saved");
              if (props.type === "wizard") {
                props.onSaveSucceed();
              } else {
                setTimeout(() => {
                  props.history.push("/onboarding/organization-management");
                }, SET_TIMEOUT_HUNDRED);
              }
            } else {
              errorNotification([""], failNotificationMessage);
            }
          })
          .catch((error) => {
            if (error.errorCode && error.errorCode === USER_EXISTS_ERROR_CODE) {
              errorNotification([""], error.message);
            } else {
              errorNotification([""], failNotificationMessage);
            }

            logErrorMessage(error);
          });
      })
      .catch((error) => {
        form.scrollToField(error.errorFields[0].name);
        logErrorMessage(error);
      });
  };

  const redirectToOrganizationHomePage = () => {
    setFormAction(true);
    setTimeout(() => {
      props.history.push(organizationManagementLandingPage);
    }, SET_TIMEOUT_TWO_HUNDRED);
  };

  const showCancelModal = () => {
    if (form.isFieldsTouched() && formChangedStatus) {
      confirm({
        title: "Are you sure you want to discard the changes?",
        icon: <ExclamationCircleOutlined />,
        okText: "Discard",
        onOk() {
          redirectToOrganizationHomePage();
        },
      });
    } else {
      redirectToOrganizationHomePage();
    }
  };

  const generateFormBlock = () => {
    return (
      <div
        className={
          props.type !== "wizard" && props.type !== "view"
            ? styles.yjOrganizationMgtModuleWrapper
            : styles.yjOrganizationMgtWizardModuleWrapper
        }
      >
        <div className={styles.yjOrganizationMgtModuleContainer}>
          {!formAction &&
            form.isFieldsTouched() &&
            formChangedStatus &&
            props.type !== "wizard" && (
              <PromptOnload isBlocking={true} isSaving={false} />
            )}
          <Beforeunload onBeforeunload={(event) => event.preventDefault()} />
          <OrganizationManagement
            handleFormChanged={(isChanged: boolean) => {
              setFormChangedState(isChanged);
              if (props.formChangedHandle) {
                props.formChangedHandle(formChangedStatus);
              }
            }}
            formRef={form}
            onFinish={handleSave}
            action={match ? "edit" : props.type === "wizard" ? "save" : "view"}
            organizationId={organizationId}
            savedEmailList={emailListValues}
          />
        </div>

        {props.type === undefined && (
          <div className={styles.stepButtonWrapper}>
            <div className={styles.stepButtonGroupWrapper}>
              <Button type="default" onClick={showCancelModal}>
                Cancel
              </Button>
              <Button type="primary" onClick={handleSave}>
                Submit
              </Button>
            </div>
          </div>
        )}
      </div>
    );
  };

  return props.type !== "view" ? (
    <Fragment>
      {props.type !== "wizard" && <PageTitle title={props.title} />}
      {props.type !== "wizard" ? (
        <PageContent>
          {dataLoaded ? generateFormBlock() : <Skeleton active />}
        </PageContent>
      ) : (
        <div>{dataLoaded ? generateFormBlock() : <Skeleton active />}</div>
      )}
    </Fragment>
  ) : (
    <Fragment>
      <div className={styles.yjOrganizationMgtModalWrapper}>
        {dataLoaded ? generateFormBlock() : <Skeleton active />}
      </div>
    </Fragment>
  );
};
