import { LicenseDetails } from '@app/types/LicenseDetails';
import { LicenseDetailsOptions } from '@app/types/LicenseDetailsOptions';

export interface LicenseManagementState {
    liceseDetails: LicenseDetails | undefined;
    options: LicenseDetailsOptions | undefined | any;
    isDataFetched: boolean;
    isOptionsFetched: boolean;
    error: {message: string, title: string} | undefined;
    saveSuccessed: boolean;
}
