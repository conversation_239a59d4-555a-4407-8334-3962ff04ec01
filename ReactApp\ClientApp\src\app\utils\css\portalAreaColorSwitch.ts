const YES_VALUE = "Yes";
const NO_VALUE = "No";
const CASE_VALUE_TWO = 2;

export const requestStatus = (value: number) => {
  let color = "";
  switch (value) {
    case 0:
      color = "#73d13d";
      break;
    case 1:
      color = "#134A82";
      break;
    default:
      break;
  }
  return color;
};

export const requestExpirationStatus = (value: number) => {
  let color = "";
  switch (value) {
    case 0:
      color = "#ff4d4f";
      break;
    case 1:
      color = "#666666";
      break;
    case CASE_VALUE_TWO:
      color = "#ffa940";
      break;
    default:
      break;
  }
  return color;
};

export const requestPasswordProtectionStatus = (value: string) => {
  let color = "";
  switch (value) {
    case YES_VALUE:
      color = "#73d13d";
      break;
    case NO_VALUE:
      color = "#ff4d4f";
      break;
    default:
      break;
  }
  return color;
};
