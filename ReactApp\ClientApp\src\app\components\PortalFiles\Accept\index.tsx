import React, { useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";

import Uploader from "@app/components/Uploader";
import { IFile } from "@app/types/fileAreaTypes";
import { FileRecord } from "@app/components/forms/UploaderSubmit/types";
//import { getFileAreaCommonData } from "@app/redux/actions/fileAreaActions";

export interface PortalFilesAcceptProps {
  siteId: string;
  binderId: string;
  selectedFiles: IFile[];
  onModalClose: () => void;
}
export default (props: PortalFilesAcceptProps) => {
  const [selectedFiles, setSelectedFiles] = useState<FileRecord[]>([]);
  const dispatch = useDispatch();
  const history = useHistory();

  useEffect(() => {
    const mapAcceptedFiles: FileRecord[] = props.selectedFiles?.map((file) => ({
      ...file,
      checked: true,
      referenceNumber: file.id,
    }));
    setSelectedFiles(mapAcceptedFiles);
  }, [props.selectedFiles]);

  // useEffect(() => {
  //   dispatch(getFileAreaCommonData(props.siteId, { history }));
  // }, [dispatch, props.siteId]);

  return (
    <>
      {selectedFiles.length > 0 && (
        <Uploader
          siteId={props.siteId}
          binderId={props.binderId}
          portalFiles={selectedFiles}
          onCloseModal={() => props.onModalClose()}
          fileAreaSelection={true}
        />
      )}
    </>
  );
};
