import React, {Fragment, useEffect, useState} from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import {
  EditOutlined,
  EyeOutlined,
  FileTextOutlined,
  MailOutlined,
  PhoneOutlined,
  PlusOutlined,
} from "@ant-design/icons";
import { withRouter } from "react-router-dom";
import styles from "./index.module.less";
import PageContent from "../../../components/PageContent";
import PageTitle from "../../../components/PageTitle";
import GenericDataTable from "@app/components/GenericDataTable";
import config from "@app/utils/config";
import userStatusColorSwitch from "@app/utils/css/userStatusColorSwitch";
import { Sorter } from "@app/components/GenericDataTable/util";
import Modal from "@app/components/Modal";
import InfinityList from "@app/components/InfinityList";
import { getParameterizedUrlWith } from "@app/utils";
import { parsePhoneNumber } from "libphonenumber-js";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import SiteDetailsDrawer from "../../../features/Site/SiteDetailsDrawer";
import { renderTag } from "@app/components/Tag";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const SORTER: Sorter = {
  value: "created",
  order: "descend",
};
const MANAGE_SITES_EDIT_PAGE = "/master-data/manage-sites/edit";

const Page = (props: any) => {
  const redirectToCreateSitesPage = () => {
    props.history.push("/master-data/manage-sites/create");
  };

  const [selectedRequestRowKeys, setSelectedRequestRowKeys] = useState([]);
  const [showFolderStructureModal, setShowFolderStructureModal] = useState<
    boolean
  >(false);
  const [siteName, setSiteName] = useState<string | undefined>();
  const [siteId, setSiteId] = useState<string>("");
  const [showDrawer, setShowDrawer] = useState(false);

  const rowSelectionSites = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRequestRowKeys(selectedRowKeys);
    },
    selectedRowKeys: selectedRequestRowKeys,
  };

  const onRowClick = (event: any, record: any) => {
    setSiteName(record.name);
    setSiteId(record.siteId);
  };

  const handleRowClick = (record: any, rowIndex: any) => ({
    onClick: (event: any) => onRowClick(event, record),
  });

  const getSiteDetails = () => {
    setShowDrawer(true);
  };

  const closeDrawer = () => {
    setShowDrawer(false);
  };

  const renderGridColumns = () => {
    return {
      status: (record: any) => {
        return renderTag(
          record.name,
          record.value,
          userStatusColorSwitch(record.value)
        );
      },

      name: (record: any) => {
        return (
          <Tooltip placement="leftTop" title={record}>
            <p className={styles.yjSiteGridTextWrap}>{record}</p>
          </Tooltip>
        );
      },

      created: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      modified: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },

      contactsCount: (record: any) => {
        return (
          <div>
            <p className={styles.yjGridTextCenter}>
              {record > 0 ? (
                <Button
                  onClick={() => setShowFolderStructureModal(true)}
                  type="link"
                >
                  {record === 1 ? "1 Contact" : `${record} Contacts`}
                </Button>
              ) : (
                `No Contacts`
              )}
            </p>
          </div>
        );
      },

      action: (text: any, record: any) => {
        return (
          <div className={"yjActionIconWrapper"}>
            <div className={`${styles.yjActionIconWrapper} `}>
              <Tooltip title="View">
                <Button
                  onClick={() => getSiteDetails()}
                  icon={<EyeOutlined />}
                />
              </Tooltip>
            </div>
            <div className={`${styles.yjActionIconWrapper} `}>
              <Tooltip title="Update">
                <Button
                  onClick={() =>
                    props.history.push(
                      `${MANAGE_SITES_EDIT_PAGE}/${record.siteId}`
                    )
                  }
                  icon={<EditOutlined />}
                />
              </Tooltip>
            </div>

            <div className={`${styles.yjActionIconWrapper}`}>
              <Tooltip title="File Area">
                <Button
                  onClick={() => {
                    const enCryptedName = encodeURIComponent(record.name);
                    const url = `/client-file-area/${record.siteId}/${enCryptedName}/${record.channel?.value}`;
                    const encodedUrl = encodeURI(url);
                    props.history.push(encodedUrl);
                  }}
                  icon={<FileTextOutlined />}
                />
              </Tooltip>
            </div>
          </div>
        );
      },
    };
  };

  const renderViewSites = () => {
    return (
      <Modal
        visible={showFolderStructureModal}
        title={`Contacts of ${siteName}`}
        onCancel={() => setShowFolderStructureModal(false)}
        footer={[
          <Button
            key="back"
            type="default"
            onClick={() => setShowFolderStructureModal(false)}
          >
            CLOSE
          </Button>,
        ]}
        size="medium"
      >
        <div>
          <InfinityList
            listClassName={"yjInfinityListClass"}
            paginatedLimit={20}
            endpoint={getParameterizedUrlWith(
              config.api[OperationalServiceTypes.MasterDataService]
                .contactsBysiteId,
              [{ name: "siteId", value: siteId }]
            )}
            idKeyValue="contactId"
            notFoundContent="No Contact(s) Found"
            formatValue={(value: any) => {
              return (
                <div className={styles.yjViewContactsManageSites}>
                  <Tooltip title={value.firstName} placement="topLeft">
                    <p
                      className={styles.yjViewContactsContactName}
                    >{`${value.lastName},${value.firstName}`}</p>
                  </Tooltip>
                  <Tooltip title={value.email} placement="topLeft">
                    <p className={styles.yjViewContactsEmail}>
                      <MailOutlined />
                      {value.email}{" "}
                    </p>
                  </Tooltip>
                  <p className={styles.yjViewContactsPhoneNumber}>
                    <PhoneOutlined />
                    {value.contactNumber
                      ? parsePhoneNumber(
                          `+${value.contactNumber}`
                        ).formatInternational()
                      : "Not Available"}
                  </p>
                </div>
              );
            }}
          />
        </div>
      </Modal>
    );
  };

  return (
    <Fragment>
      <PageTitle title={props.title}>
        <Button
          onClick={redirectToCreateSitesPage}
          type="primary"
          icon={<PlusOutlined />}
        >
          Create Site
        </Button>
      </PageTitle>
      <PageContent>
        <div className="yjCustomTblHeader">
          {renderViewSites()}
          <GenericDataTable
            rowSelection={rowSelectionSites}
            endpoint={
              config.api[OperationalServiceTypes.MasterDataService].sites
            }
            selectedRecordCount={selectedRequestRowKeys.length}
            rowKey={"siteId"}
            scrollColumnCounter={7}
            tableKey={"siteManagement"}
            customRender={renderGridColumns()}
            fixedColumns={["name"]}
            isDraggable={true}
            noRecordsAvilableMessage={"No Sites Available"}
            sorted={SORTER}
            onRow={handleRowClick}
            hasFilterManagement={false}
          />
        </div>
      </PageContent>
      <SiteDetailsDrawer
        onClose={closeDrawer}
        showDrawer={showDrawer}
        siteName={siteName ?? ""}
      />
    </Fragment>
  );
};

export default withRouter(Page);
