import logger from "@app/utils/logger";
import {
  Module,
  FunctionModule,
  PreModule,
  SubModule,
  Flows,
  FunctionSubModule,
} from "../../../../redux/types/functionalFlow/functionalFlow";

const manageRootFlows = (
  flow: Flows,
  functionModules: FunctionModule[] | null,
  moduleId: number
): Flows => {
  if (flow.isMandatory) {
    flow.isChecked = true;
  }
  const functionModule = functionModules?.find((x) => x.id === moduleId);
  if (functionModule?.flows.includes(flow.id)) {
    flow.isChecked = true;
  }
  return flow;
};

const manageFlows = (
  flow: Flows,
  functionModules: FunctionModule[] | null,
  moduleId: number,
  subModuleId: number
): Flows => {
  if (flow.isMandatory) {
    flow.isChecked = true;
  }
  const functionModule = functionModules
    ?.find((x) => x.id === moduleId)
    ?.subModules.find((x) => x.id === subModuleId);
  if (functionModule?.flows.includes(flow.id)) {
    flow.isChecked = true;
  }
  return flow;
};

export const AssignFunctionsToModule = (
  modules: Module[] | null,
  functionModules: FunctionModule[] | null
): Module[] | null => {
  modules &&
    modules.forEach((module) => {
      const dependancyIds = [] as any;
      if (module.flows.length > 0) {
        module.flows.forEach((higherFunction) => {
          const rootFlow = manageRootFlows(
            higherFunction,
            functionModules,
            module.id
          );

          if (
            rootFlow.isChecked &&
            rootFlow.dependencies &&
            rootFlow.dependencies.length > 0
          ) {
            rootFlow.dependencies.forEach((value) => {
              dependancyIds.push(value);
            });
          }
        });
        dependancyIds.forEach((value: any) => {
          const returnModule = module.flows.find((x) => x.id === value);
          if (returnModule) {
            returnModule.isMandatory = true;
          }
        });
      }

      if (
        module.subModules?.length !== undefined &&
        module.subModules?.length > 0
      ) {
        module.subModules.forEach((subModule) => {
          return subModule.flows?.forEach((flow) => {
            manageFlows(flow, functionModules, module.id, subModule.id);
          });
        });
      }
    });

  return modules;
};

export const mapModuleResponseToModule = (preModule: PreModule[]): Module[] => {
  const moduleResponse: Module[] = [];
  try {
    if (preModule && preModule.length > 0) {
      preModule.forEach((module) => {
        const modueleObject: Module = {
          id: module.id,
          name: module.name,
          isMandatory: module.isMandatory,
          flows: [],
          subModules: [],
        };

        module.flows.forEach((func) => {
          modueleObject.flows.push({
            ...func,
            isChecked: false,
            dependencies: func.dependencies ? [...func.dependencies] : [],
          });
        });

        module.subModules?.forEach((subMod) => {
          const subModule: SubModule = {
            id: subMod.id,
            name: subMod.name,
            flows: [],
          };

          subMod.flows?.forEach((func) => {
            const flow: Flows = {
              ...func,
              isChecked: false,
              dependencies: func.dependencies ? [...func.dependencies] : [],
            };
            subModule.flows?.push(flow);
          });

          modueleObject.subModules?.push(subModule);
        });
        moduleResponse.push(modueleObject);
      });
    }
  } catch (error) {
    logger.error(
      "Organization Onboarding Module",
      "Functionall Flow Management",
      error
    );
    return [];
  }

  return moduleResponse;
};

export const generateFunctionalFlowRequestBody = (
  modules: Module[] | null | undefined
): FunctionSubModule[] => {
  const returnModules: FunctionSubModule[] = [];
  modules &&
    modules.forEach((module) => {
      const checkedHigherFunctions = module.flows.filter((x) => x.isChecked);
      if (checkedHigherFunctions.length > 0) {
        const functionList: number[] = [];
        checkedHigherFunctions.forEach((higherFunction) => {
          functionList.push(higherFunction.id);
        });
        returnModules.push({ id: module.id, flows: functionList });
      }

      if (module.subModules && module?.subModules?.length > 0) {
        module.subModules.forEach((subModule) => {
          if (subModule?.flows && subModule.flows.length > 0) {
            const functionList: number[] = [];
            const checkedFunctions = subModule.flows.filter((x) => x.isChecked);
            if (checkedFunctions.length > 0) {
              checkedFunctions.forEach((flow) => {
                functionList.push(flow.id);
              });
            }
            returnModules.push({ id: subModule.id, flows: functionList });
          }
        });
      }
    });
  return returnModules;
};
