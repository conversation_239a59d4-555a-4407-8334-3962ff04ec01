import React, { Fragment, useState } from "react";
import { withRouter } from "react-router-dom";
import { <PERSON><PERSON>, Tooltip } from "antd";
import { EditOutlined, PlusOutlined } from "@ant-design/icons";

import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";
import Modal from "@app/components/Modal";
import styles from "./index.module.less";
import GenericDataTable from "@app/components/GenericDataTable";
import config from "@app/utils/config";
import { Sorter } from "@app/components/GenericDataTable/util";
import userStatusColorSwitch from "@app/utils/css/userStatusColorSwitch";
import InfinityList from "@app/components/InfinityList";
import { getParameterizedUrl } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import {renderTag} from "@app/components/Tag";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const SORTER: Sorter = {
  value: "created",
  order: "descend",
};
const IN_ACTIVE = "Inactive";

const Page = (props: any) => {
  const [groupName, setGroupName] = useState<string>("");
  const [groupId, setGroupId] = useState<string>("");
  const [showUserListModal, setShowUserListModal] = useState<boolean>(false);
  const [selectedRequestRowKeys, setSelectedRequestRowKeys] = useState<any>([]);

  const showUserDetails = (item: any) => {
    setGroupName(item.name);
    setGroupId(item.groupId);
    setShowUserListModal(true);
  };

  const rowSelectionUserGroups = {
    onChange: (selectedRowKeys: any, selectedRows: any) => {
      setSelectedRequestRowKeys(selectedRowKeys);
    },
    selectedRowKeys: selectedRequestRowKeys,
  };

  const navigateToCreateUserGroup = () => {
    props.history.push("/user-management/user-groups/create");
  };

  const redirectToEditPage = (record: any) => {
    props.history.push(`/user-management/user-groups/edit/${record.groupId}`);
  };

  const renderGridColumns = () => {
    return {
      status: (record: any) => {
        return record
          ? renderTag(
              record.name,
              record.value,
              userStatusColorSwitch(record.value)
            )
          : renderTag(IN_ACTIVE, -1, userStatusColorSwitch(-1));
      },
      userCount: (record: any, item: any) => {
        return record > 0 ? (
          <Button
            type="link"
            onClick={() => {
              showUserDetails(item);
            }}
            className={styles.yjGridTextCenter}
          >
            {record} {record === 1 ? "User" : "Users"}
          </Button>
        ) : (
          "No Users"
        );
      },
      created: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      modified: (record: any) => {
        return  <FormattedDateTime value={record} />;
      },
      action: (text: any, record: any) => {
        return (
          <div className={`${styles.yjActionIconWrapper} yJFileAreaRow`}>
            <Tooltip className="yJFileAreaRow" title="Update">
              <Button
                className="yJFileAreaRow"
                onClick={() => redirectToEditPage(record)}
                icon={<EditOutlined />}
              />
            </Tooltip>
          </div>
        );
      },
    };
  };

  const renderUserListModal = () => {
    return (
      <Modal
        visible={showUserListModal}
        title={`Users of ${groupName} User Group`}
        onCancel={() => setShowUserListModal(false)}
        footer={[
          <Button
            key="back"
            type="default"
            onClick={() => setShowUserListModal(false)}
          >
            CLOSE
          </Button>,
        ]}
        size="small"
      >
        <div>
          <InfinityList
            paginatedLimit={20}
            endpoint={getParameterizedUrl(
              config.api[OperationalServiceTypes.UserService].usersByGroupId,
              groupId
            )}
            notFoundContent="No User(s) Found"
            formatValue={(value: any) => {
              return <span>{value.fullName}</span>;
            }}
            idKeyValue="id"
          />
        </div>
      </Modal>
    );
  };

  return (
    <Fragment>
      {renderUserListModal()}
      <PageTitle title={props.title}>
        <Button
          onClick={navigateToCreateUserGroup}
          type="primary"
          icon={<PlusOutlined />}
        >
          Create User Group
        </Button>
      </PageTitle>
      <PageContent>
        <div className="yjCustomTblHeader">
          <GenericDataTable
            tableKey={"usergroupmngt"}
            endpoint={
              config.api[OperationalServiceTypes.UserService].getUserGroups
            }
            scrollColumnCounter={6}
            customRender={renderGridColumns()}
            sorted={SORTER}
            fixedColumns={["groupId", "name"]}
            isDraggable={true}
            rowKey={"groupId"}
            rowSelection={rowSelectionUserGroups}
            selectedRecordCount={selectedRequestRowKeys.length}
            noRecordsAvilableMessage={"No User Groups Available"}
          />
        </div>
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
