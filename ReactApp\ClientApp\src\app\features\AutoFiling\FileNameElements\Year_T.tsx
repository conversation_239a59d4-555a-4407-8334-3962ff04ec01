import { Form, Radio } from 'antd';
import React, { useState } from 'react';
import styles from "./index.module.less";
const Year_T = ({index, value="YY"}: any) =>{
	return (value && <Form.Item label={`Year`} name={`${index}-Year`} initialValue={value}>
			<Radio.Group>
				<Radio value="YY">YY</Radio>
				<Radio value="YYYY">YYYY</Radio>
			</Radio.Group>
		</Form.Item>
	)
};

export default Year_T;
