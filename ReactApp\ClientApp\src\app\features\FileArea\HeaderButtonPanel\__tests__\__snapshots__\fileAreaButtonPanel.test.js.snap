// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`File Area Button Panel Test Suite File Area Button Panel should render and create the snapshot 1`] = `
<div>
  <button
    className="ant-btn ant-btn-primary"
    hidden={true}
    onClick={[Function]}
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
    type="button"
  >
    <span>
      MANAGE EMAIL SUBSCRIPTION
    </span>
  </button>
  <button
    className="ant-btn ant-btn-primary"
    disabled={true}
    hidden={true}
    onClick={[Function]}
    type="button"
  >
    <span>
      CHECK-IN
    </span>
  </button>
  <button
    className="ant-btn ant-btn-primary"
    hidden={true}
    onClick={[Function]}
    onMouseEnter={[Function]}
    onMouseLeave={[Function]}
    type="button"
  >
    <span>
      HISTORY
    </span>
  </button>
  <button
    className="ant-btn ant-btn-primary"
    hidden={true}
    onClick={[Function]}
    type="button"
  >
    <span>
      REQUEST
    </span>
  </button>
  <div
    className="yjChannelSelector"
  >
    <div
      className="ant-select ant-select-single ant-select-show-arrow"
      onBlur={[Function]}
      onFocus={[Function]}
      onKeyDown={[Function]}
      onKeyUp={[Function]}
      onMouseDown={[Function]}
    >
      <div
        className="ant-select-selector"
        onClick={[Function]}
        onMouseDown={[Function]}
      >
        <span
          className="ant-select-selection-search"
        >
          <input
            aria-activedescendant="undefined_list_0"
            aria-autocomplete="list"
            aria-controls="undefined_list"
            aria-haspopup="listbox"
            aria-owns="undefined_list"
            autoComplete="off"
            className="ant-select-selection-search-input"
            onChange={[Function]}
            onCompositionEnd={[Function]}
            onCompositionStart={[Function]}
            onKeyDown={[Function]}
            onMouseDown={[Function]}
            onPaste={[Function]}
            readOnly={true}
            role="combobox"
            style={
              Object {
                "opacity": 0,
              }
            }
            type="search"
            unselectable="on"
            value=""
          />
        </span>
        <span
          className="ant-select-selection-placeholder"
        />
      </div>
      <span
        aria-hidden={true}
        className="ant-select-arrow"
        onMouseDown={[Function]}
        style={
          Object {
            "WebkitUserSelect": "none",
            "userSelect": "none",
          }
        }
        unselectable="on"
      >
        <span
          aria-label="down"
          className="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;
