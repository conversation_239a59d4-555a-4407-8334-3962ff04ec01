@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjInfinityLoadMore {
  align-items: center;
  color: rgba(0, 0, 0, .25);
  display: flex;
  justify-content: center;
  padding: 15px;
  width: 100%;
}

.yjInfinityScrollText {
  background: @color-primary;
  padding-top: 5px;
  width: 100%;

  p {
    color: @color-white;
    margin-bottom: 0;
    padding-bottom: 5px;
    text-align: center;
  }
}

/* Select option */

.yjSelectOptionSwitcher {
  border-bottom: 1px solid #ccc;
  margin-bottom: 10px;
  padding-bottom: 10px;

  a {

    svg {
      margin-right: 5px;
    }
  }

  .yjSwitcherSelectedRecords {
    color: @color-font;
    float: right;
    font-size: 12.5px;
    margin: 0;
    padding: 0;
  }
}

.yjInfiniteListItems {
  margin-left: 15px;
}
