import { ValueType } from './ValueType';

export type LicenseDetails = {
    id: string;
    companyName: string;
    vertical: ValueType;
    status: ValueType;
    effectiveDate: string;
    expirationDate: string;
    compliances: [ValueType];
    allocatedSpace: ValueType;
    userCount: ValueType;
    supportLevel: ValueType;
    integrations: [ValueType];
    modules: [LicenseModules];
};

export type LicenseModules = {
    functions: [number];
    moduleId: number;
};
