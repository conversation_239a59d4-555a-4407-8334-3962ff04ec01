import React from "react";
import {Tabs} from "antd";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import TabsComponent from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

const { TabPane } = Tabs;
jest.mock("../index.module.less", () => ({
    yjTabs: "yjTabs",
    yjPropertiesDetailTab: "yjPropertiesDetailTab",
    yjPropertiesDetailPreview: "yjPropertiesDetailPreview",
    yjPropertiesDetailInfo: "yjPropertiesDetailInfo",
}));

describe("Tabs Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const tComponent = shallow(<TabsComponent />);
        expect(tComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const tComponent = renderer.create(<TabsComponent />).toJSON();
        expect(tComponent).toMatchSnapshot();
    });

    it("should have a Tabs element",() => {
        const tComponent = shallow(<TabsComponent />);
        expect(tComponent.find(Tabs)).toHaveLength(1);
    });

    it("should have TabPane elements",() => {
        const tComponent = shallow(<TabsComponent />);
        expect(tComponent.find(TabPane)).toHaveLength(3);
    });

    it("should have  p elements",() => {
        const tComponent = shallow(<TabsComponent />);
        expect(tComponent.find("p")).toHaveLength(2);
    });

    it("should have div elements",() => {
        const tComponent = shallow(<TabsComponent />);
        expect(tComponent.find(".yjTabs")).toHaveLength(1);
        expect(tComponent.find(".yjPropertiesDetailTab")).toHaveLength(1);
        expect(tComponent.find(".yjPropertiesDetailPreview")).toHaveLength(1);
        expect(tComponent.find(".yjPropertiesDetailInfo")).toHaveLength(1);
    });
});


