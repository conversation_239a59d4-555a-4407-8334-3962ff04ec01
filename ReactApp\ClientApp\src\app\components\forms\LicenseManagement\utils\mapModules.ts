/**
 * Maping license module array to vertical modules response
 * If a module or a function is included in the module response
 * checked attribute will be added with the value true
 * @param licenceModules 
 * @param verticalModules 
 */
export default function mapModules(
    licenceModules: Array<any>,
    verticalModules: Array<any>
): any {
    return verticalModules?.map((value) => {
        const selectedModule = licenceModules.find(
            (element) => element.moduleId === value.id
        );
        if (selectedModule) {
            return {
                ...value,
                checked: true,
                functions: mapCheckedFunctions(selectedModule, value),
                subModules: value.subModules
                    ? mapModules(licenceModules, value.subModules)
                    : [],
            };
        } else {
            return {
                ...value,
                checked: false,
                functions: mapCheckedFunctions(selectedModule, value),
                subModules: value.subModules
                    ? mapModules(licenceModules, value.subModules)
                    : [],
            };
        }
    });
}

const mapCheckedFunctions = (selectedModule: any, verticalModule: any) => {
    return verticalModule?.functions?.map((value: any) => {
        const selectedFunction = selectedModule?.functions?.find(
            (e: any) => e === value.id
        );

        if (selectedFunction) {
            return {
                ...value,
                checked: true,
            };
        } else {
            return {
                ...value,
                checked: false,
            };
        }
    });
}
