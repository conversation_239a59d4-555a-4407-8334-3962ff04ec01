import { shallow } from "enzyme";
import React from "react";
import initTestSuite from "@app/utils/config/TestSuite";
import renderer from "react-test-renderer";

import NonAuthorized from "..";

describe("<NonAuthorized/>", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("should render component", () => {
    const component = shallow(<NonAuthorized />);
    expect(component.html()).not.toBe(null);
  });

  it("should render component with props", () => {
    const component = shallow(
      <NonAuthorized
        title={"You do not have the permission to access any offices"}
        subTitle={"Contact your organization's administrator for assistance"}
      />
    );
    expect(component.html()).not.toBe(null);
  });

  it("should render and create the snapshot properly", () => {
    const component = renderer.create(<NonAuthorized />).toJSON();
    expect(component).toMatchSnapshot();
  });
});
