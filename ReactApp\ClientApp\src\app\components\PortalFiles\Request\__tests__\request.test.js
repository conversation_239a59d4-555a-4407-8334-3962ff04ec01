import React from "react";
import Request from "..";
import { shallow } from "enzyme";
import { Form, TreeSelect } from "antd";

describe("Request form test suite", () => {
  beforeAll(() => {
    Object.defineProperty(window, "matchMedia", {
      writable: true,
      value: jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });
  });

  const formLabels = [
    "REQUEST",
    "Description",
    "from",
    "to",
    "cc",
    "bcc",
    "subject",
    "body",
    "securityKey",
    "securityKeyInput",
  ];

  it("Request form component should render", () => {
    const component = shallow(<Request />);
    expect(component.html()).not.toBe(null);
  });
  it("should have a form", () => {
    const component = shallow(<Request />);
    expect(component.find(Form)).toHaveLength(1);
  });

  it("should have 12 form input fields", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item)).toHaveLength(12);
  });

  it("should have label: request", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(0).props().label).toEqual(
      formLabels[0]
    );
  });

  it("should have label: Description", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(1).props().label).toEqual(
      formLabels[1]
    );
  });

  it("should have label: from", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(2).props().label).toEqual(
      formLabels[2]
    );
  });

  it("should have label: to", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(3).props().label).toEqual(
      formLabels[3]
    );
  });
  it("should have label: cc", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(4).props().label).toEqual(
      formLabels[4]
    );
  });
  it("should have label: bcc", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(5).props().label).toEqual(
      formLabels[5]
    );
  });
  it("should have label: subject", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(6).props().label).toEqual(
      formLabels[6]
    );
  });
  it("should have label: body", () => {
    const component = shallow(<Request />);
    expect(component.find(Form.Item).at(7).props().label).toEqual(
      formLabels[7]
    );
  });
  it("should have one Tree Select element", () => {
    const component = shallow(<Request />);
    expect(component.find(TreeSelect)).toHaveLength(3);
  });
});
