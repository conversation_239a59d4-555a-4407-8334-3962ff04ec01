import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import EditIntegration from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../../components/PageTitle";
import PageContent from "../../../../../components/PageContent";
import IntegrationManagement from "../../../../../components/forms/IntegrationManagement";

describe("EditIntegration Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const eIComponent = shallow(<EditIntegration />);
        expect(eIComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const eIComponent = renderer.create(<EditIntegration />).toJSON();
        expect(eIComponent).toMatchSnapshot();
    });

    it("should have a PageTitle component",() => {
        const eIComponent = shallow(<EditIntegration />);
        expect(eIComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const eIComponent = shallow(<EditIntegration />);
        expect(eIComponent.find(PageContent)).toHaveLength(1);
    });

    it("should have a IntegrationManagement component",() => {
        const eIComponent = shallow(<EditIntegration />);
        expect(eIComponent.find(IntegrationManagement)).toHaveLength(1);
    });
});




