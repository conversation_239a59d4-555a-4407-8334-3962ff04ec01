@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjLoginForm {
  align-items: center;
  display: flex;
  height: 100vh;
  justify-content: center;
  width: 100%;

  img {
    padding: 0 0 25px 0;
  }

  h4 {
    font-size: @font-size-base;
    font-weight: @yjff-bold;
    margin: 25px 0 25px;
    text-transform: @yj-transform;
  }

  .yjLoginBtn {
    display: block;
    font-size: @font-size-base;
    margin-left: 0;
    padding: 13px 25px 38px;
    text-transform: @yj-transform;
    width: 100%;
  }

  .txtPrivacy {
    border-top: 1px solid @color-border;
    font-size: @font-size-base/1.2;
    font-weight: @yjff-bold;
    margin: 25px 0;
    padding: 10px 0;
    text-align: center;
  }

  .txtforgot {
    float: right;
    font-size: @font-size-base/1.2;
  }

  .txtRemember {
    font-size: @font-size-base/1.2;
    text-transform: capitalize;
  }

  .txtHelp {
    font-size: @font-size-base/1.2;
  }

  .yjInput {
    min-height: 45px;
  }
}
