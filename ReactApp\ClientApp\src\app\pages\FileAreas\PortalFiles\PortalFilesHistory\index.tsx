import React, { Fragment, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import { EyeOutlined, SearchOutlined } from "@ant-design/icons";
import { useParams, withRouter } from "react-router-dom";
import {
  <PERSON><PERSON>,
  Col,
  DatePicker,
  Input,
  Modal,
  Row,
  Space,
  Table,
  Tooltip,
} from "antd";
import moment from "moment";
import { ColumnsType } from "antd/lib/table";
import styles from "./index.module.less";
import { portalFileSectionHistoryMockData } from "./mockdata";
import { PageContent } from "@app/layouts/MasterLayout";
import PageTitle from "@app/components/PageTitle";
import { setDynamicBreadcrums } from "@app/redux/actions/configurationActions";

const { RangePicker } = DatePicker;

interface PortalRecord {
  id: string;
  requestName: string;
  title: string;
  files: string;
  path: string;
  level1Folder: string;
  level2Folder: string;
  status: string;
  tags: string[];
  year: string;
  projects: string[];
  type: string;
  expirationStatus: string;
  expirationPeriod: string;
  description: string;
  passwordProtected: boolean;
  uploader: string;
  uploadedDate: string;
  emailStatus: string;
  size: string;
  assignee: string;
  createdBy: string;
  created: string;
  modified: string;
  fileCondition: string;
  filePreview: string;
  subject: string;
  body: string;
  link: string;
  securityKey: string;
  linkExpiresOn: string;
  from: string;
  bccSender: string;
  to: {
    primaryContacts: string[];
    secondaryContacts: string[];
    internalUsers: string[];
  };
  cc: {
    primaryContacts: string[];
    secondaryContacts: string[];
    internalUsers: string[];
  };
  bcc: {
    primaryContacts: string[];
    secondaryContacts: string[];
    internalUsers: string[];
  };
}

interface Changes {
  previous: PortalRecord;
  current: PortalRecord;
}

const DATE_SPLIT_VALUE = 10;

const Page = () => {
  const [searchedColumn, setSearchColumn] = useState("");
  const [showChangesModal, setShowChangesModal] = useState(false);
  const [changes, setChanges] = useState<Changes>();

  const { siteId, siteName, channelId } = useParams<any>();
  const dispatch = useDispatch();

  useEffect(() => {
    dispatch(setDynamicBreadcrums([`${channelId} - ${decodeURIComponent(siteName)}`]));
      return () => {
        dispatch(setDynamicBreadcrums([]));
      };
  }, []);

  let searchInput: any;
  const getColumnSearchProps = (dataIndex: any) => ({
    filterDropdown: ({
      setSelectedKeys,
      selectedKeys,
      confirm,
      clearFilters,
    }: {
      setSelectedKeys: any;
      selectedKeys: any;
      confirm: any;
      clearFilters: any;
    }) => (
      <div style={{ padding: 8 }}>
        {dataIndex === "dateTime" ? (
          <RangePicker
            ref={(node: any) => {
              searchInput = node;
            }}
            placeholder={["Start", "End"]}
            value={selectedKeys[0]}
            onChange={(e: any) => {
              setSelectedKeys(e ? [e] : []);
            }}
            style={{ width: 188, marginBottom: 8, display: "block" }}
            format={moment.localeData().longDateFormat('L')}
          />
        ) : (
          <Input
            ref={(node) => {
              searchInput = node;
            }}
            placeholder={`Search ${dataIndex}`}
            value={selectedKeys[0]}
            onChange={(e) =>
              setSelectedKeys(e.target.value ? [e.target.value] : [])
            }
            onPressEnter={() => handleSearch(selectedKeys, confirm, dataIndex)}
            style={{ width: 188, marginBottom: 8, display: "block" }}
          />
        )}
        <Space>
          <Button
            type="primary"
            onClick={() => handleSearch(selectedKeys, confirm, dataIndex)}
            icon={<SearchOutlined />}
            size="small"
            style={{ width: 90 }}
          >
            Search
          </Button>
          <Button
            onClick={() => handleReset(clearFilters)}
            type="default"
            size="small"
            style={{ width: 90 }}
          >
            Reset
          </Button>
        </Space>
      </div>
    ),
    filterIcon: (filtered: any) => (
      <SearchOutlined style={{ color: filtered ? "#1890ff" : undefined }} />
    ),
    onFilter: (value: any, record: any) => {
      if (dataIndex === "dateTime" && value && value.isArray()) {
        const startDate = value[0];
        const endDate = value[1];
        const startDateString = startDate
          ?.toISOString()
          .slice(0, DATE_SPLIT_VALUE);
        const endDateString = endDate?.toISOString().slice(0, DATE_SPLIT_VALUE);
        return (
          startDateString !== undefined &&
          endDateString !== undefined &&
          moment(record[dataIndex]).isBetween(
            moment(startDateString),
            moment(endDateString),
            "days",
            "()"
          )
        );
      } else {
        return record[dataIndex]
          ? record[dataIndex]
            .toString()
            .toLowerCase()
            .includes(value?.toLowerCase())
          : "";
      }
    },

    render: (text: any) =>
      searchedColumn === dataIndex ? (
        <div>{text ? text.toString() : ""}</div>
      ) : (
        text
      ),
  });

  const handleSearch = (selectedKeys: any, confirm: any, dataIndex: any) => {
    confirm();
    setSearchColumn(dataIndex);
  };

  const handleReset = (clearFilters: any) => {
    clearFilters();
  };

  const onClickShowChanges = (record: any) => {
    setShowChangesModal(true);
    setChanges(record.changes);
  };

  const renderChanges = () => {
    return (
      <Fragment>
        <table className={styles.yjHistoryChangesGrid}>
          <tr>
            <th>ATTRIBUTE</th>
            <th>PREVIOUS VALUE</th>
            <th>NEW VALUE</th>
          </tr>
          {(changes?.previous?.id || changes?.current?.id) && (
            <tr>
              <td>ID</td>
              <td>
                {changes?.previous?.id ? `${changes.previous.id}` : undefined}
              </td>
              <td>
                {changes?.current?.id ? `${changes.current.id}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.title || changes?.current.title) && (
            <tr>
              <td>Title</td>
              <td>
                {changes?.previous.title
                  ? `${changes?.previous.title}`
                  : undefined}
              </td>
              <td>
                {changes?.current.title
                  ? `${changes?.current.title}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.path || changes?.current.path) && (
            <tr>
              <td>Path</td>
              <td>
                {changes?.previous.path
                  ? `${changes?.previous.path}`
                  : undefined}
              </td>
              <td>
                {changes?.current.path ? `${changes?.current.path}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.level1Folder ||
            changes?.current.level1Folder) && (
              <tr>
                <td>Level 1 Folder</td>
                <td>
                  {changes?.previous.level1Folder
                    ? `${changes?.previous.level1Folder}`
                    : undefined}
                </td>
                <td>
                  {changes?.current.level1Folder
                    ? `${changes?.current.level1Folder}`
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.level2Folder ||
            changes?.current.level2Folder) && (
              <tr>
                <td>Level 2 Folder</td>
                <td>
                  {changes?.previous.level2Folder
                    ? `${changes?.previous.level2Folder}`
                    : undefined}
                </td>
                <td>
                  {changes?.current.level2Folder
                    ? `${changes?.current.level2Folder}`
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.status || changes?.current.status) && (
            <tr>
              <td>Status</td>
              <td>
                {changes?.previous.status
                  ? `${changes?.previous.status}`
                  : undefined}
              </td>
              <td>
                {changes?.current.status
                  ? `${changes?.current.status}`
                  : undefined}
              </td>
            </tr>
          )}

          {(changes?.previous.tags || changes?.current.tags) && (
            <tr>
              <td>Tags</td>
              <td>
                {changes?.previous.tags
                  ? changes?.previous.tags.length > 0
                    ? `${changes?.previous.tags.join(", ")}`
                    : "-"
                  : undefined}
              </td>
              <td>
                {changes?.current.tags
                  ? changes?.current.tags.length > 0
                    ? `${changes?.current.tags.join(", ")}`
                    : "-"
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.year || changes?.current.year) && (
            <tr>
              <td>Year</td>
              <td>
                {changes?.previous.year
                  ? `${changes?.previous.year}`
                  : undefined}
              </td>
              <td>
                {changes?.current.year ? `${changes?.current.year}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.projects || changes?.current.projects) && (
            <tr>
              <td>Projects</td>
              <td>
                {changes?.previous.projects
                  ? changes?.previous.projects.length > 0
                    ? `${changes?.previous.projects.join(", ")}`
                    : "-"
                  : undefined}
              </td>
              <td>
                {changes?.current.projects
                  ? changes?.current.projects.length > 0
                    ? `${changes?.current.projects.join(", ")}`
                    : "-"
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.type || changes?.current.type) && (
            <tr>
              <td>Type</td>
              <td>
                {changes?.previous.type
                  ? `${changes?.previous.type}`
                  : undefined}
              </td>
              <td>
                {changes?.current.type ? `${changes?.current.type}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.expirationStatus ||
            changes?.current.expirationStatus) && (
              <tr>
                <td>Expiration Status</td>
                <td>
                  {changes?.previous.expirationStatus
                    ? `${changes?.previous.expirationStatus}`
                    : undefined}
                </td>
                <td>
                  {changes?.current.expirationStatus
                    ? `${changes?.current.expirationStatus}`
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.expirationPeriod ||
            changes?.current.expirationPeriod) && (
              <tr>
                <td>Expiration Period</td>
                <td>
                  {changes?.previous.expirationPeriod
                    ? `${changes?.previous.expirationPeriod}`
                    : undefined}
                </td>
                <td>
                  {changes?.current.expirationPeriod
                    ? `${changes?.current.expirationPeriod}`
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.description || changes?.current.description) && (
            <tr>
              <td>Description</td>
              <td>
                {changes?.previous.description
                  ? `${changes?.previous.description}`
                  : undefined}
              </td>
              <td>
                {changes?.current.description
                  ? `${changes?.current.description}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.size || changes?.current.size) && (
            <tr>
              <td>Size</td>
              <td>
                {changes?.previous.size
                  ? `${changes?.previous.size}`
                  : undefined}
              </td>
              <td>
                {changes?.current.size ? `${changes?.current.size}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.assignee || changes?.current.assignee) && (
            <tr>
              <td>Assignee</td>
              <td>
                {changes?.previous.assignee
                  ? `${changes?.previous.assignee}`
                  : undefined}
              </td>
              <td>
                {changes?.current.assignee
                  ? `${changes?.current.assignee}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.createdBy || changes?.current.createdBy) && (
            <tr>
              <td>Created by</td>
              <td>
                {changes?.previous.createdBy
                  ? `${changes?.previous.createdBy}`
                  : undefined}
              </td>
              <td>
                {changes?.current.createdBy
                  ? `${changes?.current.createdBy}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.created || changes?.current.created) && (
            <tr>
              <td>Created</td>
              <td>
                {changes?.previous.created
                  ? `${changes?.previous.created}`
                  : undefined}
              </td>
              <td>
                {changes?.current.created
                  ? `${changes?.current.created}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.fileCondition ||
            changes?.current.fileCondition) && (
              <tr>
                <td>File Condition</td>
                <td>
                  {changes?.previous.fileCondition
                    ? `${changes?.previous.fileCondition}`
                    : undefined}
                </td>
                <td>
                  {changes?.current.fileCondition
                    ? `${changes?.current.fileCondition}`
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.subject || changes?.current.subject) && (
            <tr>
              <td>Subject</td>
              <td>
                {changes?.previous.subject
                  ? `${changes?.previous.subject}`
                  : undefined}
              </td>
              <td>
                {changes?.current.subject
                  ? `${changes?.current.subject}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.body || changes?.current.body) && (
            <tr>
              <td>Body</td>
              <td>
                {changes?.previous.body
                  ? `${changes?.previous.body}`
                  : undefined}
              </td>
              <td>
                {changes?.current.body ? `${changes?.current.body}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.link || changes?.current.link) && (
            <tr>
              <td>Link</td>
              <td>
                {changes?.previous.link
                  ? `${changes?.previous.link}`
                  : undefined}
              </td>
              <td>
                {changes?.current.link ? `${changes?.current.link}` : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.emailStatus || changes?.current.emailStatus) && (
            <tr>
              <td>Email Status</td>
              <td>
                {changes?.previous.emailStatus
                  ? `${changes?.previous.emailStatus}`
                  : undefined}
              </td>
              <td>
                {changes?.current.emailStatus
                  ? `${changes?.current.emailStatus}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.linkExpiresOn ||
            changes?.current.linkExpiresOn) && (
              <tr>
                <td>Link Expires On</td>
                <td>
                  {changes?.previous.linkExpiresOn
                    ? `${changes?.previous.linkExpiresOn}`
                    : undefined}
                </td>
                <td>
                  {changes?.current.linkExpiresOn
                    ? `${changes?.current.linkExpiresOn}`
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.bccSender || changes?.current.bccSender) && (
            <tr>
              <td>BCC Sender</td>
              <td>
                {changes?.previous.bccSender
                  ? `${changes?.previous.bccSender}`
                  : undefined}
              </td>
              <td>
                {changes?.current.bccSender
                  ? `${changes?.current.bccSender}`
                  : undefined}
              </td>
            </tr>
          )}
          {(changes?.previous.to || changes?.current.to) && (
            <tr>
              <td colSpan={3}>To</td>
            </tr>
          )}
          {(changes?.previous.to?.primaryContacts ||
            changes?.current.to?.primaryContacts) && (
              <tr>
                <td>Primary Contacts</td>
                <td>
                  {changes?.previous.to?.primaryContacts
                    ? changes?.previous.to.primaryContacts.length > 0
                      ? `${changes?.previous.to.primaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.to?.primaryContacts
                    ? changes?.current.to.primaryContacts.length > 0
                      ? `${changes?.current.to.primaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.to?.secondaryContacts ||
            changes?.current.to?.secondaryContacts) && (
              <tr>
                <td>Secondary Contacts</td>
                <td>
                  {changes?.previous.to?.secondaryContacts
                    ? changes?.previous.to.secondaryContacts.length > 0
                      ? `${changes?.previous.to.secondaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.to?.secondaryContacts
                    ? changes?.current.to.secondaryContacts.length > 0
                      ? `${changes?.current.to.secondaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.to?.internalUsers ||
            changes?.current.to?.internalUsers) && (
              <tr>
                <td>Internal Users</td>
                <td>
                  {changes?.previous.to?.internalUsers
                    ? changes?.previous.to.internalUsers.length > 0
                      ? `${changes?.previous.to.internalUsers.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.to?.internalUsers
                    ? changes?.current.to.internalUsers.length > 0
                      ? `${changes?.current.to.internalUsers.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.cc || changes?.current.cc) && (
            <tr>
              <td colSpan={3}>CC</td>
            </tr>
          )}
          {(changes?.previous.cc?.primaryContacts ||
            changes?.current.cc?.primaryContacts) && (
              <tr>
                <td>Primary Contacts</td>
                <td>
                  {changes?.previous.cc?.primaryContacts
                    ? changes?.previous.cc.primaryContacts.length > 0
                      ? `${changes?.previous.cc.primaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.cc?.primaryContacts
                    ? changes?.current.cc.primaryContacts.length > 0
                      ? `${changes?.current.cc.primaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.cc?.secondaryContacts ||
            changes?.current.cc?.secondaryContacts) && (
              <tr>
                <td>Secondary Contacts</td>
                <td>
                  {changes?.previous.cc?.secondaryContacts
                    ? changes?.previous.cc.secondaryContacts.length > 0
                      ? `${changes?.previous.cc.secondaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.cc?.secondaryContacts
                    ? changes?.current.cc.secondaryContacts.length > 0
                      ? `${changes?.current.cc.secondaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.cc?.internalUsers ||
            changes?.current.cc?.internalUsers) && (
              <tr>
                <td>Internal Users</td>
                <td>
                  {changes?.previous.cc?.internalUsers
                    ? changes?.previous.cc.internalUsers.length > 0
                      ? `${changes?.previous.cc.internalUsers.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.cc?.internalUsers
                    ? changes?.current.cc.internalUsers.length > 0
                      ? `${changes?.current.cc.internalUsers.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.bcc || changes?.current.bcc) && (
            <tr>
              <td colSpan={3}>BCC</td>
            </tr>
          )}
          {(changes?.previous.bcc?.primaryContacts ||
            changes?.current.bcc?.primaryContacts) && (
              <tr>
                <td>Primary Contacts</td>
                <td>
                  {changes?.previous.bcc?.primaryContacts
                    ? changes?.previous.bcc.primaryContacts.length > 0
                      ? `${changes?.previous.bcc.primaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.bcc?.primaryContacts
                    ? changes?.current.bcc.primaryContacts.length > 0
                      ? `${changes?.current.bcc.primaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.bcc?.secondaryContacts ||
            changes?.current.bcc?.secondaryContacts) && (
              <tr>
                <td>Secondary Contacts</td>
                <td>
                  {changes?.previous.bcc?.secondaryContacts
                    ? changes?.previous.bcc.secondaryContacts.length > 0
                      ? `${changes?.previous.bcc.secondaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.bcc?.secondaryContacts
                    ? changes?.current.bcc.secondaryContacts.length > 0
                      ? `${changes?.current.bcc.secondaryContacts.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.bcc?.internalUsers ||
            changes?.current.bcc?.internalUsers) && (
              <tr>
                <td>Internal Users</td>
                <td>
                  {changes?.previous.bcc?.internalUsers
                    ? changes?.previous.bcc.internalUsers.length > 0
                      ? `${changes?.previous.bcc.internalUsers.join(", ")}`
                      : "-"
                    : undefined}
                </td>
                <td>
                  {changes?.current.bcc?.internalUsers
                    ? changes?.current.bcc.internalUsers.length > 0
                      ? `${changes?.current.bcc.internalUsers.join(", ")}`
                      : "-"
                    : undefined}
                </td>
              </tr>
            )}
          {(changes?.previous.filePreview || changes?.current.filePreview) && (
            <tr>
              <td>File Preview</td>
              <td>
                {changes?.previous.filePreview ? (
                  changes?.previous.filePreview === "-" ? (
                    "-"
                  ) : (
                    <li>
                      <div className={"yjPropertiesDetailPreview"}></div>
                    </li>
                  )
                ) : undefined}
              </td>

              <td>
                {changes?.current.filePreview ? (
                  changes?.current.filePreview === "-" ? (
                    "-"
                  ) : (
                    <li>
                      <div className={"yjPropertiesDetailPreview"}></div>
                    </li>
                  )
                ) : undefined}
              </td>
            </tr>
          )}
        </table>
      </Fragment>
    );
  };

  const columns: ColumnsType<any> = [
    {
      title: "Request ID",
      dataIndex: "id",
      key: "id",
      sorter: (a: any, b: any) => a.id.localeCompare(b.id),
      ellipsis: true,
      ...getColumnSearchProps("id"),
    },
    {
      title: "Request Name",
      dataIndex: "requestName",
      key: "requestName",
      sorter: (a: any, b: any) => a.file.localeCompare(b.file),
      ...getColumnSearchProps("requestName"),
    },
    {
      title: "User Name",
      dataIndex: "userName",
      key: "userName",
      sorter: (a: any, b: any) => a.userName.localeCompare(b.userName),
      ...getColumnSearchProps("userName"),
    },
    {
      title: "Date - Time",
      dataIndex: "dateTime",
      key: "dateTime",
      sorter: (a: any, b: any) => a.dateTime.localeCompare(b.dateTime),
      ...getColumnSearchProps("dateTime"),
    },
    {
      title: "Action",
      dataIndex: "action",
      key: "action",
      sorter: (a: any, b: any) => a.action.localeCompare(b.action),
      ...getColumnSearchProps("action"),
    },
    {
      title: "Changes",
      dataIndex: "changes",
      key: "changes",
      render: (text, record, index) => (
        <Button
          disabled={record.changes === null}
          onClick={() => onClickShowChanges(record)}
          shape="circle"
          icon={<EyeOutlined />}
          className={styles.yjChangeHistoryView}
        />
      ),
    },
  ];

  return (
    <Fragment>
      <PageTitle title={`History`} />
      <PageContent>
        <Modal
          visible={showChangesModal}
          title={"Changes"}
          className="yjCommonModalSmall"
          maskClosable={false}
          destroyOnClose={true}
          onCancel={() => setShowChangesModal(false)}
          footer={[
            <Button
              key="back"
              type="default"
              onClick={() => setShowChangesModal(false)}
            >
              cancel
            </Button>,
          ]}
        >
          <Row gutter={16}>
            <Col span={24}>{renderChanges()}</Col>
          </Row>
        </Modal>
        {/* <Tooltip
          placement="topLeft"
          title={"This feature is coming soon"}
          color="#78bf59"
        >
          <div style={{ opacity: 0.5 }}>
            <Table
              columns={columns}
              dataSource={portalFileSectionHistoryMockData}
              className={styles.yjHistoryTblWrapper}
            />
          </div>
        </Tooltip> */}
      </PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
