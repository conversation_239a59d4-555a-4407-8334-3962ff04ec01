import { successNotification } from '@app/utils/antNotifications';
import { GenericFilter } from '../types';
import logger from '@app/utils/logger';

export const prepareFilterObject = (filters: GenericFilter[]) => {
  const obj: any = {};

  filters.forEach((value) => {
    if (!value.isArray) {
      obj[value.key] = value.filterType === 'search'
          ? value.value
          : isNaN(value.value) ? value.value : Number(value.value);
    } else {
      if (!obj[value.key]) {
        obj[value.key] = [];
      }

      if (typeof value.value === 'boolean') {
        obj[value.key].push(value.value);
      } else if (typeof value.value === 'string' || isNaN(value.value)) {
        obj[value.key].push(value.value);
      } else {
        obj[value.key].push(value.value[0] === '0' ? value.value : Number(value.value));
      }
    }
  });
  return obj;
};

export type Sorter = {
  value: string;
  order: SortOrder;
};

export type SortOrder = 'descend' | 'ascend';

export const copyToClipboard = (param: string) => {
  let url = new URL(window.location.href);
  let link = `${url.origin}${url.pathname}?ids=${encodeURIComponent(param)}`;

  // Fallback method using a hidden textarea
  const fallbackCopyToClipboard = (text:string) => {
    const textarea = document.createElement('textarea');
    textarea.value = text;
    document.body.appendChild(textarea);
    textarea.select();
    try {
      document.execCommand('copy');
      successNotification([''], 'File link copied to clipboard');
    } catch (err) {
      logger.error('GenericDataTable','copyToClipboard',err)
      successNotification([''], 'File link copied to clipboard');
    }
    document.body.removeChild(textarea);
  };

  // Attempt to use the Clipboard API
  if (navigator.clipboard) {
    navigator.clipboard.writeText(link)
        .then(() => {
          successNotification([''], 'File link copied to clipboard');
        })
        .catch((err) => {
          logger.error('GenericDataTable','copyToClipboard',err)
          fallbackCopyToClipboard(link);
        });
  } else {
    fallbackCopyToClipboard(link);
  }
};