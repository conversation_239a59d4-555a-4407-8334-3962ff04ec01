import { getParameterizedUrlWith } from "@app/utils";
import config, { getApiUrl } from "@app/utils/config";
import http, { httpVerbs } from "@app/utils/http";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";

export const createContact = (contactData: any) => {
  return http({
    method: httpVerbs.POST,
    url: getApiUrl(
      config.api[OperationalServiceTypes.MasterDataService].createContact
    ),
    data: contactData,
  });
};

export const checkContactExsistance = (email: any) => {
  return http({
    method: httpVerbs.HEAD,
    url: config.api[OperationalServiceTypes.MasterDataService].createContact,
    params: { email: email },
  });
};

export const getSiteContacts = (siteId: string) => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrlWith(
      config.api[OperationalServiceTypes.MasterDataService].contactsBysiteId,
      [{ name: "siteId", value: siteId }]
    ),
    params: { limit: 50 },
  });
};
