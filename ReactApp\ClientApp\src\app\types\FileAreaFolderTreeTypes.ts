export interface ITreeNode {
  title: string;
  key: string;
  isLeaf: boolean;
  children: ITreeNode[];
  icon: any;
  id: number;
  className?: any;
  retention?: any;
  disabled?: boolean;
  selectable?: boolean;
}

export interface ITreeResponseNode {
  title: string;
  key: string;
  children: ITreeResponseNode[];
}

export interface IFolderTreeResponse {
  siteId: string;
  siteName: string;
  siteStatusId: number;
  folders: IFolder[] | undefined;
}

export interface IFolder {
  id: number;
  name: string;
  subFolders: IFolder[];
  retention?: any;
}
