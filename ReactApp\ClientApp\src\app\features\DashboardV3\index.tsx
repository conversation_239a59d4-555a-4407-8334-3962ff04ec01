import React from "react";
import { <PERSON><PERSON>, <PERSON>, Collapse, Row, Tooltip } from "antd";

import "./index.module.less";
import config from "@app/utils/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import GenericDataTable from "@app/components/GenericDataTable";
import styles from "./index.module.less";
import { encrypt } from "@app/utils/crypto/cryptoText";
import { FolderOpenOutlined } from "@ant-design/icons/lib";
import { useHistory } from "react-router-dom";
import { FormattedDateTime } from "@app/components/FormattedDateTime";

const ROW_GUTTER_VALUE_TWELVE = 12;
const { Panel } = Collapse;

export default (props: any) => {
  let history = useHistory();

  const addTextWrap = (value: any) => {
    return (
      <div className={"yjGridTextWrap"}>
        <Tooltip placement="topLeft" title={value}>
          {value}
        </Tooltip>
      </div>
    );
  };

  const renderSiteGridColumns = () => {
    return {
      siteName: (value: any) => {
        return addTextWrap(value);
      },
      accessedDateTime: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      action: (text: any, record: any) => {
        return (
          <div className={styles.yjActionIconWrapper}>
            <Tooltip title="File Area">
              <Button
                className={styles.yjDashboardFileAreaIcon}
                onClick={() => {
                  const enCryptedName = encrypt(record.siteName);
                  const url = `/client-file-area/${record.channelId}/${record.siteId}/${enCryptedName}`;
                  const encodedUrl = encodeURI(url);
                  history.push(encodedUrl);
                }}
                icon={<FolderOpenOutlined />}
              />
            </Tooltip>
          </div>
        );
      },
    };
  };

  const renderGridColumns = () => {
    return {
      fileName: (value: any) => {
        return addTextWrap(value);
      },
      siteName: (value: any) => {
        return addTextWrap(value);
      },
      accessedDateTime: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      action: (text: any, record: any) => {
        return (
          <div className={styles.yjActionIconWrapper}>
            <Tooltip title="File Area">
              <Button
                className={styles.yjDashboardFileAreaIcon}
                onClick={() => {
                  const enCryptedName = encrypt(record.siteName);
                  const url = `/client-file-area/${record.channelId}/${record.siteId}/${enCryptedName}/?ids=${record.fileId}`;
                  const encodedUrl = encodeURI(url);
                  history.push(encodedUrl);
                }}
                icon={<FolderOpenOutlined />}
              />
            </Tooltip>
          </div>
        );
      },
    };
  };

  return (
    <Row gutter={[ROW_GUTTER_VALUE_TWELVE, ROW_GUTTER_VALUE_TWELVE]}>
      <Col span={15} className="yjCommonAccordian yjPieChartWrapper pxDashboard">
        <Collapse defaultActiveKey={1} expandIconPosition={"right"}>
          <Panel header="RECENT FILES" key="1">
            <GenericDataTable
              hidePagination={true}
              hideHeaderPanel={true}
              lowResolutionWidth="auto"
              highResolutionWidth="auto"
              scrollColumnCounter={6}
              endpoint={config.api[OperationalServiceTypes.FileManagementService].lastAccessedFiles}
              tableKey={'lastAccessedFiles'}
              customRender={renderGridColumns()}
            />
          </Panel>
        </Collapse>
      </Col>
      <Col span={9} className="yjCommonAccordian yjPieChartWrapper pxDashboard">
        <Collapse defaultActiveKey={2} expandIconPosition={"right"}>
          <Panel header="RECENT SITES" key="2">
            <GenericDataTable
              hidePagination={true}
              hideHeaderPanel={true}
              lowResolutionWidth="auto"
              highResolutionWidth="auto"
              scrollColumnCounter={6}
              endpoint={config.api[OperationalServiceTypes.FileManagementService].lastAccessedSites}
              tableKey={'lastAccessedSites'}
              customRender={renderSiteGridColumns()}
            />
          </Panel>
        </Collapse>
      </Col>
    </Row>
  );
};
