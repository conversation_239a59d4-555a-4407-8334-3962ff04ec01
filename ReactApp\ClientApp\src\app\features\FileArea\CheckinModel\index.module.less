@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjFileAreaButtonGroupWrapper {

  button {
    margin: 0 .2em;
  }

  .flex-mixin(center, flex, center);
}

.yjCopyLink {
  color: @color-primary;
  cursor: pointer;
  padding-right: 10px;
  text-decoration: underline;
}

.yjCheckInFileNameArea {
  justify-content: space-between;
  margin-bottom: 5px;

  .yjCheckInFileTitle {
    flex-basis: 15%;

    .flex-mixin(center, flex, flex-start);
  }

  .yjCheckInFileName {
    width: 85%;
  }

  .flex-mixin(center, flex, space-between);
}

.yjCheckInButtonGroupWrapper {
  flex-basis: 20%;

  .flex-mixin(center, flex, flex-end);
}