import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import ComplianceManagement from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

jest.mock("../index.module.less", () => ({
    sampleStyle: "sampleStyle",
}));

describe("ComplianceManagement Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const cmComponent = shallow(<ComplianceManagement />);
        expect(cmComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const cmComponent = renderer.create(<ComplianceManagement />).toJSON();
        expect(cmComponent).toMatchSnapshot();
    });

    it("should have a div element",() => {
        const cmComponent = shallow(<ComplianceManagement />);
        expect(cmComponent.find(".sampleStyle")).toHaveLength(1);
    });
});
