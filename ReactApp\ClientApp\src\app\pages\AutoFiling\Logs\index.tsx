import React, { useRef, useState } from 'react';
import { <PERSON><PERSON>, Button, Skeleton } from 'antd';
import PageTitle from '@app/components/PageTitle';
import {LinkOutlined, PlusOutlined} from '@ant-design/icons/lib';
import PageContent from '@app/components/PageContent';
import { useForm } from 'antd/lib/form/Form';
import logger from '@app/utils/logger';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import AutoFilingAddEditDrawer from "@app/features/AutoFiling/AutoFilingAddEditDrawer";
import {useHistory, withRouter} from "react-router";
import AutoFilingLogsGrid from "@app/features/AutoFilingLogs/AutoFilingLogsGrid";
import { createAutoFilingRule, updateAutoFilingRule } from "@app/api/autoFilingService";

const initialFolderData = [
  {
    id: 1,
    name: 'Primary folder',
    retention: false,
    presist: false,
    subFolders: [
      {
        id: 2,
        name: 'Secondary folder (1)',
        subFolders: [],
        retention: false,
        presist: false,
      },
      {
        id: 3,
        name: 'Secondary folder (2)',
        subFolders: [],
        retention: false,
        presist: false,
      },
    ],
  },
];

const AutoFilingLogsPage = (props: any) => {
  let [showDrawer, setShowDrawer] = useState(false);
  let [form] = useForm();
  const { userPermission } = useSelector( (state: RootState) => state.userManagement );
  const ref = useRef<{ refresh: () => void }>(null);
  const history = useHistory();


  const refresh = () => {
    if (ref.current) {
      ref.current.refresh();
    }
  };
  const openRuleSetupDrawer = (data:any) => {
    logger.debug('Auto filling logs','openRuleSetupDrawer',data?.status?.value ===1 ? true: false);
    form?.setFieldsValue({
      id: data?.id,
      name: data?.name,
      description: data?.description,
      templateStatus: !data?.id? true: data?.status?.value ===1 ? true: false,
      hide: data?.hide,
      templateFolders: data?.templateFolders,
    })
    setShowDrawer(true);
  };

  const openRuleSetupDeleteConfirm = (data:any) => {
      logger.debug('AutoFilling', 'openRuleSetupDeleteConfirm', data);

  }

  const closeDrawer = () => {
    form.resetFields();
    setShowDrawer(false);
  };

  const formatFolderData = (data: any[]): any[] => {
    return data.map((e) => (e.children ? { id: e.id, name: e.folderName, retention: e.retention, subFolders: formatFolderData(e.children) } : { id: e.id, name: e.folderName, retention: e.retention }));
  };

  const onSuccess = (values: any) => {
    values.templateFolders = formatFolderData(values.templateFolders);
    values.templateStatus = values.templateStatus ? 1 : 2;
    if(values.id){
        updateAutoFilingRule(values)
          .then(() => {
            successNotification([''], 'AutoFiling Rule has been updated successfully');
            refresh();
            closeDrawer();
          })
          .catch((e) => {
            errorNotification([''], 'Error updating AutoFiling Rule');
            logger.error('Master Data Module', 'Create Site', e);
          });
    }else {
      createAutoFilingRule(values)
          .then(() => {
            successNotification([''], 'AutoFiling Rule has been created successfully');
            refresh();
            closeDrawer();
          })
          .catch((e) => {
            errorNotification([''], 'Error creating AutoFiling Rule');
            logger.error('Master Data Module', 'Create Site', e);
          });
    }

  };

  return (
    <>
      <PageTitle title={props.title}>
          {userPermission.privDMSCanCreateTemplates &&
          <Button  data-testid="create-template-button" onClick={() => history.push("/document-maintenance/auto-filing-setting")} type="primary" icon={<LinkOutlined />}>
              AutoFiling Settings
          </Button>
          }
      </PageTitle>
      <PageContent>
        {/*{userPermission.privDMSCanManageAutoFiling && <AutoFilingAddEditDrawer*/}
        {/*    data-testid="template-grid"*/}
        {/*    initialData={ form.getFieldValue('templateFolders')  ?? initialFolderData}*/}
        {/*    formRef={form}*/}
        {/*    onSuccess={onSuccess}*/}
        {/*    onDrawerClose={closeDrawer}*/}
        {/*    showDrawer={showDrawer}*/}
        {/*/>}*/}
        {userPermission.privDMSCanManageAutoFiling && <AutoFilingLogsGrid ref={ref} onEdit={(data:any) => openRuleSetupDrawer(data)} onDelete={(data:any) => openRuleSetupDeleteConfirm(data)}/>}
        {userPermission.privDMSCanManageAutoFiling===false &&  <Alert message="You do not have permission to access any logs. Contact your organization's administrator for assistance" type="info" showIcon closable /> }
        {userPermission.privDMSCanManageAutoFiling === undefined && (
          <Skeleton />
        )}
      </PageContent>
    </>
  );
};

export default withRouter(AutoFilingLogsPage);
;
