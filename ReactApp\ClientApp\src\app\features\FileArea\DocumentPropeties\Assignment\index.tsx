import React, { useState, useEffect } from "react";
import { List, Skeleton } from "antd";

import { getFileCountStatus } from "../Checkout";
import styles from "../index.module.less";
import { getAssignmentFileHistoryListByFileId } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { errorNotification } from "@app/utils/antNotifications";
import { DATA_RETRIEVE_ERROR_MESSAGE } from "@app/features/FileArea/DocumentPropeties";
import { FORBIDDEN_ERROR_CODE, getLocalDateTime } from "@app/utils";

const LABEL_ASSIGNED_DATE = "Date and Time";
const LABEL_ASSIGNED_TO = "Assigned to";
const LABEL_ASSIGNED_BY = "Assigned By";
const LABEL_NOTE = "Note";

const TEXT_ASSIGNMENTS = "Assignment(s)";

export type AssignmentHistory = {
  assignedDate: string;
  notes: string;
  assignedBy: string;
  assignedTo: string;
};

export default (props: { fileId: string }) => {
  const [processingTrigger, setProcessingTrigger] = useState(true);
  const [assignmentHistoryList, setAssignmentHistoryList] = useState<
    AssignmentHistory[]
  >([]);
  const [
    customizedAssignmentHistoryList,
    setCustomizedCheckoutHistoryList,
  ] = useState<any[]>([]);

  useEffect(() => {
    fetchAssignmentHistoryFileList();
    return () => {
      setProcessingTrigger(true);
    };
  }, [props.fileId]);

  useEffect(() => {
    const customizedItemList = assignmentHistoryList.map((assignment) => {
      return [
        {
          name: LABEL_ASSIGNED_DATE,
          value: getLocalDateTime(assignment.assignedDate),
        },
        { name: LABEL_ASSIGNED_TO, value: assignment.assignedTo },
        { name: LABEL_ASSIGNED_BY, value: assignment.assignedBy },
        { name: LABEL_NOTE, value: assignment.notes },
      ];
    });
    setCustomizedCheckoutHistoryList(customizedItemList);
  }, [assignmentHistoryList]);

  const fetchAssignmentHistoryFileList = () => {
    getAssignmentFileHistoryListByFileId(props.fileId)
      .then((response) => {
        setAssignmentHistoryList(response.data);
        setProcessingTrigger(false);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        } else {
          errorNotification([""], DATA_RETRIEVE_ERROR_MESSAGE);
        }
        logger.error(
          "Document Properties",
          "Retrieve Checkout History List",
          error
        );
      });
  };

  const renderAssignmentsItem = (item: any) => {
    return (
      <List.Item className={styles.yjPropertiesAssignmentsListItem}>
        <List.Item.Meta
          title={
            <p className={styles.yjPropertiesAssignmentsTitle}>{item.name}</p>
          }
          description={
            <span className={styles.yjPropertiesAssignmentsDescription}>
              {item.value}
            </span>
          }
        />
      </List.Item>
    );
  };

  return (
    <>
      {!processingTrigger ? (
        <div className={styles.yjPropertiesAssignmentsTab}>
          <p className={styles.yjPropertiesAssignmentsNotifications}>
            {getFileCountStatus(
              customizedAssignmentHistoryList.length,
              TEXT_ASSIGNMENTS
            )}
          </p>
          {customizedAssignmentHistoryList.map((assignmentItem, index) => (
            <div className={styles.yjPropertiesAssignmentsList}>
              <List
                key={index}
                itemLayout="horizontal"
                dataSource={assignmentItem}
                renderItem={(item) => renderAssignmentsItem(item)}
              />
            </div>
          ))}
        </div>
      ) : (
        <Skeleton active={true} />
      )}
    </>
  );
};
