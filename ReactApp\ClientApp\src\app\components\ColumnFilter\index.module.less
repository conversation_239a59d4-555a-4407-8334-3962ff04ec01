@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjTableHeaderWrapper {
  .flex-mixin(center, flex, flex-end);
}

.yjFilterCloudWrapper {
  flex-wrap: wrap;
  margin: 0 1em;
  max-height: 8vh;
  overflow-x: hidden;
  overflow-y: auto;

  .yjFilterTags {
    background-color: @color-bg-grid-filterby-tag;
    border-color: @color-border-grid-filterby-tag;
    border-radius: 3em;
    color: @color-font-grid-filterby-tag;
    display: flex;
    float: left;
    margin-bottom: 1.5em;
    overflow: hidden;
    padding: .3em .8em;
    text-overflow: ellipsis;
    white-space: nowrap;

    span {
      color: @color-font-grid-filterby-tag-icon;
      margin-top: 4px;
    }

    .yjFilterTagText {
      margin-top: 0;
      max-width: 275px;
      overflow: hidden;
      padding-right: 10px;
      position: relative;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  .yjFilterTextLabel {
    color: #ffffff;
    font-size: small;
    margin-right: 1em;
    position: relative;
    text-transform: uppercase;
    top: 3px;

    .font-mixin(@font-primary, @yjff-semibold);
  }

  .flex-mixin(center, flex, flex-start);
}

.yjFilterArea {
  background-color: #24303b;
  box-shadow: 0 6px 7px -6px @color-filter-area-shadow;
}

.yjSelectDeSelect {
  font-weight: 700;
}