@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjPortalRequestWrapper {
  margin: -24px;
  max-height: 63vh;
  overflow: hidden auto;
  padding: 1.5em;
}

.yjPortalRequestFormRowItem {
  margin-bottom: 1em;

  label {
    display: flex;
    text-align: left;
  }

  .yjPortalRequestCheckbox {
    margin-top: 1em;
  }
}

.yjDatePicketRowItemLabel {
  float: left;
  width: 17%;
}

.yjDatePicketRowItem {
  float: left;
  width: 25%;
}

@media @breakpoint-tablet {

  .yjDatePicketRowItemLabel {
    width: 27%;
  }

  .yjDatePicketRowItem {
    float: left;
    width: 23%;
  }
}
