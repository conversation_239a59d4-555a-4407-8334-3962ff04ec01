@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjSideTrigger {
  color: @color-mainmenu-icon;
  font-size: 1.7em;
  line-height: 2em;
  padding: 0 1em;
  transition: color .3s;
}

.yjSideNav {
  box-shadow: 2px 0 6px rgba(0, 21, 41, .35);
  height: 100%;
  position: fixed;
  z-index: 1000;
}

.yjSideNav,
.yjSideMenu {
  background: #24303b;
  flex-grow: 1;

  .yjSubMenuItem {
    background: #24303b;

    ul {
      background: transparent !important;
    }
  }
}