@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjPortalUploadActionButtonPanel {
  background: @color-bg-portal-action-button-wrapper;
  border-bottom: 1px solid #ffffff;
  padding: .4em 0;

  button {
    background-color: transparent;
    border: none;
    box-shadow: none;
    color: #ffffff;
    font-weight: @yjff-black;

    &:hover {
      background: none;
    }

    &:active {
      background: none;
    }

    &:focus {
      background: none;
    }

    &:first-child {
      border-right: 1px solid fade(@color-accent-border, 10%);
      
    }

    span {
      font-size: @font-size-base / 1.2;
      color: #ffffff;
    }
  }
  .yjActionListWrapper {
    color: @color-font-filearea-action-list;
    padding: 0 0.66em;
    text-align: center;
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }

   
    button {
      background-color: transparent;
      border: none;
      box-shadow: none;
      color: #ffffff;
      font-weight: @yjff-black;
  
      &:hover {
        background: none;
      }
  
      &:active {
        background: none;
      }
  
      &:focus {
        background: none;
      }
  
      &:first-child {
        border-right: 1px solid fade(@color-accent-border, 10%);
      }
  
      span {
        font-size: @font-size-base / 1.2;
      }
    }
    

  }

}
