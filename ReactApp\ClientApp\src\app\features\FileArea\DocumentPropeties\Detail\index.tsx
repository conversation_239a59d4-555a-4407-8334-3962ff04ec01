import React, { useEffect, useState } from "react";
import { List, Skeleton } from "antd";
import { useHistory } from "react-router-dom";

import styles from "@app/features/FileArea/DocumentPropeties/index.module.less";
import { getFileDetailsByFileId } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { errorNotification } from "@app/utils/antNotifications";
import { DATA_RETRIEVE_ERROR_MESSAGE } from "@app/features/FileArea/DocumentPropeties";
import { FORBIDDEN_ERROR_CODE, getLocalDateTime } from "@app/utils";

const commaSeparatingReducer = (accu: any, current: any, index: number) => {
  return index === 0
    ? accu.toString().concat(current.name)
    : accu.toString().concat(",", current.name);
};

const mapFolderPath = (folderDetail: any) => {
  return folderDetail.parent
    ? folderDetail.parent.name.toString().concat("/", folderDetail.name)
    : folderDetail.name;
};

const LABEL_TYPE = "Type";
const LABEL_SIZE = "Size";
const LABEL_YEAR = "Year";
const LABEL_STATUS = "Status";
const LABEL_ASSIGNEE = "Assignee";
const LABEL_PATH = "Path";
const LABEL_CREATED = "Created";
const LABEL_CREATED_BY = "Created by";
const LABEL_MODIFIED = "Modified";
const LABEL_PROJECTS = "Projects";
const LABEL_TAGS = "Tags";
const LABEL_EXPIRATION_DATE = "expiration date";
const LABEL_EXPIRATION_STATUS = "expiration status";
const LABEL_DESCRIPTION = "Description";

export default (props: { fileId: string }) => {
  const [processingTrigger, setProcessingTrigger] = useState(true);
  const [fileHistoryDetail, setFileHistoryDetail] = useState<any[]>([]);
  const history = useHistory();

  useEffect(() => {
    getFileDetailsByFileId(props.fileId)
      .then(({ data }: any) => {
        const customizedFileDetails = [
          { name: LABEL_TYPE, value: data.type },
          { name: LABEL_SIZE, value: data.size },
          { name: LABEL_YEAR, value: data.year },
          { name: LABEL_STATUS, value: data.status?.name },
          { name: LABEL_ASSIGNEE, value: data.assignee?.name },
          { name: LABEL_PATH, value: mapFolderPath(data.folder) },
          { name: LABEL_CREATED, value: getLocalDateTime(data.created) },
          { name: LABEL_CREATED_BY, value: data.createdBy?.name },
          { name: LABEL_MODIFIED, value: getLocalDateTime(data.modified) },
          {
            name: LABEL_PROJECTS,
            value: data.projects?.reduce(commaSeparatingReducer, ""),
          },
          {
            name: LABEL_TAGS,
            value: data.tags?.reduce(commaSeparatingReducer, ""),
          },
          {
            name: LABEL_EXPIRATION_DATE,
            value: getLocalDateTime(data.expirationDate),
          },
          { name: LABEL_EXPIRATION_STATUS, value: data.expirationStatus },
          { name: LABEL_DESCRIPTION, value: data.description },
        ];
        setFileHistoryDetail(customizedFileDetails);
        setProcessingTrigger(false);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        } else {
          errorNotification([""], DATA_RETRIEVE_ERROR_MESSAGE);
        }
        logger.error(
          "Document Properties",
          "Retrieve File History Details",
          error
        );
      });
    return () => {
      setProcessingTrigger(true);
    };
  }, [props.fileId]);

  const renderDetailsItem = (item: any) => {
    return (
      <List.Item className={styles.yjPropertiesDetailListItem}>
        <List.Item.Meta
          title={<p className={styles.yjPropertiesDetailTitle}>{item.name}</p>}
          description={
            <span className={styles.yjPropertiesDetailDescription}>
              {item.value}
            </span>
          }
        />
      </List.Item>
    );
  };

  return (
    <>
      {!processingTrigger ? (
        <div className={styles.yjPropertiesDetailTab}>
          {/* <div className={styles.yjPropertiesDetailPreview}></div> */}
          <div className={styles.yjPropertiesDetailInfo}>
            <List
              className={styles.yjPropertiesDetailList}
              itemLayout="horizontal"
              dataSource={fileHistoryDetail}
              renderItem={(item) => renderDetailsItem(item)}
            />
          </div>
        </div>
      ) : (
        <Skeleton active={true} />
      )}
    </>
  );
};
