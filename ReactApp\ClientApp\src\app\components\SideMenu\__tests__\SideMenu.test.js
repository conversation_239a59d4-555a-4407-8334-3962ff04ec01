import { mount } from "enzyme";
import SideMenu from "..";
import React from "react";
import { MemoryRouter } from "react-router";
import rrd from "react-router-dom";

const menuConfig = [
  {
    path: "/onboarding/",
    title: "Organization Onboarding",
    key: "OrganizationOnboarding",
    guard: [],
    privilaged: true,
    icon: "testClass",
    children: [
      {
        path: "/onboarding/license-management",
        title: "License Management",
        key: "LicenseManagement",
        icon: "testClass",
        guard: [],
      },
    ],
  },
  {
    path: "/filearea/",
    title: "File Area",
    key: "FileArea",
    guard: [],
    privilaged: false,
    icon: "testClass",
    children: [
      {
        path: "/filearea/FileAreaMenu1",
        title: "File Area Menu 1",
        key: "FileAreaMenu1",
        icon: "testClass",
        guard: [],
      },
    ],
  },
];

jest.spyOn(rrd, "BrowserRouter").mockImplementation(({ children }) => children);

describe("<SideMenu />", () => {
  it("should have given submenu item", () => {
    const component = mount(
      <MemoryRouter initialEntries={["/onboarding/organization-management"]}>
        <SideMenu menu={menuConfig} />
      </MemoryRouter>
    );

    // TODO: enzyme doesn't render child components rendered through a function, have to find a solution.
  });
});
