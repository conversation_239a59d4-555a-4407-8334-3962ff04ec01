import FilterCloudTag from "..";
import { mount, shallow } from "enzyme";
import React from "react";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";
import renderer from "react-test-renderer";
import { Tag } from "antd";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const createTagElement = () => {
  const INITIAL_STATE = {
    fileArea: {
      displayPortalFilesBulkDelete: true,
    },
    grid: {
      selectedElement: {},
      tableKey: "",
      filters: [],
      selected: true,
    },
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <FilterCloudTag />
    </ReduxProvider>
  );
};

describe("<FilterCloudTag/>", () => {
  it("FilterCloudTag Component Should Render", () => {
    const component = shallow(createTagElement());
    expect(component.html()).not.toBe(null);
  });
  it("FilterCloudTag Component Should Render and create the snapshot properly", () => {
    const component = renderer.create(createTagElement()).toJSON();
    expect(component).toMatchSnapshot();
  });
});
