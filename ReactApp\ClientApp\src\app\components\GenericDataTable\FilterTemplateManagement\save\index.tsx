import { Form, Input } from "antd";
import React from "react";

export default (props: any) => {
  const { isNameValid, newFilterName, onFilterNameChangeHandler } = props;

  return (
    <>
      <Form>
        <Form.Item
          validateStatus={isNameValid ? "success" : "error"}
          help={isNameValid ? "" : "Name Already Exists"}
        >
          <Input
            placeholder={"Enter Filter Name"}
            maxLength={30}
            autoComplete="off"
            value={newFilterName}
            onChange={(e) => onFilterNameChangeHandler(e.target.value)}
          />
        </Form.Item>
      </Form>
    </>
  );
};
