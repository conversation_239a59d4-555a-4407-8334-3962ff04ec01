import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import TechnicalConfigurations from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

jest.mock("../index.module.less", () => ({
    sampleStyle: "sampleStyle",
}));

describe("TechnicalConfigurations Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const tcComponent = shallow(<TechnicalConfigurations />);
        expect(tcComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const tcComponent = renderer.create(<TechnicalConfigurations />).toJSON();
        expect(tcComponent).toMatchSnapshot();
    });

    it("should have a div element",() => {
        const tcComponent = shallow(<TechnicalConfigurations />);
        expect(tcComponent.find(".sampleStyle")).toHaveLength(1);
    });
});
