import * as types from "@app/redux/actionTypes/fileAreaActionType";
import * as actions from "@app/redux/actions/fileAreaActions";
import { formActions } from "@app/types";

describe("Redux -> File Area Actions", () => {
  it("should generate a valid action to set folder tree Data", () => {
    const data = { siteId: "S-001", siteName: "Wolf", folders: [] };
    const expectedAction = {
      type: types.SET_FOLDER_TREE,
      payload: data,
    };
    expect(actions.setFolderTree(data)).toEqual(expectedAction);
  });
  it("should generate a valid action to set set FileAreaSettings", () => {
    const data = {
      internalFileCheckInCheckout: true,
      internalFileDownload: true,
      internalFileEmailNotifications: true,
      internalFileRecategorize: false,
      internalFileSetAssignee: true,
      internalFileSetExpiration: true,
      internalFileSetProject: true,
      internalFileStatusUpdate: true,
      urlFileUpload: true,
    };
    const expectedAction = {
      type: types.SET_FILE_AREA_SETTINGS,
      payload: data,
    };
    expect(actions.setFileAreaSettings(data)).toEqual(expectedAction);
  });

  // it("should generate a valid action to set Common Data avilability", () => {
  //   const expectedActionTrue = {
  //     type: types.SET_HAS_COMMON_DATA,
  //     payload: true,
  //   };
  //   expect(actions.setHasCommonData(true)).toEqual(expectedActionTrue);
  // });

  it("should generate a valid action to set updateContextMenuDownloadOption", () => {
    const expectedActionTrue = {
      type: types.UPDATE_CONTEXT_MENU_DOWNLOAD,
      payload: true,
    };
    expect(actions.updateContextMenuDownloadOption(true)).toEqual(
      expectedActionTrue
    );
  });
  it("should generate a valid action to set updateContextMenuStatusOption", () => {
    const expectedActionTrue = {
      type: types.UPDATE_CONTEXT_MENU_STATUS,
      payload: true,
    };
    expect(actions.updateContextMenuStatusOption(true)).toEqual(
      expectedActionTrue
    );
  });

  it("should generate a valid action to set updateContextMenuAssignOption", () => {
    const expectedActionTrue = {
      type: types.UPDATE_CONTEXT_MENU_ASSIGN,
      payload: true,
    };
    expect(actions.updateContextMenuAssignOption(true)).toEqual(
      expectedActionTrue
    );
  });
  it("should generate a valid action to set updateContextMenuCheckoutOption", () => {
    const expectedActionTrue = {
      type: types.UPDATE_CONTEXT_MENU_CHECKOUT,
      payload: true,
    };
    expect(actions.updateContextMenuCheckoutOption(true)).toEqual(
      expectedActionTrue
    );
  });

  it("should generate a valid action to set updateLoadGridOption", () => {
    const expectedActionTrue = {
      type: types.UPDATE_LOAD_GRID,
      payload: true,
    };
    expect(actions.updateLoadGridOption(true)).toEqual(expectedActionTrue);
  });

  it("should generate a valid action to set updateContextMenuPropetiesoption", () => {
    const expectedActionTrue = {
      type: types.UPDATE_CONTEXT_MENU_PROPETIES,
      payload: true,
    };
    expect(actions.updateContextMenuPropetiesoption(true)).toEqual(
      expectedActionTrue
    );
  });

  it("should generate a valid action to set updatePortalFIlesUploadDetails", () => {
    const data = {
      requestId: "ddff44ffffe44555555",
      securityKey: "sssssssss",
      siteId: "S-0111",
    };
    const expectedActionTrue = {
      type: types.UPDATE_PORTAL_FILE_UPLOAD_DETAILS,
      payload: data,
    };
    expect(actions.updatePortalFIlesUploadDetails(data)).toEqual(
      expectedActionTrue
    );
  });
  it("should generate a valid action to set updateFileRequestSent", () => {
    const expectedActionTrue = {
      type: types.PORTAL_FILE_REQUEST_SENT,
      payload: true,
    };
    expect(actions.updateFileRequestSent(true)).toEqual(expectedActionTrue);
  });

  it("should generate a valid action to set updatePortalFilesSelectedRequest", () => {
    const data = {
      action: formActions.ADD,
      requestId: "hhghh5h5h5hfhfhfh",
    };
    const expectedActionTrue = {
      type: types.PORTAL_FILE_UPDATE_SELECTED_REQUEST,
      payload: data,
    };
    expect(actions.updatePortalFilesSelectedRequest(data)).toEqual(
      expectedActionTrue
    );
  });

  it("should generate a valid action to set uploadedFiles", () => {
    const expectedActionTrue = {
      type: types.FILES_UPLOADED,
      payload: true,
    };
    expect(actions.uploadedFiles(true)).toEqual(expectedActionTrue);
  });
});
