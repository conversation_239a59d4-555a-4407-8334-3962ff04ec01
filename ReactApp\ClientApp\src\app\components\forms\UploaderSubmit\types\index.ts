export type FileRecord = {
  referenceNumber: string;
  title: string;
  checked?: boolean;
  error?: string;
  created?: string;
};

export type FileEvents = {
  onTitleUpdate: (value: string, index: number) => void;
  onFileSelect: (value: boolean, index: number) => void;
  onFileSelectAll: (value: boolean) => void;
  onFileRemove: (referenceNumber: string) => void;
  onSaveSuccess: (files: FileRecord[]) => void;
};

export type UrlEvents = {
  onUrlUpdate: (value: string) => void;
  onTitleUpdate: (value: string) => void;
};

export type FileUploadFormData = {
  files?: FileRecord[];
  siteId: string;
  binderId: string;
  binderNodeId: number;
  tags: string[];
};
