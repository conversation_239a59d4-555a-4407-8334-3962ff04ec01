{"extends": ["stylelint-config-standard"], "plugins": ["stylelint-order", "stylelint-scss"], "rules": {"length-zero-no-unit": true, "declaration-no-important": true, "color-hex-case": "lower", "color-hex-length": "short", "color-named": "never", "declaration-block-trailing-semicolon": "always", "declaration-colon-space-after": "always", "number-leading-zero": "never", "font-weight-notation": "numeric", "rule-empty-line-before": "always-multi-line", "order/order": [["dollar-variables", "at-rules", "custom-properties", "declarations", "rules"], {"unspecified": "bottom"}], "order/properties-alphabetical-order": true, "at-rule-no-unknown": null, "no-descending-specificity": null, "selector-max-compound-selectors": 7, "max-nesting-depth": 7, "string-quotes": "single", "indentation": 2, "selector-max-id": 0, "selector-combinator-space-after": "always", "property-no-vendor-prefix": true, "value-no-vendor-prefix": true, "function-url-quotes": "never", "font-family-name-quotes": "always-unless-keyword", "comment-whitespace-inside": "always", "comment-empty-line-before": "always", "at-rule-no-vendor-prefix": true, "media-feature-name-no-vendor-prefix": true, "selector-pseudo-class-parentheses-space-inside": "always", "media-feature-range-operator-space-before": "always", "media-feature-range-operator-space-after": "always", "media-feature-parentheses-space-inside": "always", "media-feature-colon-space-after": "always", "selector-no-vendor-prefix": true, "scss/at-rule-no-unknown": true, "scss/at-extend-no-missing-placeholder": true, "scss/dollar-variable-colon-space-after": "always", "scss/dollar-variable-colon-space-before": "never", "scss/no-duplicate-mixins": true}}