@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../../styles/';

.yjHistoryTblWrapper {

  table {

    thead {

      span:nth-child( 2 ) {
        margin-left: 10px;
        margin-top: -.2em;

        span {

          span:nth-child( 2 ) {
            margin-left: 0;
          }
        }
      }

      tr {

        th {
          background: linear-gradient(180deg, @color-table-header-top-bg 0%, @color-table-header-bottom-bg 100%);

          &:hover {
            background: @color-bg-table-header-hover;
            transition: none;
          }

          > div {
            width: 100%;

            > span {

              &:first-child {
                width: 100%;
              }
            }
          }

          .ant-table-column-sorters {
            padding: 6px 10px;
          }
        }
      }
    }
  }
}

// Grid View Button Styles

.yjChangeHistoryView {
  background-color: transparent;
  border: none;
  box-shadow: none;
  color: @color-accent-secondary;

  &:hover,
  &:active,
  &:visited {
    color: @color-accent-secondary;
  }

  &:hover {
    background-color: transparent;
    color: @color-accent-secondary;
  }

  &:focus {
    background-color: transparent;
    color: @color-accent-secondary;
  }

  &:active {
    background-color: transparent;
    color: @color-accent-secondary;
  }

  &:disabled {
    background-color: transparent;
    color: @color-accent-secondary;
  }
}

// View Modal Table

.yjHistoryChangesGrid {
  width: 100%;

  tr:hover {
    background: @color-table-row-hover;
  }

  th {
    background: @color-bg-history-grid-header;
    border-bottom: 1px solid @border-color-base;
    color: @color-font-history-grid-header;
    font-size: @font-size-base / 1.145;
    height: 22px;
    padding-bottom: 15px;
    text-transform: @yj-transform;
    width: 33%;

    .font-mixin(@font-primary, @yjff-semibold);
  }

  td {
    border-bottom: 1px solid @border-color-base;
    color: @color-font-history-grid-record;
    font-size: ceil(@font-size-base / 1.1);
    list-style: none;
    padding: 5px;
  }

  .yjPropertiesDetailPreview {
    background-size: cover;
    border: 1px solid @color-primary;
    height: 200px;
    margin-bottom: 10px;
  }
}
