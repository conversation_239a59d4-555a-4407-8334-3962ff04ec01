import React, { useEffect, useState } from "react";
import { List, Skeleton } from "antd";
import { useHistory } from "react-router-dom";

import styles from "../index.module.less";
import { ValueType } from "@app/types/ValueType";
import { getCheckoutFileHistoryListByFileId } from "@app/api/fileAreaService";
import logger from "@app/utils/logger";
import { errorNotification } from "@app/utils/antNotifications";
import { DATA_RETRIEVE_ERROR_MESSAGE } from "@app/features/FileArea/DocumentPropeties";
import { FORBIDDEN_ERROR_CODE, getLocalDateTime } from "@app/utils";

interface Notes {
  [index: number]: string;
}

export const getFileCountStatus = (
  listLength: number,
  description?: string
) => {
  if (listLength === 0) {
    return NO_RECORDS;
  } else if (listLength < MAX_DECIMAL_NUMBER) {
    return VALUE_ZERO.concat(listLength.toString()).concat(
      " ",
      description ? description : TEXT_CHECKOUTS
    );
  } else {
    return listLength
      .toString()
      .concat(" ", description ? description : TEXT_CHECKOUTS);
  }
};

export const createNote = (record: any) => {
  let customizedNote = undefined;
  if (
    ![RESPONSE_NOT_APPLICABLE, RESPONSE_PENDING].includes(record.checkInDate) &&
    ![RESPONSE_NOT_APPLICABLE, RESPONSE_PENDING].includes(record.checkOutDate)
  ) {
    customizedNote = NOTE_FOR_CHECKED_IN;
  } else if (
    record.checkInDate === RESPONSE_NOT_APPLICABLE &&
    ![RESPONSE_NOT_APPLICABLE, RESPONSE_PENDING].includes(record.checkOutDate)
  ) {
    customizedNote = NOTE_FOR_UNDO_CHECKED_OUT;
  }
  return customizedNote;
};

const LABEL_CHECKED_OUT_BY = "checked out by";
const LABEL_CHECKED_OUT_ON = "checked out on";
const LABEL_CHECKED_IN_ON = "checked in on";
const LABEL_RETURN_DATE = "Return Date";
const LABEL_CHECKED_OUT_NOTES = "checkout notes";

const RESPONSE_NOT_APPLICABLE = "N/A";
const RESPONSE_PENDING = "Pending";

export const NOTE_FOR_CHECKED_IN = "Checked in as a new file";
export const NOTE_FOR_UNDO_CHECKED_OUT = "undo-checked-out file";

const MAX_DECIMAL_NUMBER = 10;
const VALUE_ZERO = "0";
const TEXT_CHECKOUTS = "Checkout(s)";
const NO_RECORDS = "No Records Available";
export type CheckoutHistory = {
  checkOutDate: string;
  checkInDate: string;
  returnDate: string;
  checkOutNotes: string;
  checkOutBy: ValueType;
};
export default (props: { fileId: string }) => {
  const [processingTrigger, setProcessingTrigger] = useState(true);
  const [checkoutHistoryList, setCheckoutHistoryList] = useState<
    CheckoutHistory[]
  >([]);
  const [
    customizedCheckoutHistoryList,
    setCustomizedCheckoutHistoryList,
  ] = useState<any[]>([]);
  const [notesList, setNotesList] = useState<Notes>({});
  const history = useHistory();

  useEffect(() => {
    if (props.fileId) {
      fetchCheckoutHistoryFileList();
    }
    return () => {
      setProcessingTrigger(true);
    };
  }, [props.fileId]);

  const fetchCheckoutHistoryFileList = () => {
    getCheckoutFileHistoryListByFileId(props.fileId)
      .then((response) => {
        setCheckoutHistoryList(response.data);
        setProcessingTrigger(false);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        } else {
          errorNotification([""], DATA_RETRIEVE_ERROR_MESSAGE);
        }
        logger.error(
          "Document Properties",
          "Retrieve Checkout History List",
          error
        );
      });
  };

  useEffect(() => {
    setNotesList({});
    const customizedItemList = checkoutHistoryList.map((checkout, index) => {
      const note = createNote(checkout);
      if (note) {
        setNotesList((current) => {
          return { ...current, [index]: note };
        });
      }
      return [
        { name: LABEL_CHECKED_OUT_BY, value: checkout.checkOutBy?.name },
        {
          name: LABEL_CHECKED_OUT_ON,
          value: getLocalDateTime(checkout.checkOutDate),
        },
        {
          name: LABEL_CHECKED_IN_ON,
          value: getLocalDateTime(checkout.checkInDate),
        },
        {
          name: LABEL_RETURN_DATE,
          value: getLocalDateTime(checkout.returnDate),
        },
        { name: LABEL_CHECKED_OUT_NOTES, value: checkout.checkOutNotes },
      ];
    });
    setCustomizedCheckoutHistoryList(customizedItemList);
  }, [checkoutHistoryList]);

  const renderCheckoutItem = (item: any) => {
    return (
      <List.Item className={styles.yjPropertiesCheckoutsListItem}>
        <List.Item.Meta
          title={
            <p className={styles.yjPropertiesCheckoutsTitle}>{item.name}</p>
          }
          description={
            <span className={styles.yjPropertiesCheckoutsDescription}>
              {item.value}
            </span>
          }
        />
      </List.Item>
    );
  };

  return (
    <>
      {!processingTrigger ? (
        <div className={styles.yjPropertiesCheckoutsTab}>
          <p className={styles.yjPropertiesCheckoutsNotifications}>
            {getFileCountStatus(customizedCheckoutHistoryList.length)}
          </p>
          {customizedCheckoutHistoryList.map((checkoutRecord, index) => (
            <div className={styles.yjPropertiesCheckoutsList}>
              <List
                key={index}
                itemLayout="horizontal"
                dataSource={checkoutRecord}
                renderItem={(item) => renderCheckoutItem(item)}
              />
              {notesList[index] && (
                <p className={styles.yjPropertiesRequiredText}>
                  * {notesList[index]}
                </p>
              )}
            </div>
          ))}
        </div>
      ) : (
        <Skeleton active={true} />
      )}
    </>
  );
};
