import React, { Fragment, useCallback, useContext, useEffect, useReducer, useState } from 'react';
import {
  EditOutlined, ExclamationCircleOutlined, LinkOutlined, UpOutlined,
  RightOutlined,
} from '@ant-design/icons';
import { useParams, withRouter } from 'react-router-dom';
import { Button, Form, List, Modal as AntModal, Tooltip, Tag } from 'antd';
import moment from 'moment';
import { useDispatch, useSelector } from 'react-redux';
import { PageContent } from '@app/layouts/MasterLayout';
import { FileAreaActionPanel } from '@app/features/FileArea/FileAreaActionPanel';
import GenericDataTable from '@app/components/GenericDataTable';
import config from '../../../utils/config';
import PageTitle from '@app/components/PageTitle';
import Uploader from '@app/components/Uploader';
import { FolderTree } from '@app/components/FolderTree';
import styles from './index.module.less';
import {
  canCheckinByFileId,
  checkCheckoutFileListBySiteId,
  pinUnPinFiles,
  undoCheckoutFile,
  updateFileDetailsByFileId,
  updateFileStatus,
  getFileAreaIdBySite,
  getBinderFileAreaNodes
} from '@app/api/fileAreaService';
import { FileAreaButtonPanel } from '@app/features/FileArea/HeaderButtonPanel/fileAreaButtonPanel';
import ManageFiles from '@app/features/FileArea/ManageFiles';
import Modal from '@app/components/Modal';
import { updateColumnQueryParameters, updateSearchQueryParameters } from '@app/redux/actions/gridsActions';
import { IFile } from '@app/types/fileAreaTypes';
import ContextMenu from '@app/components/ContextMenu';
import {
  //setHasCommonData,
  updateContextMenuAssignOption,
  updateContextMenuCheckoutOption,
  updateContextMenuDeleteOption,
  updateContextMenuDownloadOption,
  updateContextMenuPropetiesoption,
  updateContextMenuPublishFiles,
  updateContextMenuReCategorizeOption,
  updateContextMenuMoveFilesOption,
  updateContextMenuReNameFilesOption,
  updateContextMenuCopyFilesOption,
  updateContextMenuStatusOption,
  updateContextMenuUnpublishFiles,
  updateLoadGridOption,
  updateContextMenuLinkFilesOption,
  updateContextMenuUnlinkFilesOption,
  updateContextMenuToBeDeleted,
  updateContextMenuCopyLinkFiles,
  setFolderTree,
} from '@app/redux/actions/fileAreaActions';
import { RootState } from '@app/redux/reducers/state';
import { errorNotification, infoNotification, successNotification } from '@app/utils/antNotifications';
import { setDynamicBreadcrums } from '@app/redux/actions/configurationActions';
import { Sorter, copyToClipboard } from '../../../components/GenericDataTable/util';
import logger from '@app/utils/logger';
import { FORBIDDEN_ERROR_CODE } from '@app/utils';
import { getExistingTags } from '@app/api/tagManagementService';
import { DataTableContext } from '@app/components/GenericDataTable/DataTableContext';
import { updateFilterDropDownValues } from '@app/components/GenericDataTable/DataTableContext/actions';
import reducer from '@app/components/GenericDataTable/DataTableContext/reducer';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import { renderTag } from '@app/components/Tag';
import userStatusColorSwitch from '@app/utils/css/userStatusColorSwitch';
import { SITE_STATUS } from '@app/constants';
import hasPermission from '@app/utils/permission';
import { CheckCircleOutlined, HistoryOutlined } from '@ant-design/icons/lib';
import { CheckinModel } from '@app/features/FileArea/CheckinModel/CheckinModel';
import useQuery from '@app/hooks/useQuery';
import renderOptions from "@app/components/forms/utils/renderOptions";
import { getTemplates } from "@app/api/templateService";
import { setActiveTemplates } from "@app/redux/actions/templateActions";
import { getAutocompleteOptions, getColumns, getRecords } from "@app/api/genericDataTable";
import debounce from "lodash/debounce";
import DownloadModal, { downloadTypes } from "@app/components/DownloadModal";
import ModalCustom from '../../../components/Modal';
import { FormattedDateTime } from "@app/components/FormattedDateTime";
import { useFileArea } from '@app/hooks/useFileArea';
import { IFolder } from '@app/types/FileAreaFolderTreeTypes';
import StatusTag from "@app/features/FileArea/StatusTag";
import ShowAllSwitch from '@app/components/Switch/ShowAll';

const SORTER: Sorter = {
  value: 'modified',
  order: 'descend',
};
const CHECKOUT_STATUS = 'Checked-Out';

type fileAreaActionItem='fileUpload' | 'fileEdit' | 'fileDownload' | 'fileOptionsExceptDownload'|'manageTags'|'manageCheckin';

const statusPermissionMap: Record<string, {
  fileUpload: boolean;
  fileEdit: boolean;
  fileDownload: boolean;
  fileOptionsExceptDownload: boolean;
  manageTags: boolean;
  manageCheckin: boolean;
}> = {
  "ACTIVE": { fileUpload: true, fileEdit: true, fileDownload: true, fileOptionsExceptDownload: true, manageTags: true,manageCheckin: true },
  "INACTIVE": { fileUpload: false, fileEdit: false, fileDownload: true, fileOptionsExceptDownload: false,manageTags: false, manageCheckin: false },
  "ACTIVE - LH": { fileUpload: true, fileEdit: false, fileDownload: true, fileOptionsExceptDownload: false, manageTags: false, manageCheckin: false },
  "INACTIVE - LH": { fileUpload: false, fileEdit: false, fileDownload: true, fileOptionsExceptDownload: false, manageTags: false, manageCheckin: false },
};

const isInvalidStatus = (status: any) => {
  return status.name === CHECKOUT_STATUS;
};

const isCheckout = (status: any) => {
  return status.name === CHECKOUT_STATUS;
};

const constructManageFileRequest = (selectedFile: IFile, data: any, selectedFolder: number, title: string, binderId: string) => {
  const request = {
    ...data,
    tags: [...data.tags, ...data.newTags],
    binderNodeId: selectedFolder < 0 ? data.folder : selectedFolder,
    title: title !== '' ? title : selectedFile.title,
    binderId,
  };
  delete request.folder;
  delete request.newTags;
  return request;
};

const { confirm } = AntModal;

export const formatGridColumnValue = (value: any) => {
  return <p className={styles.yjGridTextFormat}>{value}</p>;
};

const hasFileAreaPermission = (fileArea: any, permissionType: fileAreaActionItem) => {
  return fileArea && statusPermissionMap[fileArea.status.toUpperCase()][permissionType];
};

const Page = (props: any) => {
  const reactDispatch = useDispatch();
  const [toggle, SetToggle] = useState(false);
  const { siteId, siteName, channelId, binderId, binderName } = useParams<any>();
  const [folderId, setFolderId] = useState(0);
  const [selectedRecords, setSelectedRecords] = useState(0);
  const [selectedIds, setSelectedIds] = useState(['']);
  const [selectedRowKeys, setSelectedRowKeys] = useState([]);
  const [selectedFiles, setSelectedFiles] = useState<IFile[]>([]);
  const [showViewModalState, setShowViewModalState] = useState(false);
  const [editFormChange, setEditFormChange] = useState(false);
  const [editFormAssigneeChange, setEditFormAssigneeChange] = useState(false);
  const [hasDownloaded, setHasDownloaded] = useState(false);
  const [undoCheckout, setUndoCheckout] = useState(true);
  const [actionsAllowed, setActionsAllowed] = useState(true);
  const checkinInStatus = 5;
  const [gridUpdated, setGridUpdated] = useState(false);
  const [manageFileForm] = Form.useForm();
  const [manageFileTitle, setManageFileTitle] = useState('');
  const [selectedFolderInUpload, setSelectedFolderInUpload] = useState<number>(-1);
  const { state } = useContext(DataTableContext);
  const [_, dispatch] = useReducer(reducer, state);
  const urlParams = useQuery();

  const [singleCheckinFile, setSingleCheckinFile] = useState('');
  //context menu state
  const [positon, setPositon] = useState({ x: 0, y: 0 });
  const [visibleContextMenu, setVisibleContextMenu] = useState(false);
  //const [showSelectTemplateModal, setShowSelectTemplateModal] = useState(false);
  const fileArea = useFileArea(siteId);

  const [dataTableModal, setDataTableModal] = useState({
    visible: false,
    title: '',
    modalData: [],
  });

  const { folderTree, hasCommonData, fileAreaSettings, filesUploaded } = useSelector((state: RootState) => state.fileArea);
  const { userPermission, userPreferences } = useSelector((state: RootState) => state.userManagement);
  const { activeTemplates } = useSelector((state: RootState) => state.template);

  const [selectedTemplateId, setSelectedTemplateId] = useState('');

  const [showDownloadModal, setShowDownloadModal] = useState(false);
  const [selectedFile, setSelectedFile] = useState<IFile | undefined>(undefined);
  const [checkedOuFilesDownload, setCheckedOuFilesDownload] = useState<IFile[] | undefined>(undefined);
  const [downloadType, setDownloadType] = useState<downloadTypes | undefined>(downloadTypes.individual);
  const [loading, setLoading] = useState(false);

  const linkedFiles = selectedFiles.filter((e) => e.linked);
  const originalFileAreaWithLinked: boolean = linkedFiles && selectedFiles.some((e) => e.fK_FileAreaId === fileArea?.fileAreaId);

  const tableKey = 'fileArea';

  const initialFolderData = [
    {
      id: 1,
      name: 'Primary folder',
      retention: false,
      presist: false,
      subFolders: [
        {
          id: 2,
          name: 'Secondary folder (1)',
          subFolders: [],
          retention: false,
          presist: false,
        },
        {
          id: 3,
          name: 'Secondary folder (2)',
          subFolders: [],
          retention: false,
          presist: false,
        },
      ],
    },
  ];

  const showDatatableModal = (title: string, data: []) => {
    setDataTableModal({
      visible: true,
      title: title,
      modalData: data,
    });
  };

  const hideDatatableModal = () => {
    setDataTableModal({
      visible: false,
      title: dataTableModal.title,
      modalData: dataTableModal.modalData,
    });
  };

  const fetchBinderNodes = async () => {
    try {
      const response = await getBinderFileAreaNodes(binderId);
      const transformFolders = (folders: any[]): IFolder[] => {
        const primaryFolders = folders.filter((folder) => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);

        return primaryFolders.map((folder) => ({
          id: folder.id,
          name: folder.name,
          subFolders: folder.childNodes.map((child: any) => ({
            id: child.id,
            name: child.name,
            subFolders: [],
            retention: child.retention || 0,
          })),
        }));
      };

      const transformedFolders = transformFolders(response.data.folders);
      reactDispatch(setFolderTree({
        siteId,
        siteName: binderName,
        siteStatusId: 1,
        folders: transformedFolders,
      }));
    } catch (error) {
      logger.error('Error fetching binder nodes:', 'fetchBinderNodes', error);
    }
  };

  const loadGrid = useCallback(() => {
    getExistingTags(binderId).then((res) => {
      dispatch(updateFilterDropDownValues('tags', res.data));
    });
    reactDispatch(updateColumnQueryParameters(siteId));
    reactDispatch(
      updateSearchQueryParameters([
        { key: 'siteid', value: siteId },
        { key: 'binderNodeId', value: folderId },
      ])
    );
    reactDispatch(updateLoadGridOption(true));
    setTimeout(() => {
      reactDispatch(updateLoadGridOption(false));
    });
  }, [siteId, reactDispatch, folderId, binderId]);

  useEffect(() => {
    fetchBinderNodes()
  }, []);

  useEffect(() => {
    getTemplates({ 'isHidden': false, 'templateStatus': 1 }).then((res) => {
      logger.info('File Area Module', 'Get Templates Response', res.data.records);
      if (res.status == 200 && res.data?.records) {
        reactDispatch(setActiveTemplates(res.data?.records));
        logger.debug(
          "File Area Module",
          "Set Active Templates",
          activeTemplates
        );
      }
    }).catch((e) => {
      logger.error("File Area Module", "Get Templates Error", e);
    });

    const dynamicBreadcrumbs = [
      { title: "Client File Areas", path: "/client-file-area" },
      { title: `${decodeURIComponent(siteName)} - File Areas`, path: `/client-file-area/${siteId}/${siteName}/${channelId}` },
      { title: `${decodeURIComponent(binderName)}`, path: `/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}` }
    ];
    reactDispatch(setDynamicBreadcrums(dynamicBreadcrumbs));
    return () => {
      //reactDispatch(setHasCommonData(false));
      reactDispatch(setDynamicBreadcrums([]));
    };
  }, []);




  useEffect(() => {
    reactDispatch(updateColumnQueryParameters(siteId));
    reactDispatch(
      updateSearchQueryParameters([
        { key: 'siteid', value: siteId },
        { key: 'binderNodeId', value: 0 },
      ])
    );
  }, [reactDispatch, siteId]);

  useEffect(() => {
    if (hasDownloaded) {
      setGridUpdated(true);
      loadGrid();
      setHasDownloaded(false);
      rowSelection.selectedRowKeys = [];
      rowSelection.onChange([], []);
      setGridUpdated(false);
    }
  }, [hasDownloaded, loadGrid]);

  useEffect(() => {
    if (filesUploaded) {
      setGridUpdated(true);
      loadGrid();
      setGridUpdated(false);
    }
  }, [filesUploaded, reactDispatch, loadGrid]);

  const rowSelection = {
    onChange: (selectedRowKeyValues: any, selectedRows: IFile[]) => {
      if (selectedRows?.length > 0 && selectedRows?.find((row) => userPermission?.privDMSCanCheckInCheckOutInternalFiles && row?.status?.value === checkinInStatus)) {
        setActionsAllowed(false);
      } else {
        setActionsAllowed(true);
      }
      if (userPermission.privDMSCanCheckInCheckOutInternalFiles && selectedRows.length === 1 && selectedRows[0].status?.value === checkinInStatus) {
        setUndoCheckout(true);
      } else {
        setUndoCheckout(false);
        reactDispatch(updateContextMenuPropetiesoption(false));
      }
      setSelectedRowKeys(selectedRowKeyValues);
      setSelectedRecords(selectedRows.length);
      setSelectedFiles(selectedRows);
      if (selectedRows.length > 1) {
        setSelectedIds(selectedRowKeyValues);
      } else {
        setSelectedIds(['']);
      }
    },
    getCheckboxProps: (record: any) => ({
      disabled: record?.status?.value === checkinInStatus,
      name: record.name,
    }),
    selectedRowKeys,
    fixed: true,
  };

  const selectedRecord = (recordId: string) => {
    return selectedIds.includes(recordId);
  };

  const handleCheckin = (fileId: string) => {
    canCheckinByFileId(fileId).then(() => {
      setSingleCheckinFile(fileId)
    }).catch((error) => {
      logger.error("File Area Module", "handleCheckin", error);

      if (error.response.status === FORBIDDEN_ERROR_CODE) {
        errorNotification([''], 'You do not have the permission to perform this action.');
      } else {
        errorNotification([''], 'Something went wrong!');
      }
    });
  };
  const handleUndoCheckout = (fileId: string) => {
    setHasDownloaded(false);
    confirm({
      title: 'Do you wish to undo-checkout for this file?',
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        undoCheckoutFile(selectedFiles[0].id)
          .then((response) => {
            if (response) {
              setHasDownloaded(true);
              successNotification([''], 'Undo Check-out Successful');
            }
          })
          .catch((error) => {
            if (error.response.status === FORBIDDEN_ERROR_CODE) {
              errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');
            } else {
              errorNotification([''], 'File checkout could not be undone.');
            }
          });
      },
    });
  };

  const handleShowViewModalCancel = () => {
    if (editFormChange) {
      confirm({
        title: 'Are you sure you want to discard the changes?',
        icon: <ExclamationCircleOutlined />,
        okText: 'Yes',
        cancelText: 'No',
        onOk() {
          setEditFormChange(false);
          setShowViewModalState(false);
        },
      });
    } else {
      setEditFormChange(false);
      setShowViewModalState(false);
    }
  };

  // if (!hasCommonData) {
  //   return <Spin style={{ paddingTop: 20 }} />;
  // }

  const onSelecteFolder = (folderIdValue: any) => {
    reactDispatch(
      updateSearchQueryParameters([
        { key: 'siteid', value: siteId },
        { key: 'binderNodeId', value: folderIdValue },
      ])
    );
    setFolderId(folderIdValue);
    rowSelection.onChange([], []);
  };

  const onContextMenuClicked = (event: any, record: IFile) => {
    if (rowSelection.selectedRowKeys.length <= 0) {
      if (userPermission.privDMSCanCheckInCheckOutInternalFiles && record?.status?.value === checkinInStatus) {
        setUndoCheckout(true);
        setActionsAllowed(false);
      } else {
        setUndoCheckout(false);
        setActionsAllowed(true);
      }

      setSelectedFiles([record]);
    }
    event.preventDefault();
    setVisibleContextMenu(false);
    document.addEventListener(`click`, function onClickOutside() {
      setVisibleContextMenu(false);
      document.removeEventListener(`click`, onClickOutside);
    });
    setVisibleContextMenu(true);
    setPositon({ x: event.clientX, y: event.clientY });
  };

  const onRowClick = (event: any, record: IFile) => {
    if (selectedFiles.length === 1) {
      setSelectedFiles([record]);
    }
  };

  const handleRowClick = (record: IFile, rowIndex: any) => ({
    onContextMenu: (event: any) => onContextMenuClicked(event, record),
    onClick: (event: any) => onRowClick(event, record),
  });

  const handleUpdateFileProperties = () => {
    setLoading(true);
    manageFileForm
      .validateFields()
      .then((data) => {
        data.expirationDate = moment(data.expirationDate).endOf('day').subtract(1, 'minutes');

        const requestData = constructManageFileRequest(selectedFiles[0], data, selectedFolderInUpload, manageFileTitle, binderId);
        return updateFileDetailsByFileId(selectedFiles[0].id, requestData);
      })
      .then((response) => {
        //unknown error
        if (!response.data) {
          errorNotification([''], 'File Properties Update Failed');
          setLoading(false);
          if (editFormAssigneeChange) {
            errorNotification([''], 'Assignment Failed');
          }

          return response.data;
        }

        //success scenario
        loadGrid();
        successNotification([''], 'File Properties Updated Successfully');
        if (editFormAssigneeChange) {
          successNotification([''], 'Assignment Successful');
        }
        setShowViewModalState(false);
        setEditFormChange(false);
        setLoading(false);
      })
      .catch((error) => {
        switch (error.statusCode) {
          //error code cases
          case FORBIDDEN_ERROR_CODE:
            errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');
            break;
          default:
            errorNotification([''], 'File Properties Update Failed');
            if (editFormAssigneeChange) {
              errorNotification([''], 'Assignment Failed');
            }
            break;
        }

        logger.error('Internal Files', 'Manage Files Edit', error);
        setLoading(false);
      });
  };

  function gridColumnValueFormatter(value: any) {
    return value ? formatGridColumnValue(value) : formatGridColumnValue('N/A');
  }

  const downloadFile = (value: [], file: IFile) => {

    // setDownloadType(checkoutZip ? downloadTypes.checkoutZip : downloadTypes.checkoutIndividual);
    setSelectedFile(file);
    if(hasFileAreaPermission(fileArea, 'fileDownload'))
      {
      setShowDownloadModal(true);
      }
    setCheckedOuFilesDownload(value)
  }
  const colors = [
    "magenta",
    "green",
    "red",
    "cyan",
    "volcano",
    "blue",
    "orange",
    "geekblue",
    "gold",
    "purple",
    "lime"
  ];
  const [expandedTags, setExpandedTags] = useState<Record<string, boolean>>({});
  // Inside the Page component
  const containerRefs = React.useRef<Record<string, HTMLParagraphElement | null>>({});
  const [showExpandIcons, setShowExpandIcons] = React.useState<Record<string, boolean>>({});
  const [showAllFiles, setShowAllFiles] = useState<boolean>(false);

  // Function to check overflow for all tags
  const checkOverflow = (recordId: string) => {
    const container = containerRefs.current[recordId];
    if (container && container.scrollHeight && container.clientHeight && container.scrollHeight > container.clientHeight) {
      console.log('Container:', container);
      console.log('scrollHeight:', container.scrollHeight, 'clientHeight:', container.clientHeight);
      // setTimeout(() => {  // Use setTimeout to ensure the DOM is updated before checking overflow   
      //   setShowExpandIcons((prev) => ({
      //     ...prev,
      //     [recordId]: container.scrollHeight > container.clientHeight,
      //   }));
      // }, 100)
    } else {
      console.warn(`Container for recordId ${recordId} is not available.`);
    }
  };

  // // Use useEffect in the parent component to handle overflow checks
  // useEffect(() => {
  //   console.log('containerRefs:', containerRefs.current);

  //   Object.keys(containerRefs.current).forEach((recordId) => {
  //     checkOverflow(recordId);
  //   });
  // }, [expandedTags, containerRefs]);

  const renderGridColumns = () => {
    return {
      title: (value: any, data: any) => {
        return (
          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>
            <Tooltip className="yJFileAreaRow" placement="leftTop" title={value}>
            <Button type="link" onClick={() => downloadFile(value, data)} style={{ paddingInline: 0, border: 0 , cursor: !hasFileAreaPermission(fileArea, 'fileDownload')? 'not-allowed':'pointer'}}>{value}</Button>
            </Tooltip>
          </div>
        );
      },

      id: (value: string) => {
        return (
          <div className="yjFileAreaGridTextWrap yJFileAreaRow">
            <Tooltip color="transparent" placement="right" title={<LinkOutlined style={{ color: '#0E678E', fontSize: '14px' }} onClick={() => copyToClipboard(value)} />}>
              <span className="yJFileAreaRow">{value}</span>
            </Tooltip>
          </div>
        );
      },
      created: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      createdBy: (value: any) => {
        return (
          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>
            <Tooltip className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`} placement="leftTop" title={value?.name}>
              {value?.name}
            </Tooltip>
          </div>
        );
      },
      assignee: (value: any) => {
        return value ? (
          <div className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`}>
            <Tooltip className={`${styles.yjFileAreaGridTextWrap} yJFileAreaRow`} placement="leftTop" title={value?.name}>
              {value?.name}
            </Tooltip>
          </div>
        ) : (
          'N/A'
        );
      },
      status: (value: any) => {
        return value ? formatGridColumnValue(value.name) : formatGridColumnValue('N/A');
      },
      year: gridColumnValueFormatter,
      modified: (value: string) => {
        return <FormattedDateTime value={value} />;
      },
      // isFolderExist: {
      //   render: (value: any) => {
      //     return (value ? <Tag color="blue">File Area</Tag> : '')
      //   },
      //   width: 550
      // }
      tags: {
        width: 300,
        render: (value: any, record: any) => {
          const SINGLE_LINE_HEIGHT = 24; // Adjust this value based on your CSS
          const LINES_TO_DHOW_EXPANDER = 2; // Maximum number of lines to show before expanding
          const isExpanded = expandedTags[record.id] ?? false;

          const isContentOverflowing = (recordId: string): boolean => {
            const container = containerRefs.current[recordId];
            if (!container) return false;

            const scrollHeight = container.scrollHeight ?? 0;
            const clientHeight = container.clientHeight ?? 0;

            const senario1 = scrollHeight / clientHeight >= LINES_TO_DHOW_EXPANDER;
            const senario2 = scrollHeight > SINGLE_LINE_HEIGHT;

            return senario1 || senario2;
          };

          const tagColorMap: any = {};

          value?.forEach((value: any, index: number) => {
            tagColorMap[value.name] = colors[index % colors.length];
          });

          return (
            <div
              ref={(el) => {
                if (el) {
                  containerRefs.current[record.id] = el;
                  checkOverflow(record.id);
                }
              }}
              className={`yJFileAreaRow ${isExpanded ? styles.multiLine : styles.singleLine}`}
            >
              {value?.map((t: any) => <Tag color={tagColorMap[t.name]}>{t?.name}</Tag>)}
              {/* Conditionally render the expand icon only if there are tags */}
              {isContentOverflowing(record.id) && (
                <span
                  onClick={() => {
                    setExpandedTags((prev) => ({
                      ...prev,
                      [record.id]: !isExpanded,
                    }));

                    // Use requestAnimationFrame to delay the check
                    requestAnimationFrame(() => {
                      const container = containerRefs.current[record.id];
                      if (container) {
                        console.log('After toggle:');
                        console.log('clientHeight:', container.clientHeight);
                        console.log('scrollHeight:', container.scrollHeight);
                      }
                    });
                  }}
                  style={{
                    position: "absolute",
                    right: 0,
                    cursor: "pointer",
                    marginLeft: "8px",
                  }}
                >
                  {isExpanded ? <UpOutlined /> : <RightOutlined />}
                </span>
              )}
            </div>
          );
        }
      },
      projects: (value: any) => {
        return (
          <p className={`${styles.yjGridTextCenter} yJFileAreaRow`}>
            <Button className={`${styles.yjViewButton} yJFileAreaRow`} onClick={() => showDatatableModal('Projects', value)}>
              {'View'}
            </Button>
          </p>
        );
      },
      type: gridColumnValueFormatter,
      expirationDate: (value: string, record: any) => {
        return record.expirationStatus.name === 'Permanent' ? '' : <FormattedDateTime value={value} />;
      },
      fileCondition: gridColumnValueFormatter,
      action: (text: any, record: any) => {
        return (
          <div className={`${styles.yjActionIconWrapper} yJFileAreaRow`}>
            <Tooltip className="yJFileAreaRow" title="Check In">
              <Button
                className="yJFileAreaRow"
                disabled={!isCheckout(record.status) || !userPermission.privDMSCanManageFileProperties || !hasPermission(folderTree, 'FILE_AREA_FILE_EDIT')||!hasFileAreaPermission(fileArea, 'manageCheckin')}
                onClick={() => handleCheckin(record.id)}
                icon={<CheckCircleOutlined />}
              />
            </Tooltip>
            <Tooltip className="yJFileAreaRow" title="Edit">
              <Button
                className="yJFileAreaRow"
                disabled={selectedRecord(record.id) || isInvalidStatus(record.status) || !userPermission.privDMSCanManageFileProperties || record.linked}
                onClick={() => {
                  setManageFileTitle('');
                  // rowSelection.selectedRowKeys = [];
                  rowSelection.onChange([], []);
                  setSelectedFiles([record]);
                  setShowViewModalState(true);
                }}
                icon={<EditOutlined />}
              />
            </Tooltip>
            <Tooltip className="yJFileAreaRow" title="History">
              <Button
                className="yJFileAreaRow"
                disabled={selectedRecord(record.id)}
                onClick={() => {
                  rowSelection.selectedRowKeys = [];
                  rowSelection.onChange([], []);
                  props.history.push(encodeURI(`/client-file-area/${siteId}/file-area/${siteName}/${binderId}/${binderName}/${channelId}/history/${record.id}`));
                }}
                icon={<HistoryOutlined />}
              />
            </Tooltip>
          </div>
        );
      },
    };
  };

  const setQueryParameters = () => {
    return [
      { key: 'siteid', value: siteId },
      { key: 'binderNodeId', value: folderId },
      { key: 'binderId', value: binderId },
    ];
  };

  const fileDownloaded = (value: any) => {
    if (value) {
      setHasDownloaded(true);
      successNotification([''], 'File checked-out successfully.');
    }
  };

  function onSuccess() {
    setSingleCheckinFile('');
    setGridUpdated(true);
    loadGrid();
    setGridUpdated(false);
    rowSelection.selectedRowKeys = [];
    rowSelection.onChange([], []);
  }


  const fetchFiles = async (state: any, transformFilters: any, queryParams: any) => {
    if (!showAllFiles) {
      return fetchData(state, transformFilters, queryParams);
    }

    const activeFilter: Array<any> = state.columns.find((column: any) => column.key === 'status')?.filter_data;
    if (!activeFilter) {
      return fetchData(state, transformFilters, queryParams);
    }

    const filters = {
      ...transformFilters,
      status: transformFilters.status ? transformFilters.status : activeFilter.map(f => f.value),
    }
    return fetchData(state, filters, queryParams);
  };

  const fetchColumns = async () => {
    const response = await getColumns(config.api[OperationalServiceTypes.FileManagementService].files, tableKey, [{ key: 'binderId', value: binderId }]);

    return showAllFiles ? response : response.data.map((column: any) => {
      if (column.key === 'status') {
        return {
          ...column,
          filterData: column.filterData.filter((item: any) => item.name !== 'To be Deleted'),
        };
      }

      return column;
    });
  };

  const fetchData = useCallback(
    async (state: any, transformFilters: any, queryParams: any) => {
      return getRecords(
        config.api[OperationalServiceTypes.FileManagementService].files,
        {
          pagination: {
            current: state.pagination.current,
            pageSize: state.pagination.pageSize,
          },
          sorter: state.sorter ? { key: state.sorter.columnKey, order: state.sorter.order } : {},
          filters: transformFilters,
          columns: state.columns
            .filter((i: any) => i.default === false && i.selected === true)
            .map((i: any) => i.key),
        },
        queryParams
      );
    },
    [] // Add dependencies here if needed
  );

  // Debounced API call
  const debouncedApiCall = debounce(
    (props: any, value: string, callback: (data: any) => void) => {
      logger.info("File Area Module", "debouncedApiCall", {
        props,
        propsData: props.data,
        value,
      });

      getAutocompleteOptions(
        config.api[OperationalServiceTypes.FileManagementService].files,
        props.data.key,
        value,
        props.searchFieldParameters
      )
        .then((data: any) => {
          callback(data.data);
        })
        .catch(() => {
          callback([]);
        });
    },
    config.inputDebounceInterval
  );

  // Wrapper function to return a Promise
  const searchPromiseWrapper = (props: any, value: string, callback: any) => {
    debouncedApiCall(props, value, (data: any) => {
      callback(data)
    });
  };

  const handleOnDownloadModalCancel = () => {
    dispatch(updateContextMenuDownloadOption(false));
    setShowDownloadModal(false);
  };

  const makeAsPinUnPin = (selectedFiles: IFile[], pinned: boolean) => {
    logger.debug("File Area Module", "makeAsPinUnPin", { selectedFiles });
    const fileIds = selectedFiles.map((f) => f.id);
    pinUnPinFiles(fileIds, pinned)
      .then((response) => {
        if (response) {
          setHasDownloaded(true);
          successNotification([''], `File(s) ${pinned ? 'Pinned' : 'Unpinned'} Successfully`);
        }
      })
      .catch((error) => {
        if (error.response.status === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');
        } else {
          errorNotification([''], `File(s) ${pinned ? 'Pinned' : 'Unpinned'} Failed`);
        }
      });
  };

  return (
    <Fragment>
      <PageTitle
        title={decodeURIComponent(binderName)}
        tags={fileArea && <StatusTag value={fileArea.status} />}
      >
        <FileAreaButtonPanel siteId={siteId} siteName={decodeURIComponent(siteName)} channelId={channelId} fileSection={'internal'} manageCheckin={hasFileAreaPermission(fileArea, 'manageCheckin')} binderId={binderId} {...props} />
      </PageTitle>
      <PageContent>
        <div
          hidden={
            (!userPermission.privDMSCanUploadFiles || !hasPermission(folderTree, 'FILE_AREA_UPLOADER')) ||
            !hasFileAreaPermission(fileArea, 'fileUpload')
          }
          className={styles.yjFileAreaUploadWrapper}>
          <Uploader siteId={siteId} binderId={binderId} />
        </div>
        <div className={toggle ? `${styles.yjFileAreaFileFinderCollapsed}` : `${styles.yjFileAreaFileFinderExpanded}`}>
          {!toggle && <div className={styles.yjFileFinderPanel}>
            <FolderTree onSelectFolder={(folderIdValue) => onSelecteFolder(folderIdValue)} data={folderTree} />
          </div>}

          <div className={styles.yjFileAreaDetailsGrid}>
            <FileAreaActionPanel
              fileDownloaded={fileDownloaded}
              syncGrid={(updated: boolean) => {
                if (updated) {
                  setGridUpdated(true);
                  loadGrid();
                  setGridUpdated(false);
                  rowSelection.selectedRowKeys = [];
                  rowSelection.onChange([], []);
                }
              }}
              originalFileAreaWithLinked={originalFileAreaWithLinked}
              showCheckout={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && actionsAllowed && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showDelete={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showDownload={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileDownload')
              }
              showStatus={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showAssign={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showPropeties={
                selectedFiles.length === 1 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showReCategorize={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showMoveFiles={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showLinkFiles={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.some((file) => !file.linked) && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showUnlinkFiles={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.filter((e) => !e.linked).length === 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showPublish={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.some((file) => !file.published) && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showUnpublish={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && selectedFiles.filter((e) => !e.published).length === 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showToBeDelete={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showReName={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              showCopy={
                selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
              }
              selectedFileList={selectedFiles}
              siteId={siteId}
              toggleIconClicked={(e) => {
                SetToggle(!toggle);
              }}
              showCopyLink={selectedFiles.length > 0 && rowSelection.selectedRowKeys.length > 0 && hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')}
              showManageTags={hasFileAreaPermission(fileArea, 'manageTags')}
              showManageCheckin={hasFileAreaPermission(fileArea, 'manageCheckin')}
              additionalActionPanel={<ShowAllSwitch checked={showAllFiles} onChange={setShowAllFiles} />}
            />

            <Modal {...dataTableModal} size={'small'} onCancel={hideDatatableModal} footer={null}>
              <div className={styles.yjDataTableModal}>
                {dataTableModal.modalData && (
                  <List>
                    {dataTableModal.modalData.map((i: any) => (
                      <List.Item key={i.value}>{i.name}</List.Item>
                    ))}
                  </List>
                )}
                {dataTableModal.modalData.length === 0 && `No ${dataTableModal.title} Applied.`}
              </div>
            </Modal>

            {/* Record Edit Modal */}
            <Modal
              style={{ top: 20 }}
              visible={showViewModalState}
              title={'Update File Properties'}
              onCancel={handleShowViewModalCancel}
              footer={[
                <Button key="back" type="primary" onClick={handleShowViewModalCancel}>
                  cancel
                </Button>,
                <Button loading={loading} key="update" type="primary" onClick={handleUpdateFileProperties} disabled={!editFormChange}>
                  Update
                </Button>,
              ]}
            >
              <div className={styles.yjModalContentWrapper}>
                <ManageFiles
                  dataList={selectedFiles}
                  onFilesChange={() => { }}
                  siteId={siteId}
                  binderId={binderId}
                  form={manageFileForm}
                  onFormChange={(isFormChange, isAssigneeChange) => {
                    setEditFormChange(isFormChange);
                    setEditFormAssigneeChange(isAssigneeChange);
                  }}
                  onFolderChange={setSelectedFolderInUpload}
                  onTitleChange={setManageFileTitle}
                  disabledForm={!hasPermission(folderTree, 'FILE_AREA_FILE_EDIT') || !hasFileAreaPermission(fileArea, 'fileEdit')}
                />
              </div>
            </Modal>
            {/* Checkin Model */}
            <CheckinModel
              siteId={siteId}
              binderId={binderId}
              fileId={singleCheckinFile}
              onClose={() => setSingleCheckinFile('')}
              onSuccess={onSuccess}
            />

            <div
              className="yjFileAreaGridWrapper"
              onScroll={() => {
                setVisibleContextMenu(false);
              }}
              onMouseLeave={() => {
                setVisibleContextMenu(false);
              }}
            >
              <ContextMenu
                displayCheckoutOption={
                  userPermission.privDMSCanCheckInCheckOutInternalFiles &&
                  !undoCheckout &&
                  actionsAllowed &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayAssignOption={
                  userPermission.privDMSCanManageFileAssign &&
                  actionsAllowed &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayUndoCheckoutOption={
                  userPermission.privDMSCanCheckInCheckOutInternalFiles &&
                  undoCheckout &&
                  selectedFiles.length === 1 &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayDeleteOption={
                  userPermission.privDMSCanDeleteFiles &&
                  actionsAllowed &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayDownloadOption={
                  userPermission.privDMSCanViewFileArea &&
                  hasPermission(folderTree, 'FILE_AREA_DOWNLOAD') &&
                  hasFileAreaPermission(fileArea, 'fileDownload')
                }
                displayPropetiesOption={
                  selectedFiles.length === 1 &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayStatusOption={
                  userPermission.privDMSCanManageFileProperties &&
                  actionsAllowed &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayReCategorizeOption={
                  userPermission.privDMSCanRecategorizeFiles &&
                  actionsAllowed &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayCopyOption={
                  userPermission.privDMSCanCopyFiles &&
                  !undoCheckout &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayEmailOption={
                  userPermission.privDMSCanViewFileArea &&
                  !undoCheckout &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayMoveOption={
                  userPermission.privDMSCanMoveFiles &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayRenameOption={
                  userPermission.privDMSCanRenameFiles &&
                  !undoCheckout &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                dispalyPropertiesOption={
                  userPermission.privDMSCanViewFileArea &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                hasSelectedLinkFiles={selectedFiles.some((e) => !e.linked)}
                hasSelectedPublishedFiles={selectedFiles.some((e) => !e.published)}
                displayPublishOption={
                  !undoCheckout &&
                  actionsAllowed &&
                  userPermission.privDMSCanPublishUnpublishInternalFiles &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayLinkOption={
                  !undoCheckout &&
                  actionsAllowed &&
                  userPermission.privDMSCanLinkUnlinkFiles &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayToBeDeleted={
                  actionsAllowed &&
                  selectedFiles.filter((e) => e.status.value === 6).length == 0 &&
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayCopyLinkOption={
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                displayPinUnpinOption={
                  hasPermission(folderTree, 'FILE_AREA_OTHER_OPTIONS') &&
                  hasFileAreaPermission(fileArea, 'fileOptionsExceptDownload')
                }
                onDownloadClick={() => {
                  reactDispatch(updateContextMenuDownloadOption(true));
                }}
                onStatusClick={() => reactDispatch(updateContextMenuStatusOption(true))}
                onAssignClick={() => reactDispatch(updateContextMenuAssignOption(true))}
                onReCategorizeClick={() => reactDispatch(updateContextMenuReCategorizeOption(true))}
                onMoveFilesClick={() => reactDispatch(updateContextMenuMoveFilesOption(true))}
                onReNameFilesClick={() => reactDispatch(updateContextMenuReNameFilesOption(true))}
                onCopyFilesClick={() => reactDispatch(updateContextMenuCopyFilesOption(true))}
                onLinkFilesClicked={() => reactDispatch(updateContextMenuLinkFilesOption(true))}
                onUnlinkFilesClicked={() => reactDispatch(updateContextMenuUnlinkFilesOption(true))}
                onCheckoutClick={() => {
                  reactDispatch(updateContextMenuCheckoutOption(true));
                }}
                onUndoCheckoutClicked={() => {
                  handleUndoCheckout('');
                }}
                onDeleteClick={() => reactDispatch(updateContextMenuDeleteOption(true))}
                onPropetiesClicked={() => {
                  reactDispatch(updateContextMenuPropetiesoption(true));
                }}
                onPublishClick={() => {
                  reactDispatch(updateContextMenuPublishFiles(true));
                }}
                onUnpublishClick={() => {
                  reactDispatch(updateContextMenuUnpublishFiles(true));
                }}
                onToBeDeletedClick={() => {
                  reactDispatch(updateContextMenuToBeDeleted(true));
                }}
                onCopyLink={() => {
                  reactDispatch(updateContextMenuCopyLinkFiles(true));
                }}
                onPin={() => {
                  makeAsPinUnPin(selectedFiles, true)
                }}
                onUnPin={() => {
                  makeAsPinUnPin(selectedFiles, false)
                }}
                positon={positon}
                visible={visibleContextMenu}
              />
              <GenericDataTable
                filterCloudPadding={10}
                onFiltersChange={(filters: []) => {
                  rowSelection.selectedRowKeys = [];
                  rowSelection.onChange([], []);
                }}
                onRow={handleRowClick}
                selectedRecordCount={selectedRecords}
                hideHeaderPanel={true}
                endpoint={config.api[OperationalServiceTypes.FileManagementService].files}
                searchPromise={searchPromiseWrapper}
                dataPromise={fetchFiles}
                columnPromise={fetchColumns()}
                scrollColumnCounter={9}
                rowSelection={rowSelection}
                rowKey={'id'}
                tableKey={tableKey}
                groupedValue={siteId}
                sorted={SORTER}
                columnQueryParameters={[{ key: 'siteid', value: siteId }, { key: 'binderId', value: binderId }]}
                searchQueryParameters={setQueryParameters()}
                searchFieldParameters={setQueryParameters()}
                customRender={renderGridColumns()}
                lowResolutionWidth="auto"
                highResolutionWidth="auto"
                fixedColumns={['title']}
                isDraggable={true}
                onErrorLoading={(error) => {
                  if (error.statusCode === FORBIDDEN_ERROR_CODE) {
                    props.history.push('/forbidden');
                  }
                }}
                showPublishedIcon={true}
                filterByIds={decodeURIComponent(urlParams.get('ids') ?? '')}
              />
            </div>
          </div>
        </div>
      </PageContent>
      {/* <Modal
          closable={false}
          style={{ top: 20, width: '60wv' }}
          visible={showSelectTemplateModal}
          title={'Select Template'}
          footer={[
            <Button key="update" type="primary" onClick={handleCancel} >
              Cancel
            </Button>,
            <Button key="update" type="primary" onClick={handleSelectedTemplate} disabled={!selectedTemplateId}>
              Apply
            </Button>,
          ]}
      >
        <Row>
        <Col span={24}>
          <Form.Item label="Template" name="template">
            <Select
                showSearch
                style={{ width: "100%" }}
                getPopupContainer={(trigger) =>
                  trigger.parentNode as HTMLElement
                }
                onChange={(e: any) => {
                  setSelectedTemplateId(e)
                }}
            >
              {activeTemplates &&
              renderOptions(activeTemplates)}
            </Select>
          {<FolderTree
              key={selectedTemplateId}
              showTitle={false}
              maxHeight="500px"
              data={{ siteId, siteName: decodeURIComponent(siteName), siteStatusId: 1, folders: getSelectedTemplateFolders(selectedTemplateId) }}
              disableRoot={true}
              controlSelection={true}
              defaultExpandAll={true}
              disabled={true}
              onSelectFolder={(e) => logger.debug('File Area','Folder tree-Folder selected', e)} />}
          </Col>
        </Row>
      </Modal> */}
      {/*  Download Option Menu Modal */}
      <ModalCustom
        visible={showDownloadModal}
        title={'Download Files'}
        size={'small'}
        onCancel={handleOnDownloadModalCancel}
        footer={[
          <Button onClick={handleOnDownloadModalCancel} key="submit" type="primary">
            Done
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          {selectedFile && <DownloadModal
            hasDownloaded={(hasDownloaded: boolean) => {
              if (hasDownloaded) {
                setShowDownloadModal(false);
              }
            }}
            selectedFiles={[selectedFile]}
            downloadType={downloadType}
          />}
        </div>
      </ModalCustom>

    </Fragment>
  );
};

export default withRouter(Page);
