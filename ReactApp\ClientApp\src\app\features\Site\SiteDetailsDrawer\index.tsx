import { Drawer } from "antd";
import React from "react";

type Props = {
  siteName: string;
  showDrawer: boolean;
  onClose: () => void;
};

const SiteDetailsDrawer = ({ onClose, showDrawer, siteName }: Props) => {
  return (
    <Drawer
      title={siteName}
      placement="right"
      onClose={onClose}
      visible={showDrawer}
      className={"yjDrawerPanel"}
      width={700}

    >
      <p style={{textAlign:'center'}}>No Records Available.</p>
    </Drawer>
  );
};

export default SiteDetailsDrawer;
