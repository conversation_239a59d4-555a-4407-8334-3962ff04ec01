export interface ChannelType {
  id: string;
  name: string;
}

export interface IFile {
  assignee: any;
  created: string;
  createdBy: string;
  expirationDate: string;
  expirationStatus: any;
  fileCondition: string;
  id: string;
  modified: string;
  projects: [];
  size: string;
  status: any;
  tags: [];
  title: string;
  type: string;
  year: number;
  folder?: string;
  published: boolean;
  uploadReference: string;
  linked: boolean;
  linkedSiteCount: number;
  fK_FileAreaId: number;
}

export interface IFileArea {
  clientRef: string;
  siteName: string;
  status: string;
  isFolderExist: boolean;
  binderCount: number;
  siteId: string;
  fileAreaId: number;
  legalHold: boolean
}
