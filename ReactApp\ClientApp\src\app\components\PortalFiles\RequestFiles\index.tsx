import { Button, Form, Input } from "antd";
import React, { Fragment, useEffect, useState } from "react";
import { useDispatch } from "react-redux";
import {
  getPortlFileRequest,
  validatePortalFileRequest,
} from "@app/api/portalServices";
import { updatePortalFIlesUploadDetails } from "@app/redux/actions/fileAreaActions";
import { IPortalRequest } from "@app/types/portalTypes";
import { errorNotification } from "@app/utils/antNotifications";
import styles from "./index.module.less";
import logger from "@app/utils/logger";
import { dynamicRequiredValidator } from "@app/components/forms/validators";
import { encrypt } from "@app/utils/crypto/cryptoText";
import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";

export interface IRequestFiles {
  history: any;
  requestId: string;
}

export default (props: IRequestFiles) => {
  const SPACE_BAR = "space";
  const [portalRequest, setPortalRequest] = useState<IPortalRequest>({
    expired: true,
    secured: false,
    description: "",
    name: "",
    requestId: "",
    expirationDate: "",
    deletedRequest: false,
  });

  const dispatch = useDispatch();
  const [enableInput, setEnableInput] = useState(true);
  const [enableProceed, setEnabProceed] = useState(true);

  useEffect(() => {
    if (portalRequest.requestId) {
      setEnableInput(
        portalRequest.expired ||
          portalRequest.deletedRequest ||
          !portalRequest.secured
      );
      setEnabProceed(portalRequest.expired || portalRequest.deletedRequest);
    }
  }, [portalRequest]);

  useEffect(() => {
    document.title = "Request Files(s)";
    getPortlFileRequest(props.requestId)
      .then((response) => {
        const portalRequestValue = response.data as IPortalRequest;
        if (portalRequestValue.expired) {
          errorNotification([""], "Sorry this link has expired");
        }
        setPortalRequest(portalRequestValue);
      })
      .catch(() => {
        errorNotification([""], "The link is no longer valid");
        setPortalRequest({
          ...portalRequest,
          deletedRequest: true,
          secured: true,
        });
      });
  }, [props.requestId]);

  const validateRequest = (values: any) => {
    values.securityKey = values.securityKey ? values.securityKey : "";

    validatePortalFileRequest(props.requestId, values.securityKey)
      .then((response) => {
        if (!response.data && !response.data.token) {
          errorNotification([""], "Invalid key. Please try again");
        }

        dispatch(
          updatePortalFIlesUploadDetails({
            requestId: props.requestId,
            securityKey: values.securityKey,
          })
        );

        const encrypedKey = encrypt(values.securityKey);

        props.history.push(
          `/requestFiles/${props.requestId}/${encrypedKey}/upload`
        );
      })
      .catch((error) => {
        logger.error("Portal Files Area", "Request Files", error);
        errorNotification([""], "Validating File request failed");
      });
  };

  return (
    <>
      {portalRequest && (
        <Fragment>
          <div className={styles.yjExternalUrlWrapper}>
            <header className={styles.yjExternalUrlHeader}>
              <div className={styles.yjExteralUrlLogo}></div>
              <div className={styles.yjExteralUrlHeading}>
                <h1>Request Files(s)</h1>
              </div>
            </header>
            <div className={styles.yjExternalUrlContent}>
              <Form
                onFinish={validateRequest}
                size="middle"
                name="validateRequest"
              >
                <Form.Item
                  rules={[
                    portalRequest.secured
                      ? dynamicRequiredValidator(
                          true,
                          "Please Enter a valid security key !"
                        )
                      : dynamicRequiredValidator(false),
                  ]}
                  label="Enter Security Key"
                  name="securityKey"
                  colon={false}
                  style={{
                    display: "inline-block",
                    width: "calc(100% - 8px)",
                    margin: "0 8px",
                  }}
                >
                  <Input.Password
                    onPaste={(e) => {
                      e.preventDefault();
                    }}
                    onKeyDown={(event) => {
                      if (event.key === " " || event.key === SPACE_BAR) {
                        event.preventDefault();
                      }
                    }}
                    maxLength={10}
                    disabled={enableInput}
                    placeholder="enter a valid security key"
                    autoComplete="off"
                  />
                </Form.Item>

                <Form.Item>
                  <Button
                    disabled={enableProceed}
                    htmlType="submit"
                    type={"primary"}
                  >
                    Proceed
                  </Button>
                </Form.Item>
              </Form>
            </div>
          </div>
        </Fragment>
      )}
    </>
  );
};
