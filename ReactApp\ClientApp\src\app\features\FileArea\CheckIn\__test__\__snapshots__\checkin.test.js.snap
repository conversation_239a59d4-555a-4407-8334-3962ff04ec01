// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Checkin Test Suite Checkin component should render and create the snapshot properly when any props are  provided 1`] = `
<div>
  <div>
    <div>
      <form
        autoComplete="off"
        className="ant-form ant-form-horizontal"
        onReset={[Function]}
        onSubmit={[Function]}
      >
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-24"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <label
                      className="ant-radio-wrapper ant-radio-wrapper-checked"
                    >
                      <span
                        className="ant-radio ant-radio-checked"
                        style={Object {}}
                      >
                        <input
                          checked={true}
                          className="ant-radio-input"
                          disabled={false}
                          onBlur={[Function]}
                          onChange={[Function]}
                          onFocus={[Function]}
                          onKeyDown={[Function]}
                          onKeyPress={[Function]}
                          onKeyUp={[Function]}
                          type="radio"
                        />
                        <span
                          className="ant-radio-inner"
                        />
                      </span>
                      <span>
                        <b>
                          INDIVIDUAL CHECK-IN
                        </b>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-24"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div>
              <div
                className="ant-row"
                style={
                  Object {
                    "marginLeft": -12,
                    "marginRight": -12,
                  }
                }
              >
                <div
                  className="ant-col ant-col-1"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                />
                <div
                  className="ant-col ant-col-3"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="ID"
                      >
                        ID
                      </label>
                    </div>
                    <div
                      className="ant-col ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="File"
                      >
                        File
                      </label>
                    </div>
                    <div
                      className="ant-col ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="Downloaded File Name"
                      >
                        Downloaded File Name
                      </label>
                    </div>
                    <div
                      className="ant-col ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div />
            </div>
          </div>
        </div>
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-4"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <label
                      className="ant-radio-wrapper"
                    >
                      <span
                        className="ant-radio"
                        style={Object {}}
                      >
                        <input
                          checked={false}
                          className="ant-radio-input"
                          disabled={false}
                          onBlur={[Function]}
                          onChange={[Function]}
                          onFocus={[Function]}
                          onKeyDown={[Function]}
                          onKeyPress={[Function]}
                          onKeyUp={[Function]}
                          type="radio"
                        />
                        <span
                          className="ant-radio-inner"
                        />
                      </span>
                      <span>
                        <b>
                          BULK UPLOAD
                        </b>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            className="ant-col ant-col-20"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <span
                      className="yjCheckinBulkUploader"
                    >
                      <div
                        className="ant-upload ant-upload-select ant-upload-select-text"
                      >
                        <span
                          className="ant-upload"
                          onClick={[Function]}
                          onDragOver={[Function]}
                          onDrop={[Function]}
                          onKeyDown={[Function]}
                          role="button"
                          tabIndex="0"
                        >
                          <input
                            accept=""
                            multiple={true}
                            onChange={[Function]}
                            onClick={[Function]}
                            style={
                              Object {
                                "display": "none",
                              }
                            }
                            type="file"
                          />
                          <button
                            className="ant-btn ant-btn-primary"
                            disabled={true}
                            onClick={[Function]}
                            type="button"
                          >
                            <span
                              aria-label="upload"
                              className="anticon anticon-upload"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="upload"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
                                />
                              </svg>
                            </span>
                            <span>
                               Browse
                            </span>
                          </button>
                        </span>
                      </div>
                      <div
                        className="ant-upload-list ant-upload-list-text"
                      />
                    </span>
                    <button
                      className="ant-btn ant-btn-primary"
                      disabled={true}
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="delete"
                        className="anticon anticon-delete"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="delete"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"
                          />
                        </svg>
                      </span>
                      <span>
                         Delete
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
`;

exports[`Checkin Test Suite Checkin component should render and create the snapshot properly when any props are not provided 1`] = `
<div>
  <div>
    <div>
      <form
        autoComplete="off"
        className="ant-form ant-form-horizontal"
        onReset={[Function]}
        onSubmit={[Function]}
      >
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-24"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <label
                      className="ant-radio-wrapper ant-radio-wrapper-checked"
                    >
                      <span
                        className="ant-radio ant-radio-checked"
                        style={Object {}}
                      >
                        <input
                          checked={true}
                          className="ant-radio-input"
                          disabled={false}
                          onBlur={[Function]}
                          onChange={[Function]}
                          onFocus={[Function]}
                          onKeyDown={[Function]}
                          onKeyPress={[Function]}
                          onKeyUp={[Function]}
                          type="radio"
                        />
                        <span
                          className="ant-radio-inner"
                        />
                      </span>
                      <span>
                        <b>
                          INDIVIDUAL CHECK-IN
                        </b>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-24"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div>
              <div
                className="ant-row"
                style={
                  Object {
                    "marginLeft": -12,
                    "marginRight": -12,
                  }
                }
              >
                <div
                  className="ant-col ant-col-1"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                />
                <div
                  className="ant-col ant-col-3"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="ID"
                      >
                        ID
                      </label>
                    </div>
                    <div
                      className="ant-col ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="File"
                      >
                        File
                      </label>
                    </div>
                    <div
                      className="ant-col ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
                <div
                  className="ant-col ant-col-5"
                  style={
                    Object {
                      "paddingLeft": 12,
                      "paddingRight": 12,
                    }
                  }
                >
                  <div
                    className="ant-row ant-form-item"
                    style={Object {}}
                  >
                    <div
                      className="ant-col ant-form-item-label"
                      style={Object {}}
                    >
                      <label
                        className="ant-form-item-no-colon"
                        title="Downloaded File Name"
                      >
                        Downloaded File Name
                      </label>
                    </div>
                    <div
                      className="ant-col ant-form-item-control"
                      style={Object {}}
                    >
                      <div
                        className="ant-form-item-control-input"
                      >
                        <div
                          className="ant-form-item-control-input-content"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div>
              <div />
            </div>
          </div>
        </div>
        <div
          className="ant-row"
          style={
            Object {
              "marginLeft": -12,
              "marginRight": -12,
            }
          }
        >
          <div
            className="ant-col ant-col-4"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <label
                      className="ant-radio-wrapper"
                    >
                      <span
                        className="ant-radio"
                        style={Object {}}
                      >
                        <input
                          checked={false}
                          className="ant-radio-input"
                          disabled={false}
                          onBlur={[Function]}
                          onChange={[Function]}
                          onFocus={[Function]}
                          onKeyDown={[Function]}
                          onKeyPress={[Function]}
                          onKeyUp={[Function]}
                          type="radio"
                        />
                        <span
                          className="ant-radio-inner"
                        />
                      </span>
                      <span>
                        <b>
                          BULK UPLOAD
                        </b>
                      </span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div
            className="ant-col ant-col-20"
            style={
              Object {
                "paddingLeft": 12,
                "paddingRight": 12,
              }
            }
          >
            <div
              className="ant-row ant-form-item"
              style={Object {}}
            >
              <div
                className="ant-col ant-form-item-control"
                style={Object {}}
              >
                <div
                  className="ant-form-item-control-input"
                >
                  <div
                    className="ant-form-item-control-input-content"
                  >
                    <span
                      className="yjCheckinBulkUploader"
                    >
                      <div
                        className="ant-upload ant-upload-select ant-upload-select-text"
                      >
                        <span
                          className="ant-upload"
                          onClick={[Function]}
                          onDragOver={[Function]}
                          onDrop={[Function]}
                          onKeyDown={[Function]}
                          role="button"
                          tabIndex="0"
                        >
                          <input
                            accept=""
                            multiple={true}
                            onChange={[Function]}
                            onClick={[Function]}
                            style={
                              Object {
                                "display": "none",
                              }
                            }
                            type="file"
                          />
                          <button
                            className="ant-btn ant-btn-primary"
                            disabled={true}
                            onClick={[Function]}
                            type="button"
                          >
                            <span
                              aria-label="upload"
                              className="anticon anticon-upload"
                              role="img"
                            >
                              <svg
                                aria-hidden="true"
                                data-icon="upload"
                                fill="currentColor"
                                focusable="false"
                                height="1em"
                                viewBox="64 64 896 896"
                                width="1em"
                              >
                                <path
                                  d="M400 317.7h73.9V656c0 4.4 3.6 8 8 8h60c4.4 0 8-3.6 8-8V317.7H624c6.7 0 10.4-7.7 6.3-12.9L518.3 163a8 8 0 00-12.6 0l-112 141.7c-4.1 5.3-.4 13 6.3 13zM878 626h-60c-4.4 0-8 3.6-8 8v154H214V634c0-4.4-3.6-8-8-8h-60c-4.4 0-8 3.6-8 8v198c0 17.7 14.3 32 32 32h684c17.7 0 32-14.3 32-32V634c0-4.4-3.6-8-8-8z"
                                />
                              </svg>
                            </span>
                            <span>
                               Browse
                            </span>
                          </button>
                        </span>
                      </div>
                      <div
                        className="ant-upload-list ant-upload-list-text"
                      />
                    </span>
                    <button
                      className="ant-btn ant-btn-primary"
                      disabled={true}
                      onClick={[Function]}
                      type="button"
                    >
                      <span
                        aria-label="delete"
                        className="anticon anticon-delete"
                        role="img"
                      >
                        <svg
                          aria-hidden="true"
                          data-icon="delete"
                          fill="currentColor"
                          focusable="false"
                          height="1em"
                          viewBox="64 64 896 896"
                          width="1em"
                        >
                          <path
                            d="M360 184h-8c4.4 0 8-3.6 8-8v8h304v-8c0 4.4 3.6 8 8 8h-8v72h72v-80c0-35.3-28.7-64-64-64H352c-35.3 0-64 28.7-64 64v80h72v-72zm504 72H160c-17.7 0-32 14.3-32 32v32c0 4.4 3.6 8 8 8h60.4l24.7 523c1.6 34.1 29.8 61 63.9 61h454c34.2 0 62.3-26.8 63.9-61l24.7-523H888c4.4 0 8-3.6 8-8v-32c0-17.7-14.3-32-32-32zM731.3 840H292.7l-24.2-512h487l-24.2 512z"
                          />
                        </svg>
                      </span>
                      <span>
                         Delete
                      </span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </form>
    </div>
  </div>
</div>
`;
