import React, { useEffect, useState } from "react";
import { Form, Input, Select, Skeleton,Drawer, Modal as AntModal,  But<PERSON> } from "antd";
import { useSelector,useDispatch } from "react-redux";
import { useHistory } from "react-router-dom";

import styles from "./index.module.less";
import logger from "../../../utils/logger";
import SelectedFilesGrid, {
  ColumnConfig,
} from "@app/features/FileArea/SelectedFilesGrid";
import { IFile } from "@app/types/fileAreaTypes";
import { getFileStatuses, getUsers } from "@app/api/fileAreaService";
import { required } from "@app/components/forms/validators";
import { RootState } from "@app/redux/reducers/state";
import { FormInstance } from "antd/lib/form";
import { useForm } from 'antd/lib/form/Form';
import{updateContextMenuAssignOption} from '@app/redux/actions/fileAreaActions';
import{ExclamationCircleOutlined} from '@ant-design/icons';
import{updateAssigneeNStatus } from '@app/api/fileAreaService';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import HTTPResponse from '@app/utils/http/interfaces/HttpResponse';

export interface AssignFormEvents {
  onAssigneeUpdate: (value: any) => void;
  onFilesChange: (fileList: IFile[]) => void;
}
export interface AssignOptionProps {
  formRef?: any;
  siteId: string;
  selectedFiles: IFile[];
  onClosePopup: () => void;
  showAssignModal: boolean;
  onSuccess: () => void;//sync grid
  form: FormInstance;
}

const { confirm } = AntModal;
const { Option } = Select;

const FORBIDDEN_ERROR_CODE = 403;
const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';



const defaultAssignOptionColumnConfigs: ColumnConfig[] = [
  { title: "", dataIndex: "remove", key: "remove", width: 40 },
  { title: "Id", dataIndex: "id", key: "id" },
  { title: "Title", dataIndex: "title", key: "title", ellipsis: true },
  { title: "Current Assignee", dataIndex: "assignee", key: "assignee" },
];
export default (props: AssignOptionProps) => {
  const [assignOptionDetails, setAssignOptionDetails] = useState<{
      assignee: number;
      files: IFile[];
    }>({ assignee: 0, files: [] });

  const [assignOptionColumnConfigs, setAssignOptionColumnConfigs] = useState<
    ColumnConfig[]
  >([]);
  const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';
  const [statusList, setStatusList] = useState<any[]>([]);
  const [assigneeList, setAssigneeList] = useState<any[]>([]);
  const { userPermission } = useSelector( (state: RootState) => state.userManagement );
  //const [form] = useForm();
  const dispatch = useDispatch();
  const history = useHistory();

  useEffect(() => {
    if (userPermission.privDMSCanManageFileProperties) {
      const customColumnConfigs = [
        ...defaultAssignOptionColumnConfigs,
        { title: "Current Status", dataIndex: "status", key: "status" },
      ];
      setAssignOptionColumnConfigs(customColumnConfigs);
    } else {
      setAssignOptionColumnConfigs(defaultAssignOptionColumnConfigs);
    }
  }, [props.selectedFiles]);

  useEffect(() => {
    getFileStatuses()
      .then((response) => {
        setStatusList(response.data);
      })
      .catch((error) => {
        logger.error("AssignOption", "Retrieve Status List", error);
        errorNotification([""], "Unable to Retrieve Status List");
      });
  }, []);

  useEffect(() => {
    getUsers(props.siteId)
      .then((response) => {
        setAssigneeList(response.data);
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        } else {
          errorNotification([""], "Unable to Retrieve Assignee List");
        }
        logger.error("AssignOption", "Retrieve Assignee List", error);
      });
  }, []);

  const handleFilesChange = (fileList: any[]) => {
    if (fileList.length > 0) {
      handleAssignFormEvents.onFilesChange(fileList);
    } else {
      props.onClosePopup();
    }
  };
  const layout = {
    labelCol: { span: 4 },
    wrapperCol: { span: 20 },
  };

  const filterOption = (input: any, option: any) => {
    return option?.children.toLowerCase().indexOf(input.toLowerCase()) >= 0;
  };

const resetAssignModal = () => {
    setAssignOptionDetails({ assignee: 0, files: [] });
    dispatch(updateContextMenuAssignOption(false));
    props.form.resetFields();
    props.onClosePopup();
  };

  const cancelAssignModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        props.form.resetFields();
        resetAssignModal();
      },
    });
  };

  const handleShowAssignModalCancel = (isAllFilesRemoved = false) => {
    if (assignOptionDetails.assignee > 0 || props.form.getFieldValue('statusId') > 0 || props.form.getFieldValue('assignNotes')) {
      if (isAllFilesRemoved) {
        props.form.resetFields();
        resetAssignModal();
      } else {
        cancelAssignModal();
      }
    } else {
      resetAssignModal();
    }
  };

  const onUpdatedAssignee = (response: HTTPResponse<any>) => {
    if (response.data) {
      const SET_TIMEOUT_ASSIGNEE = 500;
      successNotification([''], 'Assignment Successful');
      // successNotification([''], 'File Assignment Email sent successfully');
      props.onSuccess();
      dispatch(updateContextMenuAssignOption(false));
      setTimeout(() => {
        props.form.resetFields();
        props.onClosePopup();
        setAssignOptionDetails({ assignee: 0, files: [] });
      }, SET_TIMEOUT_ASSIGNEE);
    } else {
      errorNotification([''], 'Assignment Failed');
    }
  };

  const handleAssignOptionUpdate = () => {
      props.form.validateFields().then((values) => {
        const selectedFileIds = assignOptionDetails.files.length > 0 ? assignOptionDetails.files.map((file) => file.id) : props.selectedFiles.map((file) => file.id);
        updateAssigneeNStatus({
          assigneeId: values.assigneeId,
          statusId: values.statusId,
          fileIds: selectedFileIds,
          note: values.assignNotes,
        })
          .then((response) => {
            onUpdatedAssignee(response);
          })
          .catch((error) => {
            if (error.statusCode === FORBIDDEN_ERROR_CODE) {
              errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
            } else {
              errorNotification([''], 'Assignment Failed');
            }
            logger.error('AssignOption', 'Update Assignee and Status', error);
          });
      });
    };

    const onAssigneeUpdate = (value: any) => {
      const currentAssignState = { ...assignOptionDetails };
      currentAssignState.assignee = value;
      setAssignOptionDetails(currentAssignState);
    };
    const onFilesChange = (fileList: IFile[]) => {
      const currentAssignState = { ...assignOptionDetails };
      currentAssignState.files = fileList;
      setAssignOptionDetails(currentAssignState);
    };
    const handleAssignFormEvents: AssignFormEvents = {
      onAssigneeUpdate,
      onFilesChange,
    };
    
  return (
    <Drawer
    destroyOnClose={true}
    visible={props.showAssignModal}
    width={700}
    title={'Assign File(s)'}
    onClose={() => handleShowAssignModalCancel()}
    className={"yjDrawerPanel"}
    footer={[
      <Button key="back" type="default" onClick={() => handleShowAssignModalCancel()}>
        cancel
      </Button>,
      <Button key="back" type="primary" disabled={assignOptionDetails.assignee === 0} onClick={handleAssignOptionUpdate}>
        Update
      </Button>,
    ]}
  >
      <div className={styles.yjModalContentWrapper}>
        {assignOptionColumnConfigs.length > 0 ? (
          <SelectedFilesGrid
            onFilesChange={handleFilesChange}
            columnConfigs={assignOptionColumnConfigs}
            dataList={props.selectedFiles}
          />
        ) : (
          <Skeleton active />
        )}
        <div className={styles.yjAssignFilesFormWrapper}>
          <Form
            {...layout}
            size="middle"
            name="basic"
            form={props.formRef}
            className={styles.yjAssignFilesForm}
          >
            <Form.Item
              label={"New Assignee"}
              name="assigneeId"
              rules={[required]}
              className={styles.yjAssignFilesFormRowItem}
              colon={false}
            >
              <Select
                onChange={(value) => handleAssignFormEvents.onAssigneeUpdate(value)}
                placeholder="Select an Assignee"
                optionFilterProp="children"
                showSearch
              >
                {assigneeList.map((assignee) => (
                  <Option value={assignee.value}>{assignee.name}</Option>
                ))}
              </Select>
            </Form.Item>
            <Form.Item
              label={"Assign Notes"}
              name="assignNotes"
              className={styles.yjAssignFilesFormRowItem}
              colon={false}
            >
              <Input.TextArea maxLength={200} />
            </Form.Item>
            {userPermission.privDMSCanManageFileProperties && (
              <Form.Item
                label={"New Status"}
                name="statusId"
                className={styles.yjAssignFilesFormRowItem}
                colon={false}
              >
                <Select
                  showSearch
                  filterOption={filterOption}
                  placeholder="Select a status"
                >
                  {statusList.map((state) => (
                    <Option value={state.value}>{state.name}</Option>
                  ))}
                </Select>
              </Form.Item>
            )}
          </Form>
        </div>
      </div>
    </Drawer>
  );
};
