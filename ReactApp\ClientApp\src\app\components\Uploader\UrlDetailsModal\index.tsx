import React, { useState, useEffect } from "react";
import UploaderSubmitContainer from "@app/components/forms/UploaderSubmit/UploaderSubmitContainer";
import { Form } from "antd";

import { URL_UPLOAD } from "../constants/uploadTypes";
import { UrlEvents } from "@app/components/forms/UploaderSubmit/types";
import Modal from "@app/components/Modal";
import confirmDiscard from "../utils/confirmDiscard";
import { successNotification } from "@app/utils/antNotifications";

type PropTypes = {
  uploadType: number;
  siteId: string;
  binderId: string;
  onClose?: () => void;
  onFinish?: () => void;
};

const MaximumFileTitleLength = 100;

const UrlDetailsModal: React.FC<PropTypes> = ({
  uploadType,
  siteId,
                                                binderId,
  onClose,
  onFinish,
}) => {
  const [form] = Form.useForm();
  const [enableDone, setEnableDone] = useState(false);
  const [url, setUrl] = useState("");
  const [title, setTitle] = useState("");
  const [folderTreeChange, setFolderTreeChange] = useState(false);

  useEffect(() => {
    if (url && title && title.length <= MaximumFileTitleLength) {
      setEnableDone(true);
    } else {
      setEnableDone(false);
    }
  }, [title, url]);

  const onUrlUpdate = (value: string) => {
    setUrl(value);
  };

  const onTitleUpdate = (value: string) => {
    setTitle(value);
  };

  const urlEvents: UrlEvents = { onUrlUpdate, onTitleUpdate };

  return (
    <>
      <Modal
        width="70%"
        style={{ top: 20 }}
        title="URL File Uploader"
        visible={uploadType === URL_UPLOAD}
        destroyOnClose={true}
        okText="DONE"
        onOk={() => {
          form.validateFields().then((values) => {
            successNotification([""], "Successfully uploaded");
            if (values.assigneeId) {
              // successNotification(
              //   [""],
              //   "File assignment Email sent successfully"
              // );
            }

            if (values.users) {
              successNotification([""], "File Upload Email sent successfully");
            }

            onFinish?.();
          });
        }}
        onCancel={() => {
          if (url) {
            confirmDiscard(() => onClose?.());
            return;
          }
          onClose?.();
        }}
        okButtonProps={{ disabled: !enableDone || !folderTreeChange }}
      >
        <UploaderSubmitContainer
          uploadType={uploadType}
          siteId={siteId}
          binderId={binderId}
          form={form}
          urlEvents={urlEvents}
          disabledForm={!(url && title)}
          onFolderTreeChange={(isValid) => setFolderTreeChange(isValid >= 0)}
        />
      </Modal>
    </>
  );
};

export default UrlDetailsModal;
