import React from 'react';
import { <PERSON><PERSON>, <PERSON> } from 'antd';
import { useSelector } from 'react-redux';
import { RootState } from '@app/redux/reducers/state';

interface SubmitButtonProps {
    children: React.ReactNode;
    htmlType?: 'button' | 'submit' | 'reset';
    type?: 'primary' | 'default' | 'dashed' | 'link' | 'text';
    onClick?: () => void;
    className?: string;
    disabled?: boolean;
}

const SubmitButton: React.FC<SubmitButtonProps> = ({
    children,
    htmlType = 'submit',
    type = 'primary',
    onClick,
    className,
    disabled,
}) => {
    const isLoading = useSelector((state: RootState) => state.functionalFlow.isLoading);

    return (
        <Button
            htmlType={htmlType}
            type={type}
            onClick={onClick}
            className={className}
            disabled={disabled || isLoading}
        >
            {isLoading ? <Spin size="small" /> : children}
        </Button>
    );
};

export default SubmitButton;