import { UploadOutlined } from "@ant-design/icons";
import { useForm } from "antd/lib/form/Form";
import { RcFile } from "antd/lib/upload";
import moment from "moment";
import React, { Fragment, useEffect, useRef, useState } from "react";
import { useParams } from "react-router-dom";
import { Button, Form, Input, notification, Skeleton, Upload } from "antd";

import { getPortlFileRequest, submitPortalFile } from "@app/api/portalServices";
import { removeFilesWithToken } from "@app/api/fileAreaService";

import {
  dynamicRequiredValidator,
  type,
} from "@app/components/forms/validators";
import { IPortalRequest } from "@app/types/portalTypes";
import {
  errorNotification,
  successNotification,
} from "@app/utils/antNotifications";
import styles from "./index.module.less";
import useSimpleUploader from "../../Uploader/hooks/UseSimpleUploader";
import { EmailPattern } from "@app/utils/regex";
import logger from "@app/utils/logger";
import { TOO_MANY_FILES } from "@app/components/Uploader/constants/errors";
import { decrypt } from "@app/utils/crypto/cryptoText";
import {
  FULL_PERCENTAGE,
  LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE,
  MAXIMUM_FILE_COUNT,
} from "@app/components/Uploader";

export interface IuploadRequest {
  history: any;
}

const MAXIMUM_FILE_SIZE = 1073741824;
const UNSUCCESSFUL_MESSAGE = "File could not be uploaded.";

export default () => {
  const { files, action } = useSimpleUploader();
  const { initializePortalUpload, addPortalFile, remove, removeAll } = action;
  const [uploadedUid, setUploadedUid] = useState("");

  const [portalRequest, setPortalRequest] = useState<IPortalRequest>({
    expired: true,
    secured: false,
    description: "",
    name: "",
    requestId: "",
    expirationDate: "",
    deletedRequest: false,
  });
  const [loadingData, setLoadingData] = useState(true);
  const [form] = useForm();
  const [validated, setValidated] = useState(false);
  const [emailChanged, setEmailChanged] = useState(false);
  const [securityKeyInput, setSecurityKeyInput] = useState("");
  const isShowingError = useRef(false);
  const uploadRef = useRef() as any;
  const { id, securityKey } = useParams<any>();
  const [uploadFileList, setUploadFileList] = useState([]) as any;
  const [tooManyFiles, setTooManyFiles] = useState(false);

  useEffect(() => {
    setLoadingData(true);
    getPortlFileRequest(id).then((response) => {
      const currentPortalRequest = response.data as IPortalRequest;
      setPortalRequest(currentPortalRequest);
      setLoadingData(false);
    });
  }, [id]);

  useEffect(() => {
    if (Object.keys(files).length > 0) {
      if (files[uploadedUid].error) {
        logUploadPortalFileErrors(files[uploadedUid].error);
        removeFilesWithToken("", [files[uploadedUid].referenceNumber], false);
      } else {
        const { file } = files[uploadedUid].uploadOptions;
        const emailValue = form.getFieldValue("email");
        if (Math.round(files[uploadedUid].percent || 0) === FULL_PERCENTAGE) {
          handleSubmitPortalFile(
            files[uploadedUid].referenceNumber,
            file,
            emailValue
          );
        }
      }
    }
  }, [uploadedUid]);

  const validateEMail = (email: string) => {
    return EmailPattern.test(email);
  };

  useEffect(() => {
    if (validateEMail(form.getFieldValue("email"))) {
      setValidated(true);
    } else {
      setValidated(false);
    }
  }, [emailChanged, form]);

  useEffect(() => {
    setSecurityKeyInput(securityKey);
  }, [securityKey]);

  useEffect(() => {
    if (tooManyFiles) {
      errorNotification(
        [""],
        "You can only upload a maximum of 20 files at a time"
      );
    }
  }, [tooManyFiles]);

  const logUploadPortalFileErrors = (error: any) => {
    logger.error("Portal File Section", "Upload Request Files", error);
    errorNotification([""], UNSUCCESSFUL_MESSAGE);

    if (error.statusCode && error.statusCode === 409) {
      errorNotification([""], LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE);
    }
    return false;
  };

  const handleSubmitPortalFile = (
    referenceNo: string | null,
    file: any,
    emailValue: any
  ) => {
    const securityKeyDecrypted =
      securityKeyInput === "null" ? null : decrypt(securityKeyInput);
    submitPortalFile(id, referenceNo, securityKeyDecrypted, emailValue)
      .then(() => {
        successNotification([""], "File successfully uploaded.");
      })
      .catch((error) => {
        logUploadPortalFileErrors(error);
      });
  };

  const onCustomRequestTrigger = (options: any) => {
    const emailValue = form.getFieldValue("email");
    initializePortalUpload(id, emailValue);
    addPortalFile(options, undefined, setUploadedUid);
  };

  const onBeforeUpload = (file: RcFile, fileList: RcFile[]) => {
    removeAll();
    if (!validated) {
      return false;
    }
    if (fileList.length > MAXIMUM_FILE_COUNT) {
      uploadRef.current.state.fileList = [];
      if (!isShowingError.current) {
        isShowingError.current = true;
        notification.error({
          message: TOO_MANY_FILES,
          onClose: () => (isShowingError.current = false),
          className: "yjErrorMsg",
        });
      }
      return false;
    }
    if (file.size > MAXIMUM_FILE_SIZE) {
      errorNotification(
        [""],
        "File is too large. Maximum uploadable file size is 1GB"
      );
      return false;
    }
    return true;
  };

  return loadingData ? (
    <Skeleton active />
  ) : (
    <Fragment>
      <div className={styles.yjUploadRequestWrapper}>
        <header className={styles.yjUploadRequestHeader}>
          <div className={styles.yjUploadRequestLogo}></div>
          <div className={styles.yjUploadRequestHeading}>
            <h1>Request Files(s)</h1>
          </div>
        </header>
        <div className={styles.yjUploadRequestContent}>
          <Form form={form} name="basic" className={styles.yjUploadRequestForm}>
            <Form.Item
              label="Request ID"
              name="request"
              className={styles.yjUploadRequestFormRowItem}
              colon={false}
            >
              <p>{portalRequest.requestId}</p>
            </Form.Item>
            <Form.Item
              label="Request Name"
              name="name"
              className={styles.yjUploadRequestFormRowItem}
              colon={false}
            >
              <p>{portalRequest.name}</p>
            </Form.Item>
            <Form.Item
              label="Description"
              name="description"
              className={styles.yjUploadRequestFormRowItem}
              colon={false}
            >
              <p className={styles.yjUploadRequestDescription}>
                {portalRequest.description ? portalRequest.description : " - "}
              </p>
            </Form.Item>
            <Form.Item
              label="Email"
              name="email"
              className={styles.yjUploadRequestFormRowItem}
              colon={false}
              rules={[
                dynamicRequiredValidator(true, "Please Enter a Valid Email"),
                type("email"),
              ]}
            >
              <Input autoFocus maxLength={200} />
            </Form.Item>
            <Form.Item>
              <Upload
                onChange={(info) => {
                  setUploadFileList(() => {
                    if (
                      info.fileList.length > MAXIMUM_FILE_COUNT &&
                      !tooManyFiles
                    ) {
                      setTooManyFiles(true);
                      return [];
                    } else {
                      return [...info.fileList];
                    }
                  });
                }}
                fileList={uploadFileList}
                ref={uploadRef}
                multiple={true}
                onRemove={(file) => remove(file.uid)}
                openFileDialogOnClick={validated}
                style={{ position: "relative", width: "100%" }}
                customRequest={(options: any) =>
                  onCustomRequestTrigger(options)
                }
                beforeUpload={(file: RcFile, fileList: RcFile[]) =>
                  onBeforeUpload(file, fileList)
                }
              >
                <Button
                  onClick={() => {
                    setUploadFileList([]);
                    setTooManyFiles(false);
                  }}
                  type="primary"
                  className={styles.yjRequestUploadBtn}
                  onMouseOver={() => {
                    setEmailChanged(!emailChanged);
                  }}
                  htmlType={"submit"}
                  icon={<UploadOutlined />}
                >
                  Upload files here
                </Button>
              </Upload>
            </Form.Item>
          </Form>
          <p className={styles.yjUploadRequestInfo}>
            {portalRequest.expirationDate &&
              ` Link expires on ${moment(portalRequest.expirationDate)
                .toDate()
                .toLocaleDateString()}`}
          </p>
        </div>
      </div>
    </Fragment>
  );
};
