@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../styles/';

.yjModalContentWrapper {
  margin: -24px;
  max-height: 65vh;
  overflow: hidden auto;
  padding: 1.5em;
}

.yjCheckoutActionWrapper {

  .yjCheckoutActionUpperSection {

    .yjCheckoutActionForm {

      .yjCheckoutActionFormHeader {
        color: @color-secondary;
        font-size: @font-size-base / 1.145;
        text-transform: @yj-transform;

        .yjCheckoutActionColumn {

          label {
            color: @color-secondary;
            font-size: @font-size-base / 1.145;
            margin-top: 10px;
          }
        }

        .font-mixin(@font-primary, @yjff-semibold);
      }

      .yjCheckoutActionFormItem {
        margin-bottom: 10px;

        .yjCheckoutActionCheckbox {
          margin-top: -5px;
        }

        .yjCheckoutActionDatePicker {
          width: 100%;
        }

        .yjCheckoutActionTextArea {
          max-height: 30px;
          width: 100%;
        }

        .yjCheckoutFileName {
          border: none;
          overflow: hidden;
          padding: 0;
          pointer-events: none;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 99%;

          &:focus {
            border: none;
            box-shadow: none;
          }
        }

        .yjCheckoutId {
          border: none;
          padding: 0;
          pointer-events: none;

          &:focus {
            border: none;
            box-shadow: none;
          }
        }
      }
    }
  }
}

.yjCheckoutActionFormGrid {
  margin-bottom: 10px;
  max-height: 350px;
  overflow-x: hidden;
  overflow-y: auto;

  .yjCheckoutActionFormRow {
    border-bottom: 1px solid fade(@color-accent-border, 10%);
    margin-bottom: 10px;
  }
}

.yjCheckoutCommonNote {
  height: 350px;

  textarea {

    &.txtNotes {
      height: 175px;
    }
  }
}

.yjCheckoutCommonNotesLabel {

  span {
    color: @color-secondary;
    font-size: @font-size-base / 1.145;
    margin-top: 5px;
    text-transform: @yj-transform;

    .font-mixin(@font-primary, @yjff-semibold);
  }
}
