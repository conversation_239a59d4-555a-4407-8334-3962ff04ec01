import { useEffect, useState } from "react";
import Axios from "axios";

import { SimpleFileList } from "../types";
import { upload } from "@app/api/fileAreaService";
import { uploadPortalFile } from "@app/api/portalServices";

import HTTPResponse from "@app/utils/http/interfaces/HttpResponse";
import { DEFAULT_CHUNK_SIZE } from "@app/utils/files/formatSize";
import { ERROR_TYPE_CANCEL } from "@app/utils/http/index";
import {LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE, UPLOAD_FAILED_MESSAGE} from "@app/components/Uploader";

const UPLOAD_FILE_TYPE_INTERNAL = "I";
const UPLOAD_FILE_TYPE_PORTAL = "P";
const FULL_PERCENTAGE = 100;

export default () => {
  const [files, setFiles] = useState<SimpleFileList>({});
  const [uploadFileType, setUploadFileType] = useState<string>(
    UPLOAD_FILE_TYPE_INTERNAL
  );
  const [siteId, setSiteId] = useState("");
  const [portalRequestDetails, setPortalRequestDetails] = useState({
    requestId: "",
    email: "",
  });

  useEffect(() => {
    Object.entries(files).forEach(([uid, info]) => {
      if (info.toBeProceed) {
        if (uploadFileType === UPLOAD_FILE_TYPE_INTERNAL) {
          internalFileUpload(uid);
        } else {
          increaseChunkCounter(uid);
          portalFileUpload(uid);
        }
      }
    });
  }, [files]);

  const progressHandler = (
    uid: string,
    file: any,
    event: any,
    onProgress: any
  ) => {
    const currentProgress =
      (event.loaded / event.total / files[uid].chunkCount) * FULL_PERCENTAGE;
    const percent = (files[uid].percent as number) + currentProgress;
    onProgress({ percent }, file);
    updatePercent(uid, percent, currentProgress);
    files[uid].progress?.(percent);
  };

  const successResponseHandler = (
    uid: string,
    file: any,
    response: HTTPResponse<any>,
    onSuccess: any
  ) => {
    if (files[uid].chunkCounter === files[uid].chunkCount) {
      onSuccess(response, file);
      files[uid].uploadResult?.(uid);
    } else {
      increaseChunkCounter(uid);
    }
  };

  const initializeInternalUpload = (fileSiteId: string) => {
    setSiteId(fileSiteId);
  };

  const initializePortalUpload = (requestId: string, email: string) => {
    setUploadFileType(UPLOAD_FILE_TYPE_PORTAL);
    setPortalRequestDetails({ requestId: requestId, email: email });
  };

  const addInternalFile = (
    uploadOptions: any,
    index?: number,
    uploadResult?: (uid: string) => void,
    progress?: (percent: number) => void
  ) => {
    const { file } = uploadOptions;
    if (!files[file.uid]) {
      const cancelToken = Axios.CancelToken;
      const source = cancelToken.source();
      const _chunkCount = Math.floor(file.size / DEFAULT_CHUNK_SIZE);
      setFiles((current: SimpleFileList) => {
        return {
          ...current,
          [file.uid]: {
            id: index,
            uploadOptions: uploadOptions,
            chunkCount: _chunkCount + 1,
            chunkCounter: 1,
            chunkStartSize: 0,
            chunkEndSize: DEFAULT_CHUNK_SIZE,
            toBeProceed: true,
            referenceNumber: null,
            retryCount: 0,
            percent: 0,
            currentChunkPercent: 0,
            uploadResult: uploadResult,
            cancelTokenSource: source,
            cancel: source.cancel,
            progress: progress,
          },
        };
      });
    }
  };

  const addPortalFile = (
    uploadOptions: any,
    index?: number,
    uploadResult?: (uid: string) => void,
    progress?: (percent: number) => void
  ) => {
    const { file } = uploadOptions;
    if (!files[file.uid]) {
      const cancelToken = Axios.CancelToken;
      const source = cancelToken.source();
      const _chunkCount = Math.floor(file.size / DEFAULT_CHUNK_SIZE);
      setFiles((current: SimpleFileList) => {
        return {
          ...current,
          [file.uid]: {
            id: index ? index : undefined,
            uploadOptions: uploadOptions,
            chunkCount: _chunkCount + 1,
            chunkCounter: 1,
            chunkStartSize: 0,
            chunkEndSize: DEFAULT_CHUNK_SIZE,
            toBeProceed: true,
            referenceNumber: null,
            retryCount: 0,
            percent: 0,
            currentChunkPercent: 0,
            uploadResult: uploadResult,
            cancelTokenSource: source,
            cancel: source.cancel,
            progress: progress,
          },
        };
      });
    }
  };

  const setReferenceNumber = (uid: string, referenceNumber: string) => {
    setFiles((current: SimpleFileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], referenceNumber: referenceNumber },
      };
    });
  };

  const internalFileUpload = (uid: string) => {
    const { onSuccess, onError, file, onProgress } = files[uid].uploadOptions;
    const upFile = file.slice(
      files[uid].chunkStartSize,
      files[uid].chunkEndSize
    ) as File;
    changeToBeProceedStatus(uid, false);
    upload(
      file.name,
      upFile,
      siteId,
      files[uid].referenceNumber,
      files[uid].chunkCounter,
      files[uid].chunkCount,
      (event) => {
        progressHandler(uid, file, event, onProgress);
      },
      files[uid].cancelTokenSource?.token
    )
      .then((res) => {
        setReferenceNumber(uid, res.data.referenceNumber);
        successResponseHandler(uid, files, res, onSuccess);
      })
      .catch((error) => {

        // Ignore cancellation errors
        if (error.toString() === ERROR_TYPE_CANCEL) return;

        const { statusCode, message = 'Something went wrong.' } = error;

        if (statusCode >= 400 && statusCode < 500) {
          // No retries for 4xx errors
          resetPercent(uid);
          const errorMessage = statusCode === 409 ? LOW_SPACE_FILE_UPLOAD_ERROR_MESSAGE : message || 'An error occurred during upload.';

          updateError(uid, { name: uid, message: errorMessage });
        } else {
          resetPercent(uid);
          updateError(uid, { name: uid, message: message || UPLOAD_FAILED_MESSAGE });

        }
        files[uid].uploadResult?.(uid);
        onError(error);
      });
  };

  const portalFileUpload = (uid: string) => {
    const { onSuccess, onError, file, onProgress } = files[uid].uploadOptions;
    const upFile = file.slice(
      files[uid].chunkStartSize,
      files[uid].chunkEndSize
    ) as File;
    changeToBeProceedStatus(uid, false);
    uploadPortalFile(
      file.name,
      upFile,
      portalRequestDetails.requestId,
      portalRequestDetails.email,
      files[uid].referenceNumber,
      files[uid].chunkCounter,
      files[uid].chunkCount,
      (event) => {
        progressHandler(uid, file, event, onProgress);
      },
      files[uid].cancelTokenSource?.token
    )
      .then((res) => {
        setReferenceNumber(uid, res.data.referenceNumber);
        successResponseHandler(uid, files, res, onSuccess);
      })
      .catch((error) => {
        resetPercent(uid);

        // Ignore cancellation errors
        if (error.toString() === ERROR_TYPE_CANCEL) return;

        const { statusCode, message = 'Something went wrong.' } = error;

        // Client-side errors (4xx)
        if (statusCode >= 400 && statusCode < 500) {

          updateError(uid, { name: uid, message:  error.message || 'An error occurred during upload.' });

        // Server-side errors (5xx) or network issues
        } else if (statusCode >= 500 || !statusCode) {

          const errorMessage = error.statusCode >= 500
              ? "The server is currently unavailable. Please try again later."
              : "We couldn’t complete your upload due to a network problem. Please check your connection and try again.";

          updateError(uid, { name: uid, message: error.message || errorMessage });
        }

        files[uid].uploadResult?.(uid);
        onError(error); // General error callback
      });
  };

  const changeToBeProceedStatus = (uid: string, status: boolean) => {
    setFiles((current: SimpleFileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], toBeProceed: status },
      };
    });
  };

  const increaseChunkCounter = (uid: string) => {
    setFiles((current: SimpleFileList) => {
      return current[uid].chunkCounter !== current[uid].chunkCount
        ? {
            ...current,
            [uid]: {
              ...current[uid],
              chunkCounter: current[uid].chunkCounter + 1,
              chunkStartSize: current[uid].chunkEndSize,
              chunkEndSize: current[uid].chunkEndSize + DEFAULT_CHUNK_SIZE,
              toBeProceed: true,
            },
          }
        : { ...current };
    });
  };

  const updatePercent = (
    uid: string,
    percent: number,
    currentChunkPercent: number
  ) => {
    setFiles((current: SimpleFileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], percent, currentChunkPercent },
      };
    });
  };

  const resetPercent = (uid: string) => {
    setFiles((current: SimpleFileList) => {
      return {
        ...current,
        [uid]: {
          ...current[uid],
          percent:
            (current[uid]?.percent as number) -
            (current[uid]?.currentChunkPercent as number),
          currentChunkPercent: 0,
        },
      };
    });
  };

  const updateError = (uid: string, error: Error) => {
    setFiles((current: SimpleFileList) => {
      return {
        ...current,
        [uid]: { ...current[uid], error },
      };
    });
  };

  const remove = (uid: string) => {
    files[uid].cancel?.();

    setFiles(({ [uid]: value, ...rest }: SimpleFileList) => {
      return { ...rest };
    });
  };

  const removeAll = () => {
    Object.entries(files).forEach(([_, info]) => {
      if (info?.cancel) {
        info?.cancel();
      }
    });
    setFiles({});
  };

  return {
    files,
    action: {
      initializeInternalUpload,
      initializePortalUpload,
      addInternalFile,
      addPortalFile,
      remove,
      removeAll,
    },
  };
};
