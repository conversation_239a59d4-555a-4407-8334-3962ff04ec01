{"ast": null, "code": "import \"antd/es/drawer/style\";\nimport _Drawer from \"antd/es/drawer\";\nimport \"antd/es/form/style\";\nimport _Form from \"antd/es/form\";\nimport \"antd/es/space/style\";\nimport _Space from \"antd/es/space\";\nimport \"antd/es/radio/style\";\nimport _Radio from \"antd/es/radio\";\nimport \"antd/es/col/style\";\nimport _Col from \"antd/es/col\";\nimport \"antd/es/row/style\";\nimport _Row from \"antd/es/row\";\nimport \"antd/es/skeleton/style\";\nimport _Skeleton from \"antd/es/skeleton\";\nimport \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\Link\\\\LinkFilesDrawer.tsx\";\nimport React, { useEffect, useState } from \"react\";\nimport { CloseOutlined } from \"@ant-design/icons\";\nimport styles from \"./index.module.less\";\nimport SelectedFilesGrid from \"@app/features/FileArea/SelectedFilesGrid\";\nimport InfinitySelect from \"@app/components/InfinitySelect\";\nimport { getInfiniteRecords } from \"@app/api/infiniteRecordsService\";\nimport config from \"@app/utils/config\";\nimport { OperationalServiceTypes } from \"@iris/discovery.fe.client\";\nimport logger from \"@app/utils/logger\";\nimport { getClientAreaBinders } from \"@app/api/fileAreaService\";\nimport SubmitButton from \"@app/components/SubmitButton\";\nconst LIMIT = 10;\nexport default (({\n  onClosePopup,\n  onItemSelect,\n  selectedFiles,\n  showDrawer,\n  linkFilesLoading,\n  onSuccess\n}) => {\n  const [selectedClients, setSelectedClients] = useState([]);\n  const [selectedFileList, setSelectedFileList] = useState([]);\n  const linkFileColumnConfigs = [{\n    title: \"\",\n    dataIndex: \"remove\",\n    key: \"remove\",\n    width: 40\n  }, {\n    title: \"Title\",\n    dataIndex: \"title\",\n    key: \"title\",\n    ellipsis: true\n  }];\n  const [binderRecords, setBinderRecords] = useState([]);\n  const [clientBinderMap, setClientBinderMap] = useState({});\n  const [selectedFileAreas, setSelectedFileAreas] = useState([]);\n  const [clientIdKey, setClientIdKey] = useState(0);\n  const [clientSelectKey, setClientSelectKey] = useState(0);\n  const [isLoadingBinders, setIsLoadingBinders] = useState(false);\n  const [searchByField, setSearchByField] = useState(\"clientRef\");\n  useEffect(() => {\n    if (showDrawer && selectedFiles.length > 0) {\n      const fileIdList = selectedFiles.map(file => file.id);\n      setSelectedFileList(fileIdList);\n      setSearchByField(\"clientRef\");\n    } else {\n      setSelectedFileList([]);\n    }\n  }, [showDrawer, selectedFiles]);\n\n  const handleFilesChange = fileList => {\n    if (fileList.length > 0) {\n      setSelectedFileList(fileList);\n    } else {\n      handleCloseDrawer();\n    }\n  };\n\n  const resetFields = () => {\n    setSelectedClients([]);\n    setSelectedFileAreas([]);\n    setBinderRecords([]);\n    setClientBinderMap({});\n    setSearchByField(\"clientRef\");\n  };\n\n  const handleCloseDrawer = () => {\n    resetFields();\n    onClosePopup();\n  };\n\n  const handleClientIdChange = async (clientIdList, selectedOptions = []) => {\n    // Update the selected clients with full objects\n    setSelectedClients(selectedOptions);\n    onItemSelect(selectedFileList, clientIdList); // Auto-select if there's only one client\n\n    if (clientIdList.length === 1) {\n      setClientSelectKey(prevKey => prevKey + 1);\n    }\n\n    if (clientIdList.length === 0) {\n      setSelectedClients([]);\n      setBinderRecords([]);\n      setSelectedFileAreas([]);\n      return;\n    }\n\n    setIsLoadingBinders(true);\n\n    try {\n      let newBinderMap = { ...clientBinderMap\n      };\n      let allBinderRecords = [];\n      const clientPromises = clientIdList.filter(clientId => !newBinderMap[clientId]).map(async clientId => {\n        const selectedClient = selectedOptions.find(option => option.id === clientId);\n\n        try {\n          const response = await getClientAreaBinders(clientId);\n\n          if (response.data && response.data.records) {\n            const recordsWithClientId = response.data.records.map(record => ({ ...record,\n              clientId: clientId,\n              displayText: selectedClient.displayText\n            }));\n            return {\n              clientId,\n              records: recordsWithClientId\n            };\n          }\n\n          return {\n            clientId,\n            records: []\n          };\n        } catch (error) {\n          logger.error(\"Link Files Component\", \"fetchFileLinks\", error);\n          return {\n            clientId,\n            records: []\n          };\n        }\n      });\n      const results = await Promise.all(clientPromises);\n\n      for (const {\n        clientId,\n        records\n      } of results) {\n        newBinderMap[clientId] = records;\n      }\n\n      const removedClientIds = Object.keys(newBinderMap).filter(key => !clientIdList.includes(key));\n\n      for (const key of removedClientIds) {\n        delete newBinderMap[key];\n      }\n\n      for (const clientId of clientIdList) {\n        if (newBinderMap[clientId]) {\n          allBinderRecords = [...allBinderRecords, ...newBinderMap[clientId]];\n        }\n      }\n\n      setClientBinderMap(newBinderMap);\n      setBinderRecords(allBinderRecords); // Filter out selected file areas whose client is no longer selected\n\n      const updatedSelectedFileAreas = selectedFileAreas.filter(area => clientIdList.includes(area.clientId));\n      setSelectedFileAreas(updatedSelectedFileAreas);\n    } catch (error) {\n      logger.error(\"Link Files Component\", \"handleClientIdChange\", error);\n    } finally {\n      setClientIdKey(prevKey => prevKey + 1);\n      setIsLoadingBinders(false);\n    }\n  };\n\n  const getPaginatedClients = async (page, method, searchValue) => {\n    const transformFilters = {};\n\n    if (searchValue) {\n      transformFilters.search = searchValue;\n    }\n\n    const options = {\n      limit: LIMIT,\n      offset: page - 1,\n      field: searchByField,\n      ...transformFilters\n    };\n    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].clients, options).then(res => {\n      if (res.data) {\n        // Auto-select if there's only one client and no selection yet\n        if (res.data.records && res.data.records.length === 1 && selectedClients.length === 0) {\n          const singleClient = res.data.records[0];\n          const clientId = singleClient.id;\n          handleClientIdChange([clientId], [singleClient]);\n        } // If we have selected clients but they're not in the current records,\n        // merge them to ensure proper display\n\n\n        if (selectedClients.length > 0 && res.data.records) {\n          const existingIds = res.data.records.map(record => record.id);\n          const missingClients = selectedClients.filter(client => !existingIds.includes(client.id));\n\n          if (missingClients.length > 0) {\n            res.data.records = [...missingClients, ...res.data.records];\n          }\n        }\n\n        return res.data;\n      } else {\n        return [];\n      }\n    }).catch(error => {\n      logger.error(\"getPaginatedClients\", \"getPaginatedClients\", error);\n      return [];\n    });\n  };\n\n  const handleFileAreaChange = (values, options = []) => {\n    const selectedWithClientId = options.map(option => ({\n      id: option.binderId,\n      clientId: option.clientId || \"\"\n    }));\n    setSelectedFileAreas(selectedWithClientId);\n    onItemSelect(selectedFileList, values); // Auto-select if there's only one file area\n\n    if (values.length === 1) {\n      setClientIdKey(prevKey => prevKey + 1);\n    }\n  };\n\n  const getPaginatedFileAreas = async (page, method, searchValue) => {\n    let filteredRecords = [...binderRecords];\n    let records = filteredRecords.map(record => ({\n      id: record.binderId,\n      ...record\n    }));\n\n    if (searchValue) {\n      const lowerSearch = searchValue.toLowerCase();\n      records = records.filter(record => record.binderId.toLowerCase().includes(lowerSearch) || record.binderName.toLowerCase().includes(lowerSearch));\n    }\n\n    const startIndex = (page - 1) * LIMIT;\n    const endIndex = startIndex + LIMIT;\n    const paginatedRecords = records.slice(startIndex, endIndex); // Auto-select if there's only one file area and no selection yet\n\n    if (paginatedRecords.length === 1 && selectedFileAreas.length === 0) {\n      const singleFileArea = paginatedRecords[0];\n      handleFileAreaChange([singleFileArea.id], [singleFileArea]);\n    }\n\n    return {\n      records: paginatedRecords,\n      pageCount: Math.ceil(records.length / LIMIT),\n      pageNumber: page,\n      pageSize: LIMIT,\n      totalRecordCount: records.length\n    };\n  };\n\n  const getSelectedClientIds = () => {\n    return selectedClients.map(client => client.id);\n  };\n\n  const getSelectedFileAreaIds = () => {\n    return selectedFileAreas.map(area => area.id);\n  };\n\n  return /*#__PURE__*/React.createElement(_Drawer, {\n    title: \"Link File(s)\",\n    placement: \"right\",\n    visible: showDrawer,\n    className: \"yjDrawerPanel\",\n    width: 700,\n    closeIcon: /*#__PURE__*/React.createElement(CloseOutlined, {\n      \"data-testid\": \"template-drawer-close-icon\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 301,\n        columnNumber: 18\n      }\n    }),\n    onClose: handleCloseDrawer,\n    footer: [/*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(_Button, {\n      key: \"cancel\",\n      type: \"default\",\n      onClick: handleCloseDrawer,\n      disabled: linkFilesLoading,\n      \"data-testid\": \"template-drawer-close\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 305,\n        columnNumber: 11\n      }\n    }, \"Cancel\"), /*#__PURE__*/React.createElement(SubmitButton, {\n      key: \"linkFiles\",\n      type: \"primary\",\n      onClick: onSuccess,\n      disabled: !selectedFileAreas.length || linkFilesLoading,\n      \"data-testid\": \"template-drawer-create\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 315,\n        columnNumber: 11\n      }\n    }, \"Link\"))],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 295,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 321,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(_Col, {\n    span: 12,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 322,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Row, {\n    gutter: 10,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 323,\n      columnNumber: 13\n    }\n  }, selectedFileList.length > 0 ? /*#__PURE__*/React.createElement(SelectedFilesGrid, {\n    key: selectedFiles.map(f => f.id).join(\",\"),\n    onFilesChange: handleFilesChange,\n    columnConfigs: linkFileColumnConfigs,\n    dataList: selectedFiles,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 325,\n      columnNumber: 17\n    }\n  }) : /*#__PURE__*/React.createElement(_Skeleton, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 332,\n      columnNumber: 17\n    }\n  }))), /*#__PURE__*/React.createElement(_Col, {\n    span: 12,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 336,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"SEARCH BY\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(_Radio.Group, {\n    value: searchByField,\n    style: {\n      marginBottom: '5px'\n    },\n    onChange: e => {\n      setSearchByField(e.target.value);\n      setClientSelectKey(prevKey => prevKey + 1);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(_Space, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 349,\n      columnNumber: 19\n    }\n  }, /*#__PURE__*/React.createElement(_Radio, {\n    value: \"clientRef\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 21\n    }\n  }, \"Client Ref\"), /*#__PURE__*/React.createElement(_Radio, {\n    value: \"name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 351,\n      columnNumber: 21\n    }\n  }, \"Client Name\"))))), /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"Client(s)\",\n    name: \"clientId\",\n    className: styles.linkFileToClientLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 357,\n      columnNumber: 15\n    }\n  }), /*#__PURE__*/React.createElement(InfinitySelect, {\n    key: clientSelectKey,\n    returnObject: true,\n    getPaginatedRecords: (page, method, searchValue) => getPaginatedClients(page, method, searchValue),\n    formatValue: value => {\n      return `${value.displayText}`;\n    },\n    notFoundContent: \"No Clients Available\",\n    notLoadContent: \"Failed to load values in clients dropdown\",\n    onChange: (values, options) => {\n      // Ensure we always pass the full client objects for proper display\n      const fullOptions = options || [];\n      handleClientIdChange(values, fullOptions);\n    },\n    placeholder: \"Please Select Client(s)\",\n    waitCharCount: 3,\n    value: getSelectedClientIds(),\n    defaultValues: selectedClientsLists.length === 1 ? selectedClientsLists : undefined,\n    mode: \"multiple\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 362,\n      columnNumber: 17\n    }\n  })), /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 385,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"File Area(s)\",\n    name: \"fileArea\",\n    className: styles.linkFileToClientLabel,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 386,\n      columnNumber: 17\n    }\n  }), /*#__PURE__*/React.createElement(InfinitySelect, {\n    key: clientIdKey,\n    returnObject: true,\n    getPaginatedRecords: (page, method, searchValue) => getPaginatedFileAreas(page, method, searchValue),\n    formatValue: value => {\n      return `${value.displayText} - ${value.binderName}`;\n    },\n    notFoundContent: \"No File Areas Available\",\n    notLoadContent: \"Failed to load values in file areas dropdown\",\n    onChange: (values, options) => {\n      handleFileAreaChange(values, options);\n    },\n    placeholder: \"Please Select File Area(s)\",\n    waitCharCount: 3,\n    mode: \"multiple\",\n    disabled: selectedClientsLists.length === 0 || isLoadingBinders,\n    value: getSelectedFileAreaIds(),\n    defaultValues: selectedFileAreas.length === 1 ? getSelectedFileAreaIds() : undefined,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 391,\n      columnNumber: 19\n    }\n  })))));\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/Link/LinkFilesDrawer.tsx"], "names": ["React", "useEffect", "useState", "CloseOutlined", "styles", "SelectedFilesGrid", "InfinitySelect", "getInfiniteRecords", "config", "OperationalServiceTypes", "logger", "getClientAreaBinders", "SubmitButton", "LIMIT", "onClosePopup", "onItemSelect", "selectedFiles", "showDrawer", "linkFilesLoading", "onSuccess", "selectedClients", "setSelectedClients", "selectedFileList", "setSelectedFileList", "linkFileColumnConfigs", "title", "dataIndex", "key", "width", "ellipsis", "binderRecords", "setBinderRecords", "clientBinderMap", "setClientBinderMap", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "setSelectedFileAreas", "clientIdKey", "setClientIdKey", "clientSelectKey", "setClientSelectKey", "isLoadingBinders", "setIsLoadingBinders", "searchByField", "setSearchByField", "length", "fileIdList", "map", "file", "id", "handleFilesChange", "fileList", "handleCloseDrawer", "resetFields", "handleClientIdChange", "clientIdList", "selectedOptions", "prev<PERSON><PERSON>", "newBinderMap", "allBinderRecords", "clientPromises", "filter", "clientId", "selectedClient", "find", "option", "response", "data", "records", "recordsWithClientId", "record", "displayText", "error", "results", "Promise", "all", "removedClientIds", "Object", "keys", "includes", "updatedSelectedFileAreas", "area", "getPaginatedClients", "page", "method", "searchValue", "transformFilters", "search", "options", "limit", "offset", "field", "api", "FileManagementService", "clients", "then", "res", "singleClient", "existingIds", "missingClients", "client", "catch", "handleFileAreaChange", "values", "selectedWithClientId", "binderId", "getPaginatedFileAreas", "filteredRecords", "lowerSearch", "toLowerCase", "binderName", "startIndex", "endIndex", "paginatedRecords", "slice", "singleFileArea", "pageCount", "Math", "ceil", "pageNumber", "pageSize", "totalRecordCount", "getSelectedClientIds", "getSelectedFileAreaIds", "f", "join", "marginBottom", "e", "target", "value", "linkFileToClientLabel", "fullOptions", "selectedClientsLists", "undefined"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAP,IAAgBC,SAAhB,EAA2BC,QAA3B,QAA2C,OAA3C;AAEA,SAASC,aAAT,QAA8B,mBAA9B;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,iBAAP,MAAgD,0CAAhD;AAEA,OAAOC,cAAP,MAAyD,gCAAzD;AACA,SAASC,kBAAT,QAAmC,iCAAnC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,uBAAT,QAAwC,2BAAxC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,oBAAT,QAAqC,0BAArC;AACA,OAAOC,YAAP,MAAyB,8BAAzB;AAEA,MAAMC,KAAK,GAAG,EAAd;AAmCA,gBAAe,CAAC;AACdC,EAAAA,YADc;AAEdC,EAAAA,YAFc;AAGdC,EAAAA,aAHc;AAIdC,EAAAA,UAJc;AAKdC,EAAAA,gBALc;AAMdC,EAAAA;AANc,CAAD,KAOO;AACpB,QAAM,CAACC,eAAD,EAAkBC,kBAAlB,IAAwCnB,QAAQ,CAAQ,EAAR,CAAtD;AACA,QAAM,CAACoB,gBAAD,EAAmBC,mBAAnB,IAA0CrB,QAAQ,CAAW,EAAX,CAAxD;AACA,QAAMsB,qBAAqC,GAAG,CAC5C;AAAEC,IAAAA,KAAK,EAAE,EAAT;AAAaC,IAAAA,SAAS,EAAE,QAAxB;AAAkCC,IAAAA,GAAG,EAAE,QAAvC;AAAiDC,IAAAA,KAAK,EAAE;AAAxD,GAD4C,EAE5C;AAAEH,IAAAA,KAAK,EAAE,OAAT;AAAkBC,IAAAA,SAAS,EAAE,OAA7B;AAAsCC,IAAAA,GAAG,EAAE,OAA3C;AAAoDE,IAAAA,QAAQ,EAAE;AAA9D,GAF4C,CAA9C;AAIA,QAAM,CAACC,aAAD,EAAgBC,gBAAhB,IAAoC7B,QAAQ,CAAiB,EAAjB,CAAlD;AACA,QAAM,CAAC8B,eAAD,EAAkBC,kBAAlB,IAAwC/B,QAAQ,CAAiC,EAAjC,CAAtD;AACA,QAAM,CAACgC,iBAAD,EAAoBC,oBAApB,IAA4CjC,QAAQ,CACxD,EADwD,CAA1D;AAGA,QAAM,CAACkC,WAAD,EAAcC,cAAd,IAAgCnC,QAAQ,CAAC,CAAD,CAA9C;AACA,QAAM,CAACoC,eAAD,EAAkBC,kBAAlB,IAAwCrC,QAAQ,CAAC,CAAD,CAAtD;AACA,QAAM,CAACsC,gBAAD,EAAmBC,mBAAnB,IAA0CvC,QAAQ,CAAC,KAAD,CAAxD;AACA,QAAM,CAACwC,aAAD,EAAgBC,gBAAhB,IAAoCzC,QAAQ,CAAC,WAAD,CAAlD;AAEAD,EAAAA,SAAS,CAAC,MAAM;AACd,QAAIgB,UAAU,IAAID,aAAa,CAAC4B,MAAd,GAAuB,CAAzC,EAA4C;AAC1C,YAAMC,UAAU,GAAG7B,aAAa,CAAC8B,GAAd,CAAkBC,IAAI,IAAIA,IAAI,CAACC,EAA/B,CAAnB;AACAzB,MAAAA,mBAAmB,CAACsB,UAAD,CAAnB;AACAF,MAAAA,gBAAgB,CAAC,WAAD,CAAhB;AACD,KAJD,MAIO;AACLpB,MAAAA,mBAAmB,CAAC,EAAD,CAAnB;AACD;AACF,GARQ,EAQN,CAACN,UAAD,EAAaD,aAAb,CARM,CAAT;;AAUA,QAAMiC,iBAAiB,GAAIC,QAAD,IAAqB;AAC7C,QAAIA,QAAQ,CAACN,MAAT,GAAkB,CAAtB,EAAyB;AACvBrB,MAAAA,mBAAmB,CAAC2B,QAAD,CAAnB;AACD,KAFD,MAEO;AACLC,MAAAA,iBAAiB;AAClB;AACF,GAND;;AAQA,QAAMC,WAAW,GAAG,MAAM;AACxB/B,IAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACAc,IAAAA,oBAAoB,CAAC,EAAD,CAApB;AACAJ,IAAAA,gBAAgB,CAAC,EAAD,CAAhB;AACAE,IAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACAU,IAAAA,gBAAgB,CAAC,WAAD,CAAhB;AACD,GAND;;AAQA,QAAMQ,iBAAiB,GAAG,MAAM;AAC9BC,IAAAA,WAAW;AACXtC,IAAAA,YAAY;AACb,GAHD;;AAKA,QAAMuC,oBAAoB,GAAG,OAAOC,YAAP,EAA+BC,eAAsB,GAAG,EAAxD,KAA+D;AAC1F;AACAlC,IAAAA,kBAAkB,CAACkC,eAAD,CAAlB;AACAxC,IAAAA,YAAY,CAACO,gBAAD,EAAmBgC,YAAnB,CAAZ,CAH0F,CAK1F;;AACA,QAAIA,YAAY,CAACV,MAAb,KAAwB,CAA5B,EAA+B;AAC7BL,MAAAA,kBAAkB,CAACiB,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAlB;AACD;;AAED,QAAIF,YAAY,CAACV,MAAb,KAAwB,CAA5B,EAA+B;AAC7BvB,MAAAA,kBAAkB,CAAC,EAAD,CAAlB;AACAU,MAAAA,gBAAgB,CAAC,EAAD,CAAhB;AACAI,MAAAA,oBAAoB,CAAC,EAAD,CAApB;AACA;AACD;;AAEDM,IAAAA,mBAAmB,CAAC,IAAD,CAAnB;;AACA,QAAI;AACF,UAAIgB,YAA4C,GAAG,EAAE,GAAGzB;AAAL,OAAnD;AACA,UAAI0B,gBAAgC,GAAG,EAAvC;AAEA,YAAMC,cAAc,GAAGL,YAAY,CAChCM,MADoB,CACbC,QAAQ,IAAI,CAACJ,YAAY,CAACI,QAAD,CADZ,EAEpBf,GAFoB,CAEhB,MAAMe,QAAN,IAAkB;AACrB,cAAMC,cAAc,GAAGP,eAAe,CAACQ,IAAhB,CAAqBC,MAAM,IAAIA,MAAM,CAAChB,EAAP,KAAca,QAA7C,CAAvB;;AACA,YAAI;AACF,gBAAMI,QAAQ,GAAG,MAAMtD,oBAAoB,CAACkD,QAAD,CAA3C;;AACA,cAAII,QAAQ,CAACC,IAAT,IAAiBD,QAAQ,CAACC,IAAT,CAAcC,OAAnC,EAA4C;AAC1C,kBAAMC,mBAAmB,GAAGH,QAAQ,CAACC,IAAT,CAAcC,OAAd,CAAsBrB,GAAtB,CAA2BuB,MAAD,KAA2B,EAC/E,GAAGA,MAD4E;AAE/ER,cAAAA,QAAQ,EAAEA,QAFqE;AAG/ES,cAAAA,WAAW,EAAER,cAAc,CAACQ;AAHmD,aAA3B,CAA1B,CAA5B;AAKA,mBAAO;AAAET,cAAAA,QAAF;AAAYM,cAAAA,OAAO,EAAEC;AAArB,aAAP;AACD;;AACD,iBAAO;AAAEP,YAAAA,QAAF;AAAYM,YAAAA,OAAO,EAAE;AAArB,WAAP;AACD,SAXD,CAWE,OAAOI,KAAP,EAAc;AACd7D,UAAAA,MAAM,CAAC6D,KAAP,CAAa,sBAAb,EAAqC,gBAArC,EAAuDA,KAAvD;AACA,iBAAO;AAAEV,YAAAA,QAAF;AAAYM,YAAAA,OAAO,EAAE;AAArB,WAAP;AACD;AACF,OAnBoB,CAAvB;AAqBA,YAAMK,OAAO,GAAG,MAAMC,OAAO,CAACC,GAAR,CAAYf,cAAZ,CAAtB;;AAEA,WAAK,MAAM;AAAEE,QAAAA,QAAF;AAAYM,QAAAA;AAAZ,OAAX,IAAoCK,OAApC,EAA6C;AAC3Cf,QAAAA,YAAY,CAACI,QAAD,CAAZ,GAAyBM,OAAzB;AACD;;AAED,YAAMQ,gBAAgB,GAAGC,MAAM,CAACC,IAAP,CAAYpB,YAAZ,EAA0BG,MAA1B,CAAiCjC,GAAG,IAAI,CAAC2B,YAAY,CAACwB,QAAb,CAAsBnD,GAAtB,CAAzC,CAAzB;;AACA,WAAK,MAAMA,GAAX,IAAkBgD,gBAAlB,EAAoC;AAClC,eAAOlB,YAAY,CAAC9B,GAAD,CAAnB;AACD;;AAED,WAAK,MAAMkC,QAAX,IAAuBP,YAAvB,EAAqC;AACnC,YAAIG,YAAY,CAACI,QAAD,CAAhB,EAA4B;AAC1BH,UAAAA,gBAAgB,GAAG,CAAC,GAAGA,gBAAJ,EAAsB,GAAGD,YAAY,CAACI,QAAD,CAArC,CAAnB;AACD;AACF;;AAED5B,MAAAA,kBAAkB,CAACwB,YAAD,CAAlB;AACA1B,MAAAA,gBAAgB,CAAC2B,gBAAD,CAAhB,CA3CE,CA6CF;;AACA,YAAMqB,wBAAwB,GAAG7C,iBAAiB,CAAC0B,MAAlB,CAAyBoB,IAAI,IAC5D1B,YAAY,CAACwB,QAAb,CAAsBE,IAAI,CAACnB,QAA3B,CAD+B,CAAjC;AAGA1B,MAAAA,oBAAoB,CAAC4C,wBAAD,CAApB;AACD,KAlDD,CAkDE,OAAOR,KAAP,EAAc;AACd7D,MAAAA,MAAM,CAAC6D,KAAP,CAAa,sBAAb,EAAqC,sBAArC,EAA6DA,KAA7D;AACD,KApDD,SAoDU;AACRlC,MAAAA,cAAc,CAACmB,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAd;AACAf,MAAAA,mBAAmB,CAAC,KAAD,CAAnB;AACD;AACF,GA1ED;;AA4EA,QAAMwC,mBAAmB,GAAG,OAC1BC,IAD0B,EAE1BC,MAF0B,EAG1BC,WAH0B,KAIF;AACxB,UAAMC,gBAAqB,GAAG,EAA9B;;AACA,QAAID,WAAJ,EAAiB;AACfC,MAAAA,gBAAgB,CAACC,MAAjB,GAA0BF,WAA1B;AACD;;AAED,UAAMG,OAAO,GAAG;AACdC,MAAAA,KAAK,EAAE3E,KADO;AAEd4E,MAAAA,MAAM,EAAEP,IAAI,GAAG,CAFD;AAGdQ,MAAAA,KAAK,EAAEhD,aAHO;AAId,SAAG2C;AAJW,KAAhB;AAOA,WAAO9E,kBAAkB,CACvBC,MAAM,CAACmF,GAAP,CAAWlF,uBAAuB,CAACmF,qBAAnC,EAA0DC,OADnC,EAEvBN,OAFuB,CAAlB,CAIJO,IAJI,CAIEC,GAAD,IAAc;AAClB,UAAIA,GAAG,CAAC7B,IAAR,EAAc;AACZ;AACA,YAAI6B,GAAG,CAAC7B,IAAJ,CAASC,OAAT,IAAoB4B,GAAG,CAAC7B,IAAJ,CAASC,OAAT,CAAiBvB,MAAjB,KAA4B,CAAhD,IAAqDxB,eAAe,CAACwB,MAAhB,KAA2B,CAApF,EAAuF;AACrF,gBAAMoD,YAAY,GAAGD,GAAG,CAAC7B,IAAJ,CAASC,OAAT,CAAiB,CAAjB,CAArB;AACA,gBAAMN,QAAQ,GAAGmC,YAAY,CAAChD,EAA9B;AACAK,UAAAA,oBAAoB,CAAC,CAACQ,QAAD,CAAD,EAAa,CAACmC,YAAD,CAAb,CAApB;AACD,SANW,CAQZ;AACA;;;AACA,YAAI5E,eAAe,CAACwB,MAAhB,GAAyB,CAAzB,IAA8BmD,GAAG,CAAC7B,IAAJ,CAASC,OAA3C,EAAoD;AAClD,gBAAM8B,WAAW,GAAGF,GAAG,CAAC7B,IAAJ,CAASC,OAAT,CAAiBrB,GAAjB,CAAsBuB,MAAD,IAAiBA,MAAM,CAACrB,EAA7C,CAApB;AACA,gBAAMkD,cAAc,GAAG9E,eAAe,CAACwC,MAAhB,CAAwBuC,MAAD,IAAiB,CAACF,WAAW,CAACnB,QAAZ,CAAqBqB,MAAM,CAACnD,EAA5B,CAAzC,CAAvB;;AACA,cAAIkD,cAAc,CAACtD,MAAf,GAAwB,CAA5B,EAA+B;AAC7BmD,YAAAA,GAAG,CAAC7B,IAAJ,CAASC,OAAT,GAAmB,CAAC,GAAG+B,cAAJ,EAAoB,GAAGH,GAAG,CAAC7B,IAAJ,CAASC,OAAhC,CAAnB;AACD;AACF;;AAED,eAAO4B,GAAG,CAAC7B,IAAX;AACD,OAnBD,MAmBO;AACL,eAAO,EAAP;AACD;AACF,KA3BI,EA4BJkC,KA5BI,CA4BG7B,KAAD,IAAgB;AACrB7D,MAAAA,MAAM,CAAC6D,KAAP,CAAa,qBAAb,EAAoC,qBAApC,EAA2DA,KAA3D;AACA,aAAO,EAAP;AACD,KA/BI,CAAP;AAgCD,GAjDD;;AAmDA,QAAM8B,oBAAoB,GAAG,CAACC,MAAD,EAAmBf,OAAc,GAAG,EAApC,KAA2C;AACtE,UAAMgB,oBAAoB,GAAGhB,OAAO,CAACzC,GAAR,CAAYkB,MAAM,KAAK;AAClDhB,MAAAA,EAAE,EAAEgB,MAAM,CAACwC,QADuC;AAElD3C,MAAAA,QAAQ,EAAEG,MAAM,CAACH,QAAP,IAAmB;AAFqB,KAAL,CAAlB,CAA7B;AAKA1B,IAAAA,oBAAoB,CAACoE,oBAAD,CAApB;AACAxF,IAAAA,YAAY,CAACO,gBAAD,EAAmBgF,MAAnB,CAAZ,CAPsE,CAStE;;AACA,QAAIA,MAAM,CAAC1D,MAAP,KAAkB,CAAtB,EAAyB;AACvBP,MAAAA,cAAc,CAACmB,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAd;AACD;AACF,GAbD;;AAeA,QAAMiD,qBAAqB,GAAG,OAC5BvB,IAD4B,EAE5BC,MAF4B,EAG5BC,WAH4B,KAIX;AACjB,QAAIsB,eAAe,GAAG,CAAC,GAAG5E,aAAJ,CAAtB;AACA,QAAIqC,OAAO,GAAGuC,eAAe,CAAC5D,GAAhB,CAAoBuB,MAAM,KAAK;AAC3CrB,MAAAA,EAAE,EAAEqB,MAAM,CAACmC,QADgC;AAE3C,SAAGnC;AAFwC,KAAL,CAA1B,CAAd;;AAKA,QAAIe,WAAJ,EAAiB;AACf,YAAMuB,WAAW,GAAGvB,WAAW,CAACwB,WAAZ,EAApB;AACAzC,MAAAA,OAAO,GAAGA,OAAO,CAACP,MAAR,CACRS,MAAM,IACJA,MAAM,CAACmC,QAAP,CAAgBI,WAAhB,GAA8B9B,QAA9B,CAAuC6B,WAAvC,KACAtC,MAAM,CAACwC,UAAP,CAAkBD,WAAlB,GAAgC9B,QAAhC,CAAyC6B,WAAzC,CAHM,CAAV;AAKD;;AAED,UAAMG,UAAU,GAAG,CAAC5B,IAAI,GAAG,CAAR,IAAarE,KAAhC;AACA,UAAMkG,QAAQ,GAAGD,UAAU,GAAGjG,KAA9B;AACA,UAAMmG,gBAAgB,GAAG7C,OAAO,CAAC8C,KAAR,CAAcH,UAAd,EAA0BC,QAA1B,CAAzB,CAlBiB,CAoBjB;;AACA,QAAIC,gBAAgB,CAACpE,MAAjB,KAA4B,CAA5B,IAAiCV,iBAAiB,CAACU,MAAlB,KAA6B,CAAlE,EAAqE;AACnE,YAAMsE,cAAc,GAAGF,gBAAgB,CAAC,CAAD,CAAvC;AACAX,MAAAA,oBAAoB,CAAC,CAACa,cAAc,CAAClE,EAAhB,CAAD,EAAsB,CAACkE,cAAD,CAAtB,CAApB;AACD;;AAED,WAAO;AACL/C,MAAAA,OAAO,EAAE6C,gBADJ;AAELG,MAAAA,SAAS,EAAEC,IAAI,CAACC,IAAL,CAAUlD,OAAO,CAACvB,MAAR,GAAiB/B,KAA3B,CAFN;AAGLyG,MAAAA,UAAU,EAAEpC,IAHP;AAILqC,MAAAA,QAAQ,EAAE1G,KAJL;AAKL2G,MAAAA,gBAAgB,EAAErD,OAAO,CAACvB;AALrB,KAAP;AAOD,GArCD;;AAuCA,QAAM6E,oBAAoB,GAAG,MAAM;AACjC,WAAOrG,eAAe,CAAC0B,GAAhB,CAAoBqD,MAAM,IAAIA,MAAM,CAACnD,EAArC,CAAP;AACD,GAFD;;AAIA,QAAM0E,sBAAsB,GAAG,MAAM;AACnC,WAAOxF,iBAAiB,CAACY,GAAlB,CAAsBkC,IAAI,IAAIA,IAAI,CAAChC,EAAnC,CAAP;AACD,GAFD;;AAIA,sBACE;AACE,IAAA,KAAK,EAAE,cADT;AAEE,IAAA,SAAS,EAAC,OAFZ;AAGE,IAAA,OAAO,EAAE/B,UAHX;AAIE,IAAA,SAAS,EAAE,eAJb;AAKE,IAAA,KAAK,EAAE,GALT;AAME,IAAA,SAAS,eAAE,oBAAC,aAAD;AAAe,qBAAY,4BAA3B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MANb;AAOE,IAAA,OAAO,EAAEkC,iBAPX;AAQE,IAAA,MAAM,EAAE,cACN,uDACE;AACE,MAAA,GAAG,EAAC,QADN;AAEE,MAAA,IAAI,EAAC,SAFP;AAGE,MAAA,OAAO,EAAEA,iBAHX;AAIE,MAAA,QAAQ,EAAEjC,gBAJZ;AAKE,qBAAY,uBALd;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAWE,oBAAC,YAAD;AAAc,MAAA,GAAG,EAAC,WAAlB;AAA8B,MAAA,IAAI,EAAC,SAAnC;AAA6C,MAAA,OAAO,EAAEC,SAAtD;AAAiE,MAAA,QAAQ,EAAE,CAACe,iBAAiB,CAACU,MAAnB,IAA6B1B,gBAAxG;AAA0H,qBAAY,wBAAtI;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,cAXF,CADM,CARV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBA0BI;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACGI,gBAAgB,CAACsB,MAAjB,GAA0B,CAA1B,gBACC,oBAAC,iBAAD;AACE,IAAA,GAAG,EAAE5B,aAAa,CAAC8B,GAAd,CAAkB6E,CAAC,IAAIA,CAAC,CAAC3E,EAAzB,EAA6B4E,IAA7B,CAAkC,GAAlC,CADP;AAEE,IAAA,aAAa,EAAE3E,iBAFjB;AAGE,IAAA,aAAa,EAAEzB,qBAHjB;AAIE,IAAA,QAAQ,EAAER,aAJZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADD,gBAQC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IATJ,CADF,CADF,eAeE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,0BAAM,IAAN;AACE,IAAA,KAAK,EAAE,WADT;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAGE,2BAAO,KAAP;AACE,IAAA,KAAK,EAAE0B,aADT;AAEE,IAAA,KAAK,EAAE;AAAEmF,MAAAA,YAAY,EAAE;AAAhB,KAFT;AAGE,IAAA,QAAQ,EAAGC,CAAD,IAAO;AACfnF,MAAAA,gBAAgB,CAACmF,CAAC,CAACC,MAAF,CAASC,KAAV,CAAhB;AACAzF,MAAAA,kBAAkB,CAACiB,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAlB;AACD,KANH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAO,IAAA,KAAK,EAAC,WAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eAEE;AAAO,IAAA,KAAK,EAAC,MAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAFF,CARF,CAHF,CADF,CADF,eAoBE;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,0BAAM,IAAN;AACE,IAAA,KAAK,EAAE,WADT;AAEE,IAAA,IAAI,EAAC,UAFP;AAGE,IAAA,SAAS,EAAEpD,MAAM,CAAC6H,qBAHpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAMI,oBAAC,cAAD;AACE,IAAA,GAAG,EAAE3F,eADP;AAEE,IAAA,YAAY,EAAE,IAFhB;AAGE,IAAA,mBAAmB,EAAE,CAAC4C,IAAD,EAAOC,MAAP,EAAeC,WAAf,KACnBH,mBAAmB,CAACC,IAAD,EAAOC,MAAP,EAAeC,WAAf,CAJvB;AAME,IAAA,WAAW,EAAE4C,KAAK,IAAI;AACpB,aAAQ,GAAEA,KAAK,CAAC1D,WAAY,EAA5B;AACD,KARH;AASE,IAAA,eAAe,EAAC,sBATlB;AAUE,IAAA,cAAc,EAAC,2CAVjB;AAWE,IAAA,QAAQ,EAAE,CAACgC,MAAD,EAASf,OAAT,KAAqB;AAC7B;AACA,YAAM2C,WAAW,GAAG3C,OAAO,IAAI,EAA/B;AACAlC,MAAAA,oBAAoB,CAACiD,MAAD,EAAS4B,WAAT,CAApB;AACD,KAfH;AAgBE,IAAA,WAAW,EAAC,yBAhBd;AAiBE,IAAA,aAAa,EAAE,CAjBjB;AAkBE,IAAA,KAAK,EAAET,oBAAoB,EAlB7B;AAmBE,IAAA,aAAa,EAAEU,oBAAoB,CAACvF,MAArB,KAAgC,CAAhC,GAAoCuF,oBAApC,GAA2DC,SAnB5E;AAoBE,IAAA,IAAI,EAAC,UApBP;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IANJ,CApBF,eAiDI;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,0BAAM,IAAN;AACE,IAAA,KAAK,EAAE,cADT;AAEE,IAAA,IAAI,EAAC,UAFP;AAGE,IAAA,SAAS,EAAEhI,MAAM,CAAC6H,qBAHpB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,eAMI,oBAAC,cAAD;AACE,IAAA,GAAG,EAAE7F,WADP;AAEE,IAAA,YAAY,EAAE,IAFhB;AAGE,IAAA,mBAAmB,EAAE,CAAC8C,IAAD,EAAOC,MAAP,EAAeC,WAAf,KACnBqB,qBAAqB,CAACvB,IAAD,EAAOC,MAAP,EAAeC,WAAf,CAJzB;AAME,IAAA,WAAW,EAAE4C,KAAK,IAAI;AACpB,aAAQ,GAAEA,KAAK,CAAC1D,WAAY,MAAK0D,KAAK,CAACnB,UAAW,EAAlD;AACD,KARH;AASE,IAAA,eAAe,EAAC,yBATlB;AAUE,IAAA,cAAc,EAAC,8CAVjB;AAWE,IAAA,QAAQ,EAAE,CAACP,MAAD,EAASf,OAAT,KAAqB;AAC7Bc,MAAAA,oBAAoB,CAACC,MAAD,EAASf,OAAT,CAApB;AACD,KAbH;AAcE,IAAA,WAAW,EAAC,4BAdd;AAeE,IAAA,aAAa,EAAE,CAfjB;AAgBE,IAAA,IAAI,EAAC,UAhBP;AAiBE,IAAA,QAAQ,EAAE4C,oBAAoB,CAACvF,MAArB,KAAgC,CAAhC,IAAqCJ,gBAjBjD;AAkBE,IAAA,KAAK,EAAEkF,sBAAsB,EAlB/B;AAmBE,IAAA,aAAa,EAAExF,iBAAiB,CAACU,MAAlB,KAA6B,CAA7B,GAAiC8E,sBAAsB,EAAvD,GAA4DU,SAnB7E;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IANJ,CAjDJ,CAfF,CA1BJ,CADF;AA2HD,CA/WD", "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { Form, Row, Col, Button, Skeleton, Drawer, Radio, Space } from \"antd\";\r\nimport { CloseOutlined } from \"@ant-design/icons\";\r\nimport styles from \"./index.module.less\";\r\nimport SelectedFilesGrid, { ColumnConfig } from \"@app/features/FileArea/SelectedFilesGrid\";\r\nimport { IFile } from \"@app/types/fileAreaTypes\";\r\nimport InfinitySelect, { InfinitySelectGetOptions } from \"@app/components/InfinitySelect\";\r\nimport { getInfiniteRecords } from \"@app/api/infiniteRecordsService\";\r\nimport config from \"@app/utils/config\";\r\nimport { OperationalServiceTypes } from \"@iris/discovery.fe.client\";\r\nimport logger from \"@app/utils/logger\";\r\nimport { getClientAreaBinders } from \"@app/api/fileAreaService\";\r\nimport SubmitButton from \"@app/components/SubmitButton\";\r\n\r\nconst LIMIT = 10;\r\n\r\nexport interface LinkFilesProps {\r\n  selectedFiles: IFile[];\r\n  onItemSelect: (fileList: string[], destinationClientId: string[]) => void;\r\n  onClosePopup: () => void;\r\n  showDrawer: boolean;\r\n  linkFilesLoading: boolean;\r\n  onSuccess: () => void;\r\n}\r\n\r\ninterface BinderRecord {\r\n  binderId: string;\r\n  binderName: string;\r\n  binderTemplate: {\r\n    value: number;\r\n    name: string;\r\n  };\r\n  job: {\r\n    value: number;\r\n    name: string;\r\n  };\r\n  status: {\r\n    value: number;\r\n    name: string;\r\n  };\r\n  year: number;\r\n  created: string;\r\n  createdBy: {\r\n    value: number;\r\n    name: string;\r\n  };\r\n  clientId?: string;\r\n}\r\n\r\nexport default ({\r\n  onClosePopup,\r\n  onItemSelect,\r\n  selectedFiles,\r\n  showDrawer,\r\n  linkFilesLoading,\r\n  onSuccess,\r\n}: LinkFilesProps) => {\r\n  const [selectedClients, setSelectedClients] = useState<any[]>([]);\r\n  const [selectedFileList, setSelectedFileList] = useState<string[]>([]);\r\n  const linkFileColumnConfigs: ColumnConfig[] = [\r\n    { title: \"\", dataIndex: \"remove\", key: \"remove\", width: 40 },\r\n    { title: \"Title\", dataIndex: \"title\", key: \"title\", ellipsis: true },\r\n  ];\r\n  const [binderRecords, setBinderRecords] = useState<BinderRecord[]>([]);\r\n  const [clientBinderMap, setClientBinderMap] = useState<Record<string, BinderRecord[]>>({});\r\n  const [selectedFileAreas, setSelectedFileAreas] = useState<{ id: string; clientId: string }[]>(\r\n    []\r\n  );\r\n  const [clientIdKey, setClientIdKey] = useState(0);\r\n  const [clientSelectKey, setClientSelectKey] = useState(0);\r\n  const [isLoadingBinders, setIsLoadingBinders] = useState(false);\r\n  const [searchByField, setSearchByField] = useState(\"clientRef\");\r\n\r\n  useEffect(() => {\r\n    if (showDrawer && selectedFiles.length > 0) {\r\n      const fileIdList = selectedFiles.map(file => file.id);\r\n      setSelectedFileList(fileIdList);\r\n      setSearchByField(\"clientRef\");\r\n    } else {\r\n      setSelectedFileList([]);\r\n    }\r\n  }, [showDrawer, selectedFiles]);\r\n\r\n  const handleFilesChange = (fileList: any[]) => {\r\n    if (fileList.length > 0) {\r\n      setSelectedFileList(fileList);\r\n    } else {\r\n      handleCloseDrawer();\r\n    }\r\n  };\r\n\r\n  const resetFields = () => {\r\n    setSelectedClients([]);\r\n    setSelectedFileAreas([]);\r\n    setBinderRecords([]);\r\n    setClientBinderMap({});\r\n    setSearchByField(\"clientRef\")\r\n  };\r\n\r\n  const handleCloseDrawer = () => {\r\n    resetFields();\r\n    onClosePopup();\r\n  };\r\n\r\n  const handleClientIdChange = async (clientIdList: string[], selectedOptions: any[] = []) => {\r\n    // Update the selected clients with full objects\r\n    setSelectedClients(selectedOptions);\r\n    onItemSelect(selectedFileList, clientIdList);\r\n\r\n    // Auto-select if there's only one client\r\n    if (clientIdList.length === 1) {\r\n      setClientSelectKey(prevKey => prevKey + 1);\r\n    }\r\n\r\n    if (clientIdList.length === 0) {\r\n      setSelectedClients([]);\r\n      setBinderRecords([]);\r\n      setSelectedFileAreas([]);\r\n      return;\r\n    }\r\n\r\n    setIsLoadingBinders(true);\r\n    try {\r\n      let newBinderMap: Record<string, BinderRecord[]> = { ...clientBinderMap };\r\n      let allBinderRecords: BinderRecord[] = [];\r\n\r\n      const clientPromises = clientIdList\r\n        .filter(clientId => !newBinderMap[clientId])\r\n        .map(async clientId => {\r\n          const selectedClient = selectedOptions.find(option => option.id === clientId);\r\n          try {\r\n            const response = await getClientAreaBinders(clientId);\r\n            if (response.data && response.data.records) {\r\n              const recordsWithClientId = response.data.records.map((record: BinderRecord) => ({\r\n                ...record,\r\n                clientId: clientId,\r\n                displayText: selectedClient.displayText,\r\n              }));\r\n              return { clientId, records: recordsWithClientId };\r\n            }\r\n            return { clientId, records: [] };\r\n          } catch (error) {\r\n            logger.error(\"Link Files Component\", \"fetchFileLinks\", error);\r\n            return { clientId, records: [] };\r\n          }\r\n        });\r\n\r\n      const results = await Promise.all(clientPromises);\r\n\r\n      for (const { clientId, records } of results) {\r\n        newBinderMap[clientId] = records;\r\n      }\r\n\r\n      const removedClientIds = Object.keys(newBinderMap).filter(key => !clientIdList.includes(key));\r\n      for (const key of removedClientIds) {\r\n        delete newBinderMap[key];\r\n      }\r\n\r\n      for (const clientId of clientIdList) {\r\n        if (newBinderMap[clientId]) {\r\n          allBinderRecords = [...allBinderRecords, ...newBinderMap[clientId]];\r\n        }\r\n      }\r\n\r\n      setClientBinderMap(newBinderMap);\r\n      setBinderRecords(allBinderRecords);\r\n\r\n      // Filter out selected file areas whose client is no longer selected\r\n      const updatedSelectedFileAreas = selectedFileAreas.filter(area =>\r\n        clientIdList.includes(area.clientId)\r\n      );\r\n      setSelectedFileAreas(updatedSelectedFileAreas);\r\n    } catch (error) {\r\n      logger.error(\"Link Files Component\", \"handleClientIdChange\", error);\r\n    } finally {\r\n      setClientIdKey(prevKey => prevKey + 1);\r\n      setIsLoadingBinders(false);\r\n    }\r\n  };\r\n\r\n  const getPaginatedClients = async (\r\n    page: number,\r\n    method: InfinitySelectGetOptions,\r\n    searchValue?: string\r\n  ): Promise<Array<any>> => {\r\n    const transformFilters: any = {};\r\n    if (searchValue) {\r\n      transformFilters.search = searchValue;\r\n    }\r\n\r\n    const options = {\r\n      limit: LIMIT,\r\n      offset: page - 1,\r\n      field: searchByField,\r\n      ...transformFilters,\r\n    };\r\n\r\n    return getInfiniteRecords(\r\n      config.api[OperationalServiceTypes.FileManagementService].clients,\r\n      options\r\n    )\r\n      .then((res: any) => {\r\n        if (res.data) {\r\n          // Auto-select if there's only one client and no selection yet\r\n          if (res.data.records && res.data.records.length === 1 && selectedClients.length === 0) {\r\n            const singleClient = res.data.records[0];\r\n            const clientId = singleClient.id;\r\n            handleClientIdChange([clientId], [singleClient]);\r\n          }\r\n\r\n          // If we have selected clients but they're not in the current records,\r\n          // merge them to ensure proper display\r\n          if (selectedClients.length > 0 && res.data.records) {\r\n            const existingIds = res.data.records.map((record: any) => record.id);\r\n            const missingClients = selectedClients.filter((client: any) => !existingIds.includes(client.id));\r\n            if (missingClients.length > 0) {\r\n              res.data.records = [...missingClients, ...res.data.records];\r\n            }\r\n          }\r\n\r\n          return res.data;\r\n        } else {\r\n          return [];\r\n        }\r\n      })\r\n      .catch((error: any) => {\r\n        logger.error(\"getPaginatedClients\", \"getPaginatedClients\", error);\r\n        return [];\r\n      });\r\n  };\r\n\r\n  const handleFileAreaChange = (values: string[], options: any[] = []) => {\r\n    const selectedWithClientId = options.map(option => ({\r\n      id: option.binderId,\r\n      clientId: option.clientId || \"\",\r\n    }));\r\n\r\n    setSelectedFileAreas(selectedWithClientId);\r\n    onItemSelect(selectedFileList, values);\r\n\r\n    // Auto-select if there's only one file area\r\n    if (values.length === 1) {\r\n      setClientIdKey(prevKey => prevKey + 1);\r\n    }\r\n  };\r\n\r\n  const getPaginatedFileAreas = async (\r\n    page: number,\r\n    method: InfinitySelectGetOptions,\r\n    searchValue?: string\r\n  ): Promise<any> => {\r\n    let filteredRecords = [...binderRecords];\r\n    let records = filteredRecords.map(record => ({\r\n      id: record.binderId,\r\n      ...record,\r\n    }));\r\n\r\n    if (searchValue) {\r\n      const lowerSearch = searchValue.toLowerCase();\r\n      records = records.filter(\r\n        record =>\r\n          record.binderId.toLowerCase().includes(lowerSearch) ||\r\n          record.binderName.toLowerCase().includes(lowerSearch)\r\n      );\r\n    }\r\n\r\n    const startIndex = (page - 1) * LIMIT;\r\n    const endIndex = startIndex + LIMIT;\r\n    const paginatedRecords = records.slice(startIndex, endIndex);\r\n\r\n    // Auto-select if there's only one file area and no selection yet\r\n    if (paginatedRecords.length === 1 && selectedFileAreas.length === 0) {\r\n      const singleFileArea = paginatedRecords[0];\r\n      handleFileAreaChange([singleFileArea.id], [singleFileArea]);\r\n    }\r\n\r\n    return {\r\n      records: paginatedRecords,\r\n      pageCount: Math.ceil(records.length / LIMIT),\r\n      pageNumber: page,\r\n      pageSize: LIMIT,\r\n      totalRecordCount: records.length,\r\n    };\r\n  };\r\n\r\n  const getSelectedClientIds = () => {\r\n    return selectedClients.map(client => client.id);\r\n  };\r\n\r\n  const getSelectedFileAreaIds = () => {\r\n    return selectedFileAreas.map(area => area.id);\r\n  };\r\n\r\n  return (\r\n    <Drawer\r\n      title={\"Link File(s)\"}\r\n      placement=\"right\"\r\n      visible={showDrawer}\r\n      className={\"yjDrawerPanel\"}\r\n      width={700}\r\n      closeIcon={<CloseOutlined data-testid=\"template-drawer-close-icon\" />}\r\n      onClose={handleCloseDrawer}\r\n      footer={[\r\n        <>\r\n          <Button\r\n            key=\"cancel\"\r\n            type=\"default\"\r\n            onClick={handleCloseDrawer}\r\n            disabled={linkFilesLoading}\r\n            data-testid=\"template-drawer-close\"\r\n          >\r\n            Cancel\r\n          </Button>\r\n\r\n          <SubmitButton key=\"linkFiles\" type=\"primary\" onClick={onSuccess} disabled={!selectedFileAreas.length || linkFilesLoading} data-testid=\"template-drawer-create\">\r\n            Link\r\n          </SubmitButton>\r\n        </>\r\n      ]}\r\n    >\r\n        <Row gutter={24}>\r\n          <Col span={12}>\r\n            <Row gutter={10}>\r\n              {selectedFileList.length > 0 ? (\r\n                <SelectedFilesGrid\r\n                  key={selectedFiles.map(f => f.id).join(\",\")}\r\n                  onFilesChange={handleFilesChange}\r\n                  columnConfigs={linkFileColumnConfigs}\r\n                  dataList={selectedFiles}\r\n                />\r\n              ) : (\r\n                <Skeleton />\r\n              )}\r\n            </Row>\r\n          </Col>\r\n          <Col span={12}>\r\n            <Row gutter={24}>\r\n              <Form.Item\r\n                label={\"SEARCH BY\"}\r\n              >\r\n                <Radio.Group\r\n                  value={searchByField}\r\n                  style={{ marginBottom: '5px' }}\r\n                  onChange={(e) => {\r\n                    setSearchByField(e.target.value);\r\n                    setClientSelectKey(prevKey => prevKey + 1);\r\n                  }}\r\n                >\r\n                  <Space>\r\n                    <Radio value=\"clientRef\">Client Ref</Radio>\r\n                    <Radio value=\"name\">Client Name</Radio>\r\n                  </Space>\r\n                </Radio.Group>\r\n              </Form.Item>\r\n            </Row>\r\n            <Row gutter={24}>\r\n              <Form.Item\r\n                label={\"Client(s)\"}\r\n                name=\"clientId\"\r\n                className={styles.linkFileToClientLabel}\r\n              ></Form.Item>\r\n                <InfinitySelect\r\n                  key={clientSelectKey}\r\n                  returnObject={true}\r\n                  getPaginatedRecords={(page, method, searchValue) =>\r\n                    getPaginatedClients(page, method, searchValue)\r\n                  }\r\n                  formatValue={value => {\r\n                    return `${value.displayText}`;\r\n                  }}\r\n                  notFoundContent=\"No Clients Available\"\r\n                  notLoadContent=\"Failed to load values in clients dropdown\"\r\n                  onChange={(values, options) => {\r\n                    // Ensure we always pass the full client objects for proper display\r\n                    const fullOptions = options || [];\r\n                    handleClientIdChange(values, fullOptions);\r\n                  }}\r\n                  placeholder=\"Please Select Client(s)\"\r\n                  waitCharCount={3}\r\n                  value={getSelectedClientIds()}\r\n                  defaultValues={selectedClientsLists.length === 1 ? selectedClientsLists : undefined}\r\n                  mode=\"multiple\"\r\n                />\r\n            </Row>\r\n              <Row gutter={24}>\r\n                <Form.Item\r\n                  label={\"File Area(s)\"}\r\n                  name=\"fileArea\"\r\n                  className={styles.linkFileToClientLabel}\r\n                ></Form.Item>\r\n                  <InfinitySelect\r\n                    key={clientIdKey}\r\n                    returnObject={true}\r\n                    getPaginatedRecords={(page, method, searchValue) =>\r\n                      getPaginatedFileAreas(page, method, searchValue)\r\n                    }\r\n                    formatValue={value => {\r\n                      return `${value.displayText} - ${value.binderName}`;\r\n                    }}\r\n                    notFoundContent=\"No File Areas Available\"\r\n                    notLoadContent=\"Failed to load values in file areas dropdown\"\r\n                    onChange={(values, options) => {\r\n                      handleFileAreaChange(values, options);\r\n                    }}\r\n                    placeholder=\"Please Select File Area(s)\"\r\n                    waitCharCount={3}\r\n                    mode=\"multiple\"\r\n                    disabled={selectedClientsLists.length === 0 || isLoadingBinders}\r\n                    value={getSelectedFileAreaIds()}\r\n                    defaultValues={selectedFileAreas.length === 1 ? getSelectedFileAreaIds() : undefined}\r\n                  />\r\n              </Row>\r\n          </Col>\r\n        </Row>\r\n    </Drawer>\r\n  );\r\n};\r\n"]}, "metadata": {}, "sourceType": "module"}