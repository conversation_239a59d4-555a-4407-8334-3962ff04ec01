@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjPageTitleWrapper {
  background-color: @color-bg-titlewrapper;
  box-shadow: 0 2px 6px 0 rgba(0, 0, 0, .05);
  justify-content: space-between;
  padding: 0 2em;

  .yjPageTitle {
    color: @color-page-title;
    flex-basis: 50%;
    margin: .2em 0;

    .flex-mixin(center, flex, flex-start);
  }

  .yjLegalOnHoldStatus {
    margin-left: 10px;
    margin-top: 8px;
  }

  .flex-mixin(center, flex, space-between);
}

.yjPageButtonGroupWrapper {
  flex-basis: 50%;

  .flex-mixin(center, flex, flex-end);
}

@media (max-width: 1366px) {

  .yjPageTitleWrapper {

    .yjPageTitle {

      h2 {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        width: 570px;
      }
    }
  }
}