import React, { useC<PERSON>back, useEffect, useState } from "react";
import { <PERSON>ton, Modal as AntModal, Select } from "antd";
import { ExclamationCircleOutlined, ShareAltOutlined } from "@ant-design/icons";
import { useDispatch, useSelector } from "react-redux";
import { useParams } from "react-router-dom";

import styles from "./index.module.less";
import Request from "@app/components/PortalFiles/Request";
import Modal from "@app/components/Modal";
import { useForm } from "antd/lib/form/Form";
import { ICreateRequest } from "@app/types/portalTypes";
import { getPortalFileRequestById, saveRequest, updateRequest, } from "@app/api/portalServices";
import {
  errorNotification,
  infoNotification,
  successNotification,
  warningNotification,
} from "@app/utils/antNotifications";
import logger from "@app/utils/logger";
import {
  updateFileRequestSent,
  updateLoadGridOption,
  updatePortalFilesSelectedRequest,
} from "@app/redux/actions/fileAreaActions";
import { RootState } from "@app/redux/reducers/state";
import { checkCheckoutFileListBySiteId, checkInFiles, removeFiles, } from "@app/api/fileAreaService";
import CheckIn from "../CheckIn";
import { formActions } from "@app/types";
import { encrypt } from "@app/utils/crypto/cryptoText";
import moment from "moment";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";

const { Option } = Select;

const { confirm } = AntModal;

export interface IfileAreaButtonPanel {
  history?: any;
  fileSection?: string;
  siteId?: string;
  binderId?: string;
  siteName?: string;
  channelId?: string;
  key?: string;
  manageCheckin?: boolean;
}

const INTERNEL_FILE_SECTION = "internal";
const PORTAL_FILE_SECTION = "portal";

export const FileAreaButtonPanel: React.FC<IfileAreaButtonPanel> = (props) => {
  const [showCheckinModal, setShowCheckinModal] = useState(false);
  const [allowCheckinButton, setAllowCheckinButton] = useState(false);
  const [checkinList, setCheckinList] = useState([]);
  const [hasCheckinList, setHasCheckinList] = useState(false);
  const { fileAreaSettings, loadGrid } = useSelector(
    (state: RootState) => state.fileArea
  );
  const { userPermission } = useSelector(
    (state: RootState) => state.userManagement
  );
  const [requestFormValidated, setRequestFormValidated] = useState(false);
  const { siteId, siteName, channelId, binderId, binderName } = useParams<any>();

  const handleSuccessfulCheckins = () => {
    infoNotification([""], "File(s) are being checked in. Please wait.");
    if (
      emailForm.getFieldsValue().emailUser ||
      emailForm.getFieldsValue().emailContact
    ) {
      successNotification([""], "File Check-in Email sent Successfully");
    }
    setShowCheckinModal(false);
  };

  const onCheckin = () => {
    checkInFiles(props.binderId, checkinList)
      .then((response) => {
        handleSuccessfulCheckins();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification(
            [""],
            "You do not have the permission to perform this action. Please refresh and try again"
          );
        } else {
          errorNotification([""], "Check-In Failed");
        }
        logger.error("File Are Section", "File Area Button Panel", error);
      });
  };

  useEffect(() => {
    setFileSelection(props.fileSection ? props.fileSection : "");
  }, [props.fileSection]);

  const dispatch = useDispatch();
  useEffect(() => {
    setFileSelection(props.fileSection ? props.fileSection : "");
  }, [props.fileSection]);

  useEffect(() => {
    onCheckCheckoutFileListBySiteId();
  }, [props.siteId, dispatch, fileAreaSettings]);

  useEffect(() => {
    if (loadGrid) {
      onCheckCheckoutFileListBySiteId();
    }
  }, [loadGrid]);

  const onCheckCheckoutFileListBySiteId = () => {
    userPermission.privDMSCanCheckInCheckOutInternalFiles &&
      checkCheckoutFileListBySiteId(props.binderId)
        .then(() => {
          setHasCheckinList(true);
          dispatch(updateLoadGridOption(false));
        })
        .catch(() => setHasCheckinList(false));
  };

  const [fileSelection, setFileSelection] = useState(INTERNEL_FILE_SECTION);
  const [showRequestModal, setShowRequestModel] = useState(false);
  const [requestData, setRequestData] = useState<{} | null>(null) as any;
  const [form] = useForm();
  const [emailForm] = useForm();
  const handleShowRequestModal = useCallback(
    (showRequest: boolean) => {
      if (!showRequest) {
        dispatch(
          updatePortalFilesSelectedRequest({ action: null, requestId: null })
        );
      }
      setShowRequestModel(showRequest);
    },
    [dispatch]
  );
  const [requestFormChanged, setRequestFormChanged] = useState(false);
  const [checkingFormChanged, setCheckinFormChanged] = useState(false);
  const [checkingEmailFormChanged, setCheckinEmailFormChanged] = useState(
    false
  );

  const { portalFilesSelectedRequest } = useSelector(
    (state: RootState) => state.fileArea
  );

  const logSuccessfulRequests = (updateRequestInput: boolean) => {
    successNotification(
      [""],
      updateRequestInput
        ? "Request Updated Successfully"
        : "Request Sent Successfully"
    );
    handleShowRequestModal(false);
    dispatch(updateFileRequestSent(true));
  };

  const logFailedRequests = (logUpdateRequest: boolean, error: any) => {
    if (error.statusCode === FORBIDDEN_ERROR_CODE) {
      errorNotification(
        [""],
        "You do not have the permission to perform this action. Please refresh and try again"
      );
    } else {
      errorNotification(
        [""],
        logUpdateRequest ? "Request Update Failed" : "Request Sending Failed"
      );
    }

    logger.error("File Area", "File Area Button Panel", error);
  };

  const updateFileRequest = (request: ICreateRequest, requestId: string) => {
    updateRequest(request, requestId)
      .then((response) => {
        if (response) {
          logSuccessfulRequests(true);
        }
      })
      .catch((error) => {
        logFailedRequests(true, error);
      });
  };

  const saveFileRequest = (request: ICreateRequest) => {
    saveRequest(request)
      .then((response) => {
        if (response) {
          logSuccessfulRequests(false);
        }
      })
      .catch((error) => {
        logFailedRequests(false, error);
      });
  };

  const sendRequest = () => {
    form.validateFields().then((values) => {
      const request: ICreateRequest = {
        description: values.description,
        expirationDate: values.linkExpireDate
          ? values?.linkExpireDate?.format("YYYY-MM-DD")
          : "",
        name: values.request,
        securityKey: values.securityKeyInput ? values.securityKeyInput : null,
        siteId: props.siteId ? props.siteId : "",
      };
      if (
        portalFilesSelectedRequest.action &&
        portalFilesSelectedRequest.requestId
      ) {
        updateFileRequest(request, portalFilesSelectedRequest.requestId);
      } else {
        saveFileRequest(request);
      }
    });
  };

  useEffect(() => {
    if (portalFilesSelectedRequest.requestId) {
      getPortalFileRequestById(portalFilesSelectedRequest.requestId)
        .then((response) => {
          setRequestData(response.data);
          setRequestFormValidated(false);
          handleShowRequestModal(true);
        })
        .catch((error) => {
          if (error.statusCode === FORBIDDEN_ERROR_CODE) {
            props.history.push("/forbidden");
          }
        });
    } else {
      setRequestData(null);
    }
  }, [portalFilesSelectedRequest, handleShowRequestModal, setRequestData]);

  const handleDisplayCheckinModal = () => {
    if (checkingEmailFormChanged || checkingFormChanged) {
      confirm({
        title: "Are you sure you want to discard the changes?",
        icon: <ExclamationCircleOutlined />,
        okText: "Yes",
        cancelText: "No",
        onOk() {
          removeFiles(
            props.siteId ? props.siteId : "",
            checkinList.map((file: any) => file.referenceNumber)
          );
          setShowCheckinModal(false);
        },
      });
    } else {
      setShowCheckinModal(false);
    }
    setCheckinEmailFormChanged(false);
    setCheckinFormChanged(false);
  };

  const onClickFileAreaHistory = () => {
    if (fileSelection === INTERNEL_FILE_SECTION) {
      const url = `/client-file-area/${channelId}/${siteId}/${siteName}/internal/history`;
      const encodedUrl = encodeURI(url);
      props.history.push(encodedUrl);
    } else {
      const url = `/client-file-area/${channelId}/${siteId}/${siteName}/portal/history`;
      const encodedUrl = encodeURI(url);
      props.history.push(encodedUrl);
    }
  };

  const SecurityKeyValidation = (): boolean => {
    let isValidated = true;
    if (form.getFieldValue("securityKey")) {
      if (form.getFieldValue("securityKeyInput") === "") {
        isValidated = false;
      } else {
        isValidated = true;
      }
    }
    return isValidated;
  };

  const ExpireDateValidation = (): boolean => {
    let isValidated = true;
    if (form.getFieldValue("expireLink")) {
      if (
        form.getFieldValue("linkExpireDate") === undefined ||
        form.getFieldValue("linkExpireDate") === "" ||
        form.getFieldValue("linkExpireDate") === null
      ) {
        isValidated = false;
      } else {
        isValidated = true;
      }
    }
    return isValidated;
  };

  const onClickCopyToClipboard = () => {
    const encryptedRequestId = encrypt(
      portalFilesSelectedRequest.requestId
        ? portalFilesSelectedRequest.requestId
        : ""
    );
    navigator.clipboard.writeText(
      `${window.location.origin}/requestFiles/${encryptedRequestId}`
    );
    successNotification([""], "Link Copied to Clipboard");
  };

  const onClickCancelRequest = () => {
    if (
      portalFilesSelectedRequest.action !== formActions.VIEW &&
      requestFormChanged
    ) {
      confirm({
        title: "Are you sure you want to discard the changes?",
        icon: <ExclamationCircleOutlined />,
        okText: "Yes",
        cancelText: "No",
        onOk() {
          handleShowRequestModal(false);
          setRequestFormValidated(false);
        },
      });
    } else {
      handleShowRequestModal(false);
      setRequestFormValidated(false);
    }
    setRequestFormChanged(false);
  };

  const onChangeRequest = () => {
    setRequestFormChanged(true);
    form
      .validateFields()
      .then((values) => {
        const expiratonDateValidated = values.linkExpireDate
          ? moment(values.linkExpireDate).isAfter(new Date(), "D")
          : true;

        if (
          SecurityKeyValidation() &&
          ExpireDateValidation() &&
          expiratonDateValidated
        ) {
          setRequestFormValidated(true);
        } else {
          setRequestFormValidated(false);
        }
      })
      .catch(() => {
        setRequestFormValidated(false);
      });
  };

  return (
    <>
      <Modal
        destroyOnClose={true}
        size={"medium"}
        visible={showRequestModal}
        title={
          portalFilesSelectedRequest.action === formActions.EDIT
            ? "Edit Request"
            : portalFilesSelectedRequest.action === formActions.VIEW
              ? "Request Details"
              : "Request"
        }
        onCancel={() => onClickCancelRequest()}
        footer={[
          <>
            <span
              hidden={!requestData}
              className={styles.yjCopyLink}
              key="copyLink"
              onClick={() => onClickCopyToClipboard()}
            >
              <ShareAltOutlined /> Copy Link
            </span>
            <Button
              key="back"
              type="default"
              onClick={() => onClickCancelRequest()}
            >
              cancel
            </Button>
            <Button
              disabled={
                !requestFormValidated ||
                portalFilesSelectedRequest.action === formActions.VIEW
              }
              onClick={() => sendRequest()}
              key="send"
              type="primary"
            >
              {portalFilesSelectedRequest.action === formActions.EDIT
                ? "Update"
                : "Send"}
            </Button>
          </>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <Request
            onChange={() => onChangeRequest()}
            actionType={portalFilesSelectedRequest.action}
            formRef={form}
            onFinish={() => sendRequest()}
            data={requestData}
          />
        </div>
      </Modal>

      {/* Checkin Model */}
      <Modal
        destroyOnClose={true}
        key="100"
        visible={showCheckinModal}
        title={"Check-In File(s)"}
        onCancel={handleDisplayCheckinModal}
        footer={[
          <Button key="back" type="default" onClick={handleDisplayCheckinModal}>
            cancel
          </Button>,

          <Button
            disabled={!allowCheckinButton}
            key="checkin"
            type="primary"
            onClick={onCheckin}
          >
            check-in
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <CheckIn
            emailForm={emailForm}
            onChangeEmailForm={() => {
              setCheckinEmailFormChanged(true);
            }}
            onChangedCheckinForm={() => {
              setCheckinFormChanged(true);
            }}
            onCheckin={(requestList: any) => {
              setCheckinList(requestList);
            }}
            siteId={props.siteId}
            binderId={props.binderId}
            allowCheckinFunction={(isAllowed: boolean) => {
              setAllowCheckinButton(isAllowed);
            }}
            form={form}
          />
        </div>
      </Modal>

      <div className={styles.yjFileAreaButtonGroupWrapper}>
        {/* <Tooltip
          placement="topLeft"
          title={"This feature is coming soon"}
          color="#78bf59"
        > */}
        {/* Removed below condition since, there is not mapped new permission
           !fileAreaSettings.fileAreaManageEmailSubscription ||
           */}
        {/* <Button
            hidden={
              fileSelection === PORTAL_FILE_SECTION
            }
            type="primary"
          >
            MANAGE EMAIL SUBSCRIPTION
          </Button>
        </Tooltip> */}
        <Button
          disabled={!hasCheckinList&&!props.manageCheckin}
          hidden={
            !userPermission.privDMSCanCheckInCheckOutInternalFiles ||
            fileSelection === PORTAL_FILE_SECTION
          }
          onClick={() => {
            setShowCheckinModal(true);
            warningNotification(
              [""],
              "The file you are checking-in must match to the downloaded file name and the extension."
            );
          }}
          type="primary"
        >
          CHECK-IN
        </Button>
        {/* <Tooltip
          placement="topLeft"
          title={"This feature is coming soon"}
          color="#78bf59"
        >
          <Button
            hidden={!userPermission.privDMSCanViewFileHistory}
            type="primary"
            onClick={onClickFileAreaHistory}
          >
            HISTORY
          </Button>
        </Tooltip> */}
        {/*<Button*/}
        {/*  onClick={() => {*/}
        {/*    setRequestFormValidated(false);*/}
        {/*    handleShowRequestModal(true);*/}
        {/*  }}*/}
        {/*  hidden={fileSelection === INTERNEL_FILE_SECTION}*/}
        {/*  type="primary"*/}
        {/*>*/}
        {/*  REQUEST*/}
        {/*</Button>*/}
      </div>
    </>
  );
};
