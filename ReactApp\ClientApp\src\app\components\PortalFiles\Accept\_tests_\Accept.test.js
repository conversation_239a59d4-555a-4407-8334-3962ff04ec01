import React from "react";
import renderer from "react-test-renderer";
import {Provider} from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";
import {mount} from "enzyme";

import initTestSuite from "@app/utils/config/TestSuite";
import Accept from "../";
import Uploader from "@app/components/Uploader";

const ReduxProvider = ({children, store}) => (
    <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const CustomAcceptOption = (props) => {
    const INITIAL_STATE = {
        fileArea:{
            fileAreaSettings:{}
        },
        fileDetails:{}
    }
    const store = mockStore(INITIAL_STATE);

    return (
        <ReduxProvider store={store}>
            <Accept {...props}/>
        </ReduxProvider>
    );
};

describe("Portal Files - Accept Test Suite", () => {


    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const acceptComponent = mount(<CustomAcceptOption siteId={'xxx'} selectedFiles={[]} onModalClose={() => {
        }}/>);
        expect(acceptComponent.html()).not.toBe(null);
    });

    it("should render and create snapshot properly", () => {
        const acceptComponent = renderer.create(<CustomAcceptOption siteId={'xxx'} selectedFiles={[]} onModalClose={() => {
        }}/>).toJSON();
        expect(acceptComponent).toMatchSnapshot();
    });

    it("shouldn't have Uploader component if selectedFiles are empty", () => {
        const rejectComponent = mount(<CustomAcceptOption siteId={'xxx'} selectedFiles={[]} onModalClose={() => {
        }}/>);
        expect(rejectComponent.find(Uploader)).toHaveLength(0);
    });

    it("should have Uploader component if selectedFiles are not empty", () => {
        const mockSelectedFiles = [
            {
                assign: 'xxx',
                created: 'xxx',
                createdBy: 'xxx',
                expirationDate: 'xxx',
                expirationStatus: 'xxx',
                fileCondition: 'xxx',
                id: 'xxx',
                modified: 'xxx',
                projects: 'xxx',
                size: 'xxx',
                status: 'xxx',
                tags: [],
                title: 'xxx',
                type: 'xxx',
                year: 'xxx'
            }
        ];
        const rejectComponent = mount(<CustomAcceptOption siteId={'xxx'} selectedFiles={mockSelectedFiles}
                                                            onModalClose={() => {
                                                            }}/>);
        expect(rejectComponent.find(Uploader)).toHaveLength(1);
    });
});
