@import '_variables';
@import '_mixins';
@import '_fonts';
@import '_yjcommon';
@import '../styles/assets/images/_imagepath';

body {
  font-family: @yj-base-font-family;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  margin: 0;
  padding: 0;
  text-rendering: optimizeLegibility;
}

html,
body {
  height: 100%;
}

ul,
ol {
  list-style: none;
  margin: 0;
  padding: 0;
}

:root {
  --color-primary: @color-gray-dark;
  --color-secondary: @color-blue-dark;
  --color-tertiary: @color-red;
  --color-accent: @color-accent;
  --color-white: @color-white;
  --color-gray-light: @color-gray-light;
  --color-gray-medium: @color-gray-medium;
  --color-black:@color-black;
}
