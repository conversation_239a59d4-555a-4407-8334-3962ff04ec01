import React,{useState} from "react";
import { But<PERSON>,Dropdown,<PERSON>u, Modal } from "antd";
import {
  CloseCircleOutlined,
  CheckCircleOutlined,
  DownloadOutlined,
} from "@ant-design/icons/lib";

import { Tooltip } from "antd";
import styles from "./index.module.less";
import hasPermission from "@app/utils/permission";
import { useDispatch,useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import { IFile } from '@app/types/fileAreaTypes';
import PortalFilesDownloadModal, { downloadTypes } from '@app/features/PortalFilesDownloadModal';

import {updateContextMenuDownloadOption} from '@app/redux/actions/fileAreaActions';
export interface IPortalFilesActionPanel {
  key?: string;
  disableActionPanel: boolean;
  disableDownload?: boolean;
  disableAccept?: boolean;
  disableReject?: boolean;
  onClickReject: () => void;
  onClickAccept: () => void;
  showDownload?: boolean;
  selectedFileList: IFile[];
}

export default (props: IPortalFilesActionPanel) => {
  const dispatch = useDispatch();
  const {
    folderTree,
  } = useSelector((state: RootState) => state.fileArea);

  const [downloadType, setDownloadType] = useState<downloadTypes | undefined>(downloadTypes.individual);
  const [showDownloadModal, setShowDownloadModal] = useState(false);
  
  
     const displaydownloadModal = (downloadTypeInput: downloadTypes, display: boolean) => {
        setDownloadType(downloadTypeInput);
        setShowDownloadModal(display);
      };
  
      const handleOnDownloadModalCancel = () => {
          dispatch(updateContextMenuDownloadOption(false));
          setShowDownloadModal(false);
        };
  
    const downloadOptionsMenu = (
      <Menu className={styles.yjFilterMenuDropdownWrapper}>
        <Menu.Item
          hidden={props.selectedFileList && props.selectedFileList?.length <= 0}
          onClick={() => {
            displaydownloadModal(downloadTypes.individual, true);
          }}
          key="1"
        >
          Download Files
        </Menu.Item>
        <Menu.Item
          hidden={props.selectedFileList && props.selectedFileList?.length <= 1}
          onClick={() => {
            displaydownloadModal(downloadTypes.zip, true);
          }}
          key="2"
        >
          Download as a zip file
        </Menu.Item>
      </Menu>
    );

  return (

    <>

<Modal
        visible={showDownloadModal}
        title={'Download Files'}
        maskClosable={false}
        destroyOnClose={true}
        className="yjCommonModalSmall"
        onCancel={handleOnDownloadModalCancel}
        footer={[
          <Button onClick={handleOnDownloadModalCancel} key="submit" type="primary">
            Done
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <PortalFilesDownloadModal
            hasDownloaded={(hasDownloaded: boolean) => {
              if (hasDownloaded) {
                setShowDownloadModal(false);
              }
            }}
            selectedFiles={props.selectedFileList}
            downloadType={downloadType}
          />
        </div>
      </Modal>

    <div style={{ backgroundColor: "#0e678e" }}>
      <div
        className={styles.yjPortalUploadActionButtonPanel}
        style={{ border: "none", boxShadow: "0 3px 5px #094560" }}
      >
        <Button type="default" disabled={!props.disableActionPanel || !hasPermission(folderTree, 'FILE_AREA_CLIENT_PORTAL_FILE_EDIT') || props.disableDownload}>
        <div className={styles.yjActionListWrapper}>        
            <Dropdown getPopupContainer={() => document.getElementById('downloadOptionsMenu') as HTMLElement} overlay={downloadOptionsMenu} trigger={['click']}>
              <div>
                <Button>
                  <div id="downloadOptionsMenu">
                    <>
                      <DownloadOutlined /> <span>DOWNLOAD</span>
                    </>
                  </div>
                </Button>
              </div>
            </Dropdown>
          </div>
        </Button>
        <Button
          disabled={!props.disableActionPanel || !hasPermission(folderTree, 'FILE_AREA_CLIENT_PORTAL_FILE_EDIT') || props.disableAccept}
          type="default"
          onClick={props.onClickAccept}
        >
          <CheckCircleOutlined />
          ACCEPT
        </Button>
        <Button
          disabled={!props.disableActionPanel || !hasPermission(folderTree, 'FILE_AREA_CLIENT_PORTAL_FILE_EDIT') || props.disableReject}
          type="default"
          onClick={props.onClickReject}
        >
          <CloseCircleOutlined />
          REJECT
        </Button>
      </div>
    </div>
    </>
  );
  
};
