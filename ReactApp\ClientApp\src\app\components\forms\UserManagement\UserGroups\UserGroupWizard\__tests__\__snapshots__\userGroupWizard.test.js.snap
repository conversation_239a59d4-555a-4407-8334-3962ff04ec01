// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`<UserGroupWizard/> should render and create the snapshot properly 1`] = `
Array [
  <div
    className="yjStepper"
  >
    <div
      className="ant-steps ant-steps-horizontal ant-steps-label-horizontal"
      style={Object {}}
    />
  </div>,
  <div
    className="ant-skeleton ant-skeleton-active"
  >
    <div
      className="ant-skeleton-content"
    >
      <h3
        className="ant-skeleton-title"
        style={
          Object {
            "width": "38%",
          }
        }
      />
      <ul
        className="ant-skeleton-paragraph"
      >
        <li
          style={
            Object {
              "width": undefined,
            }
          }
        />
        <li
          style={
            Object {
              "width": undefined,
            }
          }
        />
        <li
          style={
            Object {
              "width": "61%",
            }
          }
        />
      </ul>
    </div>
  </div>,
  <div>
    <div>
      <button
        className="ant-btn ant-btn-primary"
        hidden={true}
        onClick={[Function]}
        type="submit"
      >
        <span>
          Back
        </span>
      </button>
    </div>
    <div>
      <button
        className="ant-btn ant-btn-default"
        onClick={[Function]}
        type="button"
      >
        <span>
          Cancel
        </span>
      </button>
      <button
        className="ant-btn ant-btn-primary"
        onClick={[Function]}
        type="button"
      >
        <span>
          Save and Continue
        </span>
      </button>
    </div>
  </div>,
]
`;
