// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Stats Content Test Suite should create and match to snapshot 1`] = `
<div
  className="ant-row"
  style={
    Object {
      "marginLeft": -6,
      "marginRight": -6,
    }
  }
>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              License ID
            </div>
            <div
              className="ant-card-meta-description"
            >
              100586
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Expiration Date
            </div>
            <div
              className="ant-card-meta-description"
            >
              2020/12/31
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Remaining Days
            </div>
            <div
              className="ant-card-meta-description"
            >
              45
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper yjLblStatus"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Status
            </div>
            <div
              className="ant-card-meta-description"
            >
              ACTIVE
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Allocated Space
            </div>
            <div
              className="ant-card-meta-description"
            >
              50GB
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Internal Users
            </div>
            <div
              className="ant-card-meta-description"
            >
              200
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Support Level
            </div>
            <div
              className="ant-card-meta-description"
            >
              PREMIUM
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-3"
    style={
      Object {
        "paddingLeft": 6,
        "paddingRight": 6,
      }
    }
  >
    <div
      className="ant-card ant-card-bordered yjCardWrapper"
      onMouseEnter={[Function]}
      onMouseLeave={[Function]}
    >
      <div
        className="ant-card-body"
        style={Object {}}
      >
        <div
          className="ant-card-meta"
        >
          <div
            className="ant-card-meta-detail"
          >
            <div
              className="ant-card-meta-title"
            >
              Integration
            </div>
            <div
              className="ant-card-meta-description"
            >
              SALES FORCE
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
`;
