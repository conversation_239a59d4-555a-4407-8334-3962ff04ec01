import { WEBEndpointType } from "@iris/discovery.fe.client";
import { UserClaims, CustomUserClaims } from "@okta/okta-auth-js";

export interface IAuthFunction {
  username: string;
  password: string;
  client_id?: string;
  client_secret?: string;
  grant_type?: string;
  scope?: string;
}

export interface IAuthState {
  access_token: string | null;
  isAuthenticated: boolean | null;
  isLoading: boolean;
  user: null | UserClaims<CustomUserClaims>;
  errors: string[];
  menuItems: [MenuItem] | [];
  routeKey: string;
  userLoaded: boolean;
  endPoints: WEBEndpointType[] | null;
  tenant: string | null;
}

export interface MenuItem {
  id: number;
  key: string;
  name: string;
  order: number;
  subMenus: [MenuItem] | [];
}
