@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjExternalUrlWrapper {
  background-color: @color-bg-external-wrapper;
  height: 100vh;

  .yjExternalUrlHeader {
    background-color: @color-bg-external-wrapper-header;
    padding: 0;

    .yjExteralUrlLogo {
      background: url(../../../../styles/assets/images/Logo.png) no-repeat center center;
      height: 70px;
      margin: 0 .75em;
      width: 230px;
    }

    .yjExteralUrlHeading {
      background: @color-bg-external-title-section;

      h1 {
        color: @color-font-external-title-section;
        padding: .5em 1em;
      }
    }
  }

  .yjExternalUrlContent {
    background: @color-bg-external-content;
    margin: 2em;
    padding: 5em 25em;
    text-align: center;

    button {
      margin-top: 1em;
    }
  }
}
