import React, { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";
import { Skeleton, Spin } from "antd";
import { FormInstance } from "antd/lib/form";
import { Store } from "antd/lib/form/interface";
import { useHistory } from "react-router-dom";

import { fetchOptions, saveFiles, setSuccessedFiles, } from "@app/redux/actions/fileDetailsActions";
import { UploaderSubmit } from ".";
import { RootState } from "@app/redux/reducers/state";
import { FileEvents, FileRecord, UrlEvents } from "./types";

type PropTypes = {
  uploadType: number;
  fileList?: FileRecord[];
  siteId: string;
  binderId: string;
  form: FormInstance;
  fileEvents?: FileEvents;
  urlEvents?: UrlEvents;
  disabledForm?: boolean;
  forManageFiles?: boolean;
  onFormChange?: (event: any) => void;
  onFolderTreeChange?: (event: number) => void;
  forPortalFiles?: boolean;
  fileAreaSelection?: boolean;
};

const UploaderSubmitContainer: React.FC<PropTypes> = ({
  uploadType,
  fileList,
  siteId,
  binderId,
  form,
  fileEvents,
  urlEvents,
  disabledForm,
  forManageFiles,
  onFormChange = () => {},
  onFolderTreeChange = () => {},
  forPortalFiles = false,
  fileAreaSelection
}) => {
  const dispatch = useDispatch();
  const history = useHistory();
  const {
    options,
    isOptionsFetched,
    successedFiles,
    pendingSave,
    permissions,
  } = useSelector((state: RootState) => {
    return {
      options: {
        ...state.fileDetails.options,
        folderTree: state.fileArea.folderTree,
      },
      isOptionsFetched: state.fileDetails.isOptionsFetched,
      successedFiles: state.fileDetails.successedFiles,
      pendingSave: state.fileDetails.pendingSave,
      permissions: state.userManagement.userPermission,
    };
  });

  useEffect(() => {
    dispatch(fetchOptions(siteId, history));
  }, [dispatch, siteId]);

  useEffect(() => {
    if (successedFiles && !!successedFiles.length) {
      fileEvents?.onSaveSuccess(successedFiles);
      dispatch(setSuccessedFiles([]));
    }
  }, [dispatch, fileEvents, successedFiles]);

  const handleAcceptFiles = (
    values: Store,
    inputFileList: FileRecord[],
    folderId: number
  ) => {
    const { newTags, tags, ...rest } = values;
    const combinedTags = tags ? tags.concat(newTags) : newTags;
    const fileData = {
      ...rest,
      siteId,
      binderId: form.getFieldValue("selectedBinderArea"),
      files: inputFileList
        .filter((file) => file.checked)
        .map(({ id, title }: any) => ({ portalFileId: id, title })),
      folderId: folderId,
      tags: combinedTags,
      binderNodeId: form.getFieldValue("selectedBinderNodeId"),
    } as any;

    dispatch(saveFiles(fileData, forPortalFiles));
  };

  const onFinish = (
    values: Store,
    inputFileList: FileRecord[],
    binderNodeId: number
  ) => {
    if (forPortalFiles) {
      handleAcceptFiles(values, inputFileList, binderNodeId);
    } else {
      const { newTags, tags, ...rest } = values;
      const combinedTags = tags ? tags.concat(newTags) : newTags;
      console.info(inputFileList);
      dispatch(
        saveFiles(
          {
            ...rest,
            siteId,
            binderId,
            files: transformFileList(inputFileList),
            binderNodeId,
            tags: combinedTags,
          },
          forPortalFiles
        )
      );
    }
  };

  if (!isOptionsFetched) {
    return <Skeleton active />;
  }

  return (
    <Spin spinning={pendingSave}>
      <UploaderSubmit
        uploadType={uploadType}
        options={options}
        permissions={permissions}
        fileList={fileList}
        fileEvents={fileEvents}
        urlEvents={urlEvents}
        form={form}
        onFinish={onFinish}
        disabledForm={disabledForm}
        forManageFiles={forManageFiles}
        onFormChange={onFormChange}
        onFolderTreeChange={onFolderTreeChange}
        fileAreaSelection={fileAreaSelection}
      />
    </Spin>
  );
};

const transformFileList = (fileList: FileRecord[]) => {
  return fileList.filter((v) => v.checked).map(({ checked, ...rest }) => rest);
};

export default UploaderSubmitContainer;
