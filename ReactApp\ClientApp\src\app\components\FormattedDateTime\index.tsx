import styles from './index.module.less';
import React from "react";
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import { useDateTimeFormatter } from "@app/hooks/useDateTimeFormatter";

export const FormattedDateTime: React.FC<{ value: any, showTime?: boolean }> = ({ value, showTime }) => {
  const { userPreferences } = useSelector((state: RootState) => state.userManagement);
  const localizationCultureCode = userPreferences?.localizationCultureCode ?? 'en-US';
  // Use the core hook with the derived localization culture code
  const formatDate = useDateTimeFormatter(localizationCultureCode, showTime);

  if (value === '-' || value === 'N/A' || value == null) {
    return <p className={styles.yjGridTextFormat}>{'N/A'}</p>;
  }
  return (
    <p className={styles.yjGridTextFormat}>{formatDate(new Date(value))}</p>
  );
};
