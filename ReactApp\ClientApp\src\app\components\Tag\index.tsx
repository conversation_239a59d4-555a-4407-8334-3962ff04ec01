import styles from "@pages/Onboarding/LicenseManagement/index.module.less";
import {Tag} from "antd";
import { LockFilled } from "@ant-design/icons";

import statusColorSwitch from "@app/utils/css/statusColorSwitch";
import React from "react";

export const renderTag = (value: string, id: number, color?: string) => {

  const legalHold = value?.split('_')[1];
  const text = value?.replace('_', '-');
  return ( text &&
    <div className={styles.yjGridTextCenter}>
      {" "}
      <Tag
        className={styles.yjColorTag}
        color={color ? color : statusColorSwitch(id)}
      >
        {text} {legalHold ? <LockFilled/> : ''}
      </Tag>
    </div>
  );
};
