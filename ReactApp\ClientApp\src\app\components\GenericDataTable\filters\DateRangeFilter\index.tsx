import React, { useRef, useEffect } from "react";
import { DatePicker } from "antd";
import moment from "moment";
import useFilter from "../../hooks/useFilter";
import {
  getTableHeaderElement,
  handleScrollPlacement,
} from "../../util/getTableHeaderElement";
import { GenericGridFilterTypes } from "../../types";
import {
  getUtcByShortFormat
} from "@app/utils";

const { RangePicker } = DatePicker;

export default ({ data }: any) => {
  const { filter, addFilter, removeFilter } = useFilter(
    data.key,
    data.title,
    false
  );
  const pickerRef = useRef<any>(null);
  const dateFormat = moment.localeData().longDateFormat('L');
  const { from, to } = filter.containsData
    ? filter.data[0].value
    : { from: null, to: null };

  const validatedValue = () => {

    const momentFrom = moment(from ? new Date(from) : from, dateFormat);
    const momentTo = moment(to ? new Date(to) : to, dateFormat);
    if (momentFrom.isValid() && momentTo.isValid()) {
      return [momentFrom, momentTo];
    }
    return null;
  };

  const _onChange = (dates: any, dateStrings: [string, string]) => {
    const [localFromDate, localToDate] = dateStrings;

    if (!localFromDate || !localToDate) {
      return removeFilter(null)
    }

    addFilter(
      data.key,
      {
        from: getUtcByShortFormat(localFromDate),
        to: getUtcByShortFormat(localToDate),
      },
      `From ${localFromDate} To ${localToDate}`,
      GenericGridFilterTypes.RANGE
    );

  };

  useEffect(() => {

    handleScrollPlacement(data.key, pickerRef);
  }, [data.key]);

  return (
    <div onClick={(e) => e.stopPropagation()}>
      <RangePicker
        ref={pickerRef}
        id={data.key}
        value={validatedValue() as any}
        format={dateFormat}
        onChange={_onChange}
        getPopupContainer={() => getTableHeaderElement(data.key)}
      />
    </div>
  );
};
