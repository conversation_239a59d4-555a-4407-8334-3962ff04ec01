import HTTPResponse from "../utils/http/interfaces/HttpResponse";
import httpVerbs from "../utils/http/httpVerbs";
import http from "../utils/http";
import config from "../utils/config";
import { getParameterizedUrl } from "../utils";

export const getModules = (verticalId: number): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: `${config.api.organizationAPI.verticals}/${verticalId}/modules`,
  });
};

export const getFlows = (licenceId: string): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api.organizationAPI.getEnabledFlows,
      licenceId
    ),
  });
};

export const getVerticals = (): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: config.api.organizationAPI.verticals,
  });
};
