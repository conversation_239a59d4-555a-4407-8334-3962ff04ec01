@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@file-path: '../../../styles/';

@keyframes fadeIn {

  0% {
    transform: translateY(-25%);
  }

  50% {
    transform: translateY(4%);
  }

  65% {
    transform: translateY(-2%);
  }

  80% {
    transform: translateY(2%);
  }

  95% {
    transform: translateY(-1%);
  }

  100% {
    transform: translateY(0%);
  }
}

.yjContextMenu {
  animation-duration: .4s;
  animation-name: fadeIn;
  background-clip: padding-box;
  background-color: @color-bg-filearea-actionpanel;
  left: 0;
  list-style-type: none;
  margin: 0;
  outline: none;
  overflow: hidden;
  padding: 0;
  position: absolute;
  text-align: left;
  top: 0;
  z-index: 1000;

  li {
    clear: both;
    color: @color-font-filearea-action-list;
    cursor: pointer;
    font-size: 14px;
    font-weight: @yjff-regular;
    line-height: 22px;
    margin: 0;
    padding: 5px 12px;
    transition: all .3s;
    white-space: nowrap;
    width: 150px;

    span {
      padding-right: 10px;
    }

    >i {
      margin-right: 8px;
    }

    &:hover {
      background-color: fade(@color-primary, 80%);
    }

    >svg {
      font-size: 16px;
      margin-right: 7px;
    }

  }
}