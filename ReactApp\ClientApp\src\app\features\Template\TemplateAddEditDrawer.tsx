import { <PERSON><PERSON>, Col, Drawer, Form, Input, Row, Switch } from 'antd';
import React, { useEffect, useState } from 'react';
import { FormInstance } from 'antd/lib/form/Form';
import { required } from '@app/components/forms/validators';
import { CloseOutlined } from '@ant-design/icons/lib';
import styles from '@app/features/Channel/index.module.less';
import FolderTree from '@app/components/FolderTreeEditor';
import { TreeDataNode } from 'rc-tree-select/lib/interface';
import { Rule } from 'antd/lib/form';
import CancelChangesModal from '@app/utils/confirm/CancelChangesModal';
import { checkTemplateNameExists } from '@app/api/templateService';
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";
import logger from '@app/utils/logger';
import InfinitySelect, { InfinitySelectGetOptions } from '@app/components/InfinitySelect';
import { getInfiniteRecords } from '@app/api/infiniteRecordsService';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import config from "@app/utils/config";

type Props = {
  showDrawer: boolean;
  formRef: FormInstance;
  onDrawerClose: () => void;
  onSuccess: (values: any) => void;
  initialData: any[];
};

const MAX_LENGTH_HUNDRED = 100;
const MAX_LENGTH_TWENTY_FIVE = 25;
const LIMIT = 10;
const RETENTION_LIMIT = 20;

const TemplateAddEditDrawer = ({ onDrawerClose, showDrawer, formRef, onSuccess, initialData }: Props) => {
  const [show, setShow] = useState(false);
  const [folderData, setFolderData] = useState<Array<TreeDataNode>>([]);
  const [changed, setChanged] = useState(false);
  const [formCancel, setFormCancel] = useState(false);
  const isTemplateEditAction = !!formRef.getFieldValue('id');
  const templateName = formRef.getFieldValue('name');
  const { userPermission } = useSelector( (state: RootState) => state.userManagement );
  const isDisabledPrivellage = isTemplateEditAction ? !userPermission.privDMSCanManageTemplates : !userPermission.privDMSCanCreateTemplates

  useEffect(() => {
    setShow(showDrawer);
    if (showDrawer) {
      setFolderData([]);
      setChanged(false);
      setFormCancel(false);
    }
  }, [showDrawer]);

  useEffect(() => {
    formRef.setFields([{ name: 'parentNodes', value: folderData }]);

  }, [folderData]);

  const onFinish = () => {
    formRef.setFieldsValue(['parentNodes',folderData])
    formRef.validateFields().then((values) => {
      onSuccess(values)
    });
  };

  const onCancelButton = () => {
    if (changed) {
      return setFormCancel(true);
    }
    onDrawerClose();
  };

  const validateForlder = (): Rule => ({
    validator(_rule, value) {
      if (folderData.length == 0) {
        return Promise.reject('At least one folder is required');
      }
      for (let folder of folderData) {
        if (folder.children && folder.children.length > 0) {
          return Promise.resolve();
        }
      }
      return Promise.reject('At least one secondary folder is required');
    },
  });

  const tempalteNameAvailability = (): Rule => ({
    validator(_rule, value) {
      return value ? checkTemplateName(value) : Promise.resolve();
    },
  });

  const checkTemplateName = (name: any): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (templateName == name) return resolve(true);
      checkTemplateNameExists(name)
        .then((_e) => reject('Template Name already exists'))
        .catch((_e) => resolve(true));
    });
  };

  
  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    if (searchValue) {
        transformFilters.search = searchValue;
    }

    const options = {
        limit: LIMIT,
        offset: page - 1,
        ...transformFilters,
    }

    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].jobTypes, options)
        .then((res: any) => {
            if (res.data) {
                return res.data;
            } else {
                return []
            }
        })
        .catch((error: any) => {
            return [];
        });
};

  return (
    <>
      <CancelChangesModal
        visible={formCancel}
        title="DISCARD TEMPLATE"
        content="The changes will be discarded. Are you sure you want to proceed?"
        okText="Proceed"
        cancelText="Cancel"
        onCancel={() => {
          setFormCancel(false);
        }}
        onOk={() => {
          setFormCancel(false);
          onDrawerClose();
        }}
      />
      <Drawer
        title={isTemplateEditAction ? 'Update Template' : 'Create Template'}
        placement="right"
        onClose={onCancelButton}
        visible={show}
        className={'yjDrawerPanel'}
        width={700}
        closeIcon={<CloseOutlined data-testid="template-drawer-close-icon" />}
        footer={[
          <div key={`footer-${formRef.getFieldValue('id') || 0}`} style={{ textAlign: 'right' }}>
            <Button key="back" data-testid="template-drawer-close" type="default" onClick={onCancelButton}>
              Cancel
            </Button>
            <Button
              disabled={isDisabledPrivellage || folderData.some((node) => node.children && node.children.some((child) => (child.retention || 0) > RETENTION_LIMIT))}
              key="update"
              data-testid="template-drawer-create"
              type="primary"
              onClick={onFinish}
            >
              {isTemplateEditAction ? 'Update' : 'Create'}
            </Button>
          </div>,
        ]}
      >
        <Form form={formRef} key="createSiteForm" layout="vertical" onChange={() => setChanged(true)}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label={'Name'} name="name" rules={[required, tempalteNameAvailability()]}>
                <Input maxLength={MAX_LENGTH_TWENTY_FIVE} autoComplete="off" disabled={isDisabledPrivellage} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label={'Description'} name="description" rules={[required]}>
                <Input maxLength={MAX_LENGTH_HUNDRED} autoComplete="off" disabled={isDisabledPrivellage} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item valuePropName="checked" label={'Status'} name="templateStatus" initialValue={isTemplateEditAction ? formRef.getFieldValue('templateStatus') : true }>
                <Switch style={{ margin: 0 }} disabled={isDisabledPrivellage} />
              </Form.Item>
            </Col>
            <Col span={4}>
              <Form.Item valuePropName="checked" label={'Hide'} name="hide" initialValue={isTemplateEditAction ? formRef.getFieldValue('hide') : false}>
                <Switch style={{ margin: 0 }} disabled={isDisabledPrivellage} />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={24}>
            <Col span={24}>
              <Form.Item label={'Job Type'} name="binderTemplateJobTypeId" hidden>
                <Input value={'null'} />
                {/* <InfinitySelect
                  getPaginatedRecords={(page, method, searchValue) => getPaginatedRecords(page, method, searchValue)}
                  formatValue={(value) => {
                    return `${value.description}`;
                  }}
                  notFoundContent="No Jobs Available"
                  notLoadContent="Failed to load values in job type dropdown"
                  onChange={(e) => {}}
                  placeholder="Select a Job Type"
                  waitCharCount={3}
                  disabled={action!=='add'}
                /> */}
              </Form.Item>
            </Col>
          </Row>
          <Row>
            <Col span={24}>
              <Form.Item name="parentNodes" initialValue={initialData} />
            </Col>
            <Col span={24}>
              <h2>Folder Structure</h2>
              <div className={'edit' === 'edit' ? `${styles.yjFolderStructureTreeWrapper} ${styles.yjFolderStructureTreeUpdate}` : `${styles.yjFolderStructureTreeWrapper}`}>
                {showDrawer && (
                  <FolderTree
                    onFolderTreeUnmount={(data) => {
                      setFolderData(data);
                    }}
                    onChange={() => {
                      logger.debug('TemplateAddEditDrawer', 'FolderTree - onChange', '');
                      setChanged(true);
                    }}
                    data={initialData}
                  />
                )}
              </div>
            </Col>
            {isTemplateEditAction && (
              <Form.Item name="id" initialValue={formRef.getFieldValue('id')} hidden>
                <Input type="hidden" />
              </Form.Item>
            )}
          </Row>
        </Form>
      </Drawer>
    </>
  );
};

export default TemplateAddEditDrawer;
