import React from "react";
import {Modal} from "antd";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { MemoryRouter } from 'react-router-dom';

import IntegrationManagement from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";

const CustomIntegrationManagement = (props) => {
    return (
        <MemoryRouter>
            <IntegrationManagement {...props} />
        </MemoryRouter>
    );
}
describe("IntegrationManagement Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const imComponent = mount(<CustomIntegrationManagement />);
        expect(imComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const imComponent = renderer.create(<CustomIntegrationManagement />).toJSON();
        expect(imComponent).toMatchSnapshot();
    });

    it("should have a Modal element",() => {
        const imComponent = mount(<CustomIntegrationManagement />);
        expect(imComponent.find(Modal)).toHaveLength(1);
    });

    it("should have a PageTitle component",() => {
        const imComponent = mount(<CustomIntegrationManagement />);
        expect(imComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const imComponent = mount(<CustomIntegrationManagement />);
        expect(imComponent.find(PageContent)).toHaveLength(1);
    });
});




