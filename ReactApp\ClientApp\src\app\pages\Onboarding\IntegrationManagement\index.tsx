import React, { Fragment, useState } from "react";
import { withRouter } from "react-router-dom";
import { Button } from "antd";

import Modal from "../../../components/Modal";
import IntegrationManagement from "../../../components/forms/IntegrationManagement";
import PageTitle from "../../../components/PageTitle";
import PageContent from "../../../components/PageContent";

const Page = (props: any) => {
  const [showEditModal, setShowEditModal] = useState(false);
  const handleCancel = (e: any) => {
    setShowEditModal(false);
  };

  return (
    <Fragment>
      <Modal
        visible={showEditModal}
        title={"Integration Management Details"}
        onCancel={handleCancel}
        footer={[
          <Button key="back" type="default" onClick={handleCancel}>
            cancel
          </Button>,
          <Button key="submit" type="primary" onClick={handleCancel}>
            Manage
          </Button>,
        ]}
      >
        <IntegrationManagement />
      </Modal>

      <PageTitle title={props.title} />
      <PageContent></PageContent>
    </Fragment>
  );
};

export default withRouter(Page);
