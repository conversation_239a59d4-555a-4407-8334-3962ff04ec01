import React from 'react';
import LicenseManagement from '..';
import { shallow, mount } from 'enzyme';
import { Form, Input, Select } from 'antd';
import { act } from 'react-dom/test-utils';

Object.defineProperty(window, 'matchMedia', {
    writable: true,
    value: jest.fn().mockImplementation((query) => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(), // deprecated
        removeListener: jest.fn(), // deprecated
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
    })),
});

const waitForComponentToPaint = async (wrapper) => {
    await act(async () => {
        await new Promise((resolve) => setTimeout(resolve, 0));
        wrapper.update();
    });
};

const formLabels = [
    'Company Name',
    'Vertical',
    'Status',
    'Start Date',
    'Expiration Date',
    'Compliances',
    'License ID',
    'Allocated Space',
    'Internal User Count',
    'Support Level',
    'Integrations',
];

const formData = {
    companyName: 'Test Company',
    vertical: { value: '1', name: 'CPA' }
};

describe('<LicenseManagement />', () => {
    it('should render component', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.html()).not.toBe(null);
    });

    it('should have a form', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form)).toHaveLength(1);
    });

    it('should have 11 form input fields', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item)).toHaveLength(11);
    });

    it('should have 11 form input fields', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item)).toHaveLength(11);
    });

    it('should have label: Company Name', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(0).props().label).toEqual(
            formLabels[0]
        );
    });

    it('should have label: Vertical', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(1).props().label).toEqual(
            formLabels[1]
        );
    });

    it('should have label: Status', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(2).props().label).toEqual(
            formLabels[2]
        );
    });

    it('should have label: Start Date', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(3).props().label).toEqual(
            formLabels[3]
        );
    });

    it('should have label: Expiration Date', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(4).props().label).toEqual(
            formLabels[4]
        );
    });

    it('should have label: Compliances', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(5).props().label).toEqual(
            formLabels[5]
        );
    });

    it('should have label: License ID', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(6).props().label).toEqual(
            formLabels[6]
        );
    });

    it('should have label: Allocated Space', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(7).props().label).toEqual(
            formLabels[7]
        );
    });

    it('should have label: Internal User Count', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(8).props().label).toEqual(
            formLabels[8]
        );
    });

    it('should have label: Support Level', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(9).props().label).toEqual(
            formLabels[9]
        );
    });

    it('should have label: Integrations', () => {
        const component = shallow(<LicenseManagement />);
        expect(component.find(Form.Item).at(10).props().label).toEqual(
            formLabels[10]
        );
    });

    it('should show company name value', () => {
        const component = mount(<LicenseManagement details={formData} />);
        expect(
            component.find(Form.Item).at(0).find(Input).props().value
        ).toEqual(formData[component.find(Form.Item).at(0).props().name]);
    });

    it('should show vertical value', async () => {
        const component = mount(<LicenseManagement details={formData} />);
        await waitForComponentToPaint(component);
        expect(
            component
                .find(Form.Item)
                .at(1)
                .find(Select)
                .find('.ant-select-selection-item')
                .text()
        ).toEqual(formData[component.find(Form.Item).at(1).props().name].value);
    });
});
