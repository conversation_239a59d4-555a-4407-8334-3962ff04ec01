import React, { useEffect, useState } from "react";
import { Form, Row, Col, Button, Skeleton, Drawer, Radio, Space } from "antd";
import { CloseOutlined } from "@ant-design/icons";
import styles from "./index.module.less";
import SelectedFilesGrid, { ColumnConfig } from "@app/features/FileArea/SelectedFilesGrid";
import { IFile } from "@app/types/fileAreaTypes";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import config from "@app/utils/config";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import logger from "@app/utils/logger";
import { getClientAreaBinders } from "@app/api/fileAreaService";
import SubmitButton from "@app/components/SubmitButton";

const LIMIT = 10;

export interface LinkFilesProps {
  selectedFiles: IFile[];
  onItemSelect: (fileList: string[], destinationClientId: string[]) => void;
  onClosePopup: () => void;
  showDrawer: boolean;
  linkFilesLoading: boolean;
  onSuccess: () => void;
}

interface BinderRecord {
  binderId: string;
  binderName: string;
  binderTemplate: {
    value: number;
    name: string;
  };
  job: {
    value: number;
    name: string;
  };
  status: {
    value: number;
    name: string;
  };
  year: number;
  created: string;
  createdBy: {
    value: number;
    name: string;
  };
  clientId?: string;
}

export default ({
  onClosePopup,
  onItemSelect,
  selectedFiles,
  showDrawer,
  linkFilesLoading,
  onSuccess,
}: LinkFilesProps) => {
  const [selectedClientsLists, setSelectedClientsList] = useState<string[]>([]);
  const [selectedFileList, setSelectedFileList] = useState<string[]>([]);
  const linkFileColumnConfigs: ColumnConfig[] = [
    { title: "", dataIndex: "remove", key: "remove", width: 40 },
    { title: "Title", dataIndex: "title", key: "title", ellipsis: true },
  ];
  const [binderRecords, setBinderRecords] = useState<BinderRecord[]>([]);
  const [clientBinderMap, setClientBinderMap] = useState<Record<string, BinderRecord[]>>({});
  const [selectedFileAreas, setSelectedFileAreas] = useState<{ id: string; clientId: string }[]>(
    []
  );
  const [clientIdKey, setClientIdKey] = useState(0);
  const [clientSelectKey, setClientSelectKey] = useState(0);
  const [isLoadingBinders, setIsLoadingBinders] = useState(false);
  const [searchByField, setSearchByField] = useState("clientRef");

  useEffect(() => {
    if (showDrawer && selectedFiles.length > 0) {
      const fileIdList = selectedFiles.map(file => file.id);
      setSelectedFileList(fileIdList);
      setSearchByField("clientRef");
    } else {
      setSelectedFileList([]);
    }
  }, [showDrawer, selectedFiles]);

  const handleFilesChange = (fileList: any[]) => {
    if (fileList.length > 0) {
      setSelectedFileList(fileList);
    } else {
      handleCloseDrawer();
    }
  };

  const resetFields = () => {
    setSelectedClientsList([]);
    setSelectedFileAreas([]);
    setBinderRecords([]);
    setClientBinderMap({});
    setSearchByField("clientRef")
  };

  const handleCloseDrawer = () => {
    resetFields();
    onClosePopup();
  };

  const handleClientIdChange = async (clientIdList: string[], selectedOptions: any[] = []) => {
    // Update the selected clients list
    setSelectedClientsList(clientIdList);
    onItemSelect(selectedFileList, clientIdList);

    // Auto-select if there's only one client
    if (clientIdList.length === 1) {
      setClientSelectKey(prevKey => prevKey + 1);
    }

    if (clientIdList.length === 0) {
      setBinderRecords([]);
      setSelectedFileAreas([]);
      return;
    }

    setIsLoadingBinders(true);
    try {
      let newBinderMap: Record<string, BinderRecord[]> = { ...clientBinderMap };
      let allBinderRecords: BinderRecord[] = [];

      const clientPromises = clientIdList
        .filter(clientId => !newBinderMap[clientId])
        .map(async clientId => {
          const selectedClient = selectedOptions.find(option => option.id === clientId);
          try {
            const response = await getClientAreaBinders(clientId);
            if (response.data && response.data.records) {
              const recordsWithClientId = response.data.records.map((record: BinderRecord) => ({
                ...record,
                clientId: clientId,
                displayText: selectedClient.displayText,
              }));
              return { clientId, records: recordsWithClientId };
            }
            return { clientId, records: [] };
          } catch (error) {
            logger.error("Link Files Component", "fetchFileLinks", error);
            return { clientId, records: [] };
          }
        });

      const results = await Promise.all(clientPromises);

      for (const { clientId, records } of results) {
        newBinderMap[clientId] = records;
      }

      const removedClientIds = Object.keys(newBinderMap).filter(key => !clientIdList.includes(key));
      for (const key of removedClientIds) {
        delete newBinderMap[key];
      }

      for (const clientId of clientIdList) {
        if (newBinderMap[clientId]) {
          allBinderRecords = [...allBinderRecords, ...newBinderMap[clientId]];
        }
      }

      setClientBinderMap(newBinderMap);
      setBinderRecords(allBinderRecords);

      // Filter out selected file areas whose client is no longer selected
      const updatedSelectedFileAreas = selectedFileAreas.filter(area =>
        clientIdList.includes(area.clientId)
      );
      setSelectedFileAreas(updatedSelectedFileAreas);
    } catch (error) {
      logger.error("Link Files Component", "handleClientIdChange", error);
    } finally {
      setClientIdKey(prevKey => prevKey + 1);
      setIsLoadingBinders(false);
    }
  };

  const getPaginatedClients = async (
    page: number,
    method: InfinitySelectGetOptions,
    searchValue?: string
  ): Promise<Array<any>> => {
    const transformFilters: any = {};
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const options = {
      limit: LIMIT,
      offset: page - 1,
      field: searchByField,
      ...transformFilters,
    };

    return getInfiniteRecords(
      config.api[OperationalServiceTypes.FileManagementService].clients,
      options
    )
      .then((res: any) => {
        if (res.data) {
          // // Auto-select if there's only one client and no selection yet
          if (res.data.records && res.data.records.length === 1 && selectedClientsLists.length === 0) {
            const singleClient = res.data.records[0];
            const clientId = singleClient.id;
            handleClientIdChange([clientId], [singleClient]);
          }
          return res.data;
        } else {
          return [];
        }
      })
      .catch((error: any) => {
        logger.error("getPaginatedClients", "getPaginatedClients", error);
        return [];
      });
  };

  const handleFileAreaChange = (values: string[], options: any[] = []) => {
    const selectedWithClientId = options.map(option => ({
      id: option.binderId,
      clientId: option.clientId || "",
    }));

    setSelectedFileAreas(selectedWithClientId);
    onItemSelect(selectedFileList, values);

    // Auto-select if there's only one file area
    if (values.length === 1) {
      setClientIdKey(prevKey => prevKey + 1);
    }
  };

  const getPaginatedFileAreas = async (
    page: number,
    method: InfinitySelectGetOptions,
    searchValue?: string
  ): Promise<any> => {
    let filteredRecords = [...binderRecords];
    let records = filteredRecords.map(record => ({
      id: record.binderId,
      ...record,
    }));

    if (searchValue) {
      const lowerSearch = searchValue.toLowerCase();
      records = records.filter(
        record =>
          record.binderId.toLowerCase().includes(lowerSearch) ||
          record.binderName.toLowerCase().includes(lowerSearch)
      );
    }

    const startIndex = (page - 1) * LIMIT;
    const endIndex = startIndex + LIMIT;
    const paginatedRecords = records.slice(startIndex, endIndex);

    // Auto-select if there's only one file area and no selection yet
    if (paginatedRecords.length === 1 && selectedFileAreas.length === 0) {
      const singleFileArea = paginatedRecords[0];
      handleFileAreaChange([singleFileArea.id], [singleFileArea]);
    }

    return {
      records: paginatedRecords,
      pageCount: Math.ceil(records.length / LIMIT),
      pageNumber: page,
      pageSize: LIMIT,
      totalRecordCount: records.length,
    };
  };

  const getSelectedClientIds = () => {
    return selectedClientsLists;
  };

  const getSelectedFileAreaIds = () => {
    return selectedFileAreas.map(area => area.id);
  };

  return (
    <Drawer
      title={"Link File(s)"}
      placement="right"
      visible={showDrawer}
      className={"yjDrawerPanel"}
      width={700}
      closeIcon={<CloseOutlined data-testid="template-drawer-close-icon" />}
      onClose={handleCloseDrawer}
      footer={[
        <>
          <Button
            key="cancel"
            type="default"
            onClick={handleCloseDrawer}
            disabled={linkFilesLoading}
            data-testid="template-drawer-close"
          >
            Cancel
          </Button>

          <SubmitButton key="linkFiles" type="primary" onClick={onSuccess} disabled={!selectedFileAreas.length || linkFilesLoading} data-testid="template-drawer-create">
            Link
          </SubmitButton>
        </>
      ]}
    >
        <Row gutter={24}>
          <Col span={12}>
            <Row gutter={10}>
              {selectedFileList.length > 0 ? (
                <SelectedFilesGrid
                  key={selectedFiles.map(f => f.id).join(",")}
                  onFilesChange={handleFilesChange}
                  columnConfigs={linkFileColumnConfigs}
                  dataList={selectedFiles}
                />
              ) : (
                <Skeleton />
              )}
            </Row>
          </Col>
          <Col span={12}>
            <Row gutter={24}>
              <Form.Item
                label={"SEARCH BY"}
              >
                <Radio.Group
                  value={searchByField}
                  style={{ marginBottom: '5px' }}    
                  onChange={(e) => {
                    setSearchByField(e.target.value);
                    setClientSelectKey(prevKey => prevKey + 1);
                  }}
                >
                  <Space>
                    <Radio value="clientRef">Client Ref</Radio>
                    <Radio value="name">Client Name</Radio>
                  </Space>
                </Radio.Group>
              </Form.Item>
            </Row>
            <Row gutter={24}>
              <Form.Item
                label={"Client(s)"}
                name="clientId"
                className={styles.linkFileToClientLabel}
              ></Form.Item>
                <InfinitySelect
                  key={clientSelectKey}
                  returnObject={true}
                  getPaginatedRecords={(page, method, searchValue) =>
                    getPaginatedClients(page, method, searchValue)
                  }
                  formatValue={value => {
                    return `${value.displayText}`;
                  }}
                  notFoundContent="No Clients Available"
                  notLoadContent="Failed to load values in clients dropdown"
                  onChange={(values, options) => {
                    handleClientIdChange(values, options);
                  }}
                  placeholder="Please Select Client(s)"
                  waitCharCount={3}
                  value={getSelectedClientIds()}
                  defaultValues={selectedClientsLists.length === 1 ? selectedClientsLists : undefined}
                  mode="multiple"
                />
            </Row>
              <Row gutter={24}>
                <Form.Item
                  label={"File Area(s)"}
                  name="fileArea"
                  className={styles.linkFileToClientLabel}
                ></Form.Item>
                  <InfinitySelect
                    key={clientIdKey}
                    returnObject={true}
                    getPaginatedRecords={(page, method, searchValue) =>
                      getPaginatedFileAreas(page, method, searchValue)
                    }
                    formatValue={value => {
                      return `${value.displayText} - ${value.binderName}`;
                    }}
                    notFoundContent="No File Areas Available"
                    notLoadContent="Failed to load values in file areas dropdown"
                    onChange={(values, options) => {
                      handleFileAreaChange(values, options);
                    }}
                    placeholder="Please Select File Area(s)"
                    waitCharCount={3}
                    mode="multiple"
                    disabled={selectedClientsLists.length === 0 || isLoadingBinders}
                    value={getSelectedFileAreaIds()}
                    defaultValues={selectedFileAreas.length === 1 ? getSelectedFileAreaIds() : undefined}
                  />
              </Row>
          </Col>
        </Row>
    </Drawer>
  );
};
