import React, { useEffect, useState } from "react";
import { Tree } from "antd";
import {
  FolderAddOutlined,
  FolderOutlined,
  FolderOpenOutlined,
} from "@ant-design/icons";
import { DirectoryTreeProps } from "antd/lib/tree/DirectoryTree";

import styles from "./index.module.less";
import {
  ITreeNode,
  IFolderTreeResponse,
} from "@app/types/FileAreaFolderTreeTypes";

export interface IFolderTree {
  data: IFolderTreeResponse | null;
  onSelectFolder: (selectedKeys: any, info: any) => void;
  showTitle?: boolean;
  maxHeight?: string;
  disableRoot?: boolean;
  controlSelection?: boolean;
}

const { DirectoryTree } = Tree;

const folderAddOutLined = <FolderAddOutlined />;
const folderOutlined = <FolderOutlined />;
const openOutlined = <FolderOpenOutlined />;
const rootKey = "0";

export const mapFolderResponseToTree = (
  folderResponse: IFolderTreeResponse
) => {
  const folderTree: ITreeNode[] = [];
  try {
    const mainTreeNode: ITreeNode = {
      title: folderResponse?.siteName,
      key: rootKey,
      selectable: true,
      id: 0,
      icon: folderAddOutLined,
      isLeaf: false,
      children: [],
    };

    folderResponse.folders?.forEach((mainFolder) => {
      const TreeNode: ITreeNode = {
        title: mainFolder.name,
        key: `${mainFolder.id}`,
        retention: mainFolder.retention,
        selectable: true,
        children: [],
        icon: folderAddOutLined,
        id: mainFolder.id,
        isLeaf: false,
      };

      TreeNode.isLeaf =
        mainFolder.subFolders && mainFolder.subFolders.length > 0
          ? false
          : true;

      TreeNode.icon =
        mainFolder.subFolders && mainFolder.subFolders.length > 0
          ? folderAddOutLined
          : folderOutlined;

      mainFolder.subFolders?.forEach((subFolder) => {
        const SubTreeNode: ITreeNode = {
          title: subFolder.name,
          key: `${subFolder.id}`,
          id: subFolder.id,
          retention: subFolder.retention,
          selectable: true,
          isLeaf: true,
          icon: folderOutlined,
          children: [],
        };
        TreeNode.children.push(SubTreeNode);
      });

      mainTreeNode.children.push(TreeNode);
    });
    folderTree.push(mainTreeNode);
    return folderTree;
  } catch (error) {
    return folderTree;
  }
};

export const setExpandedFolder = (
  nodesList: ITreeNode[],
  selectedKey: string,
  expanded: boolean
): ITreeNode[] => {
  nodesList.forEach((node) => {
    if (node.children.length > 0) {
      if (node.key === selectedKey.toString()) {
        node.icon = expanded ? openOutlined : folderAddOutLined;
      }
      setExpandedFolder(node.children, selectedKey, expanded);
    }
  });
  return nodesList;
};

export const setNodeNotSelectable = (
  nodesList: ITreeNode[],
  level: number
): ITreeNode[] => {
  nodesList.forEach((node) => {
    if (level === 1) {
      node.selectable = false;
    }
    if (node.children.length > 0) {
      setNodeNotSelectable(node.children, level + 1);
    }
  });
  return nodesList;
};

export const getSelectedNode = (
  nodesList: ITreeNode[],
  selectedKey: string,
  onFound: Function
) => {
  nodesList.forEach((node) => {
    if (node.key === selectedKey.toString()) {
      onFound(node);
      return;
    }
    if (node.children.length > 0) {
      getSelectedNode(node.children, selectedKey, onFound);
    }
  });
};

export const setInitialExpanedNode = (
  nodesList: ITreeNode[],
  parentNode: string,
  nodeKey: string
): Array<string> => {
  for (let i = 0; i < nodesList.length; i++) {
    if (nodesList[i].key === nodeKey) {
      return ["0", parentNode];
    }
    if (nodesList[i].children.length > 0) {
      return setInitialExpanedNode(
        nodesList[i].children,
        nodesList[i].key,
        nodeKey
      );
    }
  }
  return ["0"];
};

export const FolderTree: React.FC<IFolderTree & DirectoryTreeProps> = ({
  data,
  onSelectFolder,
  showTitle = true,
  maxHeight = "41vh",
  disableRoot,
  controlSelection,
  ...props
}) => {
  const emptyTree: ITreeNode[] = [
    {
      title: "Root Folder",
      key: rootKey,
      id: 0,
      selectable: true,
      isLeaf: false,
      icon: folderAddOutLined,
      children: [],
    },
  ];

  const [treeData, setTreeData] = useState(emptyTree);
  const [loading, setLoading] = useState<boolean>(true);
  const [expaneNodes, setExpanedNodes] = useState<Array<string>>([]);

  const handleOnSelect = (selectedKeys: any, info: any) => {
    getSelectedNode(treeData, selectedKeys[Number(rootKey)], onFoundFolder);
  };

  const handleOnExpand = (selectedKeys: any, data: any) => {
    const treeDataResponse = setExpandedFolder(
      treeData,
      data.node.key,
      data.expanded
    );
    setTreeData(treeDataResponse);
  };

  const onFoundFolder = (value: ITreeNode) => {
    onSelectFolder(value.id, value);
  };

  useEffect(() => {
    if (data) {
      const mappedResponse = mapFolderResponseToTree(data);
      const treeDataResponse = setExpandedFolder(mappedResponse, rootKey, true);
      const withDisabledNodes = configureFolderBasedOnOption(
        treeDataResponse,
        disableRoot,
        controlSelection
      );

      setTreeData(withDisabledNodes);
      const selectedkey = props.selectedKeys ? props.selectedKeys[0] : "";
      setExpanedNodes(
        setInitialExpanedNode(withDisabledNodes, "0", selectedkey.toString())
      );
      setLoading(false);
    }
  }, [data, disableRoot, controlSelection]);

  return (
    <div className={styles.yjFileFinderWrapper}>
      {showTitle && (
        <div className={styles.yjFileFinderHeader}>
          <div className={styles.yjFileFinderHeaderText}>File Finder</div>
        </div>
      )}
      <div
        className={styles.yjFileFinderTreeWrapper}
        style={{ height: maxHeight }}
      >
        <div className={"yjFileFinderTree"}>
          {!loading && (
            <DirectoryTree
              defaultSelectedKeys={["0"]}
              {...props}
              multiple
              defaultExpandedKeys={[...expaneNodes]}
              onSelect={handleOnSelect}
              onExpand={handleOnExpand}
              treeData={treeData}
            />
          )}
        </div>
      </div>
    </div>
  );
};

const configureFolderBasedOnOption = (
  tree: ITreeNode[],
  disableRoot?: boolean,
  controlSelection?: boolean
): ITreeNode[] => {
  let result: ITreeNode[] = tree;

  if (disableRoot) {
    result = tree.map((value) =>
      value.id === 0 ? { ...value, disabled: true } : value
    );
  }
  if (controlSelection) {
    result = setNodeNotSelectable(result, 0);
  }
  return result;
};
