import { IsFormDataChanged } from "../formDataEquality";

const setInitialData = () =>
  JSON.parse('{"description":"pair testing channel description","name":"pair testing channel name","folders":[{"id":690,"name":"New Folder","subFolders":[{"name":"subFolder1","id":692},{"name":"subFolder2","id":693}]},{"id":691,"name":"New Folder 1","subFolders":[{"name":"subFolderI","id":694},{"name":"subFolderII","id":695}]}]}');

const setSubfolders = () => JSON.parse('[{"name":"subFolder1","id":692},{"name":"subFolder2","id":693}]');

describe("Form data Equality function test suite", () => {
  it("IsFormDataChanged should return false when initial and updated form data is same", () => {
    const initialData = setInitialData();
    const updatedData = setInitialData();
    expect(IsFormDataChanged(initialData, updatedData)).toBeFalsy();
  });

  it("IsFormDataChanged should return true when initial and updated form data is different from a shallow property value", () => {
    const initialData = setInitialData();
    const updatedData = setInitialData();
    updatedData.name ="test name";
    expect(IsFormDataChanged(initialData, updatedData)).toBeTruthy();
  });

  it("IsFormDataChanged should return true when initial and updated form data is different from nested property value", () => {
    const initialData = setInitialData();
    const updatedData = setInitialData();
    updatedData.folders[0].subFolders[0].name ="new subFolder";
    expect(IsFormDataChanged(initialData, updatedData)).toBeTruthy();
  });

  it("IsFormDataChanged should return false when initial and updated form data is same at the end.", () => {
    const initialData = setInitialData();
    const updatedData = setInitialData();
    updatedData.folders[0].subFolders[0].name ="new subFolder";
    updatedData.folders[0].subFolders[1].name ="Empty";
    updatedData.folders[0].subFolders = setSubfolders();
    expect(IsFormDataChanged(initialData, updatedData)).toBeFalsy();
  });
  
});
