/**
 * checks if a file is more than 1GB
 * @param size
 */
import {FileNameCharacters} from "@app/utils/regex";

const MAXIMUM_FILE_SIZE = 1073741824;
const MAX_TITLE_LENGTH = 100;

const validateSize = (size: number) => {
  return size > MAXIMUM_FILE_SIZE ? false : true;
};

const validateTitle = (title: string) => {
  if (!title.length) {
    return "Required";
  }
  if (title.length > MAX_TITLE_LENGTH) {
    return "You have reached your maximum limit of characters";
  }
  return "";
};

const validateFileName = (fileName: string) =>{
  // Define the allowed characters using a regular expression
  // Test the fileName against the pattern
  return FileNameCharacters.test(fileName);
}
export { validateSize, validateTitle , validateFileName};
