import React, { useEffect, useState } from 'react';
import { Button, Progress, Typography, Upload } from 'antd';
import styles from './index.module.less';
import Modal from '@app/components/Modal';
import { errorNotification, successNotification } from '@app/utils/antNotifications';
import logger from '@app/utils/logger';
import { checkInFiles, getCheckoutFilesBySiteIdAndFileId, removeFiles } from '@app/api/fileAreaService';
import { FORBIDDEN_ERROR_CODE } from '@app/utils';
import HTTPResponse from '@app/utils/http/interfaces/HttpResponse';
import { UploadOutlined } from '@ant-design/icons/lib';
import useSimpleUploader from '@app/components/Uploader/hooks/UseSimpleUploader';
import { UploadRequestOption } from 'rc-upload/lib/interface';
import { RcFile } from 'antd/lib/upload';

const { Text } = Typography;

export interface IfileAreaButtonPanel {
  siteId?: string;
  binderId?: string;
  fileId?: string;
  onClose?: () => void;
  onSuccess?: () => void;
}
export interface ICheckoutFile {
  id: string;
  fileName: string;
  title: string | null;
}

export const CheckinModel: React.FC<IfileAreaButtonPanel> = ({ siteId, binderId, fileId, onClose, onSuccess }) => {
  const { files, action } = useSimpleUploader();
  const { initializeInternalUpload, addInternalFile, removeAll } = action;
  const [uploadedFileRef, setUploadedFileRef] = useState('');
  const [showCheckinModal, setShowCheckinModal] = useState(false);
  const [checkoutFiles, setCheckoutFiles] = useState<ICheckoutFile[]>();
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState('');

  useEffect(() => {
    setProgress(0);
    setError('');
    setShowCheckinModal(!!fileId);
    if (binderId && fileId) {
      getCheckoutFilesBySiteIdAndFileId(binderId, fileId).then((response: HTTPResponse<any>) => {
        setCheckoutFiles(response.data);
      });
    }
  }, [siteId, binderId, fileId]);

  useEffect(() => {
    let ref = files[Object.keys(files)[0]]?.referenceNumber;
    if (ref) {
      removeAll();
      setUploadedFileRef(ref);
    }
  }, [files]);

  useEffect(() => {
    if (!showCheckinModal) {
      onClose && onClose();
      if (siteId && uploadedFileRef) {
        removeFiles(siteId, [uploadedFileRef]);
        setUploadedFileRef('');
      }
    }
  }, [showCheckinModal]);

  const handleSuccessfulCheckins = () => {
    setUploadedFileRef('');
    successNotification([''], 'File checked in successfully');
    setShowCheckinModal(false);
    onSuccess?.();
  };

  const onCheckInClick = () => {
    const uploadedFiles = [{ id: fileId, referenceNumber: uploadedFileRef }];
    checkInFiles(binderId, uploadedFiles)
      .then((_response) => {
        handleSuccessfulCheckins();
      })
      .catch((error) => {
        setShowCheckinModal(false);
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], 'You do not have the permission to perform this action. Please refresh and try again');
        } else {
          errorNotification([''], 'Check-In Failed');
        }
        logger.error('File Are Section', 'File Area Button Panel', error);
      });
  };

  const uploadFile = async (options: UploadRequestOption<any>) => {
    let { file } = options;
    if ((file as RcFile).name != checkoutFiles?.[0].fileName) {
      setError('File Name or Type is invalid.');
      return;
    }
    setError('');
    if (fileId && siteId) {
      initializeInternalUpload(siteId);
      addInternalFile(options, 0, undefined, (percent) => {
        setProgress(Math.round(percent || 0));
      });
    }
  };

  return (
    <>
      <Modal
        destroyOnClose={true}
        size="medium"
        key="100"
        visible={showCheckinModal}
        title={'Check-In File'}
        onCancel={onClose}
        footer={[
          <Button key="back" type="default" onClick={onClose}>
            cancel
          </Button>,

          <Button key="checkin" type="primary" disabled={!uploadedFileRef} onClick={onCheckInClick}>
            check-in
          </Button>,
        ]}
      >
        <div className={styles.yjModalContentWrapper}>
          <div className={styles.yjCheckInFileNameArea}>
            <label className={styles.yjCheckInFileTitle}>File Name:</label>
            <span className={styles.yjCheckInFileName}>{checkoutFiles && checkoutFiles[0].fileName}</span>
            <Upload showUploadList={false} customRequest={(options) => uploadFile(options)} className={styles.yjCheckInButtonGroupWrapper}>
              <Button type="primary" disabled={!!uploadedFileRef}>
                <UploadOutlined /> Browse
              </Button>
            </Upload>
          </div>
          {error && <div style={{ color: 'red' }}>{error}</div>}
          {progress > 0 ? <Progress percent={progress} /> : null}
        </div>
      </Modal>
    </>
  );
};
