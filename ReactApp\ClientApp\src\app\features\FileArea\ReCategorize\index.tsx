import React, { useEffect, useState } from "react";
import { useDispatch, useSelector } from 'react-redux';
import { Row, Col, Skeleton, Form, Drawer, Button, Modal as AntModal } from "antd";
import { useHistory, useParams } from "react-router-dom";
import {
  DoubleRightOutlined, ExclamationCircleOutlined
} from "@ant-design/icons/lib/icons";

import styles from "./index.module.less";
import SelectedFilesGrid, {
  ColumnConfig,
} from "@app/features/FileArea/SelectedFilesGrid";
import { IFile } from "@app/types/fileAreaTypes";
import { getFileDetailsByFileId } from "@app/api/fileAreaService";
import config from "@app/utils/config";
import InfinitySelect, { InfinitySelectGetOptions } from "@app/components/InfinitySelect";
import { FORBIDDEN_ERROR_CODE, getParameterizedUrl } from "@app/utils";
import { OperationalServiceTypes } from "@iris/discovery.fe.client";
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from "@app/utils/logger";
import { FolderTree } from "@app/components/FolderTree";
import { FileDetailsOptions } from "@app/types/FileDetailsOptions";
import { IFolder } from "@app/types/FileAreaFolderTreeTypes";
import { FormInstance } from "antd/lib/form";
import { FileRecord, FileEvents, UrlEvents } from "@app/components/forms/UploaderSubmit/types";
import { Store } from "antd/lib/form/interface";
import { updateContextMenuReCategorizeOption } from "@app/redux/actions/fileAreaActions";
import { UserPermission } from "@app/types/UserPermission";
import { recategorizeFiles } from '@app/api/fileAreaService';
import { successNotification, errorNotification } from '@app/utils/antNotifications';
const { confirm } = AntModal;
const LIMIT = 10;
const DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';
const FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';

type PropTypes = {
  uploadType: number;
  disabledForm?: boolean;
  permissions: UserPermission;
  fileList?: FileRecord[];
  onFinish: (values: Store, fileList: FileRecord[], folderId: number) => void;
  fileEvents?: FileEvents;
  urlEvents?: UrlEvents;
};

export interface ReCategorizeProps {
  selectedFiles: IFile[];
  //onNewFolderSelect: (folderId: number, fileList: IFile[]) => void;
  onClosePopup: (event: boolean) => void;
  options: FileDetailsOptions;
  form: FormInstance;
  forManageFiles?: boolean;
  onFormChange?: (event: any) => void;
  onFolderTreeChange: (event: any) => void;
  onSuccess: () => void;//sync grid
  showReCategorizeModal: boolean;
  binderId: string;
}




export default (props: ReCategorizeProps) => {
  const dispatch = useDispatch();
  const [recategorizeDetails, setRecategorizeDetails] = useState<{
    fileList: IFile[];
    folderId: number;

  }>();

  const defaultFolder = props.forManageFiles
    ? props.form.getFieldValue("folder")
      ? props.form.getFieldValue("folder").toString()
      : "1"
    : "";
  let defaultRetention = -1;
  if (defaultFolder) {
    const getRetention = (node: IFolder[]) => {
      node.forEach((folder) => {
        if (folder.id === parseInt(defaultFolder)) {
          defaultRetention = folder.retention;
          return;
        }
        if (folder.subFolders?.length > 0) {
          getRetention(folder.subFolders);
        }
      });
    };
    getRetention(props.options.folderTree.folders!);
  }
  const [selectedFolder, setSelectedFolder] = useState(defaultFolder);
  const [selectedFileList, setSelectedFileList] = useState<IFile[]>([]);
  const { channelId, siteId } = useParams<any>();
  const [siteIdValue, setSiteId] = useState("");
  const reCategorizeColumnConfigs: ColumnConfig[] = [
    { title: "", dataIndex: "remove", key: "remove", width: 40 },
    { title: "Title", dataIndex: "title", key: "title", ellipsis: true },
    {
      title: "Current Folder",
      dataIndex: "folder",
      key: "folder",
    },
  ];
  const history = useHistory();

  const mapFolderPath = async () => {
    const fileList: IFile[] = await Promise.all(
      props.selectedFiles.map(async (file) => ({
        ...file,
        folder: await getFolderPath(file.id),
      }))
    );
    setSelectedFileList(fileList);
    props.onFolderTreeChange(-1);

  };

  useEffect(() => {
    setSelectedFileList([]);
    if (props.showReCategorizeModal) {
      mapFolderPath();
    }
  }, [props.selectedFiles, props.showReCategorizeModal]);

  const getFolderPath = async (fileId: string): Promise<string> => {
    let fileDetails = {} as any;
    let returnValue = "";
    await getFileDetailsByFileId(fileId)
      .then(({ data }) => {
        fileDetails = data;
        returnValue = fileDetails.folder.parent
          ? `${fileDetails.folder.parent.name} > ${fileDetails.folder.name}`
          : fileDetails.folder.name;
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        }
      });
    return returnValue;
  };

  const handleReCategorizeUpdateDetails = (folderId: number, fileList: IFile[]) => {
    setRecategorizeDetails({ folderId: folderId, fileList: fileList });
  };

  const handleFilesChange = (fileList: any[]) => {

    if (fileList.length > 0) {
      setSelectedFileList(fileList);
      handleReCategorizeUpdateDetails(selectedFolder, fileList);
    } else {
      props.onClosePopup(false);
    }
  };


  interface IFolderTreeResponse {
    //siteId: string;
    siteName: string;
    siteStatusId: number;
    folders: IFolder[] | undefined;
  }


  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    /**
     * Will add the keyvalue if dropdown still visible
     */
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const options = {
      limit: 10,
      offset: page - 1,
      ...transformFilters
    }
    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannelList, options)
      .then((res: any) => {
        logger.info('SideSelection', 'getPaginatedRecords', res.data);
        if (res.data) {
          return res.data;
        } else {
          logger.error('SideSelection', 'getPaginatedRecords', res.error);
          return []
        }
      })
      .catch((error: any) => {
        logger.error('SideSelection', 'getPaginatedRecords', error);

        return [];
      });
  };

  const handleShowReCategorizeModalCancel = (hasSelectedFiles = true) => {
    if (hasSelectedFiles && recategorizeDetails?.folderId !== undefined) {
      onCancelRecategorizeModal();
    } else {
      resetReCategorizeModal();
    }
  };

  const onCancelRecategorizeModal = () => {
    confirm({
      title: DISCARD_MESSAGE,
      icon: <ExclamationCircleOutlined />,
      okText: 'Yes',
      cancelText: 'No',
      onOk() {
        resetReCategorizeModal();
      },
    });
  };

  const resetReCategorizeModal = () => {
    props.form.resetFields();
    setRecategorizeDetails({
      folderId: undefined as any,
      fileList: undefined as any,
    });
    //dispatch(updateContextMenuReCategorizeOption(false));
    props.onClosePopup(true);
  };

  const handleRecategorizeUpdate = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {
    recategorizeFiles(
      recategorizeDetails?.fileList.map((file) => file.id),
      recategorizeDetails?.folderId ? recategorizeDetails.folderId : 0,
      props.binderId
    )
      .then((response) => {
        successNotification([''], 'File Re-Categorization Successful');
        props.onSuccess();
        resetReCategorizeModal();
      })
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);
        } else {
          errorNotification([''], 'File Re-Categorization Failed');
        }
        logger.error('File ARea Module', 'File Re-Categorization', error);
      });
  };

  return (
    <Drawer
      visible={props.showReCategorizeModal}
      title={'Re-Categorize File(s)'}
      className={'yjDrawerPanel'}
      width={1200}
      onClose={() => handleShowReCategorizeModalCancel()}
      placement="right"
      footer={[
        <div className={styles.yjReCategorizeFilesInfoFooter}>
          <span className={styles.yjReCategorizeFilesInfo}>

          </span>

          <div className={styles.yjReCategorizeFilesInfoButtons}>
            <Button key="cancel" type="default" onClick={() => handleShowReCategorizeModalCancel()}>
              cancel
            </Button>
            <Button key="recategorize" type="primary" onClick={handleRecategorizeUpdate} disabled={recategorizeDetails?.folderId === undefined}>
              Re-categorize
            </Button>
          </div>
        </div>
      ]}
    >
      {/* {JSON.stringify(props.options)} */}
      <Row gutter={24}>
        <Col span={17}>
          {selectedFileList.length > 0 ? (
            <SelectedFilesGrid
              onFilesChange={handleFilesChange}
              columnConfigs={reCategorizeColumnConfigs}
              dataList={selectedFileList}
            />
          ) : (
            <Skeleton />
          )}
        </Col>

        <Col span={1} className={styles.yjMoverArrow}>
          <DoubleRightOutlined />
        </Col>
        <Col span={6} className={styles.yjFolderTreeUrlUploader}>
          <Form.Item label="Re-Categorize" name="folder" >

            <FolderTree
              showTitle={false}
              maxHeight="255px"
              data={props.options.folderTree}
              onSelectFolder={(keys, info) => {
                props.onFolderTreeChange(keys);
                setSelectedFolder(keys);
                handleReCategorizeUpdateDetails(keys, selectedFileList)
              }}
              disableRoot={true}
              controlSelection={true}
              autoExpandParent={true}
              selectedKeys={[selectedFolder.toString()]}
              disabled={false}
              onClick={() => {
                //if (disabledForm) {
                //return
                //}
                //onFormChange(true);
              }}
            />

          </Form.Item>

        </Col>
      </Row>
    </Drawer>
  );
};
