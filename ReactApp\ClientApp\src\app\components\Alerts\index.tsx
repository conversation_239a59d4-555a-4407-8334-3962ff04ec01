import React from "react";
import { Al<PERSON>, message, Button, Space } from 'antd';
import styles from './index.module.less';
const info = () => {
  message.info('This is a normal message');
};
const success = () => {
  message.success('This is a success message');
};
const error = () => {
  message.error('This is an error message');
};
const warning = () => {
  message.warning('This is a warning message');
};

export default () => {
  return<>
  <div className={styles.yjAlerts}>
  <Alert message="Success Message" type="success" showIcon closable />
    <Alert message="Informational Notes" type="info" showIcon closable />
    <Alert message="Warning Message" type="warning" showIcon closable />
    <Alert message="Error Message" type="error" showIcon closable />
  </div>
  <div className={styles.yjMessages}>
  <Space>
    <Button type="default" onClick={info}>Info Message</Button>
    <Button type="default"  onClick={success}>Success Message</Button>
    <Button type="default" onClick={error}>Error Message</Button>
    <Button type="default"  onClick={warning}>Warning Message</Button>
  </Space>
  </div>
  </>
};
