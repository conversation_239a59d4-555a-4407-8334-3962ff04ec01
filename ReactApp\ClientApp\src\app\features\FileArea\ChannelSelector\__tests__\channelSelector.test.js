import React from "react";
import { shallow } from "enzyme";
import Page from "../index";
import renderer from "react-test-renderer";

const channelSelector = [{ id: "1", name: "channel1" }];
const channelId = "ch-001";

describe("Channel Selector Component", () => {
  it("Channel Selector Component should render", () => {
    const component = shallow(<Page />);
    expect(component.html()).not.toBe(null);
  });
  it("Channel Selector Component should render without props , and create the snapshot properly", () => {
    const component = renderer.create(<Page />).toJSON();
    expect(component).toMatchSnapshot();
  });

  it("Channel Selector Component should render when prop data is null", () => {
    const component = shallow(<Page data={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("Channel Selector Component should render when prop data is not null", () => {
    const component = shallow(<Page data={channelSelector} />);
    expect(component.html()).not.toBe(null);
  });

  it("Channel Selector Component should render when prop defaultChannel is null", () => {
    const component = shallow(<Page defaultChannel={null} />);
    expect(component.html()).not.toBe(null);
  });

  it("Channel Selector Component should render when prop defaultChannel is not null", () => {
    const component = shallow(<Page defaultChannel={channelId} />);
    expect(component.html()).not.toBe(null);
  });
});
