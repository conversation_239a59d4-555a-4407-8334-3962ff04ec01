import React from "react";
import { mount, shallow } from "enzyme";
import renderer from "react-test-renderer";
import { Button, Form, Radio, FormInstance } from "antd";
import thunk from "redux-thunk";
import { Provider } from "react-redux";
import configureMockStore from "redux-mock-store";

import initTestSuite from "@app/utils/config/TestSuite";
import Page, { getFileId } from "../index";

const ReduxProvider = ({ children, store }) => (
  <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);

const formLabels = ["ID", "File", "Downloaded File Name"];

const fileList = [
  {
    checked: false,
    downloadTitle: "FB_IMG_1588091867174.jpg",
    fileId: "AA-00020",
    fileName: null,
    id: "AA-00020",
    title: "FB_IMG_1588091867174.jpg-AA-00020.jpg",
  },
  {
    checked: false,
    downloadTitle: "FB_IMG_1588091867174.jpg",
    fileId: "AA-00020",
    fileName: null,
    id: "AA-00020",
    title: "FB_IMG_1588091867174.jpg-AA-00020.jpg",
  },
];

const createCheckinComponent = (props) => {
  const INITIAL_STATE = {
    fileArea: {
      fileAreaSettings: {},
      loadGrid: true,
      portalFilesSelectedRequest: {},
    },
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <Page
        siteId={"S-001"}
        fileList={fileList}
        key="1"
        allowCheckinFunction={() => {}}
        onCheckin={() => {}}
        form={FormInstance}
      />
    </ReduxProvider>
  );
};

const createCheckinComponentWIthoutProps = (props) => {
  const INITIAL_STATE = {
    fileArea: {
      fileAreaSettings: {},
      loadGrid: true,
      portalFilesSelectedRequest: {},
    },
  };
  const store = mockStore(INITIAL_STATE);

  return (
    <ReduxProvider store={store}>
      <Page />
    </ReduxProvider>
  );
};

describe("Checkin Test Suite", () => {
  beforeAll(() => {
    initTestSuite();
  });

  it("Checkin Component Should Render", () => {
    const component = shallow(createCheckinComponent());
    expect(component.html()).not.toBe(null);
  });
  it("Checkin component should render and create the snapshot properly when any props are not provided", () => {
    const component = renderer
      .create(createCheckinComponentWIthoutProps())
      .toJSON();
    expect(component).toMatchSnapshot();
  });
  it("Checkin component should render and create the snapshot properly when any props are  provided", () => {
    const component = renderer.create(createCheckinComponent()).toJSON();
    expect(component).toMatchSnapshot();
  });
  it("getFileId  function should be exsist", () => {
    expect(typeof getFileId).toBe("function");
  });

  it("getFileId  function should not return a value when title parameter is null", () => {
    expect(getFileId(null)).toBe(null);
  });

  it("getFileId  function should  return a value when title parameter is not null ", () => {
    expect(getFileId("sssss.ff-ss.cc")).not.toBe(null);
  });

  it("getFileId  function should  return ff-ss when title parameter is sssss.ff-ss.cc ", () => {
    expect(getFileId("sssss.ff-ss.cc")).toBe("ff-ss");
  });

  it(" Checkin component should have a Form", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form)).toHaveLength(1);
  });

  it(" Checkin component should have 6 Form Items when file list prop is not provided", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form.Item)).toHaveLength(6);
  });

  it(" Checkin component should have a Form List  when file list is  provided", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form.List)).toHaveLength(1);
  });

  it("should have label: ID", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form.Item).at(1).props().label).toEqual(
      formLabels[0]
    );
  });

  it("should have label: File", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form.Item).at(2).props().label).toEqual(
      formLabels[1]
    );
  });

  it("should have label: Downloaded Title", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form.Item).at(3).props().label).toEqual(
      formLabels[2]
    );
  });

  it("should have 2 Radio Buttons", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Radio).length).toEqual(2);
  });
  it("should have  Radio Button for Individual Upload", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Radio).at(0).props().children.props.children).toEqual(
      "INDIVIDUAL CHECK-IN"
    );
  });

  it("should have  Radio Button for Bulk Upload", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Radio).at(1).props().children.props.children).toEqual(
      "BULK UPLOAD"
    );
  });

  it("should have 2 Buttons when there are any props arent passed ", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Button).length).toEqual(2);
  });

  it("should have a Browse button when there are any props arent passed ", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Button).at(0).props().children[1].trim()).toEqual(
      "Browse"
    );
  });
  it("should have a Delete button when there are any props arent passed ", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Button).at(1).props().children[1].trim()).toEqual(
      "Delete"
    );
  });

  it("should have a Form List when there are props are passed ", () => {
    const component = mount(createCheckinComponent());
    expect(component.find(Form.List).length).toEqual(1);
  });
});
