@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjSecondaryGridFilearea {
  margin-bottom: 25px;
  max-height: 55vh;
  overflow: hidden auto;

  table {

    th {
      background: @color-bg-secondary-header;
      color: @color-font-secondary-header;
      font-size: @font-size-base / 1.145;
      height: 22px;
      text-transform: @yj-transform;

      .font-mixin(@font-primary, @yjff-semibold);
    }
  }

  button {
    background: @color-bg-remove-record-icon;
    border: none;
    box-shadow: none;
    height: 20px;
    margin-right: 10px;
    width: 20px;

    &:hover {
      background: @color-bg-remove-record-icon;
      border: none;
      box-shadow: none;
    }

    &:focus {
      background: @color-bg-remove-record-icon;
      border: none;
      box-shadow: none;
    }

    span {
      color: @color-font-remove-record-icon;
      font-size: 14px;
    }
  }
}
