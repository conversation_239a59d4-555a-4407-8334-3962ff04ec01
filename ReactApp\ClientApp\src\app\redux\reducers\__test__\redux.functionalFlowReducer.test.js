import { functionalFlowReducer } from "../functionalFlowReducer";

import * as actions from "../../actionTypes/functionalFlowActionTypes";

describe("Functional flow reducer test suite", () => {
  it("should handle DATA_LOADING action", () => {
    const action = {
      type: actions.DATA_LOADING,
      payload: true,
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: true,
      errors: [],
      isEdited: false,
      savedSuccessfully: false,
    });
  });
  it("should handle DATA_LOAD_SUCCESS action", () => {
    const action = {
      type: actions.DATA_LOAD_SUCCESS,
      payload: true,
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      errors: [],
      isEdited: false,
      savedSuccessfully: false,
    });
  });
  it("should handle VERICAL_LOAD_SUCCESS action", () => {
    const action = {
      type: actions.VERICAL_LOAD_SUCCESS,
      payload: true,
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      errors: [],
      isEdited: false,
      savedSuccessfully: false,
    });
  });
  it("should handle EDIT_FUNCTIONAL_FLOW action", () => {
    const action = {
      type: actions.EDIT_FUNCTIONAL_FLOW,
      payload: true,
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      isEdited: true,
      savedSuccessfully: false,
    });
  });

  it("should handle DATA_LOAD_ERROR action", () => {
    const action = {
      type: actions.DATA_LOAD_ERROR,
      payload: ["error"],
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      savedSuccessfully: false,
      errors: ["error"],
      hasErrors: true,
    });
  });

  it("should handle SAVE_SUCCESS_FUNCTIONAL_FLOW action", () => {
    const action = {
      type: actions.SAVE_SUCCESS_FUNCTIONAL_FLOW,
      payload: ["error"],
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      savedSuccessfully: true,
      errors: [],
      hasErrors: false,
    });
  });

  it("should handle SAVE_FAIL_FUNCTIONAL_FLOW action", () => {
    const action = {
      type: actions.SAVE_FAIL_FUNCTIONAL_FLOW,
      payload: ["error"],
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      savedSuccessfully: false,
      errors: ["error"],
      hasErrors: true,
    });
  });

  it("should handle REDIRECT_FUNCTIONAL_FLOW action", () => {
    const action = {
      type: actions.REDIRECT_FUNCTIONAL_FLOW,
      payload: true,
    };
    expect(functionalFlowReducer({}, action)).toEqual({
      isLoading: false,
      errors: [],
      hasErrors: false,
      savedSuccessfully: false,
    });
  });
});
