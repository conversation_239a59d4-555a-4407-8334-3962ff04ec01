// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Re-Categorize Test Suite should create and match to snapshot 1`] = `
<div
  className="ant-row"
  style={
    Object {
      "marginLeft": -12,
      "marginRight": -12,
    }
  }
>
  <div
    className="ant-col ant-col-15"
    style={
      Object {
        "paddingLeft": 12,
        "paddingRight": 12,
      }
    }
  >
    <div
      className="ant-skeleton"
    >
      <div
        className="ant-skeleton-content"
      >
        <h3
          className="ant-skeleton-title"
          style={
            Object {
              "width": "38%",
            }
          }
        />
        <ul
          className="ant-skeleton-paragraph"
        >
          <li
            style={
              Object {
                "width": undefined,
              }
            }
          />
          <li
            style={
              Object {
                "width": undefined,
              }
            }
          />
          <li
            style={
              Object {
                "width": "61%",
              }
            }
          />
        </ul>
      </div>
    </div>
  </div>
  <div
    className="ant-col ant-col-1 yjMoverArrow"
    style={
      Object {
        "paddingLeft": 12,
        "paddingRight": 12,
      }
    }
  >
    <span
      aria-label="double-right"
      className="anticon anticon-double-right"
      role="img"
    >
      <svg
        aria-hidden="true"
        data-icon="double-right"
        fill="currentColor"
        focusable="false"
        height="1em"
        viewBox="64 64 896 896"
        width="1em"
      >
        <path
          d="M533.2 492.3L277.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H188c-6.7 0-10.4 7.7-6.3 12.9L447.1 512 181.7 851.1A7.98 7.98 0 00188 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5zm304 0L581.9 166.1c-3-3.9-7.7-6.1-12.6-6.1H492c-6.7 0-10.4 7.7-6.3 12.9L751.1 512 485.7 851.1A7.98 7.98 0 00492 864h77.3c4.9 0 9.6-2.3 12.6-6.1l255.3-326.1c9.1-11.7 9.1-27.9 0-39.5z"
        />
      </svg>
    </span>
  </div>
  <div
    className="ant-col ant-col-8"
    style={
      Object {
        "paddingLeft": 12,
        "paddingRight": 12,
      }
    }
  >
    <div
      className="ant-row ant-form-item"
      style={Object {}}
    >
      <div
        className="ant-col ant-form-item-label"
        style={Object {}}
      >
        <label
          className="ant-form-item-no-colon"
          title="Move to Site"
        >
          Move to Site
        </label>
      </div>
      <div
        className="ant-col ant-form-item-control"
        style={Object {}}
      >
        <div
          className="ant-form-item-control-input"
        >
          <div
            className="ant-form-item-control-input-content"
          >
            <span
              aria-label="info-circle"
              className="anticon anticon-info-circle"
              onMouseEnter={[Function]}
              onMouseLeave={[Function]}
              role="img"
            >
              <svg
                aria-hidden="true"
                data-icon="info-circle"
                fill="currentColor"
                focusable="false"
                height="1em"
                viewBox="64 64 896 896"
                width="1em"
              >
                <path
                  d="M512 64C264.6 64 64 264.6 64 512s200.6 448 448 448 448-200.6 448-448S759.4 64 512 64zm0 820c-205.4 0-372-166.6-372-372s166.6-372 372-372 372 166.6 372 372-166.6 372-372 372z"
                />
                <path
                  d="M464 336a48 48 0 1096 0 48 48 0 10-96 0zm72 112h-48c-4.4 0-8 3.6-8 8v272c0 4.4 3.6 8 8 8h48c4.4 0 8-3.6 8-8V456c0-4.4-3.6-8-8-8z"
                />
              </svg>
            </span>
          </div>
        </div>
      </div>
    </div>
    <div
      className="ant-select ant-select-single ant-select-show-arrow ant-select-show-search"
      onBlur={[Function]}
      onFocus={[Function]}
      onKeyDown={[Function]}
      onKeyUp={[Function]}
      onMouseDown={[Function]}
    >
      <div
        className="ant-select-selector"
        onClick={[Function]}
        onMouseDown={[Function]}
      >
        <span
          className="ant-select-selection-search"
        >
          <input
            aria-activedescendant="undefined_list_0"
            aria-autocomplete="list"
            aria-controls="undefined_list"
            aria-haspopup="listbox"
            aria-owns="undefined_list"
            autoComplete="off"
            className="ant-select-selection-search-input"
            onChange={[Function]}
            onCompositionEnd={[Function]}
            onCompositionStart={[Function]}
            onKeyDown={[Function]}
            onMouseDown={[Function]}
            onPaste={[Function]}
            readOnly={false}
            role="combobox"
            style={
              Object {
                "opacity": null,
              }
            }
            type="search"
            unselectable={null}
            value=""
          />
        </span>
        <span
          className="ant-select-selection-placeholder"
        />
      </div>
      <span
        aria-hidden={true}
        className="ant-select-arrow"
        onMouseDown={[Function]}
        style={
          Object {
            "WebkitUserSelect": "none",
            "userSelect": "none",
          }
        }
        unselectable="on"
      >
        <span
          aria-label="down"
          className="anticon anticon-down ant-select-suffix"
          role="img"
        >
          <svg
            aria-hidden="true"
            data-icon="down"
            fill="currentColor"
            focusable="false"
            height="1em"
            viewBox="64 64 896 896"
            width="1em"
          >
            <path
              d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
            />
          </svg>
        </span>
      </span>
    </div>
  </div>
</div>
`;
