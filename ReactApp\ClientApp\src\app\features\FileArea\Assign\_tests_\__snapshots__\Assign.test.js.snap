// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Assign Option Test Suite should create and match to snapshot 1`] = `
<div
  className="yjModalContentWrapper"
>
  <div
    className="ant-skeleton ant-skeleton-active"
  >
    <div
      className="ant-skeleton-content"
    >
      <h3
        className="ant-skeleton-title"
        style={
          Object {
            "width": "38%",
          }
        }
      />
      <ul
        className="ant-skeleton-paragraph"
      >
        <li
          style={
            Object {
              "width": undefined,
            }
          }
        />
        <li
          style={
            Object {
              "width": undefined,
            }
          }
        />
        <li
          style={
            Object {
              "width": "61%",
            }
          }
        />
      </ul>
    </div>
  </div>
  <div
    className="yjAssignFilesFormWrapper"
  >
    <form
      className="ant-form ant-form-horizontal ant-form-middle yjAssignFilesForm"
      id="basic"
      onReset={[Function]}
      onSubmit={[Function]}
    >
      <div
        className="ant-row ant-form-item yjAssignFilesFormRowItem"
        style={Object {}}
      >
        <div
          className="ant-col ant-col-4 ant-form-item-label"
          style={Object {}}
        >
          <label
            className="ant-form-item-required ant-form-item-no-colon"
            htmlFor="basic_assigneeId"
            title="New Assignee"
          >
            New Assignee
          </label>
        </div>
        <div
          className="ant-col ant-col-20 ant-form-item-control"
          style={Object {}}
        >
          <div
            className="ant-form-item-control-input"
          >
            <div
              className="ant-form-item-control-input-content"
            >
              <div
                className="ant-select ant-select-single ant-select-show-arrow ant-select-show-search"
                onBlur={[Function]}
                onFocus={[Function]}
                onKeyDown={[Function]}
                onKeyUp={[Function]}
                onMouseDown={[Function]}
              >
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-search"
                  >
                    <input
                      aria-activedescendant="basic_assigneeId_list_0"
                      aria-autocomplete="list"
                      aria-controls="basic_assigneeId_list"
                      aria-haspopup="listbox"
                      aria-owns="basic_assigneeId_list"
                      autoComplete="off"
                      className="ant-select-selection-search-input"
                      id="basic_assigneeId"
                      onChange={[Function]}
                      onCompositionEnd={[Function]}
                      onCompositionStart={[Function]}
                      onKeyDown={[Function]}
                      onMouseDown={[Function]}
                      onPaste={[Function]}
                      readOnly={false}
                      role="combobox"
                      style={
                        Object {
                          "opacity": null,
                        }
                      }
                      type="search"
                      unselectable={null}
                      value=""
                    />
                  </span>
                  <span
                    className="ant-select-selection-placeholder"
                  >
                    Select an Assignee
                  </span>
                </div>
                <span
                  aria-hidden={true}
                  className="ant-select-arrow"
                  onMouseDown={[Function]}
                  style={
                    Object {
                      "WebkitUserSelect": "none",
                      "userSelect": "none",
                    }
                  }
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    className="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-row ant-form-item yjAssignFilesFormRowItem"
        style={Object {}}
      >
        <div
          className="ant-col ant-col-4 ant-form-item-label"
          style={Object {}}
        >
          <label
            className="ant-form-item-no-colon"
            htmlFor="basic_assignNotes"
            title="Assign Notes"
          >
            Assign Notes
          </label>
        </div>
        <div
          className="ant-col ant-col-20 ant-form-item-control"
          style={Object {}}
        >
          <div
            className="ant-form-item-control-input"
          >
            <div
              className="ant-form-item-control-input-content"
            >
              <textarea
                className="ant-input"
                id="basic_assignNotes"
                onChange={[Function]}
                onCompositionEnd={[Function]}
                onCompositionStart={[Function]}
                onKeyDown={[Function]}
                style={Object {}}
                value=""
              />
            </div>
          </div>
        </div>
      </div>
      <div
        className="ant-row ant-form-item yjAssignFilesFormRowItem"
        style={Object {}}
      >
        <div
          className="ant-col ant-col-4 ant-form-item-label"
          style={Object {}}
        >
          <label
            className="ant-form-item-no-colon"
            htmlFor="basic_statusId"
            title="New Status"
          >
            New Status
          </label>
        </div>
        <div
          className="ant-col ant-col-20 ant-form-item-control"
          style={Object {}}
        >
          <div
            className="ant-form-item-control-input"
          >
            <div
              className="ant-form-item-control-input-content"
            >
              <div
                className="ant-select ant-select-single ant-select-show-arrow ant-select-show-search"
                onBlur={[Function]}
                onFocus={[Function]}
                onKeyDown={[Function]}
                onKeyUp={[Function]}
                onMouseDown={[Function]}
              >
                <div
                  className="ant-select-selector"
                  onClick={[Function]}
                  onMouseDown={[Function]}
                >
                  <span
                    className="ant-select-selection-search"
                  >
                    <input
                      aria-activedescendant="basic_statusId_list_0"
                      aria-autocomplete="list"
                      aria-controls="basic_statusId_list"
                      aria-haspopup="listbox"
                      aria-owns="basic_statusId_list"
                      autoComplete="off"
                      className="ant-select-selection-search-input"
                      id="basic_statusId"
                      onChange={[Function]}
                      onCompositionEnd={[Function]}
                      onCompositionStart={[Function]}
                      onKeyDown={[Function]}
                      onMouseDown={[Function]}
                      onPaste={[Function]}
                      readOnly={false}
                      role="combobox"
                      style={
                        Object {
                          "opacity": null,
                        }
                      }
                      type="search"
                      unselectable={null}
                      value=""
                    />
                  </span>
                  <span
                    className="ant-select-selection-placeholder"
                  >
                    Select a status
                  </span>
                </div>
                <span
                  aria-hidden={true}
                  className="ant-select-arrow"
                  onMouseDown={[Function]}
                  style={
                    Object {
                      "WebkitUserSelect": "none",
                      "userSelect": "none",
                    }
                  }
                  unselectable="on"
                >
                  <span
                    aria-label="down"
                    className="anticon anticon-down ant-select-suffix"
                    role="img"
                  >
                    <svg
                      aria-hidden="true"
                      data-icon="down"
                      fill="currentColor"
                      focusable="false"
                      height="1em"
                      viewBox="64 64 896 896"
                      width="1em"
                    >
                      <path
                        d="M884 256h-75c-5.1 0-9.9 2.5-12.9 6.6L512 654.2 227.9 262.6c-3-4.1-7.8-6.6-12.9-6.6h-75c-6.5 0-10.3 7.4-6.5 12.7l352.6 486.1c12.8 17.6 39 17.6 51.7 0l352.6-486.1c3.9-5.3.1-12.7-6.4-12.7z"
                      />
                    </svg>
                  </span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </form>
  </div>
</div>
`;
