import {Form, Row, Select} from 'antd';
import React, { useEffect, useState } from 'react';
import styles  from "./index.module.less";
const { Option } = Select;

const DocumentTypeFormat_T = ({ index,value, onChange }:any) => {
	const options = [
		{
			label: "CLNT", // Type
			options: [
				{ label: "Client", value: "Client" }, // Option 1
				{ label: "Client Copy", value: "Client Copy" }, // Option 2
			],
		},
		{
			label: "ACCT", // Type
			options: [
				{ label: "Accountant", value: "Accountant" },
				{ label: "ACCT Copy", value: "ACCT Copy" },
			],
		},
		{
			label: "GOVT", // Type
			options: [
				{ label: "Government", value: "Government" },
				{ label: "GOVT Copy", value: "GOVT Copy" },
			],
		},
		{
			label: "K1", // Type
			options: [
				{ label: "K1", value: "K1" },
				{ label: "K1 Copy", value: "K1 Copy" },
			],
		},
	];
	const onSelectChange = (value:string) => {
		onChange(value);
	};

	return (<Form.Item label={'Document Type Format'} name={`${index}-DocumentType`} initialValue={value}>
		<Select placeholder="Select Type" onChange={onSelectChange} options={options} defaultValue={value} />
	</Form.Item >)
};

export default DocumentTypeFormat_T;