{"ast": null, "code": "import \"antd/es/drawer/style\";\nimport _Drawer from \"antd/es/drawer\";\nimport \"antd/es/row/style\";\nimport _Row from \"antd/es/row\";\nimport \"antd/es/alert/style\";\nimport _Alert from \"antd/es/alert\";\nimport \"antd/es/space/style\";\nimport _Space from \"antd/es/space\";\nimport \"antd/es/radio/style\";\nimport _Radio from \"antd/es/radio\";\nimport \"antd/es/col/style\";\nimport _Col from \"antd/es/col\";\nimport \"antd/es/form/style\";\nimport _Form from \"antd/es/form\";\nimport \"antd/es/skeleton/style\";\nimport _Skeleton from \"antd/es/skeleton\";\nimport \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/modal/style\";\nimport _Modal from \"antd/es/modal\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\FileAreaActionPanel\\\\MoveFiles.tsx\";\nimport React, { useEffect, useState } from 'react';\nimport { useDispatch } from 'react-redux';\nimport { ExclamationCircleOutlined } from \"@ant-design/icons/lib/icons\";\nimport styles from \"./index.module.less\";\nimport SelectedFilesGrid from \"@app/features/FileArea/SelectedFilesGrid\";\nimport { getBinderFileAreaNodes } from \"@app/api/fileAreaService\";\nimport config from \"@app/utils/config\";\nimport InfinitySelect from \"@app/components/InfinitySelect\";\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\nimport { OperationalServiceTypes } from \"@iris/discovery.fe.client\";\nimport { getInfiniteRecords } from \"@app/api/infiniteRecordsService\";\nimport logger from \"@app/utils/logger\";\nimport { FolderTree } from \"@app/components/FolderTree\";\nimport { useHistory } from \"react-router-dom\";\nimport { updateContextMenuMoveFilesOption } from '@app/redux/actions/fileAreaActions';\nimport { moveFilesService } from '@app/api/fileAreaService';\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\nconst {\n  confirm\n} = _Modal;\nconst LIMIT = 10;\nconst DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';\nconst FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';\nexport default (props => {\n  const formFolder = props.form.getFieldValue('folder');\n  const defaultFolder = true ? formFolder ? formFolder.toString() : '1' : '';\n  const history = useHistory();\n  const [selectedClient, setSelectedClient] = useState(null);\n  const [selectedFileArea, setSelectedFileArea] = useState(null);\n  const [selectedFolder, setSelectedFolder] = useState(defaultFolder);\n  const [searchByField, setSearchByField] = useState(\"clientRef\");\n  const [selectedFileList, setSelectedFileList] = useState([]); // Helper functions to get IDs from selected objects\n\n  const getSelectedClientId = () => (selectedClient === null || selectedClient === void 0 ? void 0 : selectedClient.id) || null;\n\n  const getSelectedFileAreaId = () => (selectedFileArea === null || selectedFileArea === void 0 ? void 0 : selectedFileArea.id) || null;\n\n  const moveColumnConfigs = [{\n    title: \"\",\n    dataIndex: \"remove\",\n    key: \"remove\",\n    width: 40\n  }, {\n    title: \"Title\",\n    dataIndex: \"title\",\n    key: \"title\",\n    ellipsis: true\n  }];\n  const [binderFileAreaNodes, setBinderFileAreaNodes] = useState(null);\n  const [folderTreeData, setFolderTreeData] = useState(defaultFolder);\n  const [clientSelectKey, setClientSelectKey] = useState(0);\n  const [fileAreaKey, setFileAreaKey] = useState(0);\n  const dispatch = useDispatch();\n  useEffect(() => {\n    setSelectedFileList([]);\n    props.form.resetFields();\n    setSearchByField(\"clientRef\");\n    setClientSelectKey(prevKey => prevKey + 1);\n    setFileAreaKey(prevKey => prevKey + 1);\n\n    const mapFolderPath = async () => {\n      const fileList = props.selectedFiles;\n      setSelectedFileList(fileList);\n      props.onFolderTreeChange(-1);\n    };\n\n    mapFolderPath();\n  }, [props.selectedFiles]);\n\n  const handleFilesChange = fileList => {\n    if (fileList.length > 0) {\n      setSelectedFileList(fileList);\n      handleMoveFilesUpdateDetails(selectedFolder, fileList, getSelectedClientId(), props.binderId, getSelectedFileAreaId());\n    } else {\n      handleShowMoveFilesModalCancel(false);\n      props.onClosePopup();\n    }\n  };\n\n  const transformFolders = folders => {\n    const primaryFolders = folders.filter(folder => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);\n    return primaryFolders.map(folder => ({\n      id: folder.id,\n      name: folder.name,\n      subFolders: folder.childNodes.map(child => ({\n        id: child.id,\n        name: child.name,\n        subFolders: [],\n        retention: child.retention || 0\n      }))\n    }));\n  }; //client dropdown pagination record\n\n\n  const getPaginatedRecords = async (page, method, searchValue) => {\n    const transformFilters = {};\n\n    if (searchValue) {\n      transformFilters.search = searchValue;\n    }\n\n    const getClientIdParameters = {\n      limit: LIMIT,\n      offset: page - 1,\n      field: searchByField,\n      ...transformFilters\n    };\n    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannelList, getClientIdParameters).then(res => {\n      if (res.data) {\n        // Auto-select if there's only one client and no selection yet\n        if (res.data.records && res.data.records.length === 1 && !selectedClient) {\n          const singleClient = res.data.records[0];\n          setSelectedClient(singleClient);\n          props.form.setFieldsValue({\n            clientId: singleClient.id\n          });\n          setClientSelectKey(prevKey => prevKey + 1);\n        } // If we have a selected client but it's not in the current records,\n        // merge it to ensure proper display\n\n\n        if (selectedClient && res.data.records) {\n          const existingIds = res.data.records.map(record => record.id);\n\n          if (!existingIds.includes(selectedClient.id)) {\n            res.data.records = [selectedClient, ...res.data.records];\n          }\n        }\n\n        return res.data;\n      } else {\n        // logger.error('SideSelection', 'getPaginatedRecords', res.error);\n        return [];\n      }\n    }).catch(error => {\n      // logger.error('SideSelection', 'getPaginatedRecords', error);\n      return [];\n    });\n  }; //File area pagination record\n\n\n  const getFileAreaPaginatedRecords = async (page, method, searchValue) => {\n    const transformFilters = {};\n\n    if (searchValue) {\n      transformFilters.search = searchValue;\n    }\n\n    const getClientIdParameters = {\n      limit: LIMIT,\n      offset: page - 1,\n      ...transformFilters\n    };\n\n    if (getSelectedClientId()) {\n      getClientIdParameters['siteId'] = getSelectedClientId();\n    }\n\n    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList, getClientIdParameters).then(res => {\n      if (res.data) {\n        const mappedRecords = res.data.records.map(record => ({ ...record,\n          id: record.binderId // Assign binderId as id\n\n        })); // Auto-select if there's only one file area and no selection yet\n\n        if (mappedRecords.length === 1 && !selectedFileArea) {\n          const singleFileArea = mappedRecords[0];\n          setSelectedFileArea(singleFileArea.id);\n          getFolderTreeByFileArea(singleFileArea.id);\n          props.form.setFieldsValue({\n            binderId: singleFileArea.id\n          });\n          setFileAreaKey(prevKey => prevKey + 1);\n        }\n\n        return {\n          records: mappedRecords,\n          pageCount: res.data.pageCount,\n          pageNumber: res.data.pageNumber,\n          pageSize: res.data.pageSize,\n          totalRecordCount: res.data.totalRecordCount\n        };\n      } else {\n        return {\n          records: [],\n          pageCount: 0,\n          pageNumber: 1,\n          pageSize: 0,\n          totalRecordCount: 0\n        };\n      }\n    }).catch(error => {\n      logger.error('Error fetching getFileAreaPaginatedRecords:', 'getFileAreaPaginatedRecords', error);\n      return {\n        records: [],\n        pageCount: 0,\n        pageNumber: 1,\n        pageSize: 0,\n        totalRecordCount: 0\n      };\n    });\n  }; //Getting the folder tree by the selected file Area\n\n\n  const getFolderTreeByFileArea = fileAreaId => {\n    getBinderFileAreaNodes(fileAreaId).then(response => {\n      if (response.data) {\n        const transformedFolders = transformFolders(response.data.folders);\n        setBinderFileAreaNodes({\n          siteId: props.siteId,\n          siteName: response.data.name || \"--\",\n          siteStatusId: 1,\n          folders: transformedFolders\n        });\n      } else {\n        return [];\n      }\n    }).catch(error => {\n      logger.error('File Area Module', 'Get Folder Tree By binder id', error);\n    });\n  };\n\n  const handleShowMoveFilesModalCancel = (hasSelectedFiles = true) => {\n    if (hasSelectedFiles && (moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.folderId) !== undefined) {\n      onCancelMoveFilesModal();\n    } else {\n      resetMoveFilesModal();\n    }\n  };\n\n  const onCancelMoveFilesModal = () => {\n    confirm({\n      title: DISCARD_MESSAGE,\n      icon: /*#__PURE__*/React.createElement(ExclamationCircleOutlined, {\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 250,\n          columnNumber: 13\n        }\n      }),\n      okText: 'Yes',\n      cancelText: 'No',\n\n      onOk() {\n        resetMoveFilesModal();\n      }\n\n    });\n  };\n\n  const [moveFileDetails, setMoveFilesDetails] = useState();\n\n  const resetMoveFilesModal = () => {\n    props.onSuccess();\n    props.form.resetFields();\n    setMoveFilesDetails({\n      folderId: undefined,\n      fileList: undefined,\n      sourceClientId: undefined,\n      destinationClientId: undefined,\n      sourcefileAreaId: undefined,\n      destinationfileAreaId: undefined\n    });\n    dispatch(updateContextMenuMoveFilesOption(false));\n    props.onClosePopup();\n  };\n\n  const handleMoveFilesUpdateDetails = (folderId, fileList, destinationClientId, sourcefileAreaId, destinationfileAreaId) => {\n    setMoveFilesDetails({\n      folderId: folderId,\n      fileList: fileList,\n      sourceClientId: props.siteId,\n      destinationClientId: destinationClientId,\n      sourcefileAreaId: sourcefileAreaId,\n      destinationfileAreaId: destinationfileAreaId\n    });\n  }; //Update move files\n\n\n  const handleMoveFileUpdate = event => {\n    moveFilesService(moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.fileList.map(file => file.id), (moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.folderId) ? moveFileDetails.folderId : 0, (moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.sourcefileAreaId) ? moveFileDetails.sourcefileAreaId : \"\", (moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.destinationfileAreaId) ? moveFileDetails.destinationfileAreaId : \"\").then(response => {\n      successNotification([''], 'File(s) Moved Successfully');\n      props.onSuccess();\n      resetMoveFilesModal();\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\n      } else {\n        errorNotification([''], error.message);\n      }\n\n      logger.error('File Area Module', 'Move files', error);\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(_Drawer, {\n    visible: props.showMoveFileModal,\n    title: 'Move File(s)',\n    onClose: () => handleShowMoveFilesModalCancel(),\n    className: 'yjDrawerPanel',\n    width: 700,\n    placement: \"right\",\n    footer: [/*#__PURE__*/React.createElement(\"div\", {\n      className: styles.yjMoveFilesInfoFooter,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 320,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(\"span\", {\n      className: styles.yjMoveFilesInfo,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 321,\n        columnNumber: 7\n      }\n    }), /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.yjMoveFilesInfoButtons,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 325,\n        columnNumber: 9\n      }\n    }, /*#__PURE__*/React.createElement(_Button, {\n      key: \"cancel\",\n      type: \"primary\",\n      onClick: () => handleShowMoveFilesModalCancel(),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 326,\n        columnNumber: 11\n      }\n    }, \"Cancel\"), /*#__PURE__*/React.createElement(_Button, {\n      key: \"Move\",\n      type: \"primary\",\n      onClick: handleMoveFileUpdate,\n      disabled: !(moveFileDetails === null || moveFileDetails === void 0 ? void 0 : moveFileDetails.fileList) || !clientIdSelected || !selectedFileArea || !selectedFolder,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 329,\n        columnNumber: 11\n      }\n    }, \"Move Files\")))],\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 312,\n      columnNumber: 3\n    }\n  }, /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 337,\n      columnNumber: 5\n    }\n  }, /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 338,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(_Form, {\n    form: props.form,\n    key: \"moveFilesForm\",\n    layout: \"vertical\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 339,\n      columnNumber: 7\n    }\n  }, /*#__PURE__*/React.createElement(_Row, {\n    gutter: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 340,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 341,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"\",\n    name: \"fileList\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 342,\n      columnNumber: 15\n    }\n  }, selectedFileList.length > 0 ? /*#__PURE__*/React.createElement(SelectedFilesGrid, {\n    onFilesChange: handleFilesChange,\n    columnConfigs: moveColumnConfigs,\n    dataList: selectedFileList,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 344,\n      columnNumber: 19\n    }\n  }) : /*#__PURE__*/React.createElement(_Skeleton, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 350,\n      columnNumber: 19\n    }\n  }))), /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 354,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"SEARCH BY\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 355,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(_Radio.Group, {\n    className: styles.yjSearchByRadioGroup,\n    value: searchByField,\n    onChange: e => {\n      setSearchByField(e.target.value);\n      setClientSelectKey(prevKey => prevKey + 1);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 356,\n      columnNumber: 15\n    }\n  }, /*#__PURE__*/React.createElement(_Space, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 364,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(_Radio, {\n    value: \"clientRef\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 365,\n      columnNumber: 19\n    }\n  }, \"Client Ref\"), /*#__PURE__*/React.createElement(_Radio, {\n    value: \"name\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 366,\n      columnNumber: 19\n    }\n  }, \"Client Name\"))))), /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 371,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"MOVE TO CLIENT\",\n    name: \"clientId\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 372,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(InfinitySelect, {\n    key: `client-${clientSelectKey}`,\n    getPaginatedRecords: (page, method, searchValue) => getPaginatedRecords(page, method, searchValue),\n    formatValue: value => {\n      return `${value.displayText}`;\n    },\n    notFoundContent: \"No Offices Available\",\n    notLoadContent: \"Failed to load values in office dropdown\",\n    onChange: e => {\n      props.form.resetFields(['binderId']);\n      props.form.resetFields(['folder']);\n      setBinderFileAreaNodes(null);\n      setSelectedClient(e); // Reset file area selection when client changes\n\n      setSelectedFileArea(\"\");\n    },\n    placeholder: \"Please Select Client(s)\",\n    waitCharCount: 3,\n    defaultValues: clientIdSelected || undefined,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 373,\n      columnNumber: 19\n    }\n  }))), /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 396,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"FILE AREA OF THE CLIENT\",\n    name: \"binderId\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 397,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(InfinitySelect, {\n    key: `fileArea-${clientIdSelected}-${fileAreaKey}`,\n    getPaginatedRecords: (page, method, searchValue) => getFileAreaPaginatedRecords(page, method, searchValue),\n    formatValue: value => {\n      return `${value.binderName}`;\n    },\n    notFoundContent: \"No file areas Available\",\n    notLoadContent: \"Failed to load values in file areas dropdown\",\n    onChange: e => {\n      getFolderTreeByFileArea(e);\n      setSelectedFileArea(e);\n    },\n    placeholder: \"Please Select File Areas(s)\",\n    waitCharCount: 3,\n    defaultValues: selectedFileArea || undefined,\n    disabled: !clientIdSelected,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 398,\n      columnNumber: 19\n    }\n  }))), /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    className: styles.yjFolderTreeUrlUploader,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 418,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Form.Item, {\n    label: \"Move\",\n    name: \"folder\",\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 419,\n      columnNumber: 17\n    }\n  }, /*#__PURE__*/React.createElement(FolderTree, {\n    showTitle: false,\n    maxHeight: \"255px\",\n    data: binderFileAreaNodes,\n    onSelectFolder: keys => {\n      props.onFolderTreeChange(keys);\n      setSelectedFolder(keys);\n      handleMoveFilesUpdateDetails(keys, selectedFileList, clientIdSelected, props.binderId, selectedFileArea);\n    },\n    disableRoot: true,\n    controlSelection: true,\n    autoExpandParent: true,\n    selectedKeys: [selectedFolder.toString()],\n    disabled: false,\n    onClick: () => {},\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 420,\n      columnNumber: 19\n    }\n  }))), /*#__PURE__*/React.createElement(_Col, {\n    span: 24,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 441,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_Alert, {\n    message: \"File(s) published to portal will be unpublished when moved\",\n    type: \"warning\",\n    showIcon: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 442,\n      columnNumber: 11\n    }\n  })))))));\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/FileAreaActionPanel/MoveFiles.tsx"], "names": ["React", "useEffect", "useState", "useDispatch", "ExclamationCircleOutlined", "styles", "SelectedFilesGrid", "getBinderFileAreaNodes", "config", "InfinitySelect", "FORBIDDEN_ERROR_CODE", "OperationalServiceTypes", "getInfiniteRecords", "logger", "FolderTree", "useHistory", "updateContextMenuMoveFilesOption", "moveFilesService", "errorNotification", "successNotification", "confirm", "LIMIT", "DISCARD_MESSAGE", "FORBIDDEN_ERROR_MESSAGE", "props", "formFolder", "form", "getFieldValue", "defaultFolder", "toString", "history", "selectedClient", "setSelectedClient", "selected<PERSON><PERSON><PERSON><PERSON>", "setSelectedFileArea", "selectedFolder", "setSelectedFolder", "searchByField", "setSearchByField", "selectedFileList", "setSelectedFileList", "getSelectedClientId", "id", "getSelectedFileAreaId", "moveColumnConfigs", "title", "dataIndex", "key", "width", "ellipsis", "binderFileAreaNodes", "setBinderFileAreaNodes", "folderTreeData", "setFolderTreeData", "clientSelectKey", "setClientSelectKey", "fileAreaKey", "setFile<PERSON>rea<PERSON>ey", "dispatch", "resetFields", "prev<PERSON><PERSON>", "mapFolderPath", "fileList", "selectedFiles", "onFolderTreeChange", "handleFilesChange", "length", "handleMoveFilesUpdateDetails", "binderId", "handleShowMoveFilesModalCancel", "onClosePopup", "transformFolders", "folders", "primaryFolders", "filter", "folder", "parentId", "childNodes", "map", "name", "subFolders", "child", "retention", "getPaginatedRecords", "page", "method", "searchValue", "transformFilters", "search", "getClientIdParameters", "limit", "offset", "field", "api", "FileManagementService", "fileAreasByChannelList", "then", "res", "data", "records", "singleClient", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "clientId", "existingIds", "record", "includes", "catch", "error", "getFileAreaPaginatedRecords", "clientFileAreasList", "mappedRecords", "singleFileArea", "getFolderTreeByFileArea", "pageCount", "pageNumber", "pageSize", "totalRecordCount", "fileAreaId", "response", "transformedFolders", "siteId", "siteName", "siteStatusId", "hasSelectedFiles", "moveFileDetails", "folderId", "undefined", "onCancelMoveFilesModal", "resetMoveFilesModal", "icon", "okText", "cancelText", "onOk", "setMoveFilesDetails", "onSuccess", "sourceClientId", "destinationClientId", "sourcefileAreaId", "destinationfileAreaId", "handleMoveFileUpdate", "event", "file", "statusCode", "message", "showMoveFileModal", "yjMoveFilesInfoFooter", "yjMoveFilesInfo", "yjMoveFilesInfoButtons", "clientIdSelected", "yjSearchByRadioGroup", "e", "target", "value", "displayText", "binderName", "yjFolderTreeUrlUploader", "keys"], "mappings": ";;;;;;;;;;;;;;;;;;;;;AAAA,OAAOA,KAAP,IAAiBC,SAAjB,EAA4BC,QAA5B,QAA4C,OAA5C;AACA,SAASC,WAAT,QAA4B,aAA5B;AAEA,SACwBC,yBADxB,QAEO,6BAFP;AAIA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,OAAOC,iBAAP,MAEO,0CAFP;AAIA,SAAiCC,sBAAjC,QAA+D,0BAA/D;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,OAAOC,cAAP,MAAyD,gCAAzD;AACA,SAASC,oBAAT,QAAqC,YAArC;AACA,SAASC,uBAAT,QAAwC,2BAAxC;AACA,SAASC,kBAAT,QAAmC,iCAAnC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,UAAT,QAA2B,4BAA3B;AAKA,SAASC,UAAT,QAAsC,kBAAtC;AACA,SAAQC,gCAAR,QAAgD,oCAAhD;AACA,SAASC,gBAAT,QAAiC,0BAAjC;AACA,SAASC,iBAAT,EAA4BC,mBAA5B,QAAuD,6BAAvD;AAIA,MAAM;AAAEC,EAAAA;AAAF,UAAN;AACA,MAAMC,KAAK,GAAG,EAAd;AACA,MAAMC,eAAe,GAAG,+CAAxB;AACA,MAAMC,uBAAuB,GAAG,qFAAhC;AAiBA,gBAAgBC,KAAD,IAA0B;AACvC,QAAMC,UAAU,GAAGD,KAAK,CAACE,IAAN,CAAWC,aAAX,CAAyB,QAAzB,CAAnB;AACA,QAAMC,aAAa,GAAG,OAAQH,UAAU,GAAGA,UAAU,CAACI,QAAX,EAAH,GAA2B,GAA7C,GAAoD,EAA1E;AACF,QAAMC,OAAO,GAAGf,UAAU,EAA1B;AAEA,QAAM,CAACgB,cAAD,EAAiBC,iBAAjB,IAAsC9B,QAAQ,CAAM,IAAN,CAApD;AACA,QAAM,CAAC+B,gBAAD,EAAmBC,mBAAnB,IAA0ChC,QAAQ,CAAM,IAAN,CAAxD;AACA,QAAM,CAACiC,cAAD,EAAiBC,iBAAjB,IAAsClC,QAAQ,CAAC0B,aAAD,CAApD;AACA,QAAM,CAACS,aAAD,EAAgBC,gBAAhB,IAAoCpC,QAAQ,CAAC,WAAD,CAAlD;AACA,QAAM,CAACqC,gBAAD,EAAmBC,mBAAnB,IAA0CtC,QAAQ,CAAU,EAAV,CAAxD,CATyC,CAWzC;;AACA,QAAMuC,mBAAmB,GAAG,MAAM,CAAAV,cAAc,SAAd,IAAAA,cAAc,WAAd,YAAAA,cAAc,CAAEW,EAAhB,KAAsB,IAAxD;;AACA,QAAMC,qBAAqB,GAAG,MAAM,CAAAV,gBAAgB,SAAhB,IAAAA,gBAAgB,WAAhB,YAAAA,gBAAgB,CAAES,EAAlB,KAAwB,IAA5D;;AACA,QAAME,iBAAiC,GAAG,CACtC;AAAEC,IAAAA,KAAK,EAAE,EAAT;AAAaC,IAAAA,SAAS,EAAE,QAAxB;AAAkCC,IAAAA,GAAG,EAAE,QAAvC;AAAiDC,IAAAA,KAAK,EAAE;AAAxD,GADsC,EAEtC;AAAEH,IAAAA,KAAK,EAAE,OAAT;AAAkBC,IAAAA,SAAS,EAAE,OAA7B;AAAsCC,IAAAA,GAAG,EAAE,OAA3C;AAAoDE,IAAAA,QAAQ,EAAE;AAA9D,GAFsC,CAA1C;AAKA,QAAM,CAACC,mBAAD,EAAsBC,sBAAtB,IAAgDjD,QAAQ,CAA6B,IAA7B,CAA9D;AACA,QAAM,CAACkD,cAAD,EAAiBC,iBAAjB,IAAsCnD,QAAQ,CAAC0B,aAAD,CAApD;AACA,QAAM,CAAC0B,eAAD,EAAkBC,kBAAlB,IAAwCrD,QAAQ,CAAC,CAAD,CAAtD;AACA,QAAM,CAACsD,WAAD,EAAcC,cAAd,IAAgCvD,QAAQ,CAAC,CAAD,CAA9C;AACA,QAAMwD,QAAQ,GAAGvD,WAAW,EAA5B;AAEAF,EAAAA,SAAS,CAAC,MAAM;AACduC,IAAAA,mBAAmB,CAAC,EAAD,CAAnB;AACAhB,IAAAA,KAAK,CAACE,IAAN,CAAWiC,WAAX;AACArB,IAAAA,gBAAgB,CAAC,WAAD,CAAhB;AACAiB,IAAAA,kBAAkB,CAACK,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAlB;AACAH,IAAAA,cAAc,CAACG,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAd;;AACE,UAAMC,aAAa,GAAG,YAAY;AAC9B,YAAMC,QAAiB,GAAGtC,KAAK,CAACuC,aAAhC;AACAvB,MAAAA,mBAAmB,CAACsB,QAAD,CAAnB;AACAtC,MAAAA,KAAK,CAACwC,kBAAN,CAAyB,CAAC,CAA1B;AACH,KAJD;;AAKAH,IAAAA,aAAa;AAChB,GAZQ,EAYN,CAACrC,KAAK,CAACuC,aAAP,CAZM,CAAT;;AAcA,QAAME,iBAAiB,GAAIH,QAAD,IAAqB;AAC3C,QAAIA,QAAQ,CAACI,MAAT,GAAkB,CAAtB,EAAyB;AACrB1B,MAAAA,mBAAmB,CAACsB,QAAD,CAAnB;AACAK,MAAAA,4BAA4B,CAAChC,cAAD,EAAiB2B,QAAjB,EAA2BrB,mBAAmB,EAA9C,EAAiDjB,KAAK,CAAC4C,QAAvD,EAAgEzB,qBAAqB,EAArF,CAA5B;AACH,KAHD,MAGO;AACL0B,MAAAA,8BAA8B,CAAC,KAAD,CAA9B;AACA7C,MAAAA,KAAK,CAAC8C,YAAN;AACD;AACJ,GARD;;AAUE,QAAMC,gBAAgB,GAAIC,OAAD,IAA+B;AACtD,UAAMC,cAAc,GAAGD,OAAO,CAACE,MAAR,CAAgBC,MAAD,IAAY,CAACA,MAAM,CAACC,QAAR,IAAoBD,MAAM,CAACE,UAA3B,IAAyCF,MAAM,CAACE,UAAP,CAAkBX,MAAlB,GAA2B,CAA/F,CAAvB;AAEA,WAAOO,cAAc,CAACK,GAAf,CAAoBH,MAAD,KAAa;AACrCjC,MAAAA,EAAE,EAAEiC,MAAM,CAACjC,EAD0B;AAErCqC,MAAAA,IAAI,EAAEJ,MAAM,CAACI,IAFwB;AAGrCC,MAAAA,UAAU,EAAEL,MAAM,CAACE,UAAP,CAAkBC,GAAlB,CAAuBG,KAAD,KAAiB;AACjDvC,QAAAA,EAAE,EAAEuC,KAAK,CAACvC,EADuC;AAEjDqC,QAAAA,IAAI,EAAEE,KAAK,CAACF,IAFqC;AAGjDC,QAAAA,UAAU,EAAE,EAHqC;AAIjDE,QAAAA,SAAS,EAAED,KAAK,CAACC,SAAN,IAAmB;AAJmB,OAAjB,CAAtB;AAHyB,KAAb,CAAnB,CAAP;AAUD,GAbD,CAjDuC,CAgEzC;;;AACA,QAAMC,mBAAmB,GAAG,OAAOC,IAAP,EAAqBC,MAArB,EAAuDC,WAAvD,KAAqG;AAE7H,UAAMC,gBAAqB,GAAG,EAA9B;;AACA,QAAID,WAAJ,EAAiB;AACbC,MAAAA,gBAAgB,CAACC,MAAjB,GAA0BF,WAA1B;AACH;;AAED,UAAMG,qBAAqB,GAAG;AAC1BC,MAAAA,KAAK,EAAErE,KADmB;AAE1BsE,MAAAA,MAAM,EAAEP,IAAI,GAAG,CAFW;AAG1BQ,MAAAA,KAAK,EAAEvD,aAHmB;AAI1B,SAAGkD;AAJuB,KAA9B;AAOA,WAAO3E,kBAAkB,CAACJ,MAAM,CAACqF,GAAP,CAAWlF,uBAAuB,CAACmF,qBAAnC,EAA0DC,sBAA3D,EAAmFN,qBAAnF,CAAlB,CACFO,IADE,CACIC,GAAD,IAAc;AAChB,UAAIA,GAAG,CAACC,IAAR,EAAc;AACV;AACA,YAAID,GAAG,CAACC,IAAJ,CAASC,OAAT,IAAoBF,GAAG,CAACC,IAAJ,CAASC,OAAT,CAAiBjC,MAAjB,KAA4B,CAAhD,IAAqD,CAACnC,cAA1D,EAA0E;AACtE,gBAAMqE,YAAY,GAAGH,GAAG,CAACC,IAAJ,CAASC,OAAT,CAAiB,CAAjB,CAArB;AACAnE,UAAAA,iBAAiB,CAACoE,YAAD,CAAjB;AACA5E,UAAAA,KAAK,CAACE,IAAN,CAAW2E,cAAX,CAA0B;AAAEC,YAAAA,QAAQ,EAAEF,YAAY,CAAC1D;AAAzB,WAA1B;AACAa,UAAAA,kBAAkB,CAACK,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAlB;AACH,SAPS,CASV;AACA;;;AACA,YAAI7B,cAAc,IAAIkE,GAAG,CAACC,IAAJ,CAASC,OAA/B,EAAwC;AACpC,gBAAMI,WAAW,GAAGN,GAAG,CAACC,IAAJ,CAASC,OAAT,CAAiBrB,GAAjB,CAAsB0B,MAAD,IAAiBA,MAAM,CAAC9D,EAA7C,CAApB;;AACA,cAAI,CAAC6D,WAAW,CAACE,QAAZ,CAAqB1E,cAAc,CAACW,EAApC,CAAL,EAA8C;AAC1CuD,YAAAA,GAAG,CAACC,IAAJ,CAASC,OAAT,GAAmB,CAACpE,cAAD,EAAiB,GAAGkE,GAAG,CAACC,IAAJ,CAASC,OAA7B,CAAnB;AACH;AACJ;;AAED,eAAOF,GAAG,CAACC,IAAX;AACH,OAnBD,MAmBO;AACH;AACA,eAAO,EAAP;AACH;AACJ,KAzBE,EA0BFQ,KA1BE,CA0BKC,KAAD,IAAgB;AACnB;AACA,aAAO,EAAP;AACH,KA7BE,CAAP;AAgCH,GA9CD,CAjEyC,CAiHzC;;;AACA,QAAMC,2BAA2B,GAAG,OAAOxB,IAAP,EAAqBC,MAArB,EAAuDC,WAAvD,KAA8F;AAChI,UAAMC,gBAAqB,GAAG,EAA9B;;AACA,QAAID,WAAJ,EAAiB;AACfC,MAAAA,gBAAgB,CAACC,MAAjB,GAA0BF,WAA1B;AACD;;AAED,UAAMG,qBAAqB,GAAG;AAC5BC,MAAAA,KAAK,EAAErE,KADqB;AAE5BsE,MAAAA,MAAM,EAAEP,IAAI,GAAG,CAFa;AAG5B,SAAGG;AAHyB,KAA9B;;AAMA,QAAI9C,mBAAmB,EAAvB,EAA2B;AACzBgD,MAAAA,qBAAqB,CAAC,QAAD,CAArB,GAAkChD,mBAAmB,EAArD;AACD;;AAED,WAAO7B,kBAAkB,CAACJ,MAAM,CAACqF,GAAP,CAAWlF,uBAAuB,CAACmF,qBAAnC,EAA0De,mBAA3D,EAAgFpB,qBAAhF,CAAlB,CACJO,IADI,CACEC,GAAD,IAAc;AAClB,UAAIA,GAAG,CAACC,IAAR,EAAc;AACZ,cAAMY,aAAa,GAAGb,GAAG,CAACC,IAAJ,CAASC,OAAT,CAAiBrB,GAAjB,CAAsB0B,MAAD,KAAkB,EAC3D,GAAGA,MADwD;AAE3D9D,UAAAA,EAAE,EAAE8D,MAAM,CAACpC,QAFgD,CAEtC;;AAFsC,SAAlB,CAArB,CAAtB,CADY,CAMZ;;AACA,YAAI0C,aAAa,CAAC5C,MAAd,KAAyB,CAAzB,IAA8B,CAACjC,gBAAnC,EAAqD;AACnD,gBAAM8E,cAAc,GAAGD,aAAa,CAAC,CAAD,CAApC;AACA5E,UAAAA,mBAAmB,CAAC6E,cAAc,CAACrE,EAAhB,CAAnB;AACAsE,UAAAA,uBAAuB,CAACD,cAAc,CAACrE,EAAhB,CAAvB;AACAlB,UAAAA,KAAK,CAACE,IAAN,CAAW2E,cAAX,CAA0B;AAAEjC,YAAAA,QAAQ,EAAE2C,cAAc,CAACrE;AAA3B,WAA1B;AACAe,UAAAA,cAAc,CAACG,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAd;AACD;;AAED,eAAO;AACLuC,UAAAA,OAAO,EAAEW,aADJ;AAELG,UAAAA,SAAS,EAAEhB,GAAG,CAACC,IAAJ,CAASe,SAFf;AAGLC,UAAAA,UAAU,EAAEjB,GAAG,CAACC,IAAJ,CAASgB,UAHhB;AAILC,UAAAA,QAAQ,EAAElB,GAAG,CAACC,IAAJ,CAASiB,QAJd;AAKLC,UAAAA,gBAAgB,EAAEnB,GAAG,CAACC,IAAJ,CAASkB;AALtB,SAAP;AAOD,OAtBD,MAsBO;AACL,eAAO;AAAEjB,UAAAA,OAAO,EAAE,EAAX;AAAec,UAAAA,SAAS,EAAE,CAA1B;AAA6BC,UAAAA,UAAU,EAAE,CAAzC;AAA4CC,UAAAA,QAAQ,EAAE,CAAtD;AAAyDC,UAAAA,gBAAgB,EAAE;AAA3E,SAAP;AACD;AACF,KA3BI,EA4BJV,KA5BI,CA4BGC,KAAD,IAAgB;AACrB9F,MAAAA,MAAM,CAAC8F,KAAP,CAAa,6CAAb,EAA4D,6BAA5D,EAA2FA,KAA3F;AACA,aAAO;AAAER,QAAAA,OAAO,EAAE,EAAX;AAAec,QAAAA,SAAS,EAAE,CAA1B;AAA6BC,QAAAA,UAAU,EAAE,CAAzC;AAA4CC,QAAAA,QAAQ,EAAE,CAAtD;AAAyDC,QAAAA,gBAAgB,EAAE;AAA3E,OAAP;AACD,KA/BI,CAAP;AAgCD,GAhDD,CAlHyC,CAsKzC;;;AACA,QAAMJ,uBAAuB,GAAIK,UAAD,IAAwB;AACrD9G,IAAAA,sBAAsB,CAAC8G,UAAD,CAAtB,CACMrB,IADN,CACYsB,QAAD,IAAmB;AACrB,UAAIA,QAAQ,CAACpB,IAAb,EAAmB;AACf,cAAMqB,kBAAkB,GAAGhD,gBAAgB,CAAC+C,QAAQ,CAACpB,IAAT,CAAc1B,OAAf,CAA3C;AACArB,QAAAA,sBAAsB,CAAC;AACrBqE,UAAAA,MAAM,EAAChG,KAAK,CAACgG,MADQ;AAErBC,UAAAA,QAAQ,EAAEH,QAAQ,CAACpB,IAAT,CAAcnB,IAAd,IAAoB,IAFT;AAGrB2C,UAAAA,YAAY,EAAE,CAHO;AAIrBlD,UAAAA,OAAO,EAAE+C;AAJY,SAAD,CAAtB;AAMH,OARD,MAQO;AACH,eAAO,EAAP;AACH;AACJ,KAbN,EAcMb,KAdN,CAcaC,KAAD,IAAW;AACd9F,MAAAA,MAAM,CAAC8F,KAAP,CAAa,kBAAb,EAAiC,8BAAjC,EAAiEA,KAAjE;AACH,KAhBN;AAiBF,GAlBD;;AAoBA,QAAMtC,8BAA8B,GAAG,CAACsD,gBAAgB,GAAG,IAApB,KAA6B;AAChE,QAAIA,gBAAgB,IAAI,CAAAC,eAAe,SAAf,IAAAA,eAAe,WAAf,YAAAA,eAAe,CAAEC,QAAjB,MAA8BC,SAAtD,EAAiE;AAC/DC,MAAAA,sBAAsB;AACvB,KAFD,MAEO;AACLC,MAAAA,mBAAmB;AACpB;AACF,GANH;;AAQA,QAAMD,sBAAsB,GAAG,MAAM;AACjC3G,IAAAA,OAAO,CAAC;AACNyB,MAAAA,KAAK,EAAEvB,eADD;AAEN2G,MAAAA,IAAI,eAAE,oBAAC,yBAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,QAFA;AAGNC,MAAAA,MAAM,EAAE,KAHF;AAINC,MAAAA,UAAU,EAAE,IAJN;;AAKNC,MAAAA,IAAI,GAAG;AACLJ,QAAAA,mBAAmB;AACpB;;AAPK,KAAD,CAAP;AASD,GAVH;;AAYI,QAAM,CAACJ,eAAD,EAAkBS,mBAAlB,IAAyCnI,QAAQ,EAAvD;;AASD,QAAM8H,mBAAmB,GAAG,MAAM;AAC/BxG,IAAAA,KAAK,CAAC8G,SAAN;AACA9G,IAAAA,KAAK,CAACE,IAAN,CAAWiC,WAAX;AACA0E,IAAAA,mBAAmB,CAAC;AAClBR,MAAAA,QAAQ,EAAEC,SADQ;AAElBhE,MAAAA,QAAQ,EAAEgE,SAFQ;AAGlBS,MAAAA,cAAc,EAAET,SAHE;AAIlBU,MAAAA,mBAAmB,EAAEV,SAJH;AAKlBW,MAAAA,gBAAgB,EAAEX,SALA;AAMlBY,MAAAA,qBAAqB,EAAEZ;AANL,KAAD,CAAnB;AAQApE,IAAAA,QAAQ,CAAC1C,gCAAgC,CAAC,KAAD,CAAjC,CAAR;AACAQ,IAAAA,KAAK,CAAC8C,YAAN;AACD,GAbF;;AAeE,QAAMH,4BAA4B,GAAG,CAAC0D,QAAD,EAAmB/D,QAAnB,EAAsC0E,mBAAtC,EAAkEC,gBAAlE,EAA0FC,qBAA1F,KAA2H;AAC7JL,IAAAA,mBAAmB,CAAC;AAAER,MAAAA,QAAQ,EAAEA,QAAZ;AAAsB/D,MAAAA,QAAQ,EAAEA,QAAhC;AAA0CyE,MAAAA,cAAc,EAAE/G,KAAK,CAACgG,MAAhE;AAAwEgB,MAAAA,mBAAmB,EAAEA,mBAA7F;AAAiHC,MAAAA,gBAAgB,EAACA,gBAAlI;AAAmJC,MAAAA,qBAAqB,EAACA;AAAzK,KAAD,CAAnB;AACD,GAFF,CAvOoC,CA2OzC;;;AACA,QAAMC,oBAAoB,GAAIC,KAAD,IAAsD;AAC/E3H,IAAAA,gBAAgB,CACd2G,eADc,aACdA,eADc,uBACdA,eAAe,CAAE9D,QAAjB,CAA0BgB,GAA1B,CAA+B+D,IAAD,IAAUA,IAAI,CAACnG,EAA7C,CADc,EAEd,CAAAkF,eAAe,SAAf,IAAAA,eAAe,WAAf,YAAAA,eAAe,CAAEC,QAAjB,IAA4BD,eAAe,CAACC,QAA5C,GAAuD,CAFzC,EAGd,CAAAD,eAAe,SAAf,IAAAA,eAAe,WAAf,YAAAA,eAAe,CAAEa,gBAAjB,IAAoCb,eAAe,CAACa,gBAApD,GAAuE,EAHzD,EAId,CAAAb,eAAe,SAAf,IAAAA,eAAe,WAAf,YAAAA,eAAe,CAAEc,qBAAjB,IAAyCd,eAAe,CAACc,qBAAzD,GAAiF,EAJnE,CAAhB,CAMG1C,IANH,CAMSsB,QAAD,IAAc;AAClBnG,MAAAA,mBAAmB,CAAC,CAAC,EAAD,CAAD,EAAO,4BAAP,CAAnB;AACAK,MAAAA,KAAK,CAAC8G,SAAN;AACAN,MAAAA,mBAAmB;AACpB,KAVH,EAWGtB,KAXH,CAWUC,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACmC,UAAN,KAAqBpI,oBAAzB,EAA+C;AAC7CQ,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOK,uBAAP,CAAjB;AACD,OAFD,MAEO;AACLL,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOyF,KAAK,CAACoC,OAAb,CAAjB;AACD;;AACDlI,MAAAA,MAAM,CAAC8F,KAAP,CAAa,kBAAb,EAAiC,YAAjC,EAA+CA,KAA/C;AACD,KAlBH;AAmBD,GApBH;;AAsBA,sBAEE;AACE,IAAA,OAAO,EAAEnF,KAAK,CAACwH,iBADjB;AAEE,IAAA,KAAK,EAAE,cAFT;AAGE,IAAA,OAAO,EAAE,MAAM3E,8BAA8B,EAH/C;AAIE,IAAA,SAAS,EAAE,eAJb;AAKE,IAAA,KAAK,EAAE,GALT;AAME,IAAA,SAAS,EAAC,OANZ;AAOE,IAAA,MAAM,EAAE,cACN;AAAK,MAAA,SAAS,EAAEhE,MAAM,CAAC4I,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACA;AAAM,MAAA,SAAS,EAAE5I,MAAM,CAAC6I,eAAxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MADA,eAKE;AAAK,MAAA,SAAS,EAAE7I,MAAM,CAAC8I,sBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AAAQ,MAAA,GAAG,EAAC,QAAZ;AAAqB,MAAA,IAAI,EAAC,SAA1B;AAAoC,MAAA,OAAO,EAAE,MAAM9E,8BAA8B,EAAjF;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gBADF,eAIE;AAAQ,MAAA,GAAG,EAAC,MAAZ;AAAmB,MAAA,IAAI,EAAC,SAAxB;AAAkC,MAAA,OAAO,EAAEsE,oBAA3C;AACQ,MAAA,QAAQ,EAAE,EAACf,eAAD,aAACA,eAAD,uBAACA,eAAe,CAAE9D,QAAlB,KAA8B,CAACsF,gBAA/B,IAAmD,CAACnH,gBAApD,IAAwE,CAACE,cAD3F;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBAJF,CALF,CADM,CAPV;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAyBE;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACA;AAAM,IAAA,IAAI,EAAEX,KAAK,CAACE,IAAlB;AAAwB,IAAA,GAAG,EAAC,eAA5B;AAA4C,IAAA,MAAM,EAAC,UAAnD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,MAAM,EAAE,EAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACI,0BAAM,IAAN;AAAW,IAAA,KAAK,EAAC,EAAjB;AAAoB,IAAA,IAAI,EAAC,UAAzB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KACGa,gBAAgB,CAAC2B,MAAjB,GAA0B,CAA1B,gBACC,oBAAC,iBAAD;AACE,IAAA,aAAa,EAAED,iBADjB;AAEE,IAAA,aAAa,EAAErB,iBAFjB;AAGE,IAAA,QAAQ,EAAEL,gBAHZ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADD,gBAOC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IARJ,CADJ,CADF,eAcE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,0BAAM,IAAN;AAAW,IAAA,KAAK,EAAC,WAAjB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,2BAAO,KAAP;AACE,IAAA,SAAS,EAAElC,MAAM,CAACgJ,oBADpB;AAEE,IAAA,KAAK,EAAEhH,aAFT;AAGE,IAAA,QAAQ,EAAGiH,CAAD,IAAO;AACfhH,MAAAA,gBAAgB,CAACgH,CAAC,CAACC,MAAF,CAASC,KAAV,CAAhB;AACAjG,MAAAA,kBAAkB,CAACK,OAAO,IAAIA,OAAO,GAAG,CAAtB,CAAlB;AACD,KANH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAQE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AAAO,IAAA,KAAK,EAAC,WAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBADF,eAEE;AAAO,IAAA,KAAK,EAAC,MAAb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,mBAFF,CARF,CADF,CADF,CAdF,eA+BE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACM,0BAAM,IAAN;AAAW,IAAA,KAAK,EAAC,gBAAjB;AAAkC,IAAA,IAAI,EAAC,UAAvC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,cAAD;AACE,IAAA,GAAG,EAAG,UAASN,eAAgB,EADjC;AAEE,IAAA,mBAAmB,EAAE,CAAC8B,IAAD,EAAOC,MAAP,EAAeC,WAAf,KAA+BH,mBAAmB,CAACC,IAAD,EAAOC,MAAP,EAAeC,WAAf,CAFzE;AAGE,IAAA,WAAW,EAAGkE,KAAD,IAAW;AACtB,aAAQ,GAAEA,KAAK,CAACC,WAAY,EAA5B;AACD,KALH;AAME,IAAA,eAAe,EAAC,sBANlB;AAOE,IAAA,cAAc,EAAC,0CAPjB;AAQE,IAAA,QAAQ,EAAGH,CAAD,IAAO;AACf9H,MAAAA,KAAK,CAACE,IAAN,CAAWiC,WAAX,CAAuB,CAAC,UAAD,CAAvB;AACAnC,MAAAA,KAAK,CAACE,IAAN,CAAWiC,WAAX,CAAuB,CAAC,QAAD,CAAvB;AACAR,MAAAA,sBAAsB,CAAC,IAAD,CAAtB;AACAnB,MAAAA,iBAAiB,CAACsH,CAAD,CAAjB,CAJe,CAKf;;AACApH,MAAAA,mBAAmB,CAAC,EAAD,CAAnB;AACD,KAfH;AAgBE,IAAA,WAAW,EAAC,yBAhBd;AAiBE,IAAA,aAAa,EAAE,CAjBjB;AAkBE,IAAA,aAAa,EAAEkH,gBAAgB,IAAItB,SAlBrC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CADN,CA/BF,eAwDE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACM,0BAAM,IAAN;AAAW,IAAA,KAAK,EAAC,yBAAjB;AAA2C,IAAA,IAAI,EAAC,UAAhD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,cAAD;AACE,IAAA,GAAG,EAAG,YAAWsB,gBAAiB,IAAG5F,WAAY,EADnD;AAEE,IAAA,mBAAmB,EAAE,CAAC4B,IAAD,EAAOC,MAAP,EAAeC,WAAf,KAA+BsB,2BAA2B,CAACxB,IAAD,EAAOC,MAAP,EAAeC,WAAf,CAFjF;AAGE,IAAA,WAAW,EAAGkE,KAAD,IAAW;AACtB,aAAQ,GAAEA,KAAK,CAACE,UAAW,EAA3B;AACD,KALH;AAME,IAAA,eAAe,EAAC,yBANlB;AAOE,IAAA,cAAc,EAAC,8CAPjB;AAQE,IAAA,QAAQ,EAAGJ,CAAD,IAAO;AACftC,MAAAA,uBAAuB,CAACsC,CAAD,CAAvB;AACApH,MAAAA,mBAAmB,CAACoH,CAAD,CAAnB;AACD,KAXH;AAYE,IAAA,WAAW,EAAC,6BAZd;AAaE,IAAA,aAAa,EAAE,CAbjB;AAcE,IAAA,aAAa,EAAErH,gBAAgB,IAAI6F,SAdrC;AAeE,IAAA,QAAQ,EAAE,CAACsB,gBAfb;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CADN,CAxDF,eA8EE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAe,IAAA,SAAS,EAAE/I,MAAM,CAACsJ,uBAAjC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACM,0BAAM,IAAN;AAAW,IAAA,KAAK,EAAC,MAAjB;AAAwB,IAAA,IAAI,EAAC,QAA7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE,oBAAC,UAAD;AACE,IAAA,SAAS,EAAE,KADb;AAEE,IAAA,SAAS,EAAC,OAFZ;AAGE,IAAA,IAAI,EAAEzG,mBAHR;AAIE,IAAA,cAAc,EAAG0G,IAAD,IAAU;AACxBpI,MAAAA,KAAK,CAACwC,kBAAN,CAAyB4F,IAAzB;AACAxH,MAAAA,iBAAiB,CAACwH,IAAD,CAAjB;AACAzF,MAAAA,4BAA4B,CAACyF,IAAD,EAAOrH,gBAAP,EAAyB6G,gBAAzB,EAA2C5H,KAAK,CAAC4C,QAAjD,EAA2DnC,gBAA3D,CAA5B;AACD,KARH;AASE,IAAA,WAAW,EAAE,IATf;AAUE,IAAA,gBAAgB,EAAE,IAVpB;AAWE,IAAA,gBAAgB,EAAE,IAXpB;AAYE,IAAA,YAAY,EAAE,CAACE,cAAc,CAACN,QAAf,EAAD,CAZhB;AAaE,IAAA,QAAQ,EAAE,KAbZ;AAcE,IAAA,OAAO,EAAE,MAAM,CAEd,CAhBH;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CADN,CA9EF,eAqGE;AAAK,IAAA,IAAI,EAAE,EAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACA;AAAO,IAAA,OAAO,EAAC,4DAAf;AAA4E,IAAA,IAAI,EAAC,SAAjF;AAA2F,IAAA,QAAQ,MAAnG;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADA,CArGF,CADF,CADA,CADF,CAzBF,CAFF;AA4IC,CA9YD", "sourcesContent": ["import React, {  useEffect, useState } from 'react';\r\nimport { useDispatch } from 'react-redux';\r\nimport { Row, Col, Form, Button, Drawer, Modal as AntModal, Skeleton, Alert, Radio, Space } from 'antd';\r\nimport {\r\n    DoubleRightOutlined,ExclamationCircleOutlined\r\n} from \"@ant-design/icons/lib/icons\";\r\n\r\nimport styles from \"./index.module.less\";\r\nimport SelectedFilesGrid, {\r\n    ColumnConfig,\r\n} from \"@app/features/FileArea/SelectedFilesGrid\";\r\nimport { IFile } from \"@app/types/fileAreaTypes\";\r\nimport { getFileDetailsByFileId, getBinderFileAreaNodes } from \"@app/api/fileAreaService\";\r\nimport config from \"@app/utils/config\";\r\nimport InfinitySelect, { InfinitySelectGetOptions } from \"@app/components/InfinitySelect\";\r\nimport { FORBIDDEN_ERROR_CODE } from \"@app/utils\";\r\nimport { OperationalServiceTypes } from \"@iris/discovery.fe.client\";\r\nimport { getInfiniteRecords } from \"@app/api/infiniteRecordsService\";\r\nimport logger from \"@app/utils/logger\";\r\nimport { FolderTree } from \"@app/components/FolderTree\";\r\nimport { FormInstance } from \"antd/lib/form\";\r\nimport { FileRecord, FileEvents, UrlEvents } from \"@app/components/forms/UploaderSubmit/types\";\r\nimport { Store } from \"antd/lib/form/interface\";\r\nimport { FileDetailsOptions } from \"@app/types/FileDetailsOptions\";\r\nimport { useHistory, useParams } from \"react-router-dom\";\r\nimport {updateContextMenuMoveFilesOption,} from '@app/redux/actions/fileAreaActions';\r\nimport { moveFilesService } from '@app/api/fileAreaService';\r\nimport { errorNotification, successNotification } from '@app/utils/antNotifications';\r\nimport { IFolderTreeResponse } from '@app/types/FileAreaFolderTreeTypes';\r\nimport { IFolder } from '@app/types/FileAreaFolderTreeTypes';\r\n\r\nconst { confirm } = AntModal;\r\nconst LIMIT = 10;\r\nconst DISCARD_MESSAGE = 'Are you sure you want to discard the changes?';\r\nconst FORBIDDEN_ERROR_MESSAGE = 'You do not have the permission to perform this action. Please refresh and try again';\r\n\r\n\r\nexport interface MoveFileProps {\r\n    selectedFiles: IFile[];\r\n    onClosePopup: () => void;\r\n    options: FileDetailsOptions;\r\n    form: FormInstance;\r\n    forManageFiles?: boolean;\r\n    onFormChange?: (event: any) => void;\r\n    onFolderTreeChange: (event: any) => void;\r\n    siteId: string;\r\n    binderId:string;\r\n    onSuccess: () => void;//sync grid\r\n    showMoveFileModal:boolean;\r\n}\r\n\r\nexport default (props: MoveFileProps) => {\r\n  const formFolder = props.form.getFieldValue('folder')\r\n  const defaultFolder = true ? (formFolder ? formFolder.toString() : '1') : '';\r\nconst history = useHistory();\r\n\r\nconst [selectedClient, setSelectedClient] = useState<any>(null);\r\nconst [selectedFileArea, setSelectedFileArea] = useState<any>(null);\r\nconst [selectedFolder, setSelectedFolder] = useState(defaultFolder);\r\nconst [searchByField, setSearchByField] = useState(\"clientRef\");\r\nconst [selectedFileList, setSelectedFileList] = useState<IFile[]>([]);\r\n\r\n// Helper functions to get IDs from selected objects\r\nconst getSelectedClientId = () => selectedClient?.id || null;\r\nconst getSelectedFileAreaId = () => selectedFileArea?.id || null;\r\nconst moveColumnConfigs: ColumnConfig[] = [\r\n    { title: \"\", dataIndex: \"remove\", key: \"remove\", width: 40 },\r\n    { title: \"Title\", dataIndex: \"title\", key: \"title\", ellipsis: true },\r\n\r\n];\r\nconst [binderFileAreaNodes, setBinderFileAreaNodes] = useState<IFolderTreeResponse | null>(null);\r\nconst [folderTreeData, setFolderTreeData] = useState(defaultFolder);\r\nconst [clientSelectKey, setClientSelectKey] = useState(0);\r\nconst [fileAreaKey, setFileAreaKey] = useState(0);\r\nconst dispatch = useDispatch();\r\n\r\nuseEffect(() => {\r\n  setSelectedFileList([]);\r\n  props.form.resetFields();\r\n  setSearchByField(\"clientRef\");\r\n  setClientSelectKey(prevKey => prevKey + 1);\r\n  setFileAreaKey(prevKey => prevKey + 1);\r\n    const mapFolderPath = async () => {\r\n        const fileList: IFile[] = props.selectedFiles;\r\n        setSelectedFileList(fileList);\r\n        props.onFolderTreeChange(-1);\r\n    };\r\n    mapFolderPath();\r\n}, [props.selectedFiles]);\r\n\r\nconst handleFilesChange = (fileList: any[]) => {\r\n    if (fileList.length > 0) {\r\n        setSelectedFileList(fileList);\r\n        handleMoveFilesUpdateDetails(selectedFolder, fileList, getSelectedClientId(),props.binderId,getSelectedFileAreaId())\r\n    } else {\r\n      handleShowMoveFilesModalCancel(false);\r\n      props.onClosePopup();\r\n    }\r\n};\r\n\r\n  const transformFolders = (folders: any[]): IFolder[] => {\r\n    const primaryFolders = folders.filter((folder) => !folder.parentId && folder.childNodes && folder.childNodes.length > 0);\r\n\r\n    return primaryFolders.map((folder) => ({\r\n      id: folder.id,\r\n      name: folder.name,\r\n      subFolders: folder.childNodes.map((child: any) => ({\r\n        id: child.id,\r\n        name: child.name,\r\n        subFolders: [],\r\n        retention: child.retention || 0,\r\n      })),\r\n    }));\r\n  };\r\n\r\n//client dropdown pagination record\r\nconst getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {\r\n\r\n    const transformFilters: any = {};\r\n    if (searchValue) {\r\n        transformFilters.search = searchValue;\r\n    }\r\n\r\n    const getClientIdParameters = {\r\n        limit: LIMIT,\r\n        offset: page - 1,\r\n        field: searchByField,\r\n        ...transformFilters,\r\n    }\r\n\r\n    return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].fileAreasByChannelList, getClientIdParameters)\r\n        .then((res: any) => {\r\n            if (res.data) {\r\n                // Auto-select if there's only one client and no selection yet\r\n                if (res.data.records && res.data.records.length === 1 && !selectedClient) {\r\n                    const singleClient = res.data.records[0];\r\n                    setSelectedClient(singleClient);\r\n                    props.form.setFieldsValue({ clientId: singleClient.id });\r\n                    setClientSelectKey(prevKey => prevKey + 1);\r\n                }\r\n\r\n                // If we have a selected client but it's not in the current records,\r\n                // merge it to ensure proper display\r\n                if (selectedClient && res.data.records) {\r\n                    const existingIds = res.data.records.map((record: any) => record.id);\r\n                    if (!existingIds.includes(selectedClient.id)) {\r\n                        res.data.records = [selectedClient, ...res.data.records];\r\n                    }\r\n                }\r\n\r\n                return res.data;\r\n            } else {\r\n                // logger.error('SideSelection', 'getPaginatedRecords', res.error);\r\n                return []\r\n            }\r\n        })\r\n        .catch((error: any) => {\r\n            // logger.error('SideSelection', 'getPaginatedRecords', error);\r\n            return [];\r\n        });\r\n\r\n\r\n};\r\n\r\n//File area pagination record\r\nconst getFileAreaPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<any> => {\r\n  const transformFilters: any = {};\r\n  if (searchValue) {\r\n    transformFilters.search = searchValue;\r\n  }\r\n\r\n  const getClientIdParameters = {\r\n    limit: LIMIT,\r\n    offset: page - 1,\r\n    ...transformFilters,\r\n  };\r\n\r\n  if (getSelectedClientId()) {\r\n    getClientIdParameters['siteId'] = getSelectedClientId();\r\n  }\r\n\r\n  return getInfiniteRecords(config.api[OperationalServiceTypes.FileManagementService].clientFileAreasList, getClientIdParameters)\r\n    .then((res: any) => {\r\n      if (res.data) {\r\n        const mappedRecords = res.data.records.map((record: any) => ({\r\n          ...record,\r\n          id: record.binderId, // Assign binderId as id\r\n        }));\r\n\r\n        // Auto-select if there's only one file area and no selection yet\r\n        if (mappedRecords.length === 1 && !selectedFileArea) {\r\n          const singleFileArea = mappedRecords[0];\r\n          setSelectedFileArea(singleFileArea.id);\r\n          getFolderTreeByFileArea(singleFileArea.id);\r\n          props.form.setFieldsValue({ binderId: singleFileArea.id });\r\n          setFileAreaKey(prevKey => prevKey + 1);\r\n        }\r\n\r\n        return {\r\n          records: mappedRecords,\r\n          pageCount: res.data.pageCount,\r\n          pageNumber: res.data.pageNumber,\r\n          pageSize: res.data.pageSize,\r\n          totalRecordCount: res.data.totalRecordCount,\r\n        };\r\n      } else {\r\n        return { records: [], pageCount: 0, pageNumber: 1, pageSize: 0, totalRecordCount: 0 };\r\n      }\r\n    })\r\n    .catch((error: any) => {\r\n      logger.error('Error fetching getFileAreaPaginatedRecords:', 'getFileAreaPaginatedRecords', error);\r\n      return { records: [], pageCount: 0, pageNumber: 1, pageSize: 0, totalRecordCount: 0 };\r\n    });\r\n};\r\n\r\n\r\n\r\n//Getting the folder tree by the selected file Area\r\nconst getFolderTreeByFileArea = (fileAreaId: string) => {\r\n   getBinderFileAreaNodes(fileAreaId)\r\n        .then((response: any) => {\r\n            if (response.data) {\r\n                const transformedFolders = transformFolders(response.data.folders);\r\n                setBinderFileAreaNodes({\r\n                  siteId:props.siteId,\r\n                  siteName: response.data.name||\"--\",\r\n                  siteStatusId: 1,\r\n                  folders: transformedFolders,\r\n                });\r\n            } else {\r\n                return [];\r\n            }\r\n        })\r\n        .catch((error) => {\r\n            logger.error('File Area Module', 'Get Folder Tree By binder id', error);\r\n        });\r\n};\r\n\r\nconst handleShowMoveFilesModalCancel = (hasSelectedFiles = true) => {\r\n    if (hasSelectedFiles && moveFileDetails?.folderId !== undefined) {\r\n      onCancelMoveFilesModal();\r\n    } else {\r\n      resetMoveFilesModal();\r\n    }\r\n  };\r\n\r\nconst onCancelMoveFilesModal = () => {\r\n    confirm({\r\n      title: DISCARD_MESSAGE,\r\n      icon: <ExclamationCircleOutlined />,\r\n      okText: 'Yes',\r\n      cancelText: 'No',\r\n      onOk() {\r\n        resetMoveFilesModal();\r\n      },\r\n    });\r\n  };\r\n\r\n    const [moveFileDetails, setMoveFilesDetails] = useState<{\r\n      fileList: IFile[];\r\n      folderId: number;\r\n      sourceClientId: string;\r\n      destinationClientId: number;\r\n      sourcefileAreaId:string;\r\n      destinationfileAreaId:string;\r\n    }>();\r\n\r\n   const resetMoveFilesModal = () => {\r\n      props.onSuccess();\r\n      props.form.resetFields();\r\n      setMoveFilesDetails({\r\n        folderId: undefined as any,\r\n        fileList: undefined as any,\r\n        sourceClientId: undefined as any,\r\n        destinationClientId: undefined as any,\r\n        sourcefileAreaId: undefined as any,\r\n        destinationfileAreaId: undefined as any,\r\n      });\r\n      dispatch(updateContextMenuMoveFilesOption(false));\r\n      props.onClosePopup();\r\n    };\r\n\r\n     const handleMoveFilesUpdateDetails = (folderId: number, fileList: IFile[], destinationClientId: number,sourcefileAreaId:string,destinationfileAreaId:string) => {\r\n        setMoveFilesDetails({ folderId: folderId, fileList: fileList, sourceClientId: props.siteId, destinationClientId: destinationClientId,sourcefileAreaId:sourcefileAreaId,destinationfileAreaId:destinationfileAreaId });\r\n      };\r\n\r\n//Update move files\r\nconst handleMoveFileUpdate = (event: React.MouseEvent<HTMLElement, MouseEvent>) => {\r\n    moveFilesService(\r\n      moveFileDetails?.fileList.map((file) => file.id),\r\n      moveFileDetails?.folderId ? moveFileDetails.folderId : 0,\r\n      moveFileDetails?.sourcefileAreaId ? moveFileDetails.sourcefileAreaId : \"\",\r\n      moveFileDetails?.destinationfileAreaId ? moveFileDetails.destinationfileAreaId : \"\",\r\n    )\r\n      .then((response) => {\r\n        successNotification([''], 'File(s) Moved Successfully');\r\n        props.onSuccess();\r\n        resetMoveFilesModal();\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          errorNotification([''], FORBIDDEN_ERROR_MESSAGE);\r\n        } else {\r\n          errorNotification([''], error.message);\r\n        }\r\n        logger.error('File Area Module', 'Move files', error);\r\n      });\r\n  };\r\n\r\nreturn (\r\n\r\n  <Drawer\r\n    visible={props.showMoveFileModal}\r\n    title={'Move File(s)'}\r\n    onClose={() => handleShowMoveFilesModalCancel()}\r\n    className={'yjDrawerPanel'}\r\n    width={700}\r\n    placement=\"right\"\r\n    footer={[\r\n      <div className={styles.yjMoveFilesInfoFooter}>\r\n      <span className={styles.yjMoveFilesInfo}>\r\n\r\n      </span>\r\n\r\n        <div className={styles.yjMoveFilesInfoButtons}>\r\n          <Button key=\"cancel\" type=\"primary\" onClick={() => handleShowMoveFilesModalCancel()}>\r\n            Cancel\r\n          </Button>\r\n          <Button key=\"Move\" type=\"primary\" onClick={handleMoveFileUpdate}\r\n                  disabled={!moveFileDetails?.fileList || !clientIdSelected || !selectedFileArea || !selectedFolder}>\r\n            Move Files\r\n          </Button>\r\n        </div>\r\n      </div>\r\n    ]}\r\n  >\r\n    <Row gutter={24}>\r\n      <Col span={24}>\r\n      <Form form={props.form} key=\"moveFilesForm\" layout=\"vertical\" >\r\n        <Row gutter={24}>\r\n          <Col span={24}>\r\n              <Form.Item label=\"\" name=\"fileList\">\r\n                {selectedFileList.length > 0 ? (\r\n                  <SelectedFilesGrid\r\n                    onFilesChange={handleFilesChange}\r\n                    columnConfigs={moveColumnConfigs}\r\n                    dataList={selectedFileList}\r\n                  />\r\n                ) : (\r\n                  <Skeleton/>\r\n                )}\r\n              </Form.Item>\r\n          </Col>\r\n          <Col span={24}>\r\n            <Form.Item label=\"SEARCH BY\">\r\n              <Radio.Group\r\n                className={styles.yjSearchByRadioGroup}\r\n                value={searchByField}\r\n                onChange={(e) => {\r\n                  setSearchByField(e.target.value);\r\n                  setClientSelectKey(prevKey => prevKey + 1);\r\n                }}\r\n              >\r\n                <Space>\r\n                  <Radio value=\"clientRef\">Client Ref</Radio>\r\n                  <Radio value=\"name\">Client Name</Radio>\r\n                </Space>\r\n              </Radio.Group>\r\n            </Form.Item>\r\n          </Col>\r\n          <Col span={24}>\r\n                <Form.Item label=\"MOVE TO CLIENT\" name=\"clientId\">\r\n                  <InfinitySelect\r\n                    key={`client-${clientSelectKey}`}\r\n                    getPaginatedRecords={(page, method, searchValue) => getPaginatedRecords(page, method, searchValue)}\r\n                    formatValue={(value) => {\r\n                      return `${value.displayText}`;\r\n                    }}\r\n                    notFoundContent=\"No Offices Available\"\r\n                    notLoadContent=\"Failed to load values in office dropdown\"\r\n                    onChange={(e) => {\r\n                      props.form.resetFields(['binderId']);\r\n                      props.form.resetFields(['folder']);\r\n                      setBinderFileAreaNodes(null);\r\n                      setSelectedClient(e);\r\n                      // Reset file area selection when client changes\r\n                      setSelectedFileArea(\"\")\r\n                    }}\r\n                    placeholder=\"Please Select Client(s)\"\r\n                    waitCharCount={3}\r\n                    defaultValues={clientIdSelected || undefined}\r\n                  />\r\n                </Form.Item>\r\n          </Col>\r\n\r\n          <Col span={24}>\r\n                <Form.Item label=\"FILE AREA OF THE CLIENT\" name=\"binderId\">\r\n                  <InfinitySelect\r\n                    key={`fileArea-${clientIdSelected}-${fileAreaKey}`}\r\n                    getPaginatedRecords={(page, method, searchValue) => getFileAreaPaginatedRecords(page, method, searchValue)}\r\n                    formatValue={(value) => {\r\n                      return `${value.binderName}`;\r\n                    }}\r\n                    notFoundContent=\"No file areas Available\"\r\n                    notLoadContent=\"Failed to load values in file areas dropdown\"\r\n                    onChange={(e) => {\r\n                      getFolderTreeByFileArea(e);\r\n                      setSelectedFileArea(e);\r\n                    }}\r\n                    placeholder=\"Please Select File Areas(s)\"\r\n                    waitCharCount={3}\r\n                    defaultValues={selectedFileArea || undefined}\r\n                    disabled={!clientIdSelected}\r\n                  />\r\n                </Form.Item>\r\n          </Col>\r\n\r\n          <Col span={24} className={styles.yjFolderTreeUrlUploader}>\r\n                <Form.Item label=\"Move\" name=\"folder\">\r\n                  <FolderTree\r\n                    showTitle={false}\r\n                    maxHeight=\"255px\"\r\n                    data={binderFileAreaNodes}\r\n                    onSelectFolder={(keys) => {\r\n                      props.onFolderTreeChange(keys);\r\n                      setSelectedFolder(keys);\r\n                      handleMoveFilesUpdateDetails(keys, selectedFileList, clientIdSelected, props.binderId, selectedFileArea)\r\n                    }}\r\n                    disableRoot={true}\r\n                    controlSelection={true}\r\n                    autoExpandParent={true}\r\n                    selectedKeys={[selectedFolder.toString()]}\r\n                    disabled={false}\r\n                    onClick={() => {\r\n\r\n                    }}\r\n                  />\r\n                  </Form.Item>\r\n\r\n          </Col>\r\n          <Col span={24}>\r\n          <Alert message=\"File(s) published to portal will be unpublished when moved\" type=\"warning\" showIcon />\r\n          </Col>\r\n        </Row>\r\n      </Form>\r\n      </Col>\r\n    </Row>\r\n  </Drawer>\r\n  );\r\n};"]}, "metadata": {}, "sourceType": "module"}