import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import IntegrationManagement from '../index';
import initTestSuite from "@app/utils/config/TestSuite";

jest.mock("../index.module.less", () => ({
    sampleStyle: "sampleStyle",
}));

describe("IntegrationManagement Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const imComponent = shallow(<IntegrationManagement />);
        expect(imComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const imComponent = renderer.create(<IntegrationManagement />).toJSON();
        expect(imComponent).toMatchSnapshot();
    });

    it("should have a div element",() => {
        const imComponent = shallow(<IntegrationManagement />);
        expect(imComponent.find(".sampleStyle")).toHaveLength(1);
    });
});
