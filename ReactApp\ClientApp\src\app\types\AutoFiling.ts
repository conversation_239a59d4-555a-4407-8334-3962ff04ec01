export interface AutoFilingRuleSetupData {
    id: string;
    ruleName: string;
    sourceFolderName: string;
    binderTemplate: {
        name: string;
        value: string;
    };
    binderTemplateNodes: Array<number>;
    sampleFileName: string;
    fileNameParserJson: FileNameParserJson[];
    enableNewFileNameGeneration: boolean;
    newFileNameGeneratorJson: string;
    fileNameGeneratorJson: string;
    folderDestination: string;
    templateName: string;
    year: string
}

interface FileNameParserJson {
    id: string;
    name: string;
    index: number;
    separator?: string; // Optional,
    yearFormat?: string; // Optional
    isFixedLength?: boolean; // Optional
    fixedLength?: number; // Optional
}