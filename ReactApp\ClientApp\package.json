{"name": "yj-frontend-boilerplate", "version": "3.2.0-rc.0", "private": true, "dependencies": {"@ant-design/charts": "1.0.9", "@ant-design/icons": "^4.7.0", "@iris/discovery.fe.client": "^2.0.1", "@microsoft/signalr": "^6.0.3", "@okta/okta-auth-js": "^6.8.1", "@okta/okta-react": "^6.6.0", "@testing-library/jest-dom": "^4.2.4", "@testing-library/react": "^9.5.0", "@testing-library/user-event": "^7.2.1", "@types/crypto-js": "3.1.47", "@types/downloadjs": "1.4.2", "@types/jest": "^24.9.1", "@types/jwt-decode": "2.2.1", "@types/lodash": "^4.14.155", "@types/node": "^12.12.37", "@types/rc-tooltip": "^3.7.3", "@types/rc-tree": "^1.11.3", "@types/react": "^16.9.34", "@types/react-beforeunload": "2.1.0", "@types/react-dom": "^16.9.6", "@types/react-input-mask": "^2.0.5", "@types/react-redux": "^7.1.7", "@types/react-router-dom": "^5.1.5", "@types/react-tooltip": "^4.2.4", "@types/uuid": "^8.3.0", "antd": "4.15.6", "antd-mask-input": "^0.1.15", "antv": "^0.2.2", "axios": "^0.27.2", "axios-mock-adapter": "^1.18.2", "bizcharts": "4.0.14", "caniuse-lite": "^1.0.30001515", "crypto-js": "4.0.0", "customize-cra": "1.0.0-alpha.0", "downloadjs": "^1.4.7", "enzyme": "^3.11.0", "enzyme-adapter-react-16": "^1.15.2", "eslint-config-airbnb": "^18.1.0", "i18next": "^19.6.0", "i18next-browser-languagedetector": "^5.0.0", "i18next-xhr-backend": "^3.2.2", "jest-enzyme": "^7.1.2", "jwt-decode": "2.2.0", "less": "^3.11.1", "less-loader": "^6.0.0", "libphonenumber-js": "^1.9.19", "lodash": "^4.17.15", "moment": "^2.26.0", "moxios": "^0.4.0", "rc-tooltip": "^5.1.0", "rc-tree": "^4.1.5", "react": "^16.13.1", "react-app-rewired": "^2.1.5", "react-beforeunload": "^2.2.1", "react-dom": "^16.13.1", "react-i18next": "^11.7.0", "react-icons": "^4.2.0", "react-idle-timer": "4.2.12", "react-input-mask": "2.0.4", "react-phone-input-2": "^2.14.0", "react-redux": "^7.2.0", "react-router-dom": "^5.1.2", "react-scripts": "^3.4.1", "react-tooltip": "^4.2.17", "react-usestateref": "^1.0.5", "redux": "^4.0.5", "redux-devtools-extension": "^2.13.8", "redux-thunk": "^2.3.0", "stream-to-blob": "^2.0.1", "typescript": "^3.8.3", "uuid": "^8.3.2", "world_countries_lists": "^2.8.1"}, "scripts": {"start": "cross-env REACT_APP_VERSION=$npm_package_version react-app-rewired start", "build": "cross-env REACT_APP_VERSION=$npm_package_version react-app-rewired --max_old_space_size=4096 build", "test": "react-app-rewired test --coverage=false", "test-coverage": "react-app-rewired test --coverage", "eject": "react-scripts eject", "lint:style": "stylelint \"src/**/*.less\" --syntax less"}, "eslintConfig": {"extends": "react-app"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"babel-plugin-import": "^1.13.0", "copy-webpack-plugin": "^6.0.1", "cross-env": "^7.0.3", "eslint-config-prettier": "6.11.0", "jest-canvas-mock": "^2.3.0", "prettier": "2.0.5", "react-test-renderer": "^16.13.1", "redux-mock-store": "^1.5.4", "stylelint": "^13.7.2", "stylelint-config-standard": "^20.0.0", "stylelint-order": "^4.1.0", "stylelint-scss": "^3.18.0", "typescript-plugin-css-modules": "^2.3.0"}, "jest": {"moduleNameMapper": {"^@$": "<rootDir>/src$1", "^@app(.*)$": "<rootDir>/src/app$1", "^@pages(.*)$": "<rootDir>/src/app/pages$1"}, "verbose": false, "collectCoverage": true, "transformIgnorePatterns ": ["/node_modules/(?!babel)"], "collectCoverageFrom": ["**/*.{ts,tsx}", "!coverage/**", "!node_modules/**", "!src/index.js", "!src/setupTests.js", "!public/**", "!build/**", "!src/serviceWorker.js", "!src/Routes.js", "!src/app/components/**", "!src/test/**", "!src/App.test.js", "!**/_tests_/**", "!**/menus/**", "!**/routes/**", "!**/*.test.js"], "coverageReporters": ["text", "lcov", "json", "text", "clover", "cobertura"], "setupFiles": ["jest-canvas-mock"]}}