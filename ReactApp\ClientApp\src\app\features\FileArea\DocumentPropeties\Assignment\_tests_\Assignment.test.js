import React from "react";
import {List} from "antd";
import MockAdapter from "axios-mock-adapter";
import axios from "axios";
import { shallow, mount } from "enzyme";
import renderer from "react-test-renderer";

import AssignmentHistory from "../index";
import initTestSuite from "@app/utils/config/TestSuite";

const MOCK_ID = "xxx";
const mockData = [
    {assignedDate: "12/12/12", assignedTo: "John1", assignedBy: "Peter1", notes: "xxx1"},
    {assignedDate: "11/11/11", assignedTo: "John2", assignedBy: "Peter2", notes: "xxx2"},
    {assignedDate: "10/10/10", assignedTo: "John3", assignedBy: "Peter3", notes: "xxx3"},
    {assignedDate: "09/09/09", assignedTo: "John4", assignedBy: "Peter4", notes: "xxx4"},
];

const mock = new MockAdapter(axios);
jest.mock("../../index.module.less", () => ({
    yjPropertiesAssignmentsTab: "yjPropertiesAssignmentsTab",
    yjPropertiesAssignmentsNotifications: "yjPropertiesAssignmentsNotifications",
    yjPropertiesAssignmentsList: "yjPropertiesAssignmentsList",
    yjPropertiesAssignmentsListItem: "yjPropertiesAssignmentsListItem",
    yjPropertiesAssignmentsTitle: "yjPropertiesAssignmentsTitle",
    yjPropertiesAssignmentsDescription: "yjPropertiesAssignmentsDescription"
}));

describe("Assignment History Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
        mock.onGet("YJAPI/files/xxx/assignments").reply(200, mockData);
    });

    it("should render",() => {
        const ahComponent = shallow(<AssignmentHistory />);
        expect(ahComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const ahComponent = renderer.create(<AssignmentHistory />).toJSON();
        expect(ahComponent).toMatchSnapshot();
    });

    it("should render with props",() => {
        const ahComponent = shallow(<AssignmentHistory fileId={""} />);
        expect(ahComponent.html()).not.toBe(null);
    });

    it("should render with props are null",() => {
        const ahComponent = shallow(<AssignmentHistory fileId={null} />);
        expect(ahComponent.html()).not.toBe(null);
    });

    it("should render with props are undefined",() => {
        const ahComponent = shallow(<AssignmentHistory fileId={undefined} />);
        expect(ahComponent.html()).not.toBe(null);
    });

    it("should have a List.Item elements",() => {
        const ahComponent = mount(<AssignmentHistory fileId={MOCK_ID} />);
        expect(ahComponent.find(List.Item)).toHaveLength(16);
    });

    it("should have a List.Meta elements",() => {
        const ahComponent = mount(<AssignmentHistory fileId={MOCK_ID} />);
        expect(ahComponent.find(List.Item.Meta)).toHaveLength(16);
    });

    it("should have div elements",() => {
        const ahComponent = mount(<AssignmentHistory fileId={MOCK_ID} />);
        expect(ahComponent.find(".yjPropertiesAssignmentsTab")).toHaveLength(1);
        expect(ahComponent.find(".yjPropertiesAssignmentsNotifications")).toHaveLength(1);
        expect(ahComponent.find(".yjPropertiesAssignmentsList")).toHaveLength(4);
        expect(ahComponent.find(".yjPropertiesAssignmentsListItem")).toHaveLength(32);
        expect(ahComponent.find(".yjPropertiesAssignmentsTitle")).toHaveLength(16);
        expect(ahComponent.find(".yjPropertiesAssignmentsDescription")).toHaveLength(16);
    });

});
