import React from "react";
import Tooltip from "antd/lib/tooltip";
import { useSelector } from "react-redux";
import { RootState } from "@app/redux/reducers/state";

const useAllowedDomain = (): boolean => {
  const { user } = useSelector((rootState: RootState) => rootState.auth);
  const userName = user?.preferred_username?.toLowerCase() || '';
  const w = window as any;

  const allowedDomains =
    process.env.ALLOWED_EMAIL_DOMAINS ||
    (w._env_ && w._env_.ALLOWED_EMAIL_DOMAINS) ||
    '@iris.co.uk,@irisglobal.com';

  const loggedUserDomain = userName.split('@').pop();

  return loggedUserDomain
    ? allowedDomains.split(',').includes(`@${loggedUserDomain}`)
    : false;
};

const AuthTooltip: React.FC = () => {
  const { tenant } = useSelector((rootState: RootState) => rootState.auth);
  const isAllowedDomain = useAllowedDomain();
  const copyrightYear = new Date().getFullYear();
  const version = (window as any)._env_?.REACT_APP_VERSION || process.env.REACT_APP_VERSION;
  const rawVersion =(window as any)?._env_?.REACT_APP_VERSION || process.env.REACT_APP_VERSION;
  const showVersion = (window as any)?._env_?.REACT_APP_VERSION_SHOW === 'true';
// Version validations
const isRealVersion =rawVersion && rawVersion.trim() !== '' && rawVersion !== '${VERSION}';

  return (
    <Tooltip
      placement='top'
      title={
        isAllowedDomain && showVersion && isRealVersion ? (
          <span style={{ marginRight: '3px' }}>Version: {rawVersion}</span>
        ) : (
          ''
        )
      }
    >
      <span>Copyright © IRIS Software Group {copyrightYear} </span>
      <span>Tenant: {tenant}</span>
    </Tooltip>
  );
};

export default AuthTooltip;
