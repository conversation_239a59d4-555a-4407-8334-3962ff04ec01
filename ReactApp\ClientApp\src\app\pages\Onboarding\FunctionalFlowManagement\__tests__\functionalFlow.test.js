import React from "react";
import { Provider } from 'react-redux';
import { mount } from 'enzyme';
import thunk from 'redux-thunk';
import configureMockStore from 'redux-mock-store';
import { Route, MemoryRouter } from 'react-router-dom'

import FunctionalFlowManagement from '../index';
import PageContent from "../../../../components/PageContent";

// required to facilitate missing window events
import '../../../../../unit-test-utils';

const ReduxProvider = ({ children, store }) => (
    <Provider store={store}>
        {children}
    </Provider>
);
const midllewares = [thunk]
const mockStore = configureMockStore(midllewares);

describe("Functional Flow Management Component", () => {
    it("should render without crashing", () => {
        const INITIAL_STATE = {
            grid:{
                selectedElement: {},
                columnQueryParameters: {}
            },
            fileArea: {
                displayPortalFilesBulkDelete: false
            }
        };
        const store = mockStore(INITIAL_STATE);

        const component = mount(
            <ReduxProvider store={store}>
                <MemoryRouter initialEntries={["/"]}>
                    <Route path="/">
                        <FunctionalFlowManagement />
                    </Route>
                </MemoryRouter>

            </ReduxProvider>);
        expect(component.find(PageContent).length).toBe(1);
    });
});
