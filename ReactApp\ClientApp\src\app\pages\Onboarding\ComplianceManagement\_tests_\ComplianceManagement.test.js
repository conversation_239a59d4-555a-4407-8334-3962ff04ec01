import React from "react";
import {Modal} from "antd";
import { mount } from "enzyme";
import renderer from "react-test-renderer";
import { MemoryRouter } from 'react-router-dom';

import ComplianceManagementComponent from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../components/PageTitle";
import PageContent from "../../../../components/PageContent";

const CustomComplianceManagement = (props) => {
    return (
        <MemoryRouter>
            <ComplianceManagementComponent {...props} />
        </MemoryRouter>
    );
}
describe("ComplianceManagement Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const cmComponent = mount(<CustomComplianceManagement />);
        expect(cmComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const cmComponent = renderer.create(<CustomComplianceManagement />).toJSON();
        expect(cmComponent).toMatchSnapshot();
    });

    it("should have a Modal element",() => {
        const cmComponent = mount(<CustomComplianceManagement />);
        expect(cmComponent.find(Modal)).toHaveLength(1);
    });

    it("should have a PageTitle component",() => {
        const cmComponent = mount(<CustomComplianceManagement />);
        expect(cmComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const cmComponent = mount(<CustomComplianceManagement />);
        expect(cmComponent.find(PageContent)).toHaveLength(1);
    });
});




