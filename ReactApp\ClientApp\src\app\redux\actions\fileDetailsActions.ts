import { AppThunk } from "@app/types/AppThunk";
import {
  getFileStatuses,
  getYears,
  getProjectsBySite,
  getUsers,
  saveFileDetails,
  saveAcceptFileDetails,
  getFileTypes,
} from "@app/api/fileAreaService";
import { ValueType } from "@app/types/ValueType";
import {
  SET_FILE_STATUSES,
  SET_FILE_YEARS,
  SET_PROJECTS,
  SetFileStatusesAction,
  SetFileYearsAction,
  SetProjectsAction,
  SetTagsAction,
  SET_TAGS,
  SET_USERS,
  SetUsersAction,
  SetIsOptionsFetchedAction,
  SET_IS_OPTIONS_FETCHED,
  SetSuccessFilesAction,
  SET_SUCCESSED_FILES,
  SetPendingSaveAction,
  SET_PENDING_SAVE,
  SET_FILE_TYPES,
  SetFileTypesAction,
} from "../actionTypes/fileDetailsActionTypes";
import {
  successNotification,
  errorNotification,
  infoNotification,
} from "@app/utils/antNotifications";
import {
  FileUploadFormData,
  FileRecord,
} from "@app/components/forms/UploaderSubmit/types";
import { FORBIDDEN_ERROR_CODE } from "@app/utils";

export function setFileStatuses(statuses: [ValueType]): SetFileStatusesAction {
  return {
    type: SET_FILE_STATUSES,
    fileStatuses: statuses,
  };
}

export function setYears(years: [number]): SetFileYearsAction {
  return {
    type: SET_FILE_YEARS,
    years: years,
  };
}

export function setProjects(projects: [ValueType]): SetProjectsAction {
  return {
    type: SET_PROJECTS,
    projects: projects,
  };
}

export function setTags(tags: [ValueType]): SetTagsAction {
  return {
    type: SET_TAGS,
    tags: tags,
  };
}

export function setUsers(users: [ValueType]): SetUsersAction {
  return {
    type: SET_USERS,
    users: users,
  };
}

export function setSuccessedFiles(files: FileRecord[]): SetSuccessFilesAction {
  return {
    type: SET_SUCCESSED_FILES,
    successedFiles: files,
  };
}

export function setPendingSave(isPending: boolean): SetPendingSaveAction {
  return {
    type: SET_PENDING_SAVE,
    pendingSave: isPending,
  };
}

export function setFileTypes(fileTypes: string[]): SetFileTypesAction {
  return {
    type: SET_FILE_TYPES,
    fileTypes,
  };
}

export function isOptionsFetched(
  isFetched: boolean
): SetIsOptionsFetchedAction {
  return {
    type: SET_IS_OPTIONS_FETCHED,
    isOptionsFetched: isFetched,
  };
}

export function fetchOptions(siteId: string, history?: any): AppThunk {
  return async (dispatch) => {
    isOptionsFetched(false);
    return Promise.all([
      dispatch(fetchFileStatuses()),
      dispatch(fetchYears()),
      dispatch(fetchProjects(siteId)),
      dispatch(fetchUsers(siteId)),
      dispatch(fetchFileTypes()),
    ])
      .then(() => dispatch(isOptionsFetched(true)))
      .catch((error) => {
        if (error.statusCode === FORBIDDEN_ERROR_CODE) {
          history.push("/forbidden");
        }
      });
  };
}

export function fetchFileStatuses(): AppThunk {
  return async (dispatch) => {
    const { data } = await getFileStatuses();
    dispatch(setFileStatuses(data));
  };
}

export function fetchYears(): AppThunk {
  return async (dispatch) => {
    const { data } = await getYears();
    dispatch(setYears(data));
  };
}

export function fetchProjects(siteId: string): AppThunk {
  return async (dispatch) => {
    const { data } = await getProjectsBySite(siteId);
    dispatch(setProjects(data));
  };
}

export function fetchUsers(siteId: string): AppThunk {
  return async (dispatch) => {
    const { data } = await getUsers(siteId);
    dispatch(setUsers(data));
  };
}

export function fetchFileTypes(): AppThunk {
  return async (dispatch) => {
    const { data } = await getFileTypes();
    dispatch(setFileTypes(data));
  };
}

export function saveFiles(
  data: FileUploadFormData,
  forPortalFilesData: boolean
): AppThunk {
  return async (dispatch) => {
    try {
      dispatch(setPendingSave(true));
      if (forPortalFilesData) {
        await saveAcceptFileDetails(data);
        infoNotification([""], "File(s) are being accepted. Please wait.");
        // successNotification([""], "File Acceptance Email sent successfully");
      } else {
        await saveFileDetails(data);
        infoNotification([""], "File(s) are being uploaded. Please wait.");
        if ((data as any).assigneeId) {
          // successNotification([""], "File Assignment Email sent successfully");
        }
        if ((data as any).users) {
          successNotification([""], "File Upload Email sent successfully");
        }
      }
      dispatch(setSuccessedFiles(data.files || []));
      dispatch(setPendingSave(false));
    } catch (error) {
      if (error.statusCode === FORBIDDEN_ERROR_CODE) {
        errorNotification(
          [""],
          "You do not have the permission to perform this action. Please refresh and try again"
        );
      } else {
        if (forPortalFilesData) {
          errorNotification([""], "Acceptance Failed");
          errorNotification([""], "Unsuccessful File Acceptance Email");
        } else {
          errorNotification([""], "Upload Failed");
          if ((data as any).assigneeId) {
            errorNotification([""], "Failed To Send File Assignment Email");
          }
          if ((data as any).users) {
            errorNotification([""], "Failed To Send File Upload Email");
          }
        }
      }

      dispatch(setPendingSave(false));
    }
  };
}
