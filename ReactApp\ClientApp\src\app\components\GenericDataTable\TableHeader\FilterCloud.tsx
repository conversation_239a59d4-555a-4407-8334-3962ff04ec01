import React, { useContext } from "react";
import { DataTableContext } from "../DataTableContext";
import styles from "./index.module.less";
import FilterCloudTag from "./FilterCloudTag";
import { GenericFilter } from "@app/components/GenericDataTable/types";

type FilterConfig = {
  isFilter?: boolean;
  filterCloudPadding?: any;
};
export default (props: FilterConfig) => {
  const { state } = useContext(DataTableContext);

  const { filters } = state;

  return (
    <div
      style={{
        paddingTop:
          props.filterCloudPadding && filters.length > 0
            ? props.filterCloudPadding
            : 0,
      }}
      className={styles.yjFilterCloudWrapper}
    >
      {Object.entries(filters).length > 0 ? (
        <span className={styles.yjFilterTextLabel}>Filter By :</span>
      ) : null}
      {filters.map((i: GenericFilter, index: number) => (
        <FilterCloudTag {...i} key={`${i.key}-${index}`} filterKey={i.key} />
      ))}
    </div>
  );
};
