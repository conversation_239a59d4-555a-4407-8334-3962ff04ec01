import React, { useContext, useState, useEffect } from "react";
import { Popover, Input } from "antd";
import SearchArea from "./SearchArea";
import { DataTableContext } from "../../DataTableContext";
import { updateFilterValue } from "../../DataTableContext/actions";
import styles from "./index.module.less";

export default ({ data, endpoint: endpoint, searchFieldParameters , props}: any) => {
  const { state, dispatch } = useContext(DataTableContext);

  const filter = state.filters[data.key] || { value: "" };
  const [input, setInput] = useState(filter.value);
  const [showPopOver, setShowPopOver] = useState(false);

  const onClickAction = (i: string) => {
    setShowPopOver(false);
    setInput(i);
    dispatch(
      updateFilterValue(data.key, {
        value: i,
        displayText: `${data.title} : ${i}`,
      })
    );
  };

  useEffect(() => {
    setShowPopOver(false);
    setInput(filter.value);
  }, [filter.value]);

  return (
    <div
      onClick={(e) => {
        e.preventDefault();
        e.stopPropagation();
      }}
    >
      <Popover
        destroyTooltipOnHide
        onVisibleChange={(e) => setShowPopOver(e)}
        visible={showPopOver}
        content={
          <SearchArea
            onClick={onClickAction}
            endpoint={endpoint}
            column={data.key}
            searchFieldParameters={searchFieldParameters}
          />
        }
        trigger={"click"}
        placement={"bottom"}
      >
        <Input
          className={styles.yjSearchableDropdownInput}
          placeholder={`Search ${data.title}`}
          onClick={() => setShowPopOver(true)}
          value={input}
        />
      </Popover>
    </div>
  );
};
