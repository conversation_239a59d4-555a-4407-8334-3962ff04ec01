@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

.yjFileFinderWrapper {
  margin-right: 1em;
  width: 100%;

  .yjFileFinderHeader {
    background: @color-bg-file-finder-header;
    border-top-left-radius: 3px;
    border-top-right-radius: 3px;
    padding: 1.25em;

    .yjFileFinderHeaderText {
      color: @color-font-file-finder-header;
      font-size: @font-size-base / 1.15;
      margin-bottom: 0;
      text-transform: @yj-transform;

      .font-mixin(@font-primary,@yjff-semibold);
    }
  }

  .yjFileFinderTreeWrapper {
    background: fade(@color-bg-file-finder-header, 12%);
    margin: .1em 0;
    max-height: 40vh;
    overflow: hidden auto;
    position: relative;
    z-index: 0;

    &::-webkit-scrollbar {
      width: .4em;
    }

    &::-webkit-scrollbar-track {
      background: fade(@color-bg-file-finder-header, 12%);
      border-radius: 8px;
    }

    &::-webkit-scrollbar-thumb {
      background: lighten(@color-accent-border, 10%);
      border-radius: 8px;
      outline: 1px solid lighten(@color-accent-border, 10%);
    }
  }
}
