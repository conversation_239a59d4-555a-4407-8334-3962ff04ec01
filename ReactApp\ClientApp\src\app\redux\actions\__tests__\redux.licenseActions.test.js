import configureMockStore from "redux-mock-store";
import thunk from "redux-thunk";

import types from "../../actionTypes/LicenseActionTypes";
import * as actions from "@app/redux/actions/licenseActions";

const middleware = [thunk];
const mockStore = configureMockStore(middleware);
const store = mockStore({});

describe("file details actions unit tests", () => {
  beforeEach(() => {
    store.clearActions();
  });

  it("should create an action to set license details", () => {
    const licDetails = {};
    const expectedAction = {
      type: types.SET_LICENSE_DETAILS,
      payload: licDetails,
    };
    expect(actions.setLicenseDetails(licDetails)).toEqual(expectedAction);
  });
  it("should create an action to set is successful", () => {
    const isSuccessful = true;
    const expectedAction = {
      type: types.DATA_FETCH_SUCESSFUL,
      payload: isSuccessful,
    };
    expect(actions.isDataFetchSuccessful(isSuccessful)).toEqual(expectedAction);
  });
  it("should create an action to set is option fetch successful", () => {
    const isSuccessful = true;
    const expectedAction = {
      type: types.OPTIONS_FETCH_SUCCESSFUL,
      payload: isSuccessful,
    };
    expect(actions.isOptionsFetched(isSuccessful)).toEqual(expectedAction);
  });

  it("should create an action to has error", () => {
    const error = {};
    const expectedAction = {
      type: types.HAS_ERRORED,
      payload: error,
    };
    expect(actions.hasErrored(error)).toEqual(expectedAction);
  });

  it("should create an action to set compliances", () => {
    const compliances = [];
    const expectedAction = {
      type: types.SET_COMPLIANCES,
      payload: compliances,
    };
    expect(actions.setCompliances(compliances)).toEqual(expectedAction);
  });

  it("should create an action to set statuses", () => {
    const statuses = {
      statuses: {},
      isEnabled: true,
    };
    const expectedAction = {
      type: types.SET_STATUSES,
      payload: statuses,
    };
    expect(actions.setStatuses(statuses)).toEqual(expectedAction);
  });

  it("should create an action to set verticles", () => {
    const verticals = [];
    const expectedAction = {
      type: types.SET_VERTICALS,
      payload: verticals,
    };
    expect(actions.setVerticals(verticals)).toEqual(expectedAction);
  });

  it("should create an action to set storages", () => {
    const storages = [];
    const expectedAction = {
      type: types.SET_STORAGES,
      payload: storages,
    };
    expect(actions.setStorages(storages)).toEqual(expectedAction);
  });

  it("should create an action to set user count", () => {
    const userCounts = [];
    const expectedAction = {
      type: types.SET_USER_COUNTS,
      payload: userCounts,
    };
    expect(actions.setUserCounts(userCounts)).toEqual(expectedAction);
  });

  it("should create an action to set support levels", () => {
    const supportLevels = [];
    const expectedAction = {
      type: types.SET_SUPPORT_LEVELS,
      payload: supportLevels,
    };
    expect(actions.setSupportLevels(supportLevels)).toEqual(expectedAction);
  });

  it("should create an action to set integrations", () => {
    const integrations = [];
    const expectedAction = {
      type: types.SET_INTEGRATIONS,
      payload: integrations,
    };
    expect(actions.setIntegrations(integrations)).toEqual(expectedAction);
  });

  it("should create an action to set integrations", () => {
    const isSuccess = true;
    const expectedAction = {
      type: types.SAVE_SUCCESSED,
      payload: isSuccess,
    };
    expect(actions.saveSuccessed(isSuccess)).toEqual(expectedAction);
  });

  it("expects fetch licesense action to be dispatched on successful request", () => {
    const store = mockStore({});
    store.dispatch(actions.fetchLicenseDetailsById(1111, "xxxx")).then(() => {
      const expectedActions = [
        types.SET_LICENSE_DETAILS,
        types.DATA_FETCH_SUCESSFUL,
        types.SET_LICENSE_DETAILS,
        types.DATA_FETCH_SUCESSFUL,
      ];
      expect(store.getActions()).toEqual(expectedActions);
    });
  });
});
