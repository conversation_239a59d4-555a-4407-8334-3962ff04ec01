@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../../../styles/';

.stepContentWrapper {
  background: @color-bg-wizard-container;
  margin-top: 1em;
  padding: 1.5em;

  .stepContentHeader {
    color: @color-secondary;
    font-size: @font-size-lg;
    margin-bottom: 1em;
    margin-top: 20px;
    text-transform: @yj-transform;

    .font-mixin(@font-primary, @yjff-bold);
  }
}

.stepButtonWrapper {
  background: @color-bg-wizard-button-section;
  border-top: 1px solid @border-color-base;
  padding: 1em;

  .yjWizardPageTransitionButton {
    background-color: darken(@color-primary, 20%);
    border-color: darken(@color-primary, 20%);
    margin-right: 0;
  }

  .stepButtonGroupWrapper {

    .yjWizardPageTransitionButton {
      background-color: darken(@color-primary, 20%);
      border-color: darken(@color-primary, 20%);
      margin-right: 0;
    }

    button {
      margin: 0 .3em;
    }
  }

  .flex-mixin(center, flex, space-between);
}

.yjAddUserGroupOverviewStepWrapper {

  button {
    margin-top: 30px;
    padding: 0;
  }
}

.yjUserGroupSelectedUsersList {
  display: flex;
  width: 100%;

  .yjUserGroupSelectedUsersListLeft {
    width: 92%;

    p {
      margin-bottom: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;

      svg {
        color: @color-secondary;
        margin-right: 5px;
      }
    }

    .yjUserGroupSelectedUsersListName {
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;
    }
  }

  .yjUserGroupSelectedUsersListRight {

    button {
      background: @color-danger;
      border: none;
      box-shadow: none;
      color: @color-font-white;
    }
    .flex-mixin(center, flex, flex-end);
  }
}

.yjModalContentWrapper {
  margin: -24px;
  max-height: 67vh;
  overflow: hidden auto;
  padding: 1.5em;
}

.yjUserGroupsPermissionsDrawerFooterWrapper {
  display: flex;

  .yjUserGroupViewFilesNotification {
    border-radius: 5px;
    font-size: small;
    font-style: italic;
    line-height: 200%;
    padding: 3px;
    text-align: left;
    width: 80%;

    svg {
      color: @color-primary;
      font-size: 16px;
      line-height: 150%;
      margin-right: 3px;
    }
  }

  button {
    .flex-mixin(center, flex, flex-end);
  }
}

// .testinfinitestyleclass {
//   display: flex;
//   justify-content: left;
// }

// Drawer list styles

.yjUserGroupsInfinityListComponent {
  display: flex;
  justify-content: left;

  div:nth-child( 2 ) {
    padding-left: 2em;
  }
}

.yjBadge {
  background: @color-primary;
  border-radius: 10px;
  color: #fff;
  font-size: 12px;
  font-weight: 400;
  height: 30px;
  line-height: 20px;
  margin-left: 5px;
  min-width: 30px;
  padding: 6px;
  text-align: center;
  white-space: nowrap;
  z-index: auto;
}

.yjManageUserGroupsInfinityListComponent {
  display: flex;
  width: 100%;

  .yjManageUserGroupsListItemValue {
    width: 92%;

    p {
      margin-bottom: 0;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
      width: 98%;

      svg {
        color: @color-secondary;
        margin-right: 5px;
      }
    }
  }

  .yjManageUserGroupsListItemAction {

    button {
      background: @color-danger;
      border: none;
      box-shadow: none;
      color: @color-font-white;
    }
    .flex-mixin(center, flex, flex-end);
  }
}
