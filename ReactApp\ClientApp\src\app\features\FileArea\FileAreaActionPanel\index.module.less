@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../styles/';

.yjFileAreaMainActionPanel {
  background: @color-bg-filearea-actionpanel;
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  padding: 0;
}

.yjFileAreaCollapsibleTriggerWrapper {
  background: fade(@color-font-filearea-action-list, 40%);
  border-radius: 3px;
  color: @color-font-filearea-action-list;
  cursor: pointer;
  font-size: @font-size-base*1.5;
  padding: 0 8px;
}

.yjActionListContainer {
  box-shadow: 0 3px 5px @color-shadow-filearea-actionpanel;
  padding: .6em;

  .yjActionListWrapper {
    border-right: 1px solid fade(@color-border, 30%);
    color: @color-font-filearea-action-list;
    padding: 0 0.66em;
    text-align: center;
    &.disabled {
      opacity: 0.5;
      cursor: not-allowed;
      pointer-events: none;
    }

    button {
      background: none;
      border: none;
      color: @color-filearea-action-list-icon;
      font-size: @font-size-md;
      padding: 0;

      span {
        .flex-mixin(center, flex, center);
      }
    }

    span {
      cursor: pointer;

      &:first-child {
        font-size: @font-size-lg*1.2;
      }

      &:last-child {
        font-size: @font-size-base / 1.5;
        margin-top: .3em;
        text-transform: @yj-transform;
      }
    }

    svg {
      font-size: @font-size-lg*1.2;
    }

    .flex-mixin(center, flex, center);
    .flex-direction(column);
  }

  .flex-mixin(center, flex, flex-start);
}

.yjActionButtonsContainer {
  padding: .6em;

  .yjActionButtonsLeftCorner {
    flex-basis: 50%;

    .yjFileAreaFilterActionButtonWrapper {

      .yjSaveFilterButton {
        background: @color-bg-save-new-filter-btn;
        border-color: @color-border-save-new-filter-btn;
        color: @color-font-save-new-filter-btn;
      }

      .flex-mixin(center, flex, center);
      .flex-direction(column);
    }

    .flex-mixin(center, flex, flex-start);
  }

  .yjActionButtonsRightCorner {
    flex-basis: 50%;

    .yjManageTagsButton {
      background: @color-bg-filearea-actionpanel;
      border: 1px solid @color-border-filter-dropdown-btn;
      color: @color-font-filearea-action-list;
    }

    .flex-mixin(center, flex, flex-end);
  }

  .flex-mixin(center, flex, flex-between);
}

.yjDropdownManageFilters {
  background: @color-accent-secondary;
  border-color: @color-accent-secondary;
  color: @color-white;
  font-size: @font-size-base;
  height: 30px;
  margin-left: 0;
  width: 100%;

  &:hover {
    background: @color-accent-secondary;
    border-color: @color-accent-secondary;
    opacity: .8;
  }
}

.yjFiltersButton {
  background: @color-bg-filearea-actionpanel;
  border: 1px solid @color-border-filter-dropdown-btn;
  color: @color-font-filearea-action-list;
  font-size: @font-size-base / 1.2;
  margin-left: 0;
  text-transform: @yj-transform;

  &:hover {
    background: @color-bg-filearea-actionpanel;
    border: 1px solid @color-border-filter-dropdown-btn;
    color: @color-font-filearea-action-list;
  }
}

.yjReCategorizeFilesInfoFooter {
  display: flex;

  .yjReCategorizeFilesInfo {

    font-style: italic;
    width: 50%;
    .flex-mixin(center, flex, flex-start);

    span {
      color: @color-primary;
      font-size: 22px;
      padding-right: 10px;
    }
  }

  .yjReCategorizeFilesInfoButtons {
    width: 50%;
    .flex-mixin(center, flex, flex-end);
  }
}

.yjMoveFilesInfoFooter {
  display: flex;

  .yjMoveFilesInfo {

    font-style: italic;
    width: 50%;
    .flex-mixin(center, flex, flex-start);

    span {
      color: @color-primary;
      font-size: 22px;
      padding-right: 10px;
    }
  }

  .yjMoveFilesInfoButtons {
    width: 50%;
    .flex-mixin(center, flex, flex-end);
  }
}

.yjReNameFilesInfoFooter {
  display: flex;

  .yjReNameFilesInfo {

    font-style: italic;
    width: 50%;
    .flex-mixin(center, flex, flex-start);

    span {
      color: @color-primary;
      font-size: 22px;
      padding-right: 10px;
    }
  }

  .yjReNameFilesInfoButtons {
    width: 50%;
    .flex-mixin(center, flex, flex-end);
  }
}