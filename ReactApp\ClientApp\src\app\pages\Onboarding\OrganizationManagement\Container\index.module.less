@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../../../styles/';

.yjOrganizationMgtModuleWrapper {
  background-color: @color-bg-organization-management-wrapper-edit;
  padding: 1.5em;

  .yjOrganizationMgtModuleContainer {
    margin-bottom: 1.5em;
  }
}

.stepButtonWrapper {
  border-top: 1px solid @border-color-base;
  margin: 0 -20px;
  padding: 1em 1em 0;

  .stepButtonGroupWrapper {

    button {
      margin: 0 .3em;

      &:last-child {
        margin-right: 0;
      }
    }
  }

  .flex-mixin(center, flex, flex-end);
}
