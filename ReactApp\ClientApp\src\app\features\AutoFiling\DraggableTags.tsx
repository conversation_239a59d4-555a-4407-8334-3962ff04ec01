import React, { useEffect, useState } from 'react';
import { Tag } from 'antd';
import '../Retention/tags.css'; // Ensure you have this CSS file
import logger from '@app/utils/logger';

interface DraggableTagProps {
    name: string;
    id: string; // Unique identifier for each tag
    onDragStart: (e: React.DragEvent<HTMLSpanElement>, id: string, fromArea: string, index?: number) => void;
}

const DraggableTag: React.FC<DraggableTagProps> = ({ name, id, onDragStart }) => (
    <Tag
        draggable
        onDragStart={(e) => onDragStart(e, id, 'Area1')}
        data-id={id} // Set data-id for the tag
        style={{ cursor: 'move', margin: '4px' }}
    >
        {name}
    </Tag>
);

interface DroppableTagProps {
    name: string;
    id: string; // Unique identifier for each tag
    index: number;
    onDragStart: (e: React.DragEvent<HTMLSpanElement>, id: string, fromArea: string, index?: number) => void;
    onDragOver: (e: React.DragEvent<HTMLSpanElement>) => void;
    onDrop: (e: React.DragEvent<HTMLSpanElement>, index: number) => void;
    onClose: (e: React.MouseEvent<HTMLSpanElement>, index: number) => void;
    onDragLeave: (e: React.DragEvent<HTMLSpanElement>) => void;
}


const DroppableTag: React.FC<DroppableTagProps> = ({
                                                       name,
                                                       id,
                                                       index,
                                                       onDragStart,
                                                       onDragOver,
                                                       onDrop,
                                                       onClose,
                                                   }) => {
    const [isOver, setIsOver] = useState(false);


    return (
    <Tag
        onDragOver={(e) => {
            setIsOver(true);
            onDragOver(e)}
        }
        onDrop={(e) => {
            setIsOver(false);
            onDrop(e, index)
        }}
        draggable
        onDragStart={(e) => onDragStart(e, id, 'Area2', index)}
        data-id={id} // Set data-id for the tag
        style={{ cursor: 'move', margin: '4px', boxSizing: 'border-box' }}
        closable
        onClose={(e)=> onClose(e,index)}
        onDragLeave={()=>setIsOver(false)}
        className={`droppable-tag ${isOver ? 'hover' : ''}`}
    >
        {name}
    </Tag>
)};

const DraggableTags: React.FC<any> = ({value, onChange, tags}:any) => {
    const [area1Tags,setArea1Tags] = useState<{ id: string; name: string }[]>(tags||[]);
    const [isOver, setIsOver] = useState(false);

    useEffect(()=>{
        if(tags){
            tags = tags.map((t:any,i:number)=>({...t, index: i }));
            setArea1Tags(tags);
        }

        setArea2Tags( value || []);
    }, [tags, value])
    const [area2Tags, setArea2Tags] = useState<{ id: string; name: string; index: number }[]>([]);

    const onClose = ( e: React.MouseEvent<HTMLElement>, index: number) => {
        e.preventDefault();
        setIsOver(false); // Remove hover class after drop

        logger.debug('DraggableTags', 'onClose-before update and set area2Tags', {index,area2Tags})
        const updatedTags = [...area2Tags];
        updatedTags.splice(index, 1);
        const updatedTagsWithNewIndexing = updatedTags.map((t, i) => ({ ...t, index: i }));
        setArea2Tags(updatedTagsWithNewIndexing);
        if (typeof onChange === 'function') {
            onChange(updatedTagsWithNewIndexing);
        }
        logger.debug('DraggableTags', 'onClose - after update and set area2Tags', {index,updatedTagsWithNewIndexing});
    };


    const onDragStart = (e: React.DragEvent<HTMLSpanElement>, id: string, fromArea: string, index?: number) => {
        e.dataTransfer.setData('id', id);
        e.dataTransfer.setData('fromArea', fromArea);
        logger.info('DraggableTags', 'onDragStart', 'Drag started');

        if (index !== undefined) {
            e.dataTransfer.setData('index', index.toString());
        }
    };

    const onDragOver = (e: React.DragEvent<HTMLSpanElement>) => {
        logger.info('DraggableTags', 'onDragOver', 'Drag over event triggered');
        e.preventDefault();
        setIsOver(true);  // Add hover class when dragging over
    };

    const onDrop = (e: React.DragEvent<HTMLSpanElement>, index: number) => {
        e.stopPropagation(); // Prevent the event from propagating to the parent
        e.preventDefault();
        setIsOver(false); // Remove hover class after drop

        const id = e.dataTransfer.getData('id');
        const fromArea = e.dataTransfer.getData('fromArea');
        const moveIndex = parseInt(e.dataTransfer.getData('index'), 10);

        const existingTag = area2Tags.find((tag, idx) => idx === moveIndex);
        const newTag = area1Tags.find(tag => tag.id === id);
        if (fromArea === 'Area1' && newTag) {
            const updatedTags = [...area2Tags];
            updatedTags.splice(index, 0, { ...newTag, index });
            const updatedTagsWithNewIndexing = updatedTags.map((t, i) => ({ ...t, index: i }));
            setArea2Tags(updatedTagsWithNewIndexing);
            if (typeof onChange === 'function') {
                onChange(updatedTagsWithNewIndexing);
            }
        }

        if (fromArea === 'Area2' && existingTag) {
            const updatedTags = [...area2Tags];
            const [movedTag] = updatedTags.splice(moveIndex, 1);
            updatedTags.splice(index, 0, movedTag);
            const updatedTagsWithNewIndexing = updatedTags.map((t, i) => ({ ...t, index: i }));
            setArea2Tags(updatedTagsWithNewIndexing);
            if (typeof onChange === 'function') {
                onChange(updatedTagsWithNewIndexing);
            }
        }
    };

    const onDropInArea2 = (e: React.DragEvent<HTMLDivElement>) => {
        logger.info('DraggableTags', 'onDropInArea2', 'Drop in Area2 event triggered');
        e.preventDefault();
        setIsOver(false); // Remove hover class after drop

        const id = e.dataTransfer.getData('id');
        const fromArea = e.dataTransfer.getData('fromArea');
        const newTag = area1Tags.find(tag => tag.id === id);

        if (fromArea === 'Area1' && newTag) {
            const updatedTagsWithNewIndexing = [...area2Tags, { ...newTag, index: area2Tags.length }];
            setArea2Tags(updatedTagsWithNewIndexing);
            if (typeof onChange === 'function') {
                onChange(updatedTagsWithNewIndexing);
            }
        }
    };

    const onDragLeave = () => {
        setIsOver(false);  // Remove hover class when dragging leaves
        logger.info('DraggableTags', 'onDragLeave', 'Drag leave event triggered');

    };

    const onDragLeaveTag = () => {
        setIsOver(false);  // Remove hover class when dragging leaves
        logger.info('DraggableTags', 'onDragLeaveTag', 'Drag leave event triggered');

    };

    return ( <div style={{ display: 'flex', flexDirection: 'column' }}>
            <div data-name="Area2"
                id='Area2'
                onDrop={onDropInArea2}
                onDragOver={(e) => {
                    logger.info('DraggableTags', 'onDragOver Area2', 'Drag over Area2 event triggered');
                    setIsOver(true);  // Add hover class when dragging over
                    e.preventDefault()
                }}
                 onDragLeave={onDragLeave}
                 className={`droppable-area ${isOver ? 'hover' : ''}`}
                 style={{ width: 'auto', border: '1px solid #ccc', padding: '10px', backgroundColor: 'white', minHeight: '60px', position: 'relative', boxSizing: 'border-box'}}
            >
                {area2Tags && area2Tags.map((tag, index) => (
                    <DroppableTag
                        key={`${tag.id}-${index}`}
                        name={tag.name}
                        id={tag.id}
                        index={index}
                        onDragStart={onDragStart}
                        onDragOver={onDragOver}
                        onDragLeave={onDragLeaveTag}
                        onDrop={onDrop}
                        onClose={onClose}
                    />
                ))}
            </div>
            <div id='Area1' data-name="Area1" style={{  width: 'auto', border: '1px solid #ccc', padding: '10px', backgroundColor: 'lightblue' }}>
                {area1Tags && area1Tags.map((tag, index) => (
                    <DraggableTag
                        key={`${tag.id}-${index}`}
                        name={tag.name}
                        id={tag.id}
                        onDragStart={onDragStart}
                    />
                ))}
            </div>
        </div> );
};

export default DraggableTags;
