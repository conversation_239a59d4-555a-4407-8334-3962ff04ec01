{"ast": null, "code": "import \"antd/es/skeleton/style\";\nimport _Skeleton from \"antd/es/skeleton\";\nimport \"antd/es/list/style\";\nimport _List from \"antd/es/list\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\features\\\\FileArea\\\\DocumentPropeties\\\\Detail\\\\index.tsx\";\nimport React, { useEffect, useState } from \"react\";\nimport { useHistory } from \"react-router-dom\";\nimport styles from \"@app/features/FileArea/DocumentPropeties/index.module.less\";\nimport { getFileDetailsByFileId } from \"@app/api/fileAreaService\";\nimport logger from \"@app/utils/logger\";\nimport { errorNotification } from \"@app/utils/antNotifications\";\nimport { DATA_RETRIEVE_ERROR_MESSAGE } from \"@app/features/FileArea/DocumentPropeties\";\nimport { FORBIDDEN_ERROR_CODE, getLocalDateTime } from \"@app/utils\";\n\nconst commaSeparatingReducer = (accu, current, index) => {\n  return index === 0 ? accu.toString().concat(current.name) : accu.toString().concat(\",\", current.name);\n};\n\nconst mapFolderPath = folderDetail => {\n  return folderDetail.parent ? folderDetail.parent.name.toString().concat(\"/\", folderDetail.name) : folderDetail.name;\n};\n\nconst LABEL_TYPE = \"Type\";\nconst LABEL_SIZE = \"Size\";\nconst LABEL_YEAR = \"Year\";\nconst LABEL_STATUS = \"Status\";\nconst LABEL_ASSIGNEE = \"Assignee\";\nconst LABEL_PATH = \"Path\";\nconst LABEL_CREATED = \"Created\";\nconst LABEL_CREATED_BY = \"Created by\";\nconst LABEL_MODIFIED = \"Modified\";\nconst LABEL_PROJECTS = \"Projects\";\nconst LABEL_TAGS = \"Tags\";\nconst LABEL_EXPIRATION_DATE = \"expiration date\";\nconst LABEL_EXPIRATION_STATUS = \"expiration status\";\nconst LABEL_DESCRIPTION = \"Description\";\nexport default (props => {\n  const [processingTrigger, setProcessingTrigger] = useState(true);\n  const [fileHistoryDetail, setFileHistoryDetail] = useState([]);\n  const history = useHistory();\n  useEffect(() => {\n    getFileDetailsByFileId(props.fileId).then(({\n      data\n    }) => {\n      var _data$status, _data$assignee, _data$createdBy, _data$projects, _data$tags;\n\n      const customizedFileDetails = [{\n        name: LABEL_TYPE,\n        value: data.type\n      }, {\n        name: LABEL_SIZE,\n        value: data.size\n      }, {\n        name: LABEL_YEAR,\n        value: data.year\n      }, {\n        name: LABEL_STATUS,\n        value: (_data$status = data.status) === null || _data$status === void 0 ? void 0 : _data$status.name\n      }, {\n        name: LABEL_ASSIGNEE,\n        value: (_data$assignee = data.assignee) === null || _data$assignee === void 0 ? void 0 : _data$assignee.name\n      }, {\n        name: LABEL_PATH,\n        value: mapFolderPath(data.folder)\n      }, {\n        name: LABEL_CREATED,\n        value: getLocalDateTime(data.created)\n      }, {\n        name: LABEL_CREATED_BY,\n        value: (_data$createdBy = data.createdBy) === null || _data$createdBy === void 0 ? void 0 : _data$createdBy.name\n      }, {\n        name: LABEL_MODIFIED,\n        value: getLocalDateTime(data.modified)\n      }, {\n        name: LABEL_PROJECTS,\n        value: (_data$projects = data.projects) === null || _data$projects === void 0 ? void 0 : _data$projects.reduce(commaSeparatingReducer, \"\")\n      }, {\n        name: LABEL_TAGS,\n        value: (_data$tags = data.tags) === null || _data$tags === void 0 ? void 0 : _data$tags.reduce(commaSeparatingReducer, \"\")\n      }, {\n        name: LABEL_EXPIRATION_DATE,\n        value: getLocalDateTime(data.expirationDate)\n      }, {\n        name: LABEL_EXPIRATION_STATUS,\n        value: data.expirationStatus\n      }, {\n        name: LABEL_DESCRIPTION,\n        value: data.description\n      }];\n      setFileHistoryDetail(customizedFileDetails);\n      setProcessingTrigger(false);\n    }).catch(error => {\n      if (error.statusCode === FORBIDDEN_ERROR_CODE) {\n        history.push(\"/forbidden\");\n      } else {\n        errorNotification([\"\"], DATA_RETRIEVE_ERROR_MESSAGE);\n      }\n\n      logger.error(\"Document Properties\", \"Retrieve File History Details\", error);\n    });\n    return () => {\n      setProcessingTrigger(true);\n    };\n  }, [props.fileId]);\n\n  const renderDetailsItem = item => {\n    return /*#__PURE__*/React.createElement(_List.Item, {\n      className: styles.yjPropertiesDetailListItem,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 94,\n        columnNumber: 7\n      }\n    }, /*#__PURE__*/React.createElement(_List.Item.Meta, {\n      title: /*#__PURE__*/React.createElement(\"p\", {\n        className: styles.yjPropertiesDetailTitle,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 96,\n          columnNumber: 18\n        }\n      }, item.name),\n      description: /*#__PURE__*/React.createElement(\"span\", {\n        className: styles.yjPropertiesDetailDescription,\n        __self: this,\n        __source: {\n          fileName: _jsxFileName,\n          lineNumber: 98,\n          columnNumber: 13\n        }\n      }, item.value),\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 95,\n        columnNumber: 9\n      }\n    }));\n  };\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, !processingTrigger ? /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjPropertiesDetailTab,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 110,\n      columnNumber: 9\n    }\n  }, /*#__PURE__*/React.createElement(\"div\", {\n    className: styles.yjPropertiesDetailInfo,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 112,\n      columnNumber: 11\n    }\n  }, /*#__PURE__*/React.createElement(_List, {\n    className: styles.yjPropertiesDetailList,\n    itemLayout: \"horizontal\",\n    dataSource: fileHistoryDetail,\n    renderItem: item => renderDetailsItem(item),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 113,\n      columnNumber: 13\n    }\n  }))) : /*#__PURE__*/React.createElement(_Skeleton, {\n    active: true,\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 122,\n      columnNumber: 9\n    }\n  }));\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/features/FileArea/DocumentPropeties/Detail/index.tsx"], "names": ["React", "useEffect", "useState", "useHistory", "styles", "getFileDetailsByFileId", "logger", "errorNotification", "DATA_RETRIEVE_ERROR_MESSAGE", "FORBIDDEN_ERROR_CODE", "getLocalDateTime", "commaSeparatingReducer", "accu", "current", "index", "toString", "concat", "name", "mapFolderPath", "folderDetail", "parent", "LABEL_TYPE", "LABEL_SIZE", "LABEL_YEAR", "LABEL_STATUS", "LABEL_ASSIGNEE", "LABEL_PATH", "LABEL_CREATED", "LABEL_CREATED_BY", "LABEL_MODIFIED", "LABEL_PROJECTS", "LABEL_TAGS", "LABEL_EXPIRATION_DATE", "LABEL_EXPIRATION_STATUS", "LABEL_DESCRIPTION", "props", "processingTrigger", "setProcessingTrigger", "fileHistoryDetail", "setFileHistoryDetail", "history", "fileId", "then", "data", "customizedFileDetails", "value", "type", "size", "year", "status", "assignee", "folder", "created", "created<PERSON>y", "modified", "projects", "reduce", "tags", "expirationDate", "expirationStatus", "description", "catch", "error", "statusCode", "push", "renderDetailsItem", "item", "yjPropertiesDetailListItem", "yjPropertiesDetailTitle", "yjPropertiesDetailDescription", "yjPropertiesDetailTab", "yjPropertiesDetailInfo", "yjPropertiesDetailList"], "mappings": ";;;;;AAAA,OAAOA,KAAP,IAAgBC,SAAhB,EAA2BC,QAA3B,QAA2C,OAA3C;AAEA,SAASC,UAAT,QAA2B,kBAA3B;AAEA,OAAOC,MAAP,MAAmB,4DAAnB;AACA,SAASC,sBAAT,QAAuC,0BAAvC;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,SAASC,iBAAT,QAAkC,6BAAlC;AACA,SAASC,2BAAT,QAA4C,0CAA5C;AACA,SAASC,oBAAT,EAA+BC,gBAA/B,QAAuD,YAAvD;;AAEA,MAAMC,sBAAsB,GAAG,CAACC,IAAD,EAAYC,OAAZ,EAA0BC,KAA1B,KAA4C;AACzE,SAAOA,KAAK,KAAK,CAAV,GACHF,IAAI,CAACG,QAAL,GAAgBC,MAAhB,CAAuBH,OAAO,CAACI,IAA/B,CADG,GAEHL,IAAI,CAACG,QAAL,GAAgBC,MAAhB,CAAuB,GAAvB,EAA4BH,OAAO,CAACI,IAApC,CAFJ;AAGD,CAJD;;AAMA,MAAMC,aAAa,GAAIC,YAAD,IAAuB;AAC3C,SAAOA,YAAY,CAACC,MAAb,GACHD,YAAY,CAACC,MAAb,CAAoBH,IAApB,CAAyBF,QAAzB,GAAoCC,MAApC,CAA2C,GAA3C,EAAgDG,YAAY,CAACF,IAA7D,CADG,GAEHE,YAAY,CAACF,IAFjB;AAGD,CAJD;;AAMA,MAAMI,UAAU,GAAG,MAAnB;AACA,MAAMC,UAAU,GAAG,MAAnB;AACA,MAAMC,UAAU,GAAG,MAAnB;AACA,MAAMC,YAAY,GAAG,QAArB;AACA,MAAMC,cAAc,GAAG,UAAvB;AACA,MAAMC,UAAU,GAAG,MAAnB;AACA,MAAMC,aAAa,GAAG,SAAtB;AACA,MAAMC,gBAAgB,GAAG,YAAzB;AACA,MAAMC,cAAc,GAAG,UAAvB;AACA,MAAMC,cAAc,GAAG,UAAvB;AACA,MAAMC,UAAU,GAAG,MAAnB;AACA,MAAMC,qBAAqB,GAAG,iBAA9B;AACA,MAAMC,uBAAuB,GAAG,mBAAhC;AACA,MAAMC,iBAAiB,GAAG,aAA1B;AAEA,gBAAgBC,KAAD,IAA+B;AAC5C,QAAM,CAACC,iBAAD,EAAoBC,oBAApB,IAA4CnC,QAAQ,CAAC,IAAD,CAA1D;AACA,QAAM,CAACoC,iBAAD,EAAoBC,oBAApB,IAA4CrC,QAAQ,CAAQ,EAAR,CAA1D;AACA,QAAMsC,OAAO,GAAGrC,UAAU,EAA1B;AAEAF,EAAAA,SAAS,CAAC,MAAM;AACdI,IAAAA,sBAAsB,CAAC8B,KAAK,CAACM,MAAP,CAAtB,CACGC,IADH,CACQ,CAAC;AAAEC,MAAAA;AAAF,KAAD,KAAmB;AAAA;;AACvB,YAAMC,qBAAqB,GAAG,CAC5B;AAAE3B,QAAAA,IAAI,EAAEI,UAAR;AAAoBwB,QAAAA,KAAK,EAAEF,IAAI,CAACG;AAAhC,OAD4B,EAE5B;AAAE7B,QAAAA,IAAI,EAAEK,UAAR;AAAoBuB,QAAAA,KAAK,EAAEF,IAAI,CAACI;AAAhC,OAF4B,EAG5B;AAAE9B,QAAAA,IAAI,EAAEM,UAAR;AAAoBsB,QAAAA,KAAK,EAAEF,IAAI,CAACK;AAAhC,OAH4B,EAI5B;AAAE/B,QAAAA,IAAI,EAAEO,YAAR;AAAsBqB,QAAAA,KAAK,kBAAEF,IAAI,CAACM,MAAP,iDAAE,aAAahC;AAA1C,OAJ4B,EAK5B;AAAEA,QAAAA,IAAI,EAAEQ,cAAR;AAAwBoB,QAAAA,KAAK,oBAAEF,IAAI,CAACO,QAAP,mDAAE,eAAejC;AAA9C,OAL4B,EAM5B;AAAEA,QAAAA,IAAI,EAAES,UAAR;AAAoBmB,QAAAA,KAAK,EAAE3B,aAAa,CAACyB,IAAI,CAACQ,MAAN;AAAxC,OAN4B,EAO5B;AAAElC,QAAAA,IAAI,EAAEU,aAAR;AAAuBkB,QAAAA,KAAK,EAAEnC,gBAAgB,CAACiC,IAAI,CAACS,OAAN;AAA9C,OAP4B,EAQ5B;AAAEnC,QAAAA,IAAI,EAAEW,gBAAR;AAA0BiB,QAAAA,KAAK,qBAAEF,IAAI,CAACU,SAAP,oDAAE,gBAAgBpC;AAAjD,OAR4B,EAS5B;AAAEA,QAAAA,IAAI,EAAEY,cAAR;AAAwBgB,QAAAA,KAAK,EAAEnC,gBAAgB,CAACiC,IAAI,CAACW,QAAN;AAA/C,OAT4B,EAU5B;AACErC,QAAAA,IAAI,EAAEa,cADR;AAEEe,QAAAA,KAAK,oBAAEF,IAAI,CAACY,QAAP,mDAAE,eAAeC,MAAf,CAAsB7C,sBAAtB,EAA8C,EAA9C;AAFT,OAV4B,EAc5B;AACEM,QAAAA,IAAI,EAAEc,UADR;AAEEc,QAAAA,KAAK,gBAAEF,IAAI,CAACc,IAAP,+CAAE,WAAWD,MAAX,CAAkB7C,sBAAlB,EAA0C,EAA1C;AAFT,OAd4B,EAkB5B;AACEM,QAAAA,IAAI,EAAEe,qBADR;AAEEa,QAAAA,KAAK,EAAEnC,gBAAgB,CAACiC,IAAI,CAACe,cAAN;AAFzB,OAlB4B,EAsB5B;AAAEzC,QAAAA,IAAI,EAAEgB,uBAAR;AAAiCY,QAAAA,KAAK,EAAEF,IAAI,CAACgB;AAA7C,OAtB4B,EAuB5B;AAAE1C,QAAAA,IAAI,EAAEiB,iBAAR;AAA2BW,QAAAA,KAAK,EAAEF,IAAI,CAACiB;AAAvC,OAvB4B,CAA9B;AAyBArB,MAAAA,oBAAoB,CAACK,qBAAD,CAApB;AACAP,MAAAA,oBAAoB,CAAC,KAAD,CAApB;AACD,KA7BH,EA8BGwB,KA9BH,CA8BUC,KAAD,IAAW;AAChB,UAAIA,KAAK,CAACC,UAAN,KAAqBtD,oBAAzB,EAA+C;AAC7C+B,QAAAA,OAAO,CAACwB,IAAR,CAAa,YAAb;AACD,OAFD,MAEO;AACLzD,QAAAA,iBAAiB,CAAC,CAAC,EAAD,CAAD,EAAOC,2BAAP,CAAjB;AACD;;AACDF,MAAAA,MAAM,CAACwD,KAAP,CACE,qBADF,EAEE,+BAFF,EAGEA,KAHF;AAKD,KAzCH;AA0CA,WAAO,MAAM;AACXzB,MAAAA,oBAAoB,CAAC,IAAD,CAApB;AACD,KAFD;AAGD,GA9CQ,EA8CN,CAACF,KAAK,CAACM,MAAP,CA9CM,CAAT;;AAgDA,QAAMwB,iBAAiB,GAAIC,IAAD,IAAe;AACvC,wBACE,0BAAM,IAAN;AAAW,MAAA,SAAS,EAAE9D,MAAM,CAAC+D,0BAA7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE,0BAAM,IAAN,CAAW,IAAX;AACE,MAAA,KAAK,eAAE;AAAG,QAAA,SAAS,EAAE/D,MAAM,CAACgE,uBAArB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SAA+CF,IAAI,CAACjD,IAApD,CADT;AAEE,MAAA,WAAW,eACT;AAAM,QAAA,SAAS,EAAEb,MAAM,CAACiE,6BAAxB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,SACGH,IAAI,CAACrB,KADR,CAHJ;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MADF,CADF;AAYD,GAbD;;AAeA,sBACE,0CACG,CAACT,iBAAD,gBACC;AAAK,IAAA,SAAS,EAAEhC,MAAM,CAACkE,qBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAEE;AAAK,IAAA,SAAS,EAAElE,MAAM,CAACmE,sBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBACE;AACE,IAAA,SAAS,EAAEnE,MAAM,CAACoE,sBADpB;AAEE,IAAA,UAAU,EAAC,YAFb;AAGE,IAAA,UAAU,EAAElC,iBAHd;AAIE,IAAA,UAAU,EAAG4B,IAAD,IAAUD,iBAAiB,CAACC,IAAD,CAJzC;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IADF,CAFF,CADD,gBAaC;AAAU,IAAA,MAAM,EAAE,IAAlB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IAdJ,CADF;AAmBD,CAvFD", "sourcesContent": ["import React, { useEffect, useState } from \"react\";\r\nimport { List, Skeleton } from \"antd\";\r\nimport { useHistory } from \"react-router-dom\";\r\n\r\nimport styles from \"@app/features/FileArea/DocumentPropeties/index.module.less\";\r\nimport { getFileDetailsByFileId } from \"@app/api/fileAreaService\";\r\nimport logger from \"@app/utils/logger\";\r\nimport { errorNotification } from \"@app/utils/antNotifications\";\r\nimport { DATA_RETRIEVE_ERROR_MESSAGE } from \"@app/features/FileArea/DocumentPropeties\";\r\nimport { FORBIDDEN_ERROR_CODE, getLocalDateTime } from \"@app/utils\";\r\n\r\nconst commaSeparatingReducer = (accu: any, current: any, index: number) => {\r\n  return index === 0\r\n    ? accu.toString().concat(current.name)\r\n    : accu.toString().concat(\",\", current.name);\r\n};\r\n\r\nconst mapFolderPath = (folderDetail: any) => {\r\n  return folderDetail.parent\r\n    ? folderDetail.parent.name.toString().concat(\"/\", folderDetail.name)\r\n    : folderDetail.name;\r\n};\r\n\r\nconst LABEL_TYPE = \"Type\";\r\nconst LABEL_SIZE = \"Size\";\r\nconst LABEL_YEAR = \"Year\";\r\nconst LABEL_STATUS = \"Status\";\r\nconst LABEL_ASSIGNEE = \"Assignee\";\r\nconst LABEL_PATH = \"Path\";\r\nconst LABEL_CREATED = \"Created\";\r\nconst LABEL_CREATED_BY = \"Created by\";\r\nconst LABEL_MODIFIED = \"Modified\";\r\nconst LABEL_PROJECTS = \"Projects\";\r\nconst LABEL_TAGS = \"Tags\";\r\nconst LABEL_EXPIRATION_DATE = \"expiration date\";\r\nconst LABEL_EXPIRATION_STATUS = \"expiration status\";\r\nconst LABEL_DESCRIPTION = \"Description\";\r\n\r\nexport default (props: { fileId: string }) => {\r\n  const [processingTrigger, setProcessingTrigger] = useState(true);\r\n  const [fileHistoryDetail, setFileHistoryDetail] = useState<any[]>([]);\r\n  const history = useHistory();\r\n\r\n  useEffect(() => {\r\n    getFileDetailsByFileId(props.fileId)\r\n      .then(({ data }: any) => {\r\n        const customizedFileDetails = [\r\n          { name: LABEL_TYPE, value: data.type },\r\n          { name: LABEL_SIZE, value: data.size },\r\n          { name: LABEL_YEAR, value: data.year },\r\n          { name: LABEL_STATUS, value: data.status?.name },\r\n          { name: LABEL_ASSIGNEE, value: data.assignee?.name },\r\n          { name: LABEL_PATH, value: mapFolderPath(data.folder) },\r\n          { name: LABEL_CREATED, value: getLocalDateTime(data.created) },\r\n          { name: LABEL_CREATED_BY, value: data.createdBy?.name },\r\n          { name: LABEL_MODIFIED, value: getLocalDateTime(data.modified) },\r\n          {\r\n            name: LABEL_PROJECTS,\r\n            value: data.projects?.reduce(commaSeparatingReducer, \"\"),\r\n          },\r\n          {\r\n            name: LABEL_TAGS,\r\n            value: data.tags?.reduce(commaSeparatingReducer, \"\"),\r\n          },\r\n          {\r\n            name: LABEL_EXPIRATION_DATE,\r\n            value: getLocalDateTime(data.expirationDate),\r\n          },\r\n          { name: LABEL_EXPIRATION_STATUS, value: data.expirationStatus },\r\n          { name: LABEL_DESCRIPTION, value: data.description },\r\n        ];\r\n        setFileHistoryDetail(customizedFileDetails);\r\n        setProcessingTrigger(false);\r\n      })\r\n      .catch((error) => {\r\n        if (error.statusCode === FORBIDDEN_ERROR_CODE) {\r\n          history.push(\"/forbidden\");\r\n        } else {\r\n          errorNotification([\"\"], DATA_RETRIEVE_ERROR_MESSAGE);\r\n        }\r\n        logger.error(\r\n          \"Document Properties\",\r\n          \"Retrieve File History Details\",\r\n          error\r\n        );\r\n      });\r\n    return () => {\r\n      setProcessingTrigger(true);\r\n    };\r\n  }, [props.fileId]);\r\n\r\n  const renderDetailsItem = (item: any) => {\r\n    return (\r\n      <List.Item className={styles.yjPropertiesDetailListItem}>\r\n        <List.Item.Meta\r\n          title={<p className={styles.yjPropertiesDetailTitle}>{item.name}</p>}\r\n          description={\r\n            <span className={styles.yjPropertiesDetailDescription}>\r\n              {item.value}\r\n            </span>\r\n          }\r\n        />\r\n      </List.Item>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <>\r\n      {!processingTrigger ? (\r\n        <div className={styles.yjPropertiesDetailTab}>\r\n          {/* <div className={styles.yjPropertiesDetailPreview}></div> */}\r\n          <div className={styles.yjPropertiesDetailInfo}>\r\n            <List\r\n              className={styles.yjPropertiesDetailList}\r\n              itemLayout=\"horizontal\"\r\n              dataSource={fileHistoryDetail}\r\n              renderItem={(item) => renderDetailsItem(item)}\r\n            />\r\n          </div>\r\n        </div>\r\n      ) : (\r\n        <Skeleton active={true} />\r\n      )}\r\n    </>\r\n  );\r\n};\r\n"]}, "metadata": {}, "sourceType": "module"}