import React from "react";
import { shallow, mount } from "enzyme";
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, MemoryRouter, Router } from "react-router-dom";
import { act } from "react-dom/test-utils";
import { createMemoryHistory } from "history";
import {Provider} from "react-redux";
import thunk from "redux-thunk";
import configureMockStore from "redux-mock-store";

import Breadcrumbs from "..";
const samplePageInnerPath = "/sample-page/inner";
const antBreadcrumLinkPath = ".ant-breadcrumb-link";

const _dummyComponent = <div>Page</div>;

const _dummyRoute = [
  {
    path: "/sample-page",
    title: "Sample Page",
    component: _dummyComponent,
    exact: false,
    guard: [],
    useInBreadcrumbs: true,
    routes: [
      {
        path: samplePageInnerPath,
        title: "Inner Page",
        component: _dummyComponent,
        exact: false,
        guard: [],
        useInBreadcrumbs: true,
      },
    ],
  },
];

const ReduxProvider = ({ children, store }) => (
    <Provider store={store}>{children}</Provider>
);
const midllewares = [thunk];
const mockStore = configureMockStore(midllewares);
const getCustomizedBreadCrumb = (breadComponent) => {
  const INITIAL_STATE = {configuration : {}};
  const store = mockStore(INITIAL_STATE);
  return (
      <ReduxProvider store={store}>
        {breadComponent}
      </ReduxProvider>
  );
}

describe("<Breadcrumbs/>", () => {
  it("should render Breadcrumbs component", () => {
    const component = shallow(getCustomizedBreadCrumb(
      <BrowserRouter>
        <Breadcrumbs routes={_dummyRoute} />
      </BrowserRouter>
    ));
    expect(component.html()).not.toBe(null);
  });

  it("should render proper breadcrumbs", () => {
    const component = mount(getCustomizedBreadCrumb(
      <MemoryRouter initialEntries={[samplePageInnerPath]}>
        <Breadcrumbs routes={_dummyRoute} />
      </MemoryRouter>
    ));
    expect(component.find(antBreadcrumLinkPath).length).toBe(3);
  });

  it("should render proper breadcrumbs", () => {
    const component = mount(getCustomizedBreadCrumb(
      <MemoryRouter initialEntries={["/sample-page/"]}>
        <Breadcrumbs routes={_dummyRoute} />
      </MemoryRouter>
    ));
    expect(component.find(antBreadcrumLinkPath).length).toBe(2);
  });

  it("should contain proper breadcrumb title", () => {
    const component = mount(getCustomizedBreadCrumb(
      <MemoryRouter initialEntries={[samplePageInnerPath]}>
        <Breadcrumbs routes={_dummyRoute} />
      </MemoryRouter>
    ));
    expect(component.find(antBreadcrumLinkPath).at(1).html()).toBe(
      `<a class=\"ant-breadcrumb-link\" href=\"/sample-page\">Sample Page</a>`
    );
  });

  it("should navigate on click", () => {
    const history = createMemoryHistory(samplePageInnerPath);
    history.push(samplePageInnerPath);
    const component = mount(getCustomizedBreadCrumb(
      <Router history={history} initialEntries={[samplePageInnerPath]}>
        <Breadcrumbs routes={_dummyRoute} />
      </Router>
    ));
    act(() => {
      component
        .find(antBreadcrumLinkPath)
        .at(1)
        .find("a")
        .at(0)
        .simulate("click");
    });

    expect(history.location.pathname).toBe("/sample-page");
  });
});
