@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';
@import '@{file-path}/_yjcommon';

@file-path: '../../../../../styles/';

.yjCheckInUploadBar {
  display: inline-flex;
  line-height: 220%;
  margin-left: 10px;
  width: 95%;

  p {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    width: 93%;

    svg {
      font-size: 14px;
      margin-right: 5px;
    }
  }

  button {
    background: none;
    border: none;
    box-shadow: none;
    padding: 0;

    svg {
      color: @color-danger;
      font-size: 14px;
    }
  }
}

.yjUploadBlock {
  display: inline-flex;
  position: relative;
  width: 70%;
}

.yjCheckInProgressBar {
  left: 10px;
  position: absolute;
  top: 20px;
  width: 100%;
}
