@import '~antd/es/style/themes/default.less';
@import '@{file-path}/assets/images/_imagepath';
@import '@{file-path}/_mixins';
@import '@{file-path}/_variables';

@file-path: '../../../styles/';

// Common Accordion

.yjCommonAccordian {
  border: 0;
  margin-bottom: 16px;

  .ant-collapse {
    border: none;

    .ant-collapse-item {
      box-shadow: none;
      margin-bottom: 10px;


      .ant-collapse-content {

        max-height: 352px;

      }
    }
  }
}

.yjNonAuthorizedDashboardWrapper {
  background-color: #fff;
  min-height: 25vh;
  padding: 1.5em;

  div {
    margin: 8% 0%;
    text-align: center;

    img {
      opacity: .5;
      width: 50px;
    }

    h1 {
      line-height: 120%;
      margin-bottom: 0;
    }
  }
}

.yjDashboardFileAreaIcon {

  background-color: transparent;
  border: none;
  box-shadow: none;
  color: #24303B;
  margin-left: 0;

  &:hover {
    background-color: transparent;
    color: #4b647a;
  }

  &:active {
    background-color: transparent;
  }

  &:focus {
    background-color: transparent;
  }
}