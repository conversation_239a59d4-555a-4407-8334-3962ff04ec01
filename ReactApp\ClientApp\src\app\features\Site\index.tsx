import { Button, Col, Form, FormInstance, Input, Row, Tooltip, Modal as AntModal, List, Collapse, Switch, Popconfirm } from 'antd';
import React, { useEffect, useState } from 'react';
import { PlusOutlined, ExclamationCircleOutlined, CloseOutlined, MailOutlined, UpOutlined, DownOutlined, SearchOutlined, InfoCircleOutlined } from '@ant-design/icons';
import { HiPhone } from 'react-icons/hi';
import { FiEdit } from 'react-icons/fi';
import { useForm } from 'antd/lib/form/Form';
import TextArea from 'antd/lib/input/TextArea';
import { formActionType } from '@app/utils';
import { AvoidWhitespace } from '@app/utils/regex';
import { required, typeWithPattern } from '../../components/forms/validators';
import styles from './index.module.less';
import Modal from '@app/components/Modal';
import Contact from '../Contact';
import InfinitySelect, { InfinitySelectGetOptions } from '@app/components/InfinitySelect';
import config from '@app/utils/config';
import { prevenPrecedingSpaces, prevenPrecedingSpacesOnPaste } from '@app/utils/forms';
import { OperationalServiceTypes } from '@iris/discovery.fe.client';
import { errorNotification } from '@app/utils/antNotifications';
import AddressDetails from '@app/features/Site/AddressDetails';
import { getSiteContacts } from '@app/api/contactService';
import { Rule } from 'antd/lib/form';
import { checkCrmSiteIdAvailability, checkSiteNameAvailability } from '@app/api/sitesServices';
import { getInfiniteRecords } from "@app/api/infiniteRecordsService";
import logger from "@app/utils/logger";
const { confirm } = AntModal;

export interface Site {
  action: formActionType;
  formRef?: FormInstance;
  onFinish?: (values: any) => void;
  onChange?: () => void;
  siteDetails?: any;
  siteData?: any;
  hasError?: {
    statusCode: number;
    message: string;
    errorCode: number;
  };
}

const layout = {
  labelCol: {
    span: 24,
  },
  wrapperCol: {
    span: 24,
  },
};

const MAX_LENGTH_FIFTY = 50;
const MAX_LENGTH_HUNDRED = 100;
const MAX_LENGTH_TWENTYFIVE = 25;

const SITE_NAME_EXSISTS_ERROR_CODE = 40002;
const CRM_SITE_ID_EXSISTS_ERROR_CODE = 40007;

export default ({ action, formRef, onChange, onFinish, siteData, hasError }: Site) => {
  const [displayAddContactModal, setDisplayAddContactModal] = useState<boolean>(false);
  const [displayEditContactModal, setDisplayEditContactModal] = useState<boolean>(false);
  const [contactFormChanged, setContactFormChanged] = useState<boolean>(false);
  const [channelId, setChannelId] = useState<string | undefined>(siteData?.channel?.value);
  const [contactForm] = useForm();
  const [selectedContactList, setSelectedContactList] = useState<Array<any>>([]);
  const [deletedPreSelectedContacts, setDeletedPreSelectedContacts] = useState<Array<any>>([]);
  const [updatedContacts, setUpdatedContacts] = useState<Array<any>>([]);
  const [loadedContacts, setLoadedContacts] = useState<Array<any>>([]);
  const [showDelete, setShowDelete] = useState<{ show: boolean; item: any }>({
    show: false,
    item: null,
  });
  const [updatedContactId, setUpdatedContactId] = useState();
  const [contacts, setContacts] = useState<Array<any>>([]);
  const initialIsActive = action === 'save' ? true : siteData.status.value === 1 || siteData.status.value === 3 ? true : false;
  const [isActive, setIsActive] = useState<boolean>(initialIsActive);

  useEffect(() => {
    if (hasError && ((hasError?.errorCode < 40000 && hasError.errorCode > 50000) || !hasError.errorCode)) errorNotification([''], hasError.message);
  }, [hasError]);

  useEffect(() => {
    if (siteData && action === 'edit') {
      setLoadedContacts(siteData.linkedContacts);
      getSiteContacts(siteData.siteId).then((data) => setContacts(data.data.records.map((e: any) => ({ ...e, id: e.contactId }))));
    }
  }, [siteData, action]);

  useEffect(() => {
    formRef?.setFieldsValue({
      active: isActive,
      deletedContacts: deletedPreSelectedContacts,
      updateContacts: updatedContacts,
      contacts: selectedContactList.map((tmpContact: any) => {
        if (tmpContact.id) {
          tmpContact = { ...tmpContact, id: tmpContact.id };
        }
        if (tmpContact.portalAccess) {
          tmpContact = {
            ...tmpContact,
            portalAccess: tmpContact.portalAccess ?? false,
          };
        }
        return tmpContact;
      }),
    });
  }, [deletedPreSelectedContacts, updatedContacts, selectedContactList, isActive]);

  const onCancelContact = () => {
    if (contactFormChanged) {
      confirm({
        title: 'Your details will not be saved. Are you sure you want to discard the changes?',
        className: 'yjContactsConfirmationPopup',
        icon: <ExclamationCircleOutlined />,
        okText: 'Discard',
        cancelText: 'CANCEL',
        onOk() {
          setDisplayAddContactModal(false);
          setDisplayEditContactModal(false);
          setContactFormChanged(false);
        },
      });
    } else {
      setDisplayAddContactModal(false);
      setDisplayEditContactModal(false);
    }
  };

  const mapNewContactData = (data: any) => {
    return {
      tmpId: Date.now(),
      firstName: data.firstName,
      lastName: data.lastName,
      email: data.email,
      contactNumber: data.contactNumber ?? '',
      countryCode: data.countryCode,
      portalAccess: data.portalAccess ?? false,
      country: data.country,
      address: data.address,
      city: data.city,
      state: data.state,
      zipCode: data.zipCode,
    };
  };

  const mapUpdateContactData = (data: any) => {
    return {
      id: updatedContactId,
      ...mapNewContactData(data),
    };
  };

  const onSaveContact = (isAddAnother: boolean) => {
    contactForm.validateFields().then((values) => {
      setSelectedContactList((contacts) => {
        return [...contacts, mapNewContactData(values)];
      });
      contactForm.resetFields();
      setContactFormChanged(false);
      if (!isAddAnother) setDisplayAddContactModal(false);
    });
  };

  const onUpdateContact = () => {
    if (!contactFormChanged) {
      setDisplayEditContactModal(false);
      return;
    }
    contactForm.validateFields().then((values) => {
      if (!updatedContactId) {
        setSelectedContactList((data) => {
          return [...data.filter((e) => e.tmpId != values.tmpId), values];
        });
      } else {
        setSelectedContactList((data) => {
          logger.debug('Site', 'onUpdateContact-selected contact list', {data, values});
          return data.filter((e) => e.email != values.email);
        });
        setUpdatedContacts((data) => {
          logger.debug('Site', 'onUpdateContact-updated contact list', {data, values});
          return [...data.filter((e) => e.id != updatedContactId), mapUpdateContactData(values)];
        });
      }
      contactForm.resetFields();
      setContactFormChanged(false);
      setDisplayEditContactModal(false);
    });
  };

  const addContactModal = () => {
    return (
      <Modal
        size="medium"
        visible={displayAddContactModal}
        key="discardPopup"
        title="CREATE CONTACT"
        closable={displayAddContactModal}
        destroyOnClose={true}
        onCancel={onCancelContact}
        footer={[
          <Button key="cancel" onClick={onCancelContact} type="default">
            CANCEL
          </Button>,
          <Button key="repeat" onClick={() => onSaveContact(true)} type="primary">
            CREATE AND ADD ANOTHER
          </Button>,
          <Button key="yes" onClick={() => onSaveContact(false)} type="primary">
            CREATE AND CLOSE
          </Button>,
        ]}
      >
        <Contact formRef={contactForm} contacts={[...selectedContactList, ...updatedContacts]} onFinish={() => onSaveContact(false)} onChange={() => setContactFormChanged(true)} />
      </Modal>
    );
  };

  const editContactModal = () => {
    return (
      <Modal
        size="medium"
        visible={displayEditContactModal}
        key="editContactModal"
        title="Update Contact"
        closable={displayEditContactModal}
        destroyOnClose={true}
        onCancel={onCancelContact}
        footer={[
          <Button key="cancel" onClick={onCancelContact} type="default">
            CANCEL
          </Button>,
          <Button key="yes" onClick={onUpdateContact} type="primary">
            UPDATE
          </Button>,
        ]}
      >
        <Contact
          formRef={contactForm}
          contacts={[...selectedContactList, ...updatedContacts]}
          onFinish={onUpdateContact}
          onChange={() => setContactFormChanged(true)}
          isEmailDiert={true}
        />
      </Modal>
    );
  };

  const removeContactModal = () => (
    <Modal
      size="small"
      visible={showDelete.show}
      title="Remove Contact"
      onCancel={() => setShowDelete({ show: false, item: null })}
      okText="REMOVE"
      cancelText="CANCEL"
      onOk={() => onRemoveContact()}
    >
      <p>Are you sure you want to remove the contact from the site?</p>
    </Modal>
  );

  const onRemoveContact = () => {
    const item = showDelete.item;
    const contactId = item.id ?? item.contactId;
    setUpdatedContacts((contact) => contact.filter((e) => e.id != contactId));
    if (loadedContacts.includes(contactId)) {
      setShowDelete({ show: false, item: null });
      return onRemovePreSelectedContact(contactId);
    }
    const currenntContacts = selectedContactList.filter((contact) => item != contact);
    setSelectedContactList([...currenntContacts]);
    onChange && onChange();
    setShowDelete({ show: false, item: null });
  };

  const onRemovePreSelectedContact = (contactId: any) => {
    onChange && onChange();
    setDeletedPreSelectedContacts((contacts) => [...contacts, contactId]);
  };

  const renderInfinityListItem = (item: any) => (
    <div className={styles.yjManageSitesContactList}>
      <div className={styles.yjManageSitesContactListItems}>
        <Row justify="space-between">
          <Col span={13} className={styles.yjManageSitesContactNameSection}>
            <Row justify="start">
              <Col span={1}>
                <div className={styles.yjManageSitesContactNameStatus}>
                  {action == 'edit' && selectedContactList.filter((e: any) => e.id === item.id).length > 0 && (
                    <div className={styles.YjManageSitesNewlyCreatedContactsStatus}></div>
                  )}
                  {updatedContacts.filter((e: any) => e.id === item.id).length > 0 && <div className={styles.YjManageSitesUpdatedContactsStatus}></div>}
                </div>
              </Col>
              <Col span={23} className={styles.yjManageSitesContactName}>
                <Tooltip title={item.lastName + ',' + item.firstName} placement={'topLeft'}>
                  <div className={styles.yjManageSitesPreviouslySelectedUsersListName}>
                    {item.lastName},{item.firstName}
                  </div>
                </Tooltip>

                <div className={styles.YJPortalUserTagSpace}>{item.portalAccess && <div className={styles.YJPortalUserTag}>Portal User</div>}</div>
              </Col>
            </Row>
          </Col>

          <Col span={6}>
            <Tooltip title={item.email} placement={'topLeft'}>
              <p>
                <MailOutlined />
                {item.email}
              </p>
            </Tooltip>
          </Col>
          <Col span={4}>
            <p>
              {item.contactNumber && (
                <>
                  <HiPhone />
                  {'+' + item.contactNumber}
                </>
              )}
            </p>
          </Col>
          <Col span={1}>
            <Tooltip title="Update">
              <FiEdit
                style={{ marginRight: 10, cursor: 'pointer' }}
                onClick={() => {
                  contactForm.setFieldsValue(item);
                  setUpdatedContactId(item.id);
                  setDisplayEditContactModal(true);
                }}
              />
            </Tooltip>
            <Tooltip title="Remove">
              <CloseOutlined onClick={() => setShowDelete({ show: true, item })} />
            </Tooltip>
          </Col>
        </Row>
      </div>
    </div>
  );

  const renderContactList = () => {
    let availableContacts = contacts.filter((contact) => !deletedPreSelectedContacts.find((e) => e == contact.id) && !updatedContacts.find((e) => e.id == contact.id));
    return action === 'edit' ? (
      <List
        locale={{ emptyText: 'No Records Available' }}
        itemLayout="horizontal"
        dataSource={[...selectedContactList.reverse(), ...updatedContacts.reverse(), ...availableContacts]}
        renderItem={(item) => (
          <List.Item style={{ padding: 0, margin: 0 }} key={item.id}>
            {renderInfinityListItem(item)}
          </List.Item>
        )}
      />
    ) : (
      <List
        locale={{ emptyText: 'No Records Available' }}
        itemLayout="horizontal"
        dataSource={[...updatedContacts.reverse(), ...selectedContactList.reverse()]}
        renderItem={(item) => <List.Item key={item.id}>{renderInfinityListItem(item)}</List.Item>}
      />
    );
  };

  const onChangeContacts = (event: any, contactList: Array<any> | undefined) => {
    onChange && onChange();
    if (contactList) {
      const value = contactList.filter((tmpContact) => !loadedContacts.includes(tmpContact.id)).find((tmpContact) => tmpContact.id === event);
      if (value) setSelectedContactList((data) => [...data, value]);

      if (deletedPreSelectedContacts.includes(event)) {
        setDeletedPreSelectedContacts((data) => data.filter((e) => e != event));
      }
    }
  };

  const checkSiteName = (name: any): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (siteData?.name == name) return resolve(true);
      if (channelId)
        checkSiteNameAvailability(channelId, name)
          .then((_e) => reject('Site Name already exists'))
          .catch((_e) => resolve(true));
    });
  };

  const siteNameAvailability = (): Rule => ({
    validator(_rule, value) {
      return value ? checkSiteName(value) : Promise.resolve();
    },
  });

  const checkCrmSiteId = (name: any): Promise<any> => {
    return new Promise((resolve, reject) => {
      if (siteData?.crmSiteId == name) return resolve(true);
      checkCrmSiteIdAvailability(name)
        .then((_e) => reject('The CRM SITE ID already exists'))
        .catch((_e) => resolve(true));
    });
  };

  const crmSiteIdAvailability = (): Rule => ({
    validator(_rule, value) {
      return value ? checkCrmSiteId(value) : Promise.resolve();
    },
  });


  const getPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    /**
     * Will add the keyvalue if dropdown still visible
     */
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const options = {
      limit: 10,
      offset: page - 1,
      ...transformFilters
    }
    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].channels, options)
      .then((res: any) => {
        logger.info('SideSelection', 'getPaginatedRecords', res.data);
        if (res.data) {
          return res.data;
        } else {
          logger.error('SideSelection', 'getPaginatedRecords', res.error);
          return []
        }
      })
      .catch((error: any) => {
        logger.error('SideSelection', 'getPaginatedRecords', error);

        return [];
      });
  };


  const renderBasicInfo = () => (
    <>
      <Col span={8}>
        <Form.Item
          label={'Site Name'}
          name="name"
          rules={[required, siteNameAvailability()]}
          validateStatus={hasError?.errorCode == SITE_NAME_EXSISTS_ERROR_CODE ? 'error' : undefined}
          help={hasError?.errorCode == SITE_NAME_EXSISTS_ERROR_CODE ? 'Site Name already exists' : undefined}
        >
          <Input maxLength={MAX_LENGTH_FIFTY} onKeyDown={(event) => prevenPrecedingSpaces(event)} onInput={(event) => prevenPrecedingSpacesOnPaste(event)} autoComplete="off" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item rules={[required]} label="Office" name="channelId">
          <InfinitySelect
            getPaginatedRecords={getPaginatedRecords}
            formatValue={(value) => {
              return `${value.name}`;
            }}
            notFoundContent="No Offices Available"
            notLoadContent="Failed to load values in office dropdown"
            value={channelId}
            onChange={(e) => setChannelId(e)}
            placeholder="Select an Office"
            disabled={action === 'edit' ? true : false}
            preSelected={
              action === 'edit' && siteData
                ? [
                  {
                    name: siteData.channel.name,
                    id: siteData.channel.value,
                  },
                ]
                : undefined
            }
          />
        </Form.Item>
      </Col>
      <Col span={8} className={'yjSiteDescription'}>
        <Form.Item label={'Description'} name="description" rules={[required]}>
          <TextArea
            maxLength={MAX_LENGTH_HUNDRED}
            onKeyDown={(event) => prevenPrecedingSpaces(event)}
            onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
            autoSize={{ minRows: 1, maxRows: 2 }}
            autoComplete="off"
          />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item
          rules={[required]}
          valuePropName="checked"
          label={
            <div>
              Status{' '}
              <Tooltip
                color="#0E678E"
                title={
                  isActive
                    ? 'If you inactivate this site you will not be able to upload documents to this Site. Also you will not able to share and receive documents to and from the File Area of this site.'
                    : 'If you activate this site you will be able to upload documents to this Site. Also you will able to share and receive documents to and from the File Area of this site.'
                }
              >
                <InfoCircleOutlined />
              </Tooltip>
            </div>
          }
          name="active"
          initialValue={action === 'save' ? true : undefined}
        >
          <Popconfirm
            overlayClassName={'yjStatusActiveInactiveConfirmationPopup'}
            title={`Are you sure you want to change the status?`}
            okText={'Yes'}
            cancelText={'No'}
            onConfirm={() => {
              setIsActive((pre) => !pre);
            }}
          >
            <Switch style={{ margin: 0 }} defaultChecked checked={isActive} />
          </Popconfirm>
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item rules={[required]} valuePropName="checked" label="Legal Hold" name="legalHold" initialValue={action === 'save' ? false : undefined}>
          <Switch style={{ margin: 0 }} />
        </Form.Item>
      </Col>
      <Col span={8} className={'yjSiteCrmSiteID'}>
        <Form.Item
          label={'CRM SITE ID'}
          rules={[crmSiteIdAvailability()]}
          name="crmSiteId"
          validateStatus={hasError?.errorCode == CRM_SITE_ID_EXSISTS_ERROR_CODE ? 'error' : undefined}
          help={hasError?.errorCode == CRM_SITE_ID_EXSISTS_ERROR_CODE ? 'The CRM SITE ID already exists' : undefined}
        >
          <Input
            maxLength={MAX_LENGTH_TWENTYFIVE}
            onKeyDown={(event) => prevenPrecedingSpaces(event)}
            onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
            autoComplete="off"
          />
        </Form.Item>
      </Col>
    </>
  );


  const getClientListPaginatedRecords = async (page: number, method: InfinitySelectGetOptions, searchValue?: string): Promise<Array<any>> => {
    const transformFilters: any = {};
    /**
     * Will add the keyvalue if dropdown still visible
     */
    if (searchValue) {
      transformFilters.search = searchValue;
    }

    const options = {
      limit: 10,
      offset: page - 1,
      ...transformFilters
    }
    return getInfiniteRecords(config.api[OperationalServiceTypes.MasterDataService].clientList, options)
      .then((res: any) => {
        logger.info('SideSelection', 'getPaginatedRecords', res.data);
        if (res.data) {
          return res.data;
        } else {
          logger.error('SideSelection', 'getPaginatedRecords', res.error);
          return []
        }
      })
      .catch((error: any) => {
        logger.error('SideSelection', 'getPaginatedRecords', error);

        return [];
      });
  };


  const renderContactsInfo = () => (
    <>
      <Form.Item hidden name="contacts">
        <></>
      </Form.Item>
      <Form.Item hidden name="deletedContacts">
        <></>
      </Form.Item>
      <Form.Item hidden name="updateContacts">
        <></>
      </Form.Item>
      <Col span={6}>
        <InfinitySelect
          getPaginatedRecords={getClientListPaginatedRecords}
          formatValue={(value) => {
            return `${value.fullName}`;
          }}
          notFoundContent="No matching records."
          notLoadContent="Failed to search Contacts."
          hideSelected={true}
          filterValues={(e: any) =>
            selectedContactList.filter((e2) => e2.id === e.id).length == 0 && !loadedContacts.filter((id) => !deletedPreSelectedContacts.includes(id)).includes(e.id)
          }
          onChange={onChangeContacts}
          placeholder="Search and add contacts"
          returnObject={true}
          suffixIcon={<SearchOutlined />}
          waitCharCount={1}
          onTop={true}
        />
      </Col>
      <Col style={{ padding: 0 }}>
        <Button style={{ margin: 0 }} type="primary" onClick={() => setDisplayAddContactModal(true)}>
          <PlusOutlined />
          Create Contact
        </Button>
      </Col>
      <Col span={24}>{renderContactList()}</Col>
    </>
  );

  const renderAdditionalInfo = () => (
    <>
      <Col span={8}>
        <Form.Item label={'Field 1'} name="field1">
          <Input maxLength={MAX_LENGTH_FIFTY} onKeyDown={(event) => prevenPrecedingSpaces(event)} onInput={(event) => prevenPrecedingSpacesOnPaste(event)} autoComplete="off" />
        </Form.Item>
      </Col>
      <Col span={8}>
        <Form.Item label={'Field 2'} name="field2" rules={[typeWithPattern('string', AvoidWhitespace)]}>
          <Input maxLength={MAX_LENGTH_FIFTY} onKeyDown={(event) => prevenPrecedingSpaces(event)} onInput={(event) => prevenPrecedingSpacesOnPaste(event)} autoComplete="off" />
        </Form.Item>
      </Col>
    </>
  );

  const renderAddressDetails = () => (
    <>
      {['Country', 'Address', 'City', 'State/province/region', 'Zip Code'].map((e) => (
        <Col span={8} key={e}>
          {/* <Tooltip placement="topLeft" title={'This feature is coming soon'} color="#134A82">
            <Form.Item label={e} name={e}>
              <Input
                maxLength={MAX_LENGTH_FIFTY}
                onKeyDown={(event) => prevenPrecedingSpaces(event)}
                onInput={(event) => prevenPrecedingSpacesOnPaste(event)}
                autoComplete="off"
                disabled
              />
            </Form.Item>
          </Tooltip> */}
        </Col>
      ))}
    </>
  );

  const generateCreateMode = () => (
    <>
      <h2 className="yjModuleSubHeading">Basic Information</h2>
      <Row gutter={24}>{renderBasicInfo()}</Row>
      <div className={'yjCommonAccordian'}>
        <Collapse expandIcon={({ isActive }) => (isActive ? <UpOutlined /> : <DownOutlined />)} bordered={false} defaultActiveKey={['1']} expandIconPosition="right">
          <Collapse.Panel
            header={
              <div className={styles.yjManageSitesAccordianHeader}>
                Contact(s)
                <div
                  style={{
                    display: 'inline',
                    marginLeft: '10px',
                    textTransform: 'capitalize',
                    fontSize: '11px',
                    fontWeight: 'normal',
                  }}
                >
                  {selectedContactList.length + updatedContacts.length > 0 ? selectedContactList.length + updatedContacts.length + ' Selected' : 'None Selected'}
                </div>
              </div>
            }
            key="1"
          >
            <Row gutter={24} justify="end" className="yjCreateSiteContactList">
              {renderContactsInfo()}
            </Row>
          </Collapse.Panel>
          <Collapse.Panel header={<div className={styles.yjManageSitesAccordianHeader}>Address details</div>} key="2">
            <Row gutter={24}>
              <AddressDetails />
            </Row>
          </Collapse.Panel>
          <Collapse.Panel header={<div className={styles.yjManageSitesAccordianHeader}>Additional Information</div>} key="3">
            <Row gutter={24}>{renderAdditionalInfo()}</Row>
          </Collapse.Panel>
        </Collapse>
      </div>
    </>
  );
  const generateEditMode = () => (
    <>
      <h2 className="yjModuleSubHeading">Basic Information</h2>
      <Row gutter={24}>
        <Col span={8} style={{ display: 'none' }}>
          <Form.Item label={'Site Id'} name="siteId">
            <Input disabled={true} />
          </Form.Item>
        </Col>
        {renderBasicInfo()}
        <Col span={24}>
          <Row gutter={24}>
            <Col span={8}>
              <Form.Item label={'Created By'} name="createdBy">
                <Input disabled={true} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label={'Created Date'} name="created">
                <Input disabled={true} />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label={'Modified Date'} name="modified">
                <Input disabled={true} />
              </Form.Item>
            </Col>
          </Row>
        </Col>
      </Row>
      <div className={'yjCommonAccordian'}>
        <Collapse expandIcon={({ isActive }) => (isActive ? <UpOutlined /> : <DownOutlined />)} bordered={false} defaultActiveKey={['1']} expandIconPosition="right">
          <Collapse.Panel
            header={
              <div className={styles.yjManageSitesAccordianHeader}>
                Contact(s)
                <div
                  style={{
                    display: 'inline',
                    marginLeft: '10px',
                    textTransform: 'capitalize',
                    fontSize: '11px',
                    fontWeight: 'normal',
                  }}
                >
                  {selectedContactList.length +
                    loadedContacts.filter((e) => !deletedPreSelectedContacts.includes(e) && !updatedContacts.find((item) => item.id == e)).length +
                    updatedContacts.length >
                    0
                    ? selectedContactList.length +
                    loadedContacts.filter((e) => !deletedPreSelectedContacts.includes(e) && !updatedContacts.find((item) => item.id == e)).length +
                    updatedContacts.length +
                    ' Selected'
                    : 'None Selected'}
                </div>
              </div>
            }
            key="1"
          >
            <Row justify="end" gutter={24}>
              {renderContactsInfo()}
            </Row>
          </Collapse.Panel>
          <Collapse.Panel header={<div className={styles.yjManageSitesAccordianHeader}>Address details</div>} key="2">
            <AddressDetails />
          </Collapse.Panel>
          <Collapse.Panel header={<div className={styles.yjManageSitesAccordianHeader}>Additional Information</div>} key="3">
            <Row gutter={24}>{renderAdditionalInfo()}</Row>
          </Collapse.Panel>
        </Collapse>
      </div>
    </>
  );

  return (
    <div className="yjManageSiteContainer">
      <div className={styles.yjCreateSiteWrapper}>
        {addContactModal()}
        {editContactModal()}
        {removeContactModal()}
        <Form form={formRef} key="createSiteForm" onFinish={onFinish} onChange={onChange} {...layout} layout="horizontal">
          {action === 'save' && generateCreateMode()}
          {action === 'edit' && generateEditMode()}
        </Form>
      </div>
    </div>
  );
};
