import {
  getLicenseDetails,
  getCompliances,
  getStatuses,
  getStorages,
  getUserCounts,
  getSupportLevels,
  createLicense,
  modifyLicense,
} from "../../api/licenseService";
import types from "../actionTypes/LicenseActionTypes";
import { AppThunk } from "@app/types/AppThunk";
import { getVerticals } from "@app/api/verticalService";
import { ValueType } from "@app/types/ValueType";
import { getIntegrations } from "@app/api/organizationService";
import { fetchModulesByVerticalId } from "./verticalActions";
import { OptionType } from "antd/lib/select";

const saveFailedTitle = "Failed to Save";
const dataLoadFailedTitle = "Failed to load data";

export function setLicenseDetails(licDetails: any) {
  return {
    type: types.SET_LICENSE_DETAILS,
    payload: licDetails,
  };
}

export function isDataFetchSuccessful(isSuccessful: boolean) {
  return {
    type: types.DATA_FETCH_SUCESSFUL,
    payload: isSuccessful,
  };
}

export function isOptionsFetched(isSuccessful: boolean) {
  return {
    type: types.OPTIONS_FETCH_SUCCESSFUL,
    payload: isSuccessful,
  };
}

export function hasErrored(error: any) {
  return {
    type: types.HAS_ERRORED,
    payload: error,
  };
}

export function setCompliances(compliances: [ValueType]) {
  return {
    type: types.SET_COMPLIANCES,
    payload: compliances,
  };
}

export function setStatuses(statuses: {
  statuses: OptionType;
  isEnabled: boolean;
}) {
  return {
    type: types.SET_STATUSES,
    payload: statuses,
  };
}

export function setVerticals(verticals: [ValueType]) {
  return {
    type: types.SET_VERTICALS,
    payload: verticals,
  };
}

export function setStorages(storages: [ValueType]) {
  return {
    type: types.SET_STORAGES,
    payload: storages,
  };
}

export function setUserCounts(userCounts: [ValueType]) {
  return {
    type: types.SET_USER_COUNTS,
    payload: userCounts,
  };
}

export function setSupportLevels(supportLevels: [ValueType]) {
  return {
    type: types.SET_SUPPORT_LEVELS,
    payload: supportLevels,
  };
}

export function setIntegrations(integrations: [ValueType]) {
  return {
    type: types.SET_INTEGRATIONS,
    payload: integrations,
  };
}

export function saveSuccessed(isSuccess: boolean) {
  return {
    type: types.SAVE_SUCCESSED,
    payload: isSuccess,
  };
}

export function fetchLicenseDetailsById(
  verticalId: number,
  licenseId: string | undefined
): AppThunk {
  return async (dispatch) => {
    try {
      hasErrored(null);
      dispatch(setLicenseDetails(null));
      if (licenseId) {
        dispatch(isDataFetchSuccessful(false));
        const { data: licData } = await getLicenseDetails(licenseId);
        dispatch(fetchModulesByVerticalId(licData.vertical.value));
        dispatch(setLicenseDetails(licData));
        dispatch(isDataFetchSuccessful(true));
      } else {
        dispatch(fetchModulesByVerticalId(verticalId));
        dispatch(isDataFetchSuccessful(true));
      }
    } catch (error) {
      const errorObj = {
        title: dataLoadFailedTitle,
        message: error?.response?.data?.message || error,
      };
      dispatch(hasErrored(errorObj));
    }
  };
}

export function fetchOptions(): AppThunk {
  return async (dispatch) => {
    dispatch(hasErrored(null));
    dispatch(isOptionsFetched(false));
    return Promise.all([
      dispatch(fetchCompliances()),
      dispatch(fetchVerticals()),
      dispatch(fetchStorages()),
      dispatch(fetchUserCounts()),
      dispatch(fetchSupportLevels()),
      dispatch(fetchIntegrations()),
    ])
      .then(() => dispatch(isOptionsFetched(true)))
      .catch((error) => {
        const errorObj = {
          title: dataLoadFailedTitle,
          message: error?.response?.data?.message || error,
        };
        dispatch(hasErrored(errorObj));
      });
  };
}

export function fetchCompliances(): AppThunk {
  return async (dispatch) => {
    const { data: compliances } = await getCompliances();
    dispatch(setCompliances(compliances));
  };
}

export function fetchStatuses(licenseId: string): AppThunk {
  return async (dispatch) => {
    const { data: statuses } = await getStatuses(licenseId);
    dispatch(setStatuses(statuses));
  };
}

export function fetchVerticals(): AppThunk {
  return async (dispatch) => {
    const { data: verticals } = await getVerticals();
    dispatch(setVerticals(verticals));
  };
}

export function fetchStorages(): AppThunk {
  return async (dispatch) => {
    const { data: storages } = await getStorages();
    dispatch(setStorages(storages));
  };
}

export function fetchUserCounts(): AppThunk {
  return async (dispatch) => {
    const { data: userCounts } = await getUserCounts();
    dispatch(setUserCounts(userCounts));
  };
}

export function fetchSupportLevels(): AppThunk {
  return async (dispatch) => {
    const { data: supportLevels } = await getSupportLevels();
    dispatch(setSupportLevels(supportLevels));
  };
}

export function fetchIntegrations(): AppThunk {
  return async (dispatch) => {
    const { data: supportLevels } = await getIntegrations();
    dispatch(setIntegrations(supportLevels));
  };
}

export function saveLicense(data: any): AppThunk {
  return async (dispatch) => {
    try {
      dispatch(hasErrored(null));
      dispatch(setLicenseDetails(data));
      dispatch(saveSuccessed(false));
      const res = await createLicense(data);
      dispatch(setLicenseDetails({ ...data, id: res.data.id }));
      dispatch(saveSuccessed(true));
    } catch (error) {
      const errorObj = {
        message: error?.response?.data?.message || error,
        title: saveFailedTitle,
      };
      dispatch(setLicenseDetails(data));
      dispatch(hasErrored(errorObj));
    }
  };
}

export function updateLicense(licenseId: string, data: any): AppThunk {
  return async (dispatch) => {
    try {
      dispatch(hasErrored(null));
      dispatch(setLicenseDetails(data));
      dispatch(saveSuccessed(false));
      await modifyLicense(licenseId, data);
      dispatch(setLicenseDetails({ ...data, licenseId }));
      dispatch(saveSuccessed(true));
    } catch (error) {
      const errorObj = {
        message: error?.response?.data?.message || error,
        title: saveFailedTitle,
      };
      dispatch(setLicenseDetails(data));
      dispatch(hasErrored(errorObj));
    }
  };
}
