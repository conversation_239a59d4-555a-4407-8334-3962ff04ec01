const EXTERNAL_USER_UPLOADED_FILES = "External user uploaded the file";
const REQUESTED_FILES = "Requested Files";
const ACCEPTED_FILES = "Accepted files ";
const ACCEPTED_AND_UPLOADED_FILES = "Uploaded files via acceptance.";
const REJECTED_FILES = "Rejected File";

const MOCK_ID1 = "82ce002c-a192-4a60-bbb4-a01b3ae9";
const MOCK_ID2 = "d0da58b0-41f4-40f6-a5e3-1fb0ce27031e";
const MOCK_REQUEST_NAME = "Requesting Tax Files for Year 2018";
const MOCK_CURRENT_EMPTY = {
    title: "-",
    files: "-",
    created: "-",
    modified: "-",
    createdBy: "-",
    expirationStatus: "-",
    expirationPeriod: "-",
    passwordProtected: "-",
    type: "-",
    size: "-",
    uploader: "-",
    uploadedDate: "-"
};
export const portalFileSectionHistoryMockData = [
    {
        id: MOCK_ID1,
        requestName: MOCK_REQUEST_NAME,
        userName: "External User",
        dateTime: "2020-07-21",
        action: EXTERNAL_USER_UPLOADED_FILES,
        changes: {
            previous: {
                id: MOCK_ID1,
                requestName: MOCK_REQUEST_NAME,
                files: "-",
                title: "-",
                type: "-",
                created: "-",
                createdBy: "-",
                size: "-",
                uploader: "-",
                uploadedDate: "-",
                emailStatus: "-"
            },
            current: {
                id: MOCK_ID1,
                requestName: MOCK_REQUEST_NAME,
                files: "1",
                title: "Tax Files 2018.xls",
                type: "XLS",
                created: "2020/11/10",
                createdBy: "Thomas",
                size: "3MB",
                uploader: "<EMAIL>",
                uploadedDate: "2020/11/11",
                emailStatus: "Successful"
            }
        }
    },
    {
        id: "330b201b-b681-4d44-a3fc-d09415bdf02b",
        requestName: "Requesting Tax Files",
        userName: "John",
        dateTime: "2020-10-20",
        action: REJECTED_FILES,
        changes: {
            previous: {
                title: "Tex File.Pdf",
                files: "0",
                created: "2020/10/25",
                modified: "-",
                createdBy: "John",
                expirationStatus: "Pending",
                expirationPeriod: "2020/12/01",
                passwordProtected: "No",
                type: "PDF",
                size: "3MB",
                uploader: "<EMAIL>",
                uploadedDate: "2020/11/14"

            },
            current: MOCK_CURRENT_EMPTY
        }
    },
    {
        id: MOCK_ID2,
        requestName: "Request for Files",
        userName: "Smith",
        dateTime: "2020-11-24",
        action: ACCEPTED_AND_UPLOADED_FILES,
        changes: {
            previous: {
                id: "-",
                title: "-",
                path: "-",
                level1Folder: "-",
                level2Folder: "-",
                status: "-",
                tags: [],
                year: "-",
                projects: [],
                type: "-",
                expirationStatus: "-",
                expirationDate: "-",
                description: "-",
                size: "-",
                assignee: "-",
                createdBy: "-",
                created: "-",
                fileCondition: "-",
                filePreview: "-",
                version: "-",
            },
            current: {
                id: "AA-00208",
                title: "File.Doc",
                path: "Main Folder > Folder 1 > Folder 2",
                level1Folder: "Folder 1",
                level2Folder: "Folder 1 > Folder 2",
                status: "Arrived",
                tags: ["Tax", "Accounting"],
                year: "2020",
                projects: [],
                type: "DOC",
                expirationStatus: "Pending",
                expirationDate: "2021/06/30",
                description: "-",
                size: "2MB",
                assignee: "Smith",
                createdBy: "Smith",
                created: "2020/10/10 02:30",
                fileCondition: "None",
                filePreview: "Display the preview",
                version: "Version 0",
            }
        }
    },
    {
        id: MOCK_ID2,
        requestName: "Request for Files",
        userName: "Smith",
        dateTime: "2020-11-14",
        action: ACCEPTED_FILES,
        changes: {
            previous: {
                title: "File.Doc",
                files: "0",
                created: "2020/10/15",
                modified: "-",
                createdBy: "Smith",
                expirationStatus: "Pending",
                expirationPeriod: "2020/10/31",
                passwordProtected: "No",
                type: "DOC",
                size: "2MB",
                uploader: "<EMAIL>",
                uploadedDate: "2020/10/08"
            },
            current: MOCK_CURRENT_EMPTY
        }
    },
    {
        id: MOCK_ID2,
        requestName: MOCK_REQUEST_NAME,
        userName: "Thomas",
        dateTime: "2020-10-14",
        action: REQUESTED_FILES,
        changes: {
            previous: {
                id: "-",
                requestName: "-",
                files: "-",
                created: "-",
                createdBy: "-",
                description: "-",
                from: "-",
                bccSender: "-",
                to: {
                    primaryContacts: [],
                    secondaryContacts: [],
                    internalUsers: []
                },
                cc: {
                    primaryContacts: [],
                    secondaryContacts: [],
                    internalUsers: []
                },
                bcc: {
                    primaryContacts: [],
                    secondaryContacts: [],
                    internalUsers: []
                },
                subject: "-",
                body: "-",
                link: "-",
                securityKey: "-",
                linkExpiresOn: "-",
            },
            current: {
                id: MOCK_ID2,
                requestName: MOCK_REQUEST_NAME,
                files: "-",
                created: "2020/11/10",
                createdBy: "Thomas",
                description: "-",
                from: "<EMAIL>",
                bccSender: "No",
                to: {
                    primaryContacts: ["Lacy", "Brian"],
                    secondaryContacts: [],
                    internalUsers: ["Crystal", "Smith", "Jack"]
                },
                cc: {
                    primaryContacts: [],
                    secondaryContacts: [],
                    internalUsers: []
                },
                bcc: {
                    primaryContacts: [],
                    secondaryContacts: [],
                    internalUsers: []
                },
                subject: "Request Files",
                body: "Hi All," +
                    " \nPlease upload the following documents via the link display below:" +
                    "\nDocument 1\nDocument 2\nDocument Upload Link: https://efvuwqd.lk\nBest Regards,\nThomas.",
                link: "Link sent",
                securityKey: "Not Added",
                linkExpiresOn: "-",
            },
        }
    },
];
