import React from "react";
import { shallow } from "enzyme";
import renderer from "react-test-renderer";

import EditCompliance from '../index';
import initTestSuite from "@app/utils/config/TestSuite";
import PageTitle from "../../../../../components/PageTitle";
import PageContent from "../../../../../components/PageContent";
import ComplianceManagement from "../../../../../components/forms/ComplianceManagement";

describe("EditCompliance Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render",() => {
        const eComponent = shallow(<EditCompliance />);
        expect(eComponent.html()).not.toBe(null);
    }) ;

    it("should create and match to snapshot",() => {
        const eComponent = renderer.create(<EditCompliance />).toJSON();
        expect(eComponent).toMatchSnapshot();
    });

    it("should have a PageTitle component",() => {
        const eComponent = shallow(<EditCompliance />);
        expect(eComponent.find(PageTitle)).toHaveLength(1);
    });

    it("should have a PageContent component",() => {
        const eComponent = shallow(<EditCompliance />);
        expect(eComponent.find(PageContent)).toHaveLength(1);
    });

    it("should have a ComplianceManagement component",() => {
        const eComponent = shallow(<EditCompliance />);
        expect(eComponent.find(ComplianceManagement)).toHaveLength(1);
    });
});



