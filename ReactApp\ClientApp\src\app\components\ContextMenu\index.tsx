import React from 'react';
import styles from './index.module.less';
import {
  ApartmentOutlined,
  AuditOutlined,
  ContainerOutlined,
  DeleteOutlined,
  DownloadOutlined,
  DragOutlined,
  FileDoneOutlined,
  LinkOutlined,
  SettingOutlined,
  EditOutlined,
  CopyOutlined,
  DisconnectOutlined,
  ShareAltOutlined
} from '@ant-design/icons';
import { RiDeleteBin6Line } from 'react-icons/ri';
import { MdOpenInNew } from 'react-icons/md';
import { PushpinFilled, PushpinOutlined } from "@ant-design/icons/lib";

export interface IContextMenu {
  positon: { x: any; y: any };
  visible: boolean;
  onDownloadClick?: () => void;
  onStatusClick?: () => void;
  onAssignClick?: () => void;
  onCheckoutClick?: () => void;
  onUndoCheckoutClicked?: () => void;
  onPropetiesClicked?: () => void;
  onDeleteClick?: () => void;
  onReCategorizeClick?: () => void;
  onReNameFilesClick?:()=>void;
  onMoveFilesClick?:()=>void;
  onCopyFilesClick?:()=>void;
  onPublishClick?: () => void;
  onUnpublishClick?: () => void;
  onToBeDeletedClick?: () => void;
  onCopyLink?: () => void;
  onPin?:()=> void;
  onUnPin?:()=> void;
  onLinkFilesClicked?:()=>void;
  onUnlinkFilesClicked?:()=>void;
  displayDownloadOption?: boolean;
  displayStatusOption?: boolean;
  displayAssignOption?: boolean;
  displayUndoCheckoutOption?: boolean;
  displayCheckoutOption?: boolean;
  displayPropetiesOption?: boolean;
  displayDeleteOption?: boolean;
  displayReCategorizeOption?: boolean;
  displayEmailOption?: boolean;
  displayMoveOption?: boolean;
  displayRenameOption?: boolean;
  displayCopyOption?: boolean;
  displayCopyLinkOption?: boolean;
  dispalyPropertiesOption?: boolean;
  displayPublishOption?: boolean;
  displayToBeDeleted?: boolean;
  displayLinkOption?: boolean;
  displayPinUnpinOption?: boolean;
  hasSelectedLinkFiles?: boolean;
  hasSelectedPublishedFiles?: boolean;
}

export default ({
  positon,
  visible,
  onDownloadClick,
  onStatusClick,
  onAssignClick,
  onCheckoutClick,
  onUndoCheckoutClicked,
  onPropetiesClicked,
  onDeleteClick,
  onReCategorizeClick,
  onReNameFilesClick,
  onMoveFilesClick,
  onCopyFilesClick,
  onPublishClick,
  onUnpublishClick,
  onToBeDeletedClick,
  onCopyLink,
  onPin,
  onUnPin,
  onLinkFilesClicked,
  onUnlinkFilesClicked,
  displayDownloadOption = true,
  displayStatusOption = true,
  displayAssignOption = true,
  displayUndoCheckoutOption = true,
  displayCheckoutOption = true,
  displayPropetiesOption = true,
  displayDeleteOption = true,
  displayReCategorizeOption = true,
  displayEmailOption = true,
  displayMoveOption = true,
  displayCopyOption = true,
  displayRenameOption = true,
  dispalyPropertiesOption = true,
  displayPublishOption = true,
  displayToBeDeleted = true,
  displayCopyLinkOption = true,
  displayLinkOption = true,
  hasSelectedLinkFiles = false,
  hasSelectedPublishedFiles = false,
  displayPinUnpinOption = false,
}: IContextMenu) => {
  return visible ? (
    <ul className={styles.yjContextMenu} style={{ left: `${positon?.x}px`, top: `${positon?.y}px` }}>
      <li hidden={!displayDownloadOption} onClick={onDownloadClick}>
        <DownloadOutlined />
        Download
      </li>
      {/* <Tooltip placement="topLeft" title={'This feature is coming soon'} color="#78bf59">
        <li hidden={!displayEmailOption}>
          <MailOutlined />
          Email
        </li>
      </Tooltip> */}
      <li hidden={!displayAssignOption} onClick={onAssignClick}>
        <AuditOutlined />
        Assign
      </li>
      <li hidden={!displayStatusOption} onClick={onStatusClick}>
        <FileDoneOutlined />
        Status
      </li>
     
        <li hidden={!displayMoveOption} onClick={onMoveFilesClick}>
          <DragOutlined />
          Move
        </li>
      
      <li hidden={!displayReCategorizeOption} onClick={onReCategorizeClick}>
        <ApartmentOutlined />
        Re-Categorize
      </li>
    
      <li hidden={!displayRenameOption} onClick={onReNameFilesClick}>
        <EditOutlined />
        Rename
      </li>
     
    
        <li hidden={!displayCopyOption} onClick={onCopyFilesClick}>
          <CopyOutlined />
          Copy
        </li>
     
      <li hidden={!displayUndoCheckoutOption} onClick={onUndoCheckoutClicked}>
        <DragOutlined />
        Undo-Checkout
      </li>
      <li hidden={!displayCheckoutOption} onClick={onCheckoutClick}>
        <ContainerOutlined />
        Check-out
      </li>
      {hasSelectedPublishedFiles ? (
        <li hidden={!displayPublishOption} onClick={onPublishClick}>
          <MdOpenInNew />
          Publish
        </li>
      ) : (
        <li hidden={!displayPublishOption} onClick={onUnpublishClick}>
          <MdOpenInNew style={{ transform: 'rotate(180deg)' }} />
          Unpublish
        </li>
      )}
      <li onClick={onDeleteClick} hidden={!displayDeleteOption}>
        <DeleteOutlined />
        Delete
      </li>
      <li hidden={!displayPropetiesOption} className="yJFileAreaRow" onClick={onPropetiesClicked}>
        <SettingOutlined className="yJFileAreaRow" />
        Properties
      </li>
      <li hidden={!displayToBeDeleted} className="yJFileAreaRow" onClick={onToBeDeletedClick}>
        <RiDeleteBin6Line className="yJFileAreaRow" />
        To be Deleted
      </li>
      <li hidden={!displayCopyLinkOption} className="yJFileAreaRow" onClick={onCopyLink}>
        <ShareAltOutlined className="yJFileAreaRow" />
        Copy Link
      </li>
      <li className="yJFileAreaRow" hidden={!displayPinUnpinOption} onClick={onPin}>
        <PushpinOutlined className="yJFileAreaRow" />
        Pin
      </li>
      <li className="yJFileAreaRow" hidden={!displayPinUnpinOption} onClick={onUnPin}>
          <PushpinFilled className="yJFileAreaRow" />
          Unpin
      </li>
      {hasSelectedLinkFiles ? (
        <li hidden={!displayLinkOption} className='yJFileAreaRow' onClick={onLinkFilesClicked}>
        <LinkOutlined className="yJFileAreaRow"/>
          Link
        </li>
      ) : (
        <li hidden={!displayLinkOption} className='yJFileAreaRow' onClick={onUnlinkFilesClicked}>
          <DisconnectOutlined className="yJFileAreaRow" />
          Unlink
        </li>
      )}

    </ul>
  ) : (
    <></>
  );
};
