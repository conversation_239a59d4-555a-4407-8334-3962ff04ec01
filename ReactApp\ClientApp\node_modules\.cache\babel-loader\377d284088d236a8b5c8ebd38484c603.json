{"ast": null, "code": "import \"antd/es/button/style\";\nimport _Button from \"antd/es/button\";\nimport \"antd/es/select/style\";\nimport _Select from \"antd/es/select\";\nimport \"antd/es/spin/style\";\nimport _Spin from \"antd/es/spin\";\nvar _jsxFileName = \"D:\\\\Zone24x7\\\\Workspaces\\\\CICAL\\\\FrontEnd-Internal\\\\ReactApp\\\\ClientApp\\\\src\\\\app\\\\components\\\\InfinitySelect\\\\index.tsx\";\nimport React, { useEffect, useRef, useState } from \"react\";\nimport { DeleteOutlined } from \"@ant-design/icons\";\nimport config from \"@app/utils/config\";\nimport debounce from \"lodash/debounce\";\nimport styles from \"./index.module.less\";\nimport { uniqueArray } from \"../../utils/array\";\nimport logger from \"@app/utils/logger\";\nconst DEFAULT_VIEW_SIZE = 15;\nexport default (props => {\n  const dropdown = useRef({});\n  const [loading, setLoading] = useState(false);\n  const [isEndOfScroll, setIsEndOfScroll] = useState(false);\n  const [isBottom, setIsBottom] = useState(false); // const [values, setValues] = useState<Array<string | number>>([]);\n\n  const [options, setOptions] = useState([]);\n  const [keyValue, setKeyValue] = useState(\"\");\n  const [currentPage, setCurrentPage] = useState(1);\n  const [pageCount, setPageCount] = useState(1);\n  window.addEventListener(\"keydown\", event => {\n    if (event.key === \"Backspace\" && !event.target.value) {\n      event.stopPropagation();\n    }\n  }, true);\n\n  const fetchInitialData = () => {\n    setLoading(true);\n    props.getPaginatedRecords(1, 'fetch', keyValue).then(res => {\n      setOptions(res.records);\n      setPageCount(res.pageCount);\n      setCurrentPage(res.pageNumber);\n      setLoading(false);\n    });\n  };\n\n  useEffect(() => {\n    fetchInitialData();\n  }, []);\n\n  const loadMore = () => {\n    setIsEndOfScroll(false);\n    setLoading(true);\n    props.getPaginatedRecords(currentPage + 1, \"load\", keyValue).then(res => {\n      console.info('loadMore', uniqueArray([...options, ...res.records]));\n      setOptions(uniqueArray([...options, ...res.records]));\n      setPageCount(res.pageCount);\n      setCurrentPage(res.pageNumber);\n      setLoading(false);\n    });\n  };\n\n  const debounceFetcher = React.useMemo(() => {\n    const loadOptions = async keyVal => {\n      if (props.waitCharCount && keyVal.length < props.waitCharCount) {\n        setKeyValue(keyVal);\n        return;\n      }\n\n      setLoading(true);\n      props.getPaginatedRecords(1, \"fetch\", keyVal).then(res => {\n        console.info('debounceFetcher', options, uniqueArray([...res.records, ...options]));\n        setOptions(uniqueArray([...res.records, ...options]));\n        setLoading(false);\n      });\n    };\n\n    return debounce(loadOptions, config.inputDebounceInterval);\n  }, [config.inputDebounceInterval, options]);\n\n  const onVisibleChange = visibleState => {\n    if (visibleState && props.mode) {\n      setOptions([]);\n      setKeyValue('');\n      setCurrentPage(1);\n      setPageCount(1);\n      setIsEndOfScroll(false);\n      setIsBottom(false);\n      setLoading(false);\n      props.getPaginatedRecords(1, 'fetch', '').then(res => {\n        setOptions(res.records);\n        setPageCount(res.pageCount);\n        setCurrentPage(res.pageNumber);\n      });\n    } else if (keyValue && !visibleState && props.mode) {\n      setLoading(true);\n      props.getPaginatedRecords(1, 'fetch', keyValue).then(res => {\n        console.info('onVisibleChange', uniqueArray([...options, ...res.records]));\n        setOptions(uniqueArray([...options, ...res.records]));\n        setPageCount(res.pageCount);\n        setCurrentPage(res.pageNumber);\n        setLoading(false);\n      });\n    }\n  };\n\n  const onScroll = event => {\n    var _dropdown$current;\n\n    setIsBottom(event.target.scrollHeight - event.target.scrollTop === event.target.clientHeight);\n    const lastElement = document.getElementById(options ? options[(options || []).length - 1].id : \"null\");\n    const containerTop = dropdown ? dropdown === null || dropdown === void 0 ? void 0 : dropdown.current.getBoundingClientRect().top : null;\n    const lastElementTopPos = (lastElement === null || lastElement === void 0 ? void 0 : lastElement.getBoundingClientRect().top) - containerTop;\n    const containerHeight = dropdown === null || dropdown === void 0 ? void 0 : (_dropdown$current = dropdown.current) === null || _dropdown$current === void 0 ? void 0 : _dropdown$current.getBoundingClientRect().height;\n    logger.debug('GenericDataTable', 'Infinity select - on scroll', {\n      pageCount,\n      currentPage\n    });\n\n    if (lastElementTopPos - DEFAULT_VIEW_SIZE < containerHeight && pageCount > currentPage && !loading) {\n      loadMore();\n    } else {\n      if (pageCount === currentPage) {\n        setIsEndOfScroll(true);\n      }\n    }\n  };\n\n  const handleBlur = () => {\n    props.getPaginatedRecords(1, 'fetch', '').then(res => {\n      console.info('handleBlur', options, uniqueArray([...options, ...res.records]));\n      setKeyValue('');\n      setOptions(uniqueArray([...options, ...res.records]));\n      setPageCount(res.pageCount);\n      setCurrentPage(res.pageNumber);\n    });\n  };\n\n  return /*#__PURE__*/React.createElement(React.Fragment, null, /*#__PURE__*/React.createElement(_Select, {\n    showSearch: true,\n    showArrow: true,\n    value: props.value // Use props.value instead of internal state\n    ,\n    filterOption: false,\n    disabled: props.disabled,\n    notFoundContent: loading ? /*#__PURE__*/React.createElement(_Spin, {\n      size: \"small\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 185,\n        columnNumber: 40\n      }\n    }) : props.notFoundContent,\n    mode: props.mode,\n    onSearch: e => {\n      debounceFetcher(e);\n    },\n    onFocus: () => {},\n    onBlur: () => {\n      setLoading(true);\n      setIsEndOfScroll(false);\n      handleBlur();\n    },\n    onChange: (value, option) => {\n      console.info('OnChange', value, option);\n\n      if (props.returnObject) {\n        // Check if value is an array (multiple selection) or not (single selection)\n        if (!Array.isArray(value)) {\n          // Single selection\n          const selectedOption = options.find(opt => opt.id === value);\n          props.onChange(selectedOption || value);\n        } else {\n          // Multiple selection\n          const selectedObjects = value.map(val => options.find(opt => opt.id === val) || val);\n          props.onChange(value, selectedObjects);\n        }\n      } else {\n        props.onChange(value);\n      }\n    },\n    onDropdownVisibleChange: e => onVisibleChange(e),\n    defaultValue: props.defaultValues,\n    onPopupScroll: e => onScroll(e),\n    getPopupContainer: trigger => trigger.parentNode,\n    placeholder: props.placeholder,\n    maxTagCount: props.maxTagCount // maxTagPlaceholder={(e) =>\n    //   props.maxTagPlaceHolder && props.maxTagPlaceHolder(e)\n    // }\n    ,\n    listHeight: 200,\n    dropdownRender: menu => /*#__PURE__*/React.createElement(\"div\", {\n      ref: dropdown,\n      id: \"dropdown\",\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 228,\n        columnNumber: 17\n      }\n    }, menu, /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.yjInfinityLoadMore,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 230,\n        columnNumber: 19\n      }\n    }, /*#__PURE__*/React.createElement(_Spin, {\n      size: \"small\",\n      spinning: loading,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 231,\n        columnNumber: 21\n      }\n    }), isEndOfScroll && isBottom && /*#__PURE__*/React.createElement(\"div\", {\n      className: styles.yjInfinityScrollText,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 233,\n        columnNumber: 25\n      }\n    }, /*#__PURE__*/React.createElement(\"p\", {\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 234,\n        columnNumber: 27\n      }\n    }, \"You have seen it all !\")))),\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 179,\n      columnNumber: 9\n    }\n  }, options === null || options === void 0 ? void 0 : options.map(value => {\n    var _props$excludeList;\n\n    return !((_props$excludeList = props.excludeList) === null || _props$excludeList === void 0 ? void 0 : _props$excludeList.includes(value.id)) && /*#__PURE__*/React.createElement(_Select.Option, {\n      key: value.id,\n      id: value.id,\n      value: value.id,\n      __self: this,\n      __source: {\n        fileName: _jsxFileName,\n        lineNumber: 244,\n        columnNumber: 21\n      }\n    }, props.formatValue(value));\n  })), props.isDelete && /*#__PURE__*/React.createElement(_Button, {\n    onClick: () => {\n      // setValues([]);\n      props.onChange([]);\n    },\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 252,\n      columnNumber: 13\n    }\n  }, /*#__PURE__*/React.createElement(DeleteOutlined, {\n    __self: this,\n    __source: {\n      fileName: _jsxFileName,\n      lineNumber: 258,\n      columnNumber: 15\n    }\n  })));\n});", "map": {"version": 3, "sources": ["D:/Zone24x7/Workspaces/CICAL/FrontEnd-Internal/ReactApp/ClientApp/src/app/components/InfinitySelect/index.tsx"], "names": ["React", "useEffect", "useRef", "useState", "DeleteOutlined", "config", "debounce", "styles", "uniqueArray", "logger", "DEFAULT_VIEW_SIZE", "props", "dropdown", "loading", "setLoading", "isEndOfScroll", "setIsEndOfScroll", "isBottom", "setIsBottom", "options", "setOptions", "keyValue", "setKeyValue", "currentPage", "setCurrentPage", "pageCount", "setPageCount", "window", "addEventListener", "event", "key", "target", "value", "stopPropagation", "fetchInitialData", "getPaginatedRecords", "then", "res", "records", "pageNumber", "loadMore", "console", "info", "deboun<PERSON><PERSON><PERSON><PERSON>", "useMemo", "loadOptions", "keyVal", "waitCharCount", "length", "inputDebounceInterval", "onVisibleChange", "visibleState", "mode", "onScroll", "scrollHeight", "scrollTop", "clientHeight", "lastElement", "document", "getElementById", "id", "containerTop", "current", "getBoundingClientRect", "top", "lastElementTopPos", "containerHeight", "height", "debug", "handleBlur", "disabled", "notFoundContent", "e", "option", "returnObject", "Array", "isArray", "selectedOption", "find", "opt", "onChange", "selectedObjects", "map", "val", "defaultValues", "trigger", "parentNode", "placeholder", "maxTag<PERSON>ount", "menu", "yjInfinityLoadMore", "yjInfinityScrollText", "excludeList", "includes", "formatValue", "isDelete"], "mappings": ";;;;;;;AAAA,OAAOA,KAAP,IAA2BC,SAA3B,EAAsCC,MAAtC,EAA8CC,QAA9C,QAA+D,OAA/D;AAEA,SAASC,cAAT,QAA+B,mBAA/B;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AACA,OAAOC,QAAP,MAAqB,iBAArB;AACA,OAAOC,MAAP,MAAmB,qBAAnB;AACA,SAASC,WAAT,QAA4B,mBAA5B;AACA,OAAOC,MAAP,MAAmB,mBAAnB;AAoCA,MAAMC,iBAAiB,GAAG,EAA1B;AAEA,gBAAgBC,KAAD,IAAsB;AACnC,QAAMC,QAAQ,GAAGV,MAAM,CAAC,EAAD,CAAvB;AACA,QAAM,CAACW,OAAD,EAAUC,UAAV,IAAwBX,QAAQ,CAAU,KAAV,CAAtC;AACA,QAAM,CAACY,aAAD,EAAgBC,gBAAhB,IAAoCb,QAAQ,CAAU,KAAV,CAAlD;AACA,QAAM,CAACc,QAAD,EAAWC,WAAX,IAA0Bf,QAAQ,CAAU,KAAV,CAAxC,CAJmC,CAKnC;;AACA,QAAM,CAACgB,OAAD,EAAUC,UAAV,IAAwBjB,QAAQ,CAAa,EAAb,CAAtC;AACA,QAAM,CAACkB,QAAD,EAAWC,WAAX,IAA0BnB,QAAQ,CAAS,EAAT,CAAxC;AACA,QAAM,CAACoB,WAAD,EAAcC,cAAd,IAAgCrB,QAAQ,CAAS,CAAT,CAA9C;AACA,QAAM,CAACsB,SAAD,EAAYC,YAAZ,IAA4BvB,QAAQ,CAAS,CAAT,CAA1C;AAEAwB,EAAAA,MAAM,CAACC,gBAAP,CACI,SADJ,EAEKC,KAAD,IAAgB;AACd,QAAIA,KAAK,CAACC,GAAN,KAAc,WAAd,IAA6B,CAACD,KAAK,CAACE,MAAN,CAAaC,KAA/C,EAAsD;AACpDH,MAAAA,KAAK,CAACI,eAAN;AACD;AACF,GANL,EAOI,IAPJ;;AAUA,QAAMC,gBAAgB,GAAG,MAAM;AAC7BpB,IAAAA,UAAU,CAAC,IAAD,CAAV;AACAH,IAAAA,KAAK,CAACwB,mBAAN,CAA0B,CAA1B,EAA6B,OAA7B,EAAsCd,QAAtC,EAAgDe,IAAhD,CAAsDC,GAAD,IAAc;AACjEjB,MAAAA,UAAU,CAACiB,GAAG,CAACC,OAAL,CAAV;AACAZ,MAAAA,YAAY,CAACW,GAAG,CAACZ,SAAL,CAAZ;AACAD,MAAAA,cAAc,CAACa,GAAG,CAACE,UAAL,CAAd;AACAzB,MAAAA,UAAU,CAAC,KAAD,CAAV;AACD,KALD;AAMD,GARD;;AAUAb,EAAAA,SAAS,CAAC,MAAM;AACdiC,IAAAA,gBAAgB;AACjB,GAFQ,EAEN,EAFM,CAAT;;AAIA,QAAMM,QAAQ,GAAG,MAAM;AACrBxB,IAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACAF,IAAAA,UAAU,CAAC,IAAD,CAAV;AACAH,IAAAA,KAAK,CAACwB,mBAAN,CAA0BZ,WAAW,GAAG,CAAxC,EAA2C,MAA3C,EAAmDF,QAAnD,EAA6De,IAA7D,CAAmEC,GAAD,IAAc;AAC9EI,MAAAA,OAAO,CAACC,IAAR,CAAa,UAAb,EAAwBlC,WAAW,CAAC,CAAC,GAAGW,OAAJ,EAAa,GAAGkB,GAAG,CAACC,OAApB,CAAD,CAAnC;AACAlB,MAAAA,UAAU,CAACZ,WAAW,CAAC,CAAC,GAAGW,OAAJ,EAAa,GAAGkB,GAAG,CAACC,OAApB,CAAD,CAAZ,CAAV;AACAZ,MAAAA,YAAY,CAACW,GAAG,CAACZ,SAAL,CAAZ;AACAD,MAAAA,cAAc,CAACa,GAAG,CAACE,UAAL,CAAd;AACAzB,MAAAA,UAAU,CAAC,KAAD,CAAV;AACD,KAND;AAOD,GAVD;;AAYA,QAAM6B,eAAe,GAAG3C,KAAK,CAAC4C,OAAN,CAAc,MAAM;AAC1C,UAAMC,WAAW,GAAG,MAAOC,MAAP,IAA0B;AAC5C,UAAInC,KAAK,CAACoC,aAAN,IAAuBD,MAAM,CAACE,MAAP,GAAgBrC,KAAK,CAACoC,aAAjD,EAAgE;AAC9DzB,QAAAA,WAAW,CAACwB,MAAD,CAAX;AACA;AACD;;AACDhC,MAAAA,UAAU,CAAC,IAAD,CAAV;AACAH,MAAAA,KAAK,CAACwB,mBAAN,CAA0B,CAA1B,EAA6B,OAA7B,EAAsCW,MAAtC,EAA8CV,IAA9C,CAAoDC,GAAD,IAAc;AAC/DI,QAAAA,OAAO,CAACC,IAAR,CAAa,iBAAb,EAA+BvB,OAA/B,EAAuCX,WAAW,CAAC,CAAC,GAAG6B,GAAG,CAACC,OAAR,EAAgB,GAAGnB,OAAnB,CAAD,CAAlD;AAEAC,QAAAA,UAAU,CAACZ,WAAW,CAAC,CAAC,GAAG6B,GAAG,CAACC,OAAR,EAAiB,GAAGnB,OAApB,CAAD,CAAZ,CAAV;AACAL,QAAAA,UAAU,CAAC,KAAD,CAAV;AACD,OALD;AAMD,KAZD;;AAaA,WAAOR,QAAQ,CAACuC,WAAD,EAAcxC,MAAM,CAAC4C,qBAArB,CAAf;AACD,GAfuB,EAerB,CAAC5C,MAAM,CAAC4C,qBAAR,EAA+B9B,OAA/B,CAfqB,CAAxB;;AAiBA,QAAM+B,eAAe,GAAIC,YAAD,IAA2B;AACjD,QAAIA,YAAY,IAAIxC,KAAK,CAACyC,IAA1B,EAAgC;AAC5BhC,MAAAA,UAAU,CAAC,EAAD,CAAV;AACAE,MAAAA,WAAW,CAAC,EAAD,CAAX;AACAE,MAAAA,cAAc,CAAC,CAAD,CAAd;AACAE,MAAAA,YAAY,CAAC,CAAD,CAAZ;AACAV,MAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACAE,MAAAA,WAAW,CAAC,KAAD,CAAX;AACAJ,MAAAA,UAAU,CAAC,KAAD,CAAV;AAEFH,MAAAA,KAAK,CAACwB,mBAAN,CAA0B,CAA1B,EAA6B,OAA7B,EAAsC,EAAtC,EAA0CC,IAA1C,CAAgDC,GAAD,IAAc;AAC3DjB,QAAAA,UAAU,CAACiB,GAAG,CAACC,OAAL,CAAV;AACAZ,QAAAA,YAAY,CAACW,GAAG,CAACZ,SAAL,CAAZ;AACAD,QAAAA,cAAc,CAACa,GAAG,CAACE,UAAL,CAAd;AACF,OAJA;AAKD,KAdD,MAcO,IAAIlB,QAAQ,IAAI,CAAC8B,YAAb,IAA6BxC,KAAK,CAACyC,IAAvC,EAA6C;AAClDtC,MAAAA,UAAU,CAAC,IAAD,CAAV;AACAH,MAAAA,KAAK,CAACwB,mBAAN,CAA0B,CAA1B,EAA6B,OAA7B,EAAqCd,QAArC,EAA+Ce,IAA/C,CAAqDC,GAAD,IAAc;AAChEI,QAAAA,OAAO,CAACC,IAAR,CAAa,iBAAb,EAA+BlC,WAAW,CAAC,CAAC,GAAGW,OAAJ,EAAa,GAAGkB,GAAG,CAACC,OAApB,CAAD,CAA1C;AAEAlB,QAAAA,UAAU,CAACZ,WAAW,CAAC,CAAC,GAAGW,OAAJ,EAAa,GAAGkB,GAAG,CAACC,OAApB,CAAD,CAAZ,CAAV;AACAZ,QAAAA,YAAY,CAACW,GAAG,CAACZ,SAAL,CAAZ;AACAD,QAAAA,cAAc,CAACa,GAAG,CAACE,UAAL,CAAd;AACAzB,QAAAA,UAAU,CAAC,KAAD,CAAV;AACD,OAPD;AAQD;AACF,GA1BD;;AA4BA,QAAMuC,QAAQ,GAAIxB,KAAD,IAAgB;AAAA;;AAC/BX,IAAAA,WAAW,CACPW,KAAK,CAACE,MAAN,CAAauB,YAAb,GAA4BzB,KAAK,CAACE,MAAN,CAAawB,SAAzC,KACA1B,KAAK,CAACE,MAAN,CAAayB,YAFN,CAAX;AAIA,UAAMC,WAAgB,GAAGC,QAAQ,CAACC,cAAT,CACrBxC,OAAO,GAAGA,OAAO,CAAC,CAACA,OAAO,IAAI,EAAZ,EAAgB6B,MAAhB,GAAyB,CAA1B,CAAP,CAAoCY,EAAvC,GAA4C,MAD9B,CAAzB;AAGA,UAAMC,YAAY,GAAGjD,QAAQ,GACvBA,QADuB,aACvBA,QADuB,uBACvBA,QAAQ,CAAEkD,OAAV,CAAkBC,qBAAlB,GAA0CC,GADnB,GAEvB,IAFN;AAGA,UAAMC,iBAAiB,GACnB,CAAAR,WAAW,SAAX,IAAAA,WAAW,WAAX,YAAAA,WAAW,CAAEM,qBAAb,GAAqCC,GAArC,IAA2CH,YAD/C;AAEA,UAAMK,eAAe,GAAGtD,QAAH,aAAGA,QAAH,4CAAGA,QAAQ,CAAEkD,OAAb,sDAAG,kBAAmBC,qBAAnB,GAA2CI,MAAnE;AACA1D,IAAAA,MAAM,CAAC2D,KAAP,CAAa,kBAAb,EAAgC,6BAAhC,EAA8D;AAAC3C,MAAAA,SAAD;AAAWF,MAAAA;AAAX,KAA9D;;AACA,QACI0C,iBAAiB,GAAGvD,iBAApB,GAAwCwD,eAAxC,IACAzC,SAAS,GAAGF,WADZ,IAEA,CAACV,OAHL,EAIE;AACA2B,MAAAA,QAAQ;AACT,KAND,MAMO;AACL,UAAIf,SAAS,KAAKF,WAAlB,EAA+B;AAC7BP,QAAAA,gBAAgB,CAAC,IAAD,CAAhB;AACD;AACF;AACF,GA1BD;;AA4BA,QAAMqD,UAAU,GAAG,MAAM;AACvB1D,IAAAA,KAAK,CAACwB,mBAAN,CAA0B,CAA1B,EAA6B,OAA7B,EAAsC,EAAtC,EAA0CC,IAA1C,CAAgDC,GAAD,IAAc;AAC3DI,MAAAA,OAAO,CAACC,IAAR,CAAa,YAAb,EAA0BvB,OAA1B,EAAkCX,WAAW,CAAC,CAAC,GAAGW,OAAJ,EAAa,GAAGkB,GAAG,CAACC,OAApB,CAAD,CAA7C;AAEAhB,MAAAA,WAAW,CAAC,EAAD,CAAX;AACAF,MAAAA,UAAU,CAACZ,WAAW,CAAC,CAAC,GAAGW,OAAJ,EAAa,GAAGkB,GAAG,CAACC,OAApB,CAAD,CAAZ,CAAV;AACAZ,MAAAA,YAAY,CAACW,GAAG,CAACZ,SAAL,CAAZ;AACAD,MAAAA,cAAc,CAACa,GAAG,CAACE,UAAL,CAAd;AACD,KAPD;AAQD,GATD;;AAWA,sBACI,uDACE;AACI,IAAA,UAAU,MADd;AAEI,IAAA,SAAS,MAFb;AAGI,IAAA,KAAK,EAAE5B,KAAK,CAACqB,KAHjB,CAGyB;AAHzB;AAII,IAAA,YAAY,EAAE,KAJlB;AAKI,IAAA,QAAQ,EAAErB,KAAK,CAAC2D,QALpB;AAMI,IAAA,eAAe,EAAEzD,OAAO,gBAAG;AAAM,MAAA,IAAI,EAAC,OAAX;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MAAH,GAA2BF,KAAK,CAAC4D,eAN7D;AAOI,IAAA,IAAI,EAAE5D,KAAK,CAACyC,IAPhB;AAQI,IAAA,QAAQ,EAAGoB,CAAD,IAAO;AACf7B,MAAAA,eAAe,CAAC6B,CAAD,CAAf;AACD,KAVL;AAWI,IAAA,OAAO,EAAE,MAAM,CACd,CAZL;AAaI,IAAA,MAAM,EAAE,MAAM;AACZ1D,MAAAA,UAAU,CAAC,IAAD,CAAV;AACAE,MAAAA,gBAAgB,CAAC,KAAD,CAAhB;AACAqD,MAAAA,UAAU;AACX,KAjBL;AAkBI,IAAA,QAAQ,EAAE,CAACrC,KAAD,EAAQyC,MAAR,KAAmB;AAC3BhC,MAAAA,OAAO,CAACC,IAAR,CAAa,UAAb,EAAyBV,KAAzB,EAAgCyC,MAAhC;;AAEA,UAAI9D,KAAK,CAAC+D,YAAV,EAAwB;AACtB;AACA,YAAI,CAACC,KAAK,CAACC,OAAN,CAAc5C,KAAd,CAAL,EAA2B;AACzB;AACA,gBAAM6C,cAAc,GAAG1D,OAAO,CAAC2D,IAAR,CAAaC,GAAG,IAAIA,GAAG,CAACnB,EAAJ,KAAW5B,KAA/B,CAAvB;AACArB,UAAAA,KAAK,CAACqE,QAAN,CAAeH,cAAc,IAAI7C,KAAjC;AACD,SAJD,MAIO;AACL;AACA,gBAAMiD,eAAe,GAAGjD,KAAK,CAACkD,GAAN,CAAUC,GAAG,IACnChE,OAAO,CAAC2D,IAAR,CAAaC,GAAG,IAAIA,GAAG,CAACnB,EAAJ,KAAWuB,GAA/B,KAAuCA,GADjB,CAAxB;AAGAxE,UAAAA,KAAK,CAACqE,QAAN,CAAehD,KAAf,EAAsBiD,eAAtB;AACD;AACF,OAbD,MAaO;AACLtE,QAAAA,KAAK,CAACqE,QAAN,CAAehD,KAAf;AACD;AACF,KArCL;AAsCI,IAAA,uBAAuB,EAAGwC,CAAD,IAAOtB,eAAe,CAACsB,CAAD,CAtCnD;AAuCI,IAAA,YAAY,EAAE7D,KAAK,CAACyE,aAvCxB;AAwCI,IAAA,aAAa,EAAGZ,CAAD,IAAOnB,QAAQ,CAACmB,CAAD,CAxClC;AAyCI,IAAA,iBAAiB,EAAGa,OAAD,IAAaA,OAAO,CAACC,UAzC5C;AA0CI,IAAA,WAAW,EAAE3E,KAAK,CAAC4E,WA1CvB;AA2CI,IAAA,WAAW,EAAE5E,KAAK,CAAC6E,WA3CvB,CA4CI;AACA;AACA;AA9CJ;AA+CI,IAAA,UAAU,EAAE,GA/ChB;AAgDI,IAAA,cAAc,EAAGC,IAAD,iBACZ;AAAK,MAAA,GAAG,EAAE7E,QAAV;AAAoB,MAAA,EAAE,EAAC,UAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OACG6E,IADH,eAEE;AAAK,MAAA,SAAS,EAAElF,MAAM,CAACmF,kBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AAAM,MAAA,IAAI,EAAC,OAAX;AAAmB,MAAA,QAAQ,EAAE7E,OAA7B;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,MADF,EAEGE,aAAa,IAAIE,QAAjB,iBACG;AAAK,MAAA,SAAS,EAAEV,MAAM,CAACoF,oBAAvB;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,oBACE;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,gCADF,CAHN,CAFF,CAjDR;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,KA8DGxE,OA9DH,aA8DGA,OA9DH,uBA8DGA,OAAO,CAAE+D,GAAT,CAAclD,KAAD,IAAgB;AAAA;;AAC5B,WACI,wBAACrB,KAAK,CAACiF,WAAP,uDAAC,mBAAmBC,QAAnB,CAA4B7D,KAAK,CAAC4B,EAAlC,CAAD,kBACI,4BAAQ,MAAR;AAAe,MAAA,GAAG,EAAE5B,KAAK,CAAC4B,EAA1B;AAA8B,MAAA,EAAE,EAAE5B,KAAK,CAAC4B,EAAxC;AAA4C,MAAA,KAAK,EAAE5B,KAAK,CAAC4B,EAAzD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,OACGjD,KAAK,CAACmF,WAAN,CAAkB9D,KAAlB,CADH,CAFR;AAOD,GARA,CA9DH,CADF,EAyEGrB,KAAK,CAACoF,QAAN,iBACG;AACI,IAAA,OAAO,EAAE,MAAM;AACb;AACApF,MAAAA,KAAK,CAACqE,QAAN,CAAe,EAAf;AACD,KAJL;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,kBAME,oBAAC,cAAD;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA,IANF,CA1EN,CADJ;AAsFD,CAzND", "sourcesContent": ["import React, { ReactNode, useEffect, useRef, useState, } from \"react\";\r\nimport { Button, Select, Spin } from \"antd\";\r\nimport { DeleteOutlined } from \"@ant-design/icons\";\r\nimport config from \"@app/utils/config\";\r\nimport debounce from \"lodash/debounce\";\r\nimport styles from \"./index.module.less\";\r\nimport { uniqueArray } from \"../../utils/array\";\r\nimport logger from \"@app/utils/logger\";\r\n\r\nexport type InfinitySelectGetOptions = 'fetch' | 'load';\r\n\r\nexport interface ISelector {\r\n  data?: any;\r\n  getPaginatedRecords: (page: number, type: getOptions, keyValue: string) => Promise<Array<any>>;\r\n  onChange: (value: any, selectedValues?: Array<any>) => void;\r\n  onLoaded?: (isLoaded: boolean) => void;\r\n  formatValue: (value: any) => any;\r\n  mode?: \"multiple\" | \"tags\";\r\n  notFoundContent: string;\r\n  notLoadContent: string;\r\n  value?: any;\r\n  placeholder?: string;\r\n  isDelete?: boolean;\r\n  isDefault?: boolean;\r\n  queryParameters?: {};\r\n  excludeList?: Array<string>;\r\n  preSelected?: Array<any>;\r\n  defaultValues?: any;\r\n  removeById?: any;\r\n  maxTagCount?: any;\r\n  maxTagPlaceHolder?: (value: any) => void;\r\n  disabled?: boolean;\r\n  returnObject?: boolean;\r\n  newlyAddedValue?: any;\r\n  hideSelected?: boolean;\r\n  filterValues?: any;\r\n  suffixIcon?: ReactNode;\r\n  waitCharCount?: number;\r\n  onTop?: boolean;\r\n}\r\n\r\ntype getOptions = \"fetch\" | \"load\";\r\n\r\nconst DEFAULT_VIEW_SIZE = 15;\r\n\r\nexport default (props: ISelector) => {\r\n  const dropdown = useRef({}) as any;\r\n  const [loading, setLoading] = useState<boolean>(false);\r\n  const [isEndOfScroll, setIsEndOfScroll] = useState<boolean>(false);\r\n  const [isBottom, setIsBottom] = useState<boolean>(false);\r\n  // const [values, setValues] = useState<Array<string | number>>([]);\r\n  const [options, setOptions] = useState<Array<any>>([]);\r\n  const [keyValue, setKeyValue] = useState<string>(\"\");\r\n  const [currentPage, setCurrentPage] = useState<number>(1);\r\n  const [pageCount, setPageCount] = useState<number>(1);\r\n\r\n  window.addEventListener(\r\n      \"keydown\",\r\n      (event: any) => {\r\n        if (event.key === \"Backspace\" && !event.target.value) {\r\n          event.stopPropagation();\r\n        }\r\n      },\r\n      true\r\n  );\r\n\r\n  const fetchInitialData = () => {\r\n    setLoading(true);\r\n    props.getPaginatedRecords(1, 'fetch', keyValue).then((res: any) => {\r\n      setOptions(res.records);\r\n      setPageCount(res.pageCount);\r\n      setCurrentPage(res.pageNumber);\r\n      setLoading(false);\r\n    });\r\n  };\r\n\r\n  useEffect(() => {\r\n    fetchInitialData();\r\n  }, []);\r\n\r\n  const loadMore = () => {\r\n    setIsEndOfScroll(false);\r\n    setLoading(true);\r\n    props.getPaginatedRecords(currentPage + 1, \"load\", keyValue).then((res: any) => {\r\n      console.info('loadMore',uniqueArray([...options, ...res.records]))\r\n      setOptions(uniqueArray([...options, ...res.records]));\r\n      setPageCount(res.pageCount);\r\n      setCurrentPage(res.pageNumber);\r\n      setLoading(false);\r\n    });\r\n  };\r\n\r\n  const debounceFetcher = React.useMemo(() => {\r\n    const loadOptions = async (keyVal: string) => {\r\n      if (props.waitCharCount && keyVal.length < props.waitCharCount) {\r\n        setKeyValue(keyVal);\r\n        return;\r\n      }\r\n      setLoading(true);\r\n      props.getPaginatedRecords(1, \"fetch\", keyVal).then((res: any) => {\r\n        console.info('debounceFetcher',options,uniqueArray([...res.records,...options ]))\r\n\r\n        setOptions(uniqueArray([...res.records, ...options]));\r\n        setLoading(false);\r\n      });\r\n    };\r\n    return debounce(loadOptions, config.inputDebounceInterval);\r\n  }, [config.inputDebounceInterval, options]);\r\n\r\n  const onVisibleChange = (visibleState: boolean) => {\r\n    if (visibleState && props.mode) {\r\n        setOptions([]);\r\n        setKeyValue('');\r\n        setCurrentPage(1);\r\n        setPageCount(1);\r\n        setIsEndOfScroll(false);\r\n        setIsBottom(false);\r\n        setLoading(false);\r\n\r\n      props.getPaginatedRecords(1, 'fetch', '').then((res: any) => {\r\n        setOptions(res.records);\r\n        setPageCount(res.pageCount);\r\n        setCurrentPage(res.pageNumber);\r\n     });\r\n    } else if (keyValue && !visibleState && props.mode) {\r\n      setLoading(true);\r\n      props.getPaginatedRecords(1, 'fetch',keyValue).then((res: any) => {\r\n        console.info('onVisibleChange',uniqueArray([...options, ...res.records]))\r\n\r\n        setOptions(uniqueArray([...options, ...res.records]));\r\n        setPageCount(res.pageCount);\r\n        setCurrentPage(res.pageNumber);\r\n        setLoading(false);\r\n      });\r\n    }\r\n  };\r\n\r\n  const onScroll = (event: any) => {\r\n    setIsBottom(\r\n        event.target.scrollHeight - event.target.scrollTop ===\r\n        event.target.clientHeight\r\n    );\r\n    const lastElement: any = document.getElementById(\r\n        options ? options[(options || []).length - 1].id : \"null\"\r\n    );\r\n    const containerTop = dropdown\r\n        ? dropdown?.current.getBoundingClientRect().top\r\n        : null;\r\n    const lastElementTopPos =\r\n        lastElement?.getBoundingClientRect().top - containerTop;\r\n    const containerHeight = dropdown?.current?.getBoundingClientRect().height;\r\n    logger.debug('GenericDataTable','Infinity select - on scroll',{pageCount,currentPage})\r\n    if (\r\n        lastElementTopPos - DEFAULT_VIEW_SIZE < containerHeight &&\r\n        pageCount > currentPage &&\r\n        !loading\r\n    ) {\r\n      loadMore();\r\n    } else {\r\n      if (pageCount === currentPage) {\r\n        setIsEndOfScroll(true);\r\n      }\r\n    }\r\n  };\r\n\r\n  const handleBlur = () => {\r\n    props.getPaginatedRecords(1, 'fetch', '').then((res: any) => {\r\n      console.info('handleBlur',options,uniqueArray([...options, ...res.records]))\r\n\r\n      setKeyValue('');\r\n      setOptions(uniqueArray([...options, ...res.records]));\r\n      setPageCount(res.pageCount);\r\n      setCurrentPage(res.pageNumber);\r\n    });\r\n  };\r\n\r\n  return (\r\n      <>\r\n        <Select\r\n            showSearch\r\n            showArrow\r\n            value={props.value}  // Use props.value instead of internal state\r\n            filterOption={false}\r\n            disabled={props.disabled}\r\n            notFoundContent={loading ? <Spin size=\"small\" /> : props.notFoundContent}\r\n            mode={props.mode}\r\n            onSearch={(e) => {\r\n              debounceFetcher(e);\r\n            }}\r\n            onFocus={() => {\r\n            }}\r\n            onBlur={() => {\r\n              setLoading(true);\r\n              setIsEndOfScroll(false);\r\n              handleBlur();\r\n            }}\r\n            onChange={(value, option) => {\r\n              console.info('OnChange', value, option);\r\n              \r\n              if (props.returnObject) {\r\n                // Check if value is an array (multiple selection) or not (single selection)\r\n                if (!Array.isArray(value)) {\r\n                  // Single selection\r\n                  const selectedOption = options.find(opt => opt.id === value);\r\n                  props.onChange(selectedOption || value);\r\n                } else {\r\n                  // Multiple selection\r\n                  const selectedObjects = value.map(val => \r\n                    options.find(opt => opt.id === val) || val\r\n                  );\r\n                  props.onChange(value, selectedObjects);\r\n                }\r\n              } else {\r\n                props.onChange(value);\r\n              }\r\n            }}\r\n            onDropdownVisibleChange={(e) => onVisibleChange(e)}\r\n            defaultValue={props.defaultValues}\r\n            onPopupScroll={(e) => onScroll(e)}\r\n            getPopupContainer={(trigger) => trigger.parentNode as HTMLElement}\r\n            placeholder={props.placeholder}\r\n            maxTagCount={props.maxTagCount}\r\n            // maxTagPlaceholder={(e) =>\r\n            //   props.maxTagPlaceHolder && props.maxTagPlaceHolder(e)\r\n            // }\r\n            listHeight={200}\r\n            dropdownRender={(menu) => (\r\n                <div ref={dropdown} id=\"dropdown\">\r\n                  {menu}\r\n                  <div className={styles.yjInfinityLoadMore}>\r\n                    <Spin size=\"small\" spinning={loading} />\r\n                    {isEndOfScroll && isBottom && (\r\n                        <div className={styles.yjInfinityScrollText}>\r\n                          <p>You have seen it all !</p>\r\n                        </div>\r\n                    )}\r\n                  </div>\r\n                </div>\r\n            )}\r\n        >\r\n          {options?.map((value: any) => {\r\n            return (\r\n                !props.excludeList?.includes(value.id) && (\r\n                    <Select.Option key={value.id} id={value.id} value={value.id}>\r\n                      {props.formatValue(value)}\r\n                    </Select.Option>\r\n                )\r\n            );\r\n          })}\r\n        </Select>\r\n        {props.isDelete && (\r\n            <Button\r\n                onClick={() => {\r\n                  // setValues([]);\r\n                  props.onChange([])\r\n                }}\r\n            >\r\n              <DeleteOutlined />\r\n            </Button>\r\n        )}\r\n      </>\r\n  );\r\n};\r\n"]}, "metadata": {}, "sourceType": "module"}