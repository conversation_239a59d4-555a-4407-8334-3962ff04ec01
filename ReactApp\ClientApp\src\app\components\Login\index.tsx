import React, { Fragment, useEffect } from "react";
import { useOktaAuth } from "@okta/okta-react";
import { useHistory } from "react-router-dom";
import Constants from "@app/constants";
import logger from "@app/utils/logger";

const Login = () => {
  const history = useHistory();
  const { oktaAuth, authState } = useOktaAuth();
  useEffect(() => {
    const generateRandomString = (length: number) => {
      const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
      let result = '';
      for (let i = 0; i < length; i++) {
        result += characters.charAt(Math.floor(Math.random() * characters.length));
      }
      return result;
    };

    const checkAuthentication = async () => {
      if (authState?.isAuthenticated) {
        history.push(Constants.postSignedInRedirect);
      } else {
        try {
          const session = await oktaAuth.session.exists();
          if (session) {
            // Generate random state and nonce strings
            const state = generateRandomString(10);  // Example length, adjust as needed
            const nonce = generateRandomString(16);  // Example length, adjust as needed

            // Use the generated state and nonce in your authentication request
            const tokens = await oktaAuth.token.getWithoutPrompt({
              responseType: ['token', 'id_token'],
              scopes: ['openid', 'profile', 'email'],
              state: state,
              nonce: nonce
            });

            // Process tokens as needed
            logger.info('Auth Module', 'checkAuthentication', tokens); 
          } else {
            oktaAuth.signInWithRedirect({
              responseType: ['token', 'id_token'],
              scopes: ['openid', 'profile', 'email'],
              prompt: 'none'  // Ensures no UI prompt during the redirect flow
              // Additional options as needed
            });
          }
        } catch (error) {
          logger.error('Auth Module', 'checkAuthentication', error);
          oktaAuth.signInWithRedirect();  // Fall back to redirect authentication on error
        }
      }
    };

    checkAuthentication();
  }, [authState, oktaAuth, history]);

  return <Fragment></Fragment>;
};

export default Login;
