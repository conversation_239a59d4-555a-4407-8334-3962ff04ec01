import React from "react";
import {Button} from "antd";
import renderer from "react-test-renderer";
import { shallow } from "enzyme";
import initTestSuite from "@app/utils/config/TestSuite";
import Reject from "../";
import SelectedFilesGrid from "@app/features/FileArea/SelectedFilesGrid";


describe("Portal Files - Reject Test Suite",() => {
    beforeAll(() => {
        initTestSuite();
    });

    it("should render", () => {
        const rejectComponent = shallow(<Reject />);
        expect(rejectComponent.html()).not.toBe(null);
    });

    it("should render and create snapshot properly", () => {
        const rejectComponent = renderer.create(<Reject />).toJSON();
        expect(rejectComponent).toMatchSnapshot();
    });

    it("should have two buttons", () => {
        const rejectComponent = shallow(<Reject />);
        expect(rejectComponent.find(Button)).toHaveLength(2);
    });

    it("should have SelectedFilesGrid component",() => {
        const rejectComponent = shallow(<Reject />);
        expect(rejectComponent.find(SelectedFilesGrid)).toHaveLength(1);
    });
});
