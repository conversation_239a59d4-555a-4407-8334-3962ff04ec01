import HTTPResponse from "../utils/http/interfaces/HttpResponse";
import httpVerbs from "../utils/http/httpVerbs";
import http from "../utils/http";
import config from "@app/utils/config";
import { getParameterizedUrl } from "@app/utils";

export const getLicenseDetails = (
  licenseId: string
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: `${config.api.organizationAPI.licenses}/${licenseId}`,
  });
};

export const getCompliances = (): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: config.api.organizationAPI.compliances,
  });
};

export const getStatuses = (licenseId: string): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: getParameterizedUrl(
      config.api.organizationAPI.statusesByLicense,
      licenseId
    ),
  });
};

export const getStorages = (): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: config.api.organizationAPI.storages,
  });
};

export const getUserCounts = (): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: config.api.organizationAPI.userCounts,
  });
};

export const getSupportLevels = (): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.GET,
    url: config.api.organizationAPI.supportLevels,
  });
};

export const createLicense = (data: any): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.POST,
    url: config.api.organizationAPI.licenses,
    data: data,
  });
};

export const modifyLicense = (
  licenseId: string,
  data: any
): Promise<HTTPResponse<any>> => {
  return http({
    method: httpVerbs.PUT,
    url: `${config.api.organizationAPI.licenses}/${licenseId}`,
    data: data,
  });
};
