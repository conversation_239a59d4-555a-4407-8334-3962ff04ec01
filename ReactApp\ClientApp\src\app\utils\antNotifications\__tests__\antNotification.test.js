import {
  errorNotification,
  successNotification,
  warningNotification,
} from "../index";

describe("Ant notifications test suite", () => {
  it("errorNotification function should exsist", () => {
    expect(typeof errorNotification).toBe("function");
  });
  it("successNotification function should exsist", () => {
    expect(typeof successNotification).toBe("function");
  });
  it("warningNotification function should exsist", () => {
    expect(typeof warningNotification).toBe("function");
  });
});
